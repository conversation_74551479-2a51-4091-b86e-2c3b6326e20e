<script>
  import NoData from '@/components/no_data.vue'
  import TextButton from '@/components/text_button.vue'
  import RoleEditCard from '../../teacher/components/role_edit_card.vue'

  import { apiGetDownloadAssetConfigs, apiAddDownloadAssetConfig, apiDeleteDownloadAssetConfig } from '@/api/user'

  import { deepCopy } from '@/utils/object'

  export default {
    components: {
      'no-data': NoData,
      'role-edit-card': RoleEditCard,
    },
    data() {
      return {
        configList: [],
        tableSelected: [],
        showEditCard: false,
        loading: false,

        tableColumns: [
          {
            type: 'selection',
            width: 40,
            align: 'center',
          },
          {
            title: '姓名',
            align: 'center',
            key: 'realName',
            width: 260,
          },
          {
            title: '手机号码',
            align: 'center',
            key: 'mobile',
            width: 400,
          },
          {
            title: '下载范围',
            align: 'center',
            render: h => h('span', {}, '答题卡、答案、听力音频'),
          },
          {
            title: '删除',
            align: 'center',
            width: 200,
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'warning',
                  onClick: () => {
                    this.$Modal.confirm({
                      title: '删除配置',
                      content: '确定要删除权限配置 【' + params.row.realName + '】 ？',
                      onOk: () => {
                        this.deleteConfigs([params.row])
                          .then(() =>
                            this.$Message.success({
                              duration: 3,
                              content: '已删除',
                            })
                          )
                          .finally(this.fetchConfigs)
                      },
                    })
                  },
                },
                () => '删除'
              ),
          },
        ],
      }
    },
    created() {
      this.fetchConfigs()
    },
    methods: {
      fetchConfigs() {
        this.tableSelected = []
        return apiGetDownloadAssetConfigs().then(
          response =>
            (this.configList = (response || []).map(item => {
              item.userId = item.teacherId
              return item
            }))
        )
      },

      handleEditOk(teachers) {
        const TeachersForAdd = teachers.filter(t => !this.configList.some(c => c.userId === t.userId))
        const TeachersForDelete = this.configList.filter(c => !teachers.some(t => t.userId === c.userId))
        const RequestFunctions = []
        if (TeachersForAdd.length) {
          RequestFunctions.push({
            func: this.addConfigs,
            params: TeachersForAdd,
          })
        }
        if (TeachersForDelete) {
          RequestFunctions.push({
            func: this.deleteConfigs,
            params: TeachersForDelete,
          })
        }
        if (RequestFunctions.length) {
          Promise.all(RequestFunctions.map(item => item.func(item.params)))
            .then(() => {
              this.$Message.success({
                duration: 3,
                content: '已修改',
              })
              this.showEditCard = false
            })
            .finally(this.fetchConfigs)
        }
      },

      addConfigs(teachers = []) {
        if (!Array.isArray(teachers) || !teachers.length) {
          return Promise.resolve()
        }

        return apiAddDownloadAssetConfig(teachers.map(t => t.userId))
      },

      deleteConfigs(teachers = []) {
        if (!Array.isArray(teachers) || !teachers.length) {
          return Promise.resolve()
        }

        return apiDeleteDownloadAssetConfig(teachers.map(t => t.userId))
      },

      handleTableSelectionChanged(teachers = []) {
        this.tableSelected = deepCopy(teachers)
      },

      handleDeleteTableSelectedClick() {
        this.$Modal.confirm({
          title: '删除配置',
          content: '确定要删除已选的 ' + this.tableSelected.length + ' 个权限配置？',
          onOk: () => {
            this.deleteConfigs(this.tableSelected)
              .then(() =>
                this.$Message.success({
                  duration: 3,
                  content: '已删除',
                })
              )
              .finally(this.fetchConfigs)
          },
        })
      },
    },
  }
</script>

<template>
  <div class="container-school-download-supplementary-exercise">
    <div class="header">
      <div class="title">教辅下载权限配置</div>
      <div>
        <TextButton
          v-if="tableSelected.length"
          type="warning"
          style="margin-right: 10px"
          @click="handleDeleteTableSelectedClick"
          >删除已选权限配置</TextButton
        >
        <Button type="primary" @click="showEditCard = true">设置</Button>
      </div>
    </div>

    <div class="body">
      <Table
        v-if="configList.length"
        :columns="tableColumns"
        :data="configList"
        @on-selection-change="handleTableSelectionChanged"
      ></Table>
      <no-data v-else></no-data>
    </div>

    <role-edit-card
      :show="showEditCard"
      :teachers="configList"
      title="教辅下载权限配置"
      @cancel="showEditCard = false"
      @ok="handleEditOk"
    ></role-edit-card>
  </div>
</template>

<style lang="scss" scoped>
  .container-school-download-supplementary-exercise {
    padding: 20px;
    background-color: white;
  }

  .header {
    @include flex(row, space-between, center);
    margin-bottom: 20px;

    .title {
      font-size: 30px;
    }
  }
</style>
