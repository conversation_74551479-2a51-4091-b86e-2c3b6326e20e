<template>
  <el-input
    :maxlength="options?.maxLength?.value || 50"
    :minlength="options?.minLength?.value || 0"
    :placeholder="options?.placeholder?.value || '请输入内容'"
    clearable
  ></el-input>
</template>

<script>
  import { ElInput } from 'element-plus'
  import 'element-plus/es/components/input/style/css'

  export default {
    components: {
      'el-input': ElInput,
    },
    props: {
      options: {
        type: Object,
        default: () => ({
          placeholder: {
            value: '请输入内容',
          },
          minLength: {
            value: 1,
          },
          maxLength: {
            value: 50,
          },
        }),
      },
    },
  }
</script>
