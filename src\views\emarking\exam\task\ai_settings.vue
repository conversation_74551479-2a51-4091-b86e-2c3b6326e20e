<script>
  import TextButton from '@/components/text_button'
  import ComponentProgress from '@/views/emarking/monitor/components/progress.vue'
  import ModalAIPrompt from './components/ai_settings/modal_ai_prompt.vue'
  import ModalAiScoreHistory from './components/ai_settings/modal_ai_score_history.vue'
  import ModalAiAbnormalHistory from './components/ai_settings/modal_ai_abnormal_history.vue'

  import { apiGetBlocks, apiGetBindPaperContent, apiGetSubjectiveQuestions } from '@/api/emarking'
  import { apiChangeAiScoreStatus, apiGetAIMarkingStat, apiRedoBlockAIMarking } from '@/api/emarking/ai'

  import AIMarkingScoreTypeEnum from '@/enum/emarking/ai_marking/ai_marking_score_type'
  import AiScoreStatusEnum from '@/enum/emarking/ai_score_status'

  export default {
    components: {
      'modal-ai-prompt': ModalAIPrompt,
      'modal-ai-score-history': ModalAiScoreHistory,
      'modal-ai-abnormal-history': ModalAiAbnormalHistory,
    },

    data() {
      return {
        tableColumns: [
          {
            title: '题块名称',
            slot: 'blockName',
          },
          {
            title: '包含小题',
            align: 'center',
            render: (h, params) => {
              const QuestionCodes = (params.row.subScore || '').split(';').map(item => {
                const QuestionMap = item.split('=')
                return QuestionMap[0]
              })

              return h(
                'div',
                {
                  style: {
                    padding: '6px 8px',
                    'line-height': 1.4,
                  },
                },
                QuestionCodes.filter(String).join('、')
              )
            },
          },
          {
            title: '满分',
            align: 'center',
            key: 'fullScore',
            width: 80,
          },
          {
            title: '平均分',
            align: 'center',
            key: 'avgScore',
            width: 80,
          },
          {
            title: '最高分',
            align: 'center',
            key: 'maxScore',
            width: 80,
          },
          {
            title: '最低分',
            align: 'center',
            key: 'minScore',
            width: 80,
          },
          {
            title: 'AI评卷提示词',
            width: 120,
            align: 'center',
            render: (h, params) => {
              return h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => {
                    this.modalPromptInitBlockId = params.row.blockId
                    this.showModalPrompt = true
                  },
                },
                () => '点击查看'
              )
            },
          },
          {
            title: 'AI评卷进度',
            width: 160,
            align: 'center',
            render: (h, params) =>
              h(ComponentProgress, {
                total: params.row.totalCount,
                current: params.row.markedCount,
                class: 'table-cell-progress',
                onClick: () => {
                  this.modalTargetBlockProgressId = params.row.blockId
                  this.modalAiScoreHistoryShow = true
                },
              }),
          },
          {
            title: 'AI疑难卷',
            width: 160,
            align: 'center',
            render: (h, params) =>
              h(ComponentProgress, {
                total: params.row.abnormalCount,
                current: params.row.abnormalMarkedCount,
                class: 'table-cell-progress',
                onClick: () => {
                  this.modalTargetBlockProgressId = params.row.blockId
                  this.modalAiAbnormalHistoryShow = true
                },
              }),
          },
          {
            title: '操作',
            width: 200,
            align: 'center',
            render: (h, params) => {
              const Btns = []
              const AIScoreStatus = params.row.aiScoreStatus
              if (!AIScoreStatus || AIScoreStatus == AiScoreStatusEnum.Pause.id) {
                Btns.push(
                  h(
                    TextButton,
                    {
                      type: 'primary',
                      disabled: this.subjectStatusSuspended,
                      onClick: () => this.changeAiScoreStatus(params.row, AiScoreStatusEnum.Doing.id),
                    },
                    () => '开启AI评卷'
                  )
                )
              }
              if (AIScoreStatus == AiScoreStatusEnum.Doing.id) {
                Btns.push(
                  h(
                    TextButton,
                    {
                      type: 'warning',
                      onClick: () => this.changeAiScoreStatus(params.row, AiScoreStatusEnum.Pause.id),
                    },
                    () => '暂停AI评卷'
                  )
                )
              }
              Btns.push(
                h(
                  TextButton,
                  {
                    type: 'warning',
                    onClick: () => this.resetMarking(params.row),
                  },
                  () => '重置AI评卷'
                )
              )
              return h('div', {}, Btns)
            },
          },
        ],

        showModalPrompt: false,
        modalPromptInitBlockId: '',

        // exam subject params
        blocks: [],
        blockList: [],
        subjectiveQuestions: [],
        subjectPaperContent: null,

        // progress
        modalTargetBlockProgressId: '',
        modalAiScoreHistoryShow: false,
        modalAiAbnormalHistoryShow: false,
      }
    },

    computed: {
      exam() {
        return this.$store.getters['emarking/exam']
      },
      isExamAdministrator() {
        return this.$store.getters['emarking/isExamAdministrator']
      },
      userId() {
        return this.$store.getters['user/info'].userId
      },
      currentExamSubject() {
        return this.$store.getters['emarking/currentExamSubject']
      },
      isMultipleSchool() {
        return this.$store.getters['emarking/isMultipleSchool']
      },
      adminExamSubjects() {
        return this.$store.getters['emarking/adminExamSubjects']
      },
      currentExamSubjectId() {
        return this.$store.getters['emarking/currentExamSubjectId']
      },
      isSubjectAdministrator() {
        return (this.currentExamSubject?.administrators || []).some(u => u.userId === this.userId)
      },
      isSubjectSpoter() {
        return (this.currentExamSubject?.spoters || []).some(u => u.userId === this.userId)
      },
    },

    watch: {
      currentExamSubjectId() {
        if (this.currentExamSubjectId) {
          this.initSubject()
        }
      },
    },

    created() {
      this.initSubject()
    },

    methods: {
      initSubject() {
        if (this.adminExamSubjects && this.adminExamSubjects.length) {
          let changeSubjectId
          if (this.adminExamSubjects.every(s => s.examSubjectId !== this.currentExamSubjectId)) {
            changeSubjectId = this.adminExamSubjects[0].examSubjectId
          }
          if (changeSubjectId) {
            this.changeSubject(changeSubjectId)
          }

          this.fetchBlockStat()
        }
      },
      changeSubject(examSubjectId) {
        if (examSubjectId !== this.currentExamSubjectId) {
          this.$store.commit('emarking/changeCurrentExamSubjectId', examSubjectId)
          this.blockList = []
          this.subjectiveQuestions = []
          this.subjectPaperContent = null
        }
      },
      backToTask() {
        if (window.history?.state?.back) {
          this.$router.back()
        } else {
          this.$router.replace({
            name: 'emarking-exam-task',
          })
        }
      },
      changeAiScoreStatus(block, aiScoreStatus) {
        return apiChangeAiScoreStatus({
          examSubjectId: this.currentExamSubjectId,
          blockIds: [block.blockId],
          aiScoreStatus,
        }).then(() => {
          this.$Message.success('已更改状态 ')
          this.fetchBlockStat()
        })
      },
      resetMarking(block) {
        if (block.aiScoreStatus == AiScoreStatusEnum.Doing.id) {
          this.$Message.warning('重置前请先暂停AI评卷')
          return
        }

        const _DoRest = () => {
          this.$Spin.show({
            render: h =>
              h(
                'div',
                {
                  style: {
                    fontSize: '24px',
                  },
                },
                `正在重置【${block.blockName}】AI评卷`
              ),
          })
          apiRedoBlockAIMarking(block.blockId)
            .then(() => {
              this.$Message.success({
                content: `已重置【${block.blockName}】AI评卷`,
              })
            })
            .finally(() => {
              this.fetchBlockStat()
              this.$Spin.hide()
            })
        }

        const ConfirmMessage = `<span style="color:red">重置题块AI评卷将会清空本题块所有AI评卷数据，且不可恢复，请务必谨慎操作！</span><br><br>确定重置【${block.blockName}】的AI评卷记录？`
        this.$Modal.confirm({
          title: '重置题块AI评卷',
          content: ConfirmMessage,
          onOk: () => {
            setTimeout(() => {
              this.$Modal.confirm({
                title: '请再次确认',
                content: ConfirmMessage,
                onOk: () => {
                  _DoRest()
                },
              })
            }, 1000)
          },
        })
      },

      fetchBlockStat() {
        this.blocks = []
        return apiGetAIMarkingStat({
          examSubjectId: this.currentExamSubjectId,
        }).then(async response => {
          if (response && Array.isArray(response) && response.length) {
            const BlockList = await this.getBlockList()
            const SubjectiveQuestions = await this.getSubjectiveQuestions()
            const SubjectPaperContent = await this.getSubjectPaperContent()
            const SubjectPaperContentQuestions = SubjectPaperContent?.questionList || []

            this.blocks = response.map(b => {
              const StatusClasses = ['btn']
              let statusText = ''

              if (!b.aiScoreStatus) {
                statusText = '未开启'
              } else if (b.aiScoreStatus === AiScoreStatusEnum.Doing.id) {
                StatusClasses.push('btn-marking')
                statusText = '进行中'
              } else if (b.aiScoreStatus === AiScoreStatusEnum.Pause.id) {
                statusText = '已暂停'
              } else if (b.aiScoreStatus === AiScoreStatusEnum.Success.id) {
                StatusClasses.push('btn-finish')
                statusText = '已完成'
              }

              const TargetBlock = BlockList.find(rb => rb.blockId === b.subjectiveId)
              const BlockQuestions = (TargetBlock?.questions || []).map(bq => {
                let answer

                const CorrespondingSubjectiveQuestion = SubjectiveQuestions.find(
                  sq => sq.questionCode === bq.questionCode && sq.branchCode === bq.branchCode
                )
                if (CorrespondingSubjectiveQuestion) {
                  const TargetSubjectPaperContentQuestion = SubjectPaperContentQuestions.find(
                    sq => sq.id === CorrespondingSubjectiveQuestion.originalQuestionId
                  )
                  const TargetSubjectPaperContentBranch = (TargetSubjectPaperContentQuestion?.branches || []).find(
                    sb => sb.id === CorrespondingSubjectiveQuestion.originalBranchId
                  )
                  const TargetSubjectPaperContentSolution = TargetSubjectPaperContentBranch?.solution || ''
                  try {
                    answer = JSON.parse(TargetSubjectPaperContentSolution)
                  } catch {
                    answer = TargetSubjectPaperContentSolution
                  }
                }

                return {
                  ...bq,
                  answer: answer || undefined,
                }
              })

              return {
                ...b,
                blockId: b.subjectiveId,
                blockName: b.subjectiveName,
                statusText: statusText,
                statusClasses: StatusClasses.join(' '),
                questions: BlockQuestions,
              }
            })
          }
        })
      },
      fetchBlockList() {
        this.blockList = []
        return apiGetBlocks({
          examId: this.exam.examId,
          examSubjectId: this.currentExamSubjectId,
        }).then(response => (this.blockList = response || []))
      },
      async getBlockList() {
        if (!this.blockList || !this.blockList.length) {
          await this.fetchBlockList()
        }
        return Promise.resolve(this.blockList)
      },
      fetchSubjectiveQuestions() {
        this.subjectiveQuestions = []
        return apiGetSubjectiveQuestions({
          examId: this.exam.examId,
          examSubjectId: this.currentExamSubjectId,
        }).then(response => (this.subjectiveQuestions = response || []))
      },
      async getSubjectiveQuestions() {
        if (!this.subjectiveQuestions || !this.subjectiveQuestions.length) {
          await this.fetchSubjectiveQuestions()
        }
        return Promise.resolve(this.subjectiveQuestions)
      },
      fetchSubjectPaperContent() {
        this.subjectPaperContent = null
        return apiGetBindPaperContent({
          examId: this.exam.examId,
          examSubjectId: this.currentExamSubjectId,
        }).then(response => (this.subjectPaperContent = response || null))
      },
      async getSubjectPaperContent() {
        if (!this.subjectPaperContent) {
          await this.fetchSubjectPaperContent()
        }
        return Promise.resolve(this.subjectPaperContent)
      },

      // for show
      isBlockAiScore(block) {
        return (
          block &&
          block.aiScoreType &&
          [AIMarkingScoreTypeEnum.AIAssist.id, AIMarkingScoreTypeEnum.AIRound.id].includes(block.aiScoreType)
        )
      },
      getBlockAiScoreTypeName(block) {
        return AIMarkingScoreTypeEnum.getNameById(block && block.aiScoreType) || ''
      },
    },
  }
</script>

<template>
  <div class="page-exam-task-ai-settings">
    <div v-if="adminExamSubjects.length && !isMultipleSchool" class="main-panel">
      <div class="section-subject-selector">
        <RadioGroup :model-value="currentExamSubjectId" type="button" button-style="solid" @on-change="changeSubject">
          <Radio v-for="s of adminExamSubjects" :key="s.examSubjectId" :label="s.examSubjectId">{{
            s.subjectName
          }}</Radio>
        </RadioGroup>
      </div>

      <div class="section-actions">
        <div class="action-left">
          <TextButton type="primary" icon="md-arrow-back" @click="backToTask">返回评卷任务</TextButton>
          <Divider type="vertical" />
          <div class="page-title">AI评卷设置</div>
        </div>

        <Button type="primary" size="small" icon="md-refresh" @click="fetchBlockStat">刷新</Button>
      </div>

      <Table :columns="tableColumns" :data="blocks" border class="table-blocks">
        <template #blockName="{ row }">
          <div class="box-block-name">
            <span>{{ row.blockName }}</span>
            <div :class="row.statusClasses">
              <span>{{ row.statusText }}</span>
            </div>
          </div>
        </template>
      </Table>
    </div>
    <div v-else class="no-permission">抱歉，由于您权限不足，无法设置评卷任务</div>

    <modal-ai-prompt
      v-model="showModalPrompt"
      :exam-subject-id="currentExamSubjectId"
      :blocks="blocks"
      :init-block-id="modalPromptInitBlockId"
      @refresh-prompt="fetchBlockStat"
    ></modal-ai-prompt>

    <modal-ai-score-history
      v-model="modalAiScoreHistoryShow"
      :blocks="blocks"
      :target-block-id="modalTargetBlockProgressId"
    ></modal-ai-score-history>

    <modal-ai-abnormal-history
      v-model="modalAiAbnormalHistoryShow"
      :blocks="blocks"
      :target-block-id="modalTargetBlockProgressId"
    ></modal-ai-abnormal-history>
  </div>
</template>

<style lang="scss" scoped>
  .page-exam-task-ai-settings {
    .main-panel {
      padding: 20px;
      background-color: white;

      .section-subject-selector {
        @include flex(row, space-between, center);
        height: 32px;
      }

      .section-actions {
        @include flex(row, space-between, center);
        margin-top: 15px;
        margin-bottom: 10px;
        line-height: 1;

        .action-left {
          @include flex(row, flex-start, center);

          .page-title {
            margin-left: 0.6em;
            color: $color-content;
            font-weight: bold;
            font-size: $font-size-medium-x;
          }
        }
      }

      .table-blocks {
        :deep(.status-primary) {
          color: $color-primary;
        }

        :deep(.status-warning) {
          color: $color-warning;
        }

        :deep(.table-cell-progress) {
          user-select: none;

          &:hover {
            cursor: pointer;
          }
        }

        .box-block-name {
          @include flex(row, flex-start, center);

          .btn {
            box-sizing: content-box;
            width: 4em;
            height: 20px;
            margin-left: 6px;
            padding: 2px 0;
            color: $color-warning;
            font-size: $font-size-small;
            line-height: 20px;
            text-align: center;
            background-color: #f0f0f0;
            user-select: none;
          }

          .btn-marking {
            color: $color-primary;
          }

          .btn-finish {
            color: white;
            background-color: $color-primary;
          }
        }
      }
    }

    .no-permission {
      font-weight: bold;
      font-size: $font-size-large;
      line-height: 100px;
      text-align: center;
    }
  }
</style>
