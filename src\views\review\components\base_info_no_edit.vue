<script>
  export default {
    props: {
      formOptions: {
        type: Object,
        default: () => {},
      },
    },
  }
</script>

<template>
  <div class="container-base-info">
    <div class="header"><span>基本信息</span></div>
    <div class="body">
      <div class="info-item">
        <div class="info-label">活动名称</div>
        <div class="info-value">{{ formOptions.name || '' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">活动简介</div>
        <div class="info-value">{{ formOptions.description || '' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">参赛者</div>
        <div class="info-value">
          {{ formOptions.participantIdentity ? (formOptions.participantIdentity === 'student' ? '学生' : '教师') : '' }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">报名开始时间</div>
        <div class="info-value">
          {{ (formOptions.registerStartTime && formOptions.registerStartTime.slice(0, 10)) || '' }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">报名截止时间</div>
        <div class="info-value">
          {{ (formOptions.registerEndTime && formOptions.registerEndTime.slice(0, 10)) || '' }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">提交截止时间</div>
        <div class="info-value">{{ (formOptions.submitEndTime && formOptions.submitEndTime.slice(0, 10)) || '' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">评审类别</div>
        <div class="info-value">
          {{ (formOptions.categories || []).map(c => c.name).join('、') }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">报名后需审核</div>
        <div class="info-value">{{ formOptions.needAudit ? '是' : '否' }}</div>
      </div>
      <div v-if="formOptions.needAudit" class="info-item">
        <div class="info-label">允许本校审核报名</div>
        <div class="info-value">{{ formOptions.enableSchoolAudit ? '是' : '否' }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .container-base-info {
    .body {
      .info-item {
        @include flex(row, flex-start, stretch);
        margin-bottom: 18px;

        .info-label {
          flex: none;
          width: 200px;
          padding-right: 12px;
          text-align: right;
        }

        .info-value {
          flex: 1;
          margin-left: 18px;
        }
      }
    }
  }
</style>
