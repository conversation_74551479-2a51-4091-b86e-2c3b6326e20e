<template>
  <div class="container-supplementary-exercise-setting">
    <div class="header">
      <div v-if="test || !loading" class="nav">
        <Button type="info" icon="md-arrow-back" @click="handleBtnBackToEmarkingHome">返回</Button>
        <span class="test-name">{{ (test && test.examName) || '' }}</span>
        <span class="upload-mode" :class="{ 'post-scan': test.isPostScan }">{{ test.uploadModeText }}</span>
      </div>
      <div v-if="test && canEditExam" class="action">
        <TextButton type="warning" icon="md-create" @click="showModalEditInfo = true">修改名称</TextButton>
        <TextButton type="error" icon="md-trash" @click="handleTBtnDeleteTestDetailClick">删除项目</TextButton>
      </div>
    </div>

    <div v-if="test" class="main">
      <Tabs class="section section-question" :animated="false">
        <TabPane label="客观题">
          <TabObjective
            :exam-id="test.examId"
            :exam-subject-id="test.examSubjectId"
            :objectives="objectives"
            :objectives-total-score="objectivesTotalScore"
            :enable-change-score-answer="canEditExam"
            @update-objectives="fetchObjectives"
          ></TabObjective>
        </TabPane>
        <TabPane label="主观题">
          <TabSubjective
            :exam-id="test.examId"
            :exam-subject-id="test.examSubjectId"
            :subjectives="subjectives"
            :subjectives-total-score="subjectivesTotalScore"
            :enable-change-score="canEditExam && test.needMark"
            @update-subjectives="fetchSubjectives"
          ></TabSubjective>
        </TabPane>
        <template v-if="test.needMark">
          <TabPane label="评卷题块">
            <TabBlock
              :exam-id="examId"
              :exam-subject-id="examSubjectId"
              :can-edit-exam="canEditExam"
              :blocks="blocks"
              :subjectives="subjectives"
              :enable-ai-score="false"
              @refresh-blocks="fetchBlocks"
            ></TabBlock>
          </TabPane>
          <TabPane v-if="canEditExam" label="评卷任务">
            <tab-marking-settings
              ref="tabMarkingSettings"
              :exam-id="test.examId"
              :current-exam-subject-id="test.examSubjectId"
              :current-exam-subject-name="test.subjectName"
            ></tab-marking-settings>
          </TabPane>
        </template>
      </Tabs>

      <div v-if="canEditExam" class="section section-class">
        <div class="tabs-actions">
          <Tabs v-model="activeClassId" class="tabs" :animated="false">
            <TabPane v-for="cls in classList" :key="cls.classId" :name="cls.classId" :label="cls.className"></TabPane>
          </Tabs>
          <span v-if="canEditExam && subjectUnfinish" class="actions">
            <TextButton type="primary" @click="syncStudents">同步考生</TextButton>
            <TextButton v-if="test.needMark && showTeacherInfoInSectionClass" type="primary" @click="syncTeachers"
              >同步评卷老师</TextButton
            >
          </span>
        </div>

        <TabClass
          v-if="activeClass"
          :cls="activeClass"
          :need-mark="test.needMark && showTeacherInfoInSectionClass"
        ></TabClass>
      </div>
    </div>

    <ModalEditInfo v-model="showModalEditInfo" :exam="test" @on-refresh="fetchTestDetailAndQuestions" />
  </div>
</template>

<script>
  import TabObjective from '@/views/class_practise/setting/components/objective.vue'
  import TabSubjective from '@/views/class_practise/setting/components/subjective.vue'
  import TabBlock from '@/views/class_practise/setting/components/block.vue'
  import TabClass from '@/views/class_practise/setting/components/class.vue'
  import TabMarkingSettings from './components/marking_settings.vue'
  import ModalEditInfo from '@/views/class_practise/setting/components/modal_edit_info.vue'

  import {
    apiDeleteExam,
    apiGetObjectiveQuestions,
    apiGetSubjectiveQuestions,
    apiGetBlocks,
    apiGetExamStudents,
    apiGetMarkersByClass,
  } from '@/api/emarking'
  import { apiSyncStudents, apiSyncTeachers } from '@/api/emarking/practise'
  import { apiGetCoachHomeworkInfo } from '@/api/emarking/coach_homework'

  import Store from '@/store/index'

  import SubjectStatusEnum from '@/enum/emarking/subject_status'
  import UploadModeEnum from '@/enum/emarking/upload_mode'
  import MarkingWayEnum from '@/enum/emarking/marking_way'

  export default {
    components: {
      TabObjective,
      TabSubjective,
      TabBlock,
      TabClass,
      'tab-marking-settings': TabMarkingSettings,
      ModalEditInfo,
    },

    beforeRouteEnter(to, from, next) {
      const IsSchoolAdministrator = Store.getters['user/isSchoolAdministrator']
      if (IsSchoolAdministrator) {
        Store.dispatch('emarking/setAdminExamById', to.params.examId).then(() => {
          Store.commit('emarking/changeCurrentExamSubjectId', to.params.examSubjectId)
          next()
        })
      } else {
        next()
      }
    },

    data() {
      return {
        // 加载中
        loading: false,
        // 测验信息
        test: null,
        // 客观题
        objectives: [],
        // 主观题
        subjectives: [],
        // 题块
        blocks: [],
        // 当前班级
        activeClassId: '',
        // 各班考生、评卷员
        classList: [],
        // 修改名称弹框
        showModalEditInfo: false,

        refTabMarkingSettings: null,
      }
    },

    computed: {
      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },
      userInfo() {
        return this.$store.getters['user/info']
      },
      canEditExam() {
        return this.isSchoolAdministrator
      },
      examId() {
        return this.$route.params.examId
      },
      examSubjectId() {
        return this.$route.params.examSubjectId
      },
      exam() {
        return this.$store.getters['emarking/exam']
      },
      objectivesTotalScore() {
        return this.objectives && this.objectives.length
          ? this.objectives.reduce((acc, cur) => acc + cur.fullScore, 0)
          : 0
      },
      subjectivesTotalScore() {
        return this.subjectives && this.subjectives.length
          ? this.subjectives.reduce((acc, cur) => acc + cur.fullScore, 0)
          : 0
      },
      totalFullScore() {
        return this.objectivesTotalScore + this.subjectivesTotalScore
      },
      activeClass() {
        return this.classList.find(x => x.classId == this.activeClassId)
      },
      subjectUnfinish() {
        return this.test.subjectStatus != SubjectStatusEnum.ReportSucceeded.id
      },
      markingWay() {
        if (this.refTabMarkingSettings) {
          return this.refTabMarkingSettings.subjectMarkingWay
        } else {
          return null
        }
      },
      showTeacherInfoInSectionClass() {
        return !(this.markingWay && this.markingWay.id && this.markingWay.id === MarkingWayEnum.Normal.id)
      },
    },

    watch: {
      canEditExam() {
        if (this.test && this.test.needMark && this.canEditExam) {
          this.$nextTick().then(() => (this.refTabMarkingSettings = this.$refs.tabMarkingSettings))
        }
      },
    },

    created() {
      this.fetchTestDetailAndQuestions().then(() => {
        if (this.canEditExam) {
          this.fetchStudents()
          this.fetchMarkers()
        }
      })
    },

    methods: {
      /**
       * 加载数据
       */
      fetchTestDetailAndQuestions() {
        this.loading = true
        return Promise.all([
          apiGetCoachHomeworkInfo(this.examId),
          this.fetchObjectives(),
          this.fetchSubjectives(),
          this.fetchBlocks(),
        ])
          .then(([test]) => {
            test.isPostScan = test.uploadMode == UploadModeEnum.PostScan.id
            test.uploadModeText = test.isPostScan ? '先阅后扫' : '线上阅卷'
            this.test = test
            if (this.test && this.test.needMark && this.canEditExam) {
              this.$nextTick().then(() => (this.refTabMarkingSettings = this.$refs.tabMarkingSettings))
            }
            this.activeClassId = this.test.classList[0].classId
            this.classList = this.test.classList.map(cls => ({
              ...cls,
              students: [],
              markers: [],
            }))
          })
          .finally(() => {
            this.loading = false
          })
      },
      fetchObjectives() {
        return apiGetObjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.objectives = response
        })
      },
      fetchSubjectives() {
        return apiGetSubjectiveQuestions({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.subjectives = response
        })
      },
      fetchBlocks() {
        return apiGetBlocks({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
        }).then(response => {
          this.blocks = response
        })
      },
      fetchStudents() {
        return apiGetExamStudents({
          examId: this.examId,
          examSubjectId: this.examSubjectId,
          pageSize: 10000,
          currentPage: 1,
        })
          .then(data => {
            this.classList.forEach(cls => {
              cls.students = []
            })
            data.students.forEach(stu => {
              let cls = this.classList.find(x => x.classId == stu.classId)
              if (cls) {
                cls.students.push(stu)
              }
            })
          })
          .catch(err => {
            this.classList.forEach(cls => {
              cls.students = []
            })
            throw err
          })
      },
      fetchMarkers() {
        apiGetMarkersByClass(this.examSubjectId).then(map => {
          this.classList.forEach(cls => {
            cls.markers = map[cls.classId] || []
          })
        })
      },

      /**
       * 同步
       */
      syncStudents() {
        this.$Modal.confirm({
          title: '同步考生',
          content:
            '本操作将：<br>1. 同步学生的姓名、性别、学籍号、班级、准考号到测练项目；<br>2. 添加班级中存在，测练中不存在的学生；<br>3. 删除班级中不存在的学生（已扫描的除外）',
          onOk: () => {
            apiSyncStudents(this.examSubjectId)
              .then(data => {
                setTimeout(() => {
                  this.$Modal.success({
                    title: '同步结果',
                    content:
                      '<span style="display: inline-block; width: 100%; max-height: 250px; ' +
                      'overflow-y: auto; margin-top: 10px;">' +
                      data.replace(/\n/g, '<br>') +
                      '</span>',
                  })
                }, 500)
              })
              .catch(err => {
                setTimeout(() => {
                  this.$Modal.error({
                    title: '同步考生失败',
                    content: (err && err.msg) || '',
                  })
                }, 500)
                throw err
              })
              .finally(() => {
                this.fetchStudents()
              })
          },
        })
      },
      syncTeachers() {
        this.$Modal.confirm({
          title: '同步评卷员',
          content: '本操作将添加科目任课老师为评卷员，并更新各评卷员的任务量',
          onOk: () => {
            apiSyncTeachers(this.examSubjectId)
              .then(data => {
                setTimeout(() => {
                  this.$Modal.success({
                    title: '同步结果',
                    content:
                      '<span style="display: inline-block; width: 100%; max-height: 250px; ' +
                      'overflow-y: auto; margin-top: 10px;">' +
                      data.replace(/\n/g, '<br>') +
                      '</span>',
                  })
                }, 500)
              })
              .catch(err => {
                setTimeout(() => {
                  this.$Modal.error(
                    {
                      title: '同步评卷员失败',
                      content: (err && err.msg) || '',
                    },
                    500
                  )
                })
                throw err
              })
              .finally(() => {
                this.fetchMarkers()
              })
          },
        })
      },

      /**
       * 其他操作
       */
      handleTBtnDeleteTestDetailClick() {
        this.$Modal.confirm({
          title: '注意',
          content: '确定要删除项目：' + this.test.examName + ' ？',
          onOk: () => {
            apiDeleteExam(this.test.examId).then(() => {
              this.$Message.success({
                duration: 2,
                content: '已删除',
              })
              this.handleBtnBackToEmarkingHome()
            })
          },
        })
      },
      handleBtnBackToEmarkingHome() {
        this.$router.back()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-supplementary-exercise-setting {
    position: relative;

    .header {
      @include flex(row, space-between, center);
      height: 32px;
      margin-top: 10px;
      margin-bottom: 20px;
      line-height: 32px;

      .nav {
        flex-grow: 1;
        flex-shrink: 1;

        .test-name {
          margin-left: 30px;
          font-weight: bold;
          font-size: $font-size-medium-x;
        }

        .upload-mode {
          margin-left: 8px;
          padding: 0.3em 0.6em;
          border: 1px solid #13ce66;
          border-radius: 4px;
          color: #13ce66;
          line-height: 1.3;
          text-align: center;
          background-color: #e8faf0;

          &.post-scan {
            border-color: $color-warning;
            color: $color-warning;
            background-color: #fff7eb;
          }
        }
      }
    }

    .section {
      padding: 20px;
      background-color: white;

      &:not(:first-child) {
        margin-top: 10px;
      }

      &.section-question,
      &.section-class {
        padding-top: 10px;

        .tabs-actions {
          @include flex(row, flex-start, flex-start);

          .tabs {
            flex-grow: 1;
          }

          .actions {
            flex-shrink: 0;
            padding: 7px 0 7px 30px;
            border-bottom: 1px solid $color-border;
          }
        }
      }
    }
  }
</style>
