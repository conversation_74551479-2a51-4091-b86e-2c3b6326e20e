<template>
  <div class="panel-batch">
    <div class="panel-header">
      <RadioGroup :model-value="searchType" type="button" button-style="solid" @on-change="changeSearchType">
        <Radio label="batchNo">搜索批次</Radio>
        <Radio label="roomNo">搜索考场</Radio>
      </RadioGroup>
      <Input
        :model-value="keyword"
        class="search-room-no"
        search
        clearable
        :maxlength="20"
        :placeholder="searchType == 'roomNo' ? '考场' : '批次'"
        @on-change="handleChangeKeyword"
      ></Input>
      <TextButton class="btn-refresh" type="primary" icon="md-refresh" @click="refresh">刷新</TextButton>
    </div>
    <Table
      class="table-batch"
      :columns="tableColumns"
      :data="batches"
      :row-class-name="rowClassName"
      :max-height="tableMaxHeight"
      @on-row-click="handleTableRowClick"
    ></Table>
    <div class="panel-footer">
      <span v-if="batchTotal > 0" class="summary">共 {{ batchTotal }} 个批次</span>
      <Page :model-value="currentPage" :page-size="pageSize" :total="batchTotal" @on-change="changeCurrentPage"></Page>
    </div>
  </div>
</template>

<script>
  import {
    apiGetPersonalScanBatchPage,
    apiGetScanUserScanBatchPage,
    apiGetScanStationScanBatchPage,
  } from '@/api/scan/scan_batch'

  import { mapGetters } from 'vuex'
  import { formatDateTime } from '@/utils/date'
  import { debounce } from '@/utils/function'

  export default {
    props: {
      activeBatch: Object,
      tableMaxHeight: Number,
    },
    emits: ['change-active-batch'],
    data() {
      return {
        // 搜索关键字
        searchType: 'batchNo',
        keyword: '',
        // 批次总数
        batchTotal: 0,
        // 当前页批次
        batches: [],
        // 分页
        currentPage: 1,
        pageSize: 10,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'viewMode']),
      tableColumns() {
        return [
          {
            title: '批次',
            key: 'batchNumber',
            width: 90,
          },
          {
            title: '已扫张数',
            key: 'scanPaperCount',
            width: 60,
            align: 'center',
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'line-block',
                    whiteSpace: 'pre-line',
                  },
                },
                '已扫\n张数'
              ),
          },
          {
            title: '考场考生',
            align: 'center',
            render: (h, params) => {
              let { batchId, rooms } = params.row
              if (!rooms || rooms.length == 0) {
                return null
              }
              return h(
                'div',
                {},
                rooms.map(room => {
                  if (batchId) {
                    return h('div', {}, [
                      h('span', {}, room.roomNo),
                      h(
                        'span',
                        {
                          style: {
                            display: 'inline-block',
                            minWidth: '4em',
                            textAlign: 'left',
                          },
                        },
                        `（${room.stuCount}人）`
                      ),
                    ])
                  } else {
                    return h('div', {}, `${room.stuCount}人`)
                  }
                })
              )
            },
          },
          {
            title: '扫描员',
            key: 'scanRealName',
            width: 80,
            align: 'center',
          },
          {
            title: '扫描时间',
            key: 'scanTimeText',
            align: 'center',
            width: 80,
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'line-block',
                    whiteSpace: 'pre-line',
                  },
                },
                '扫描\n时间'
              ),
            render: (h, params) =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                params.row.scanTimeText
              ),
          },
        ]
      },
    },
    watch: {
      // 切换模式重置批次
      viewMode() {
        this.keyword = ''
        this.batchTotal = 0
        this.batches = []
        this.currentPage = 1
        this.changeActiveBatch(null)
        this.getScanBatch()
      },
    },
    created() {
      this.getScanBatch()
    },
    methods: {
      /**
       * 加载数据
       */
      getScanBatch() {
        let { isAdmin, scanStationId, scanUserId } = this.viewMode
        let api
        if (!isAdmin) {
          api = apiGetPersonalScanBatchPage
        } else if (scanUserId) {
          api = apiGetScanUserScanBatchPage
        } else {
          api = apiGetScanStationScanBatchPage
        }
        return api({
          examSubjectId: this.examSubjectId,
          scanStationId,
          scanUserId,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          ...this.getSearchParams(),
        }).then(batchPage => {
          this.batchTotal = batchPage.total
          this.batches = batchPage.records.map(item => {
            return {
              ...item,
              batchNumber: item.batchId ? `${item.scanClient}-${item.batchNo}` : item.scanClient,
              scanTimeText: item.batchId ? formatDateTime(new Date(item.scanTime), 'MM-DD\nHH:mm:ss') : '',
            }
          })

          // 设置当前批次
          let nextActiveBatch = null
          if (this.batches.length > 0) {
            if (this.activeBatch) {
              nextActiveBatch = this.batches.find(x => x.batchId == this.activeBatch.batchId)
            }
            if (!nextActiveBatch) {
              nextActiveBatch = this.batches.find(x => Boolean(x.batchId))
            }
          }
          this.changeActiveBatch(nextActiveBatch)
        })
      },
      getSearchParams() {
        let scanClient = ''
        let batchNo = ''
        let roomNo = ''
        if (this.keyword) {
          if (this.searchType == 'roomNo') {
            roomNo = this.keyword
          } else {
            let strs = this.keyword.split('-').filter(Boolean)
            if (strs.length == 1) {
              if (strs[0].length < 5) {
                batchNo = strs[0]
              } else {
                scanClient = strs[0]
              }
            }
            if (strs.length == 2) {
              scanClient = strs[0]
              batchNo = strs[1]
            }
          }
        }
        return { scanClient, batchNo, roomNo }
      },

      /**
       * 操作
       */
      changeSearchType(searchType) {
        this.searchType = searchType
        this.keyword = ''
        this.getScanBatch()
      },
      handleChangeKeyword: debounce(function (e) {
        let newKeyword = (e.target.value || '').trim()
        let oldKeyword = this.keyword
        this.keyword = newKeyword
        if (newKeyword) {
          if (this.searchType == 'batchNo' && !this.isValidBatchNo(newKeyword)) {
            this.$Message.warning({
              content: '批次号格式错误',
              duration: 5,
              closable: true,
            })
            setTimeout(() => {
              this.keyword = oldKeyword
            }, 100)
            return
          }
          if (this.searchType == 'roomNo' && !this.isValidRoomNo(newKeyword)) {
            this.$Message.warning({
              content: '考场号格式错误',
              duration: 5,
              closable: true,
            })
            setTimeout(() => {
              this.keyword = oldKeyword
            }, 100)
            return
          }
        }
        this.changeCurrentPage(1)
      }, 500),
      isValidRoomNo(str) {
        return /^\d+$/.test(str)
      },
      isValidBatchNo(str) {
        return /^\d+(-\d*)?$/.test(str)
      },
      refresh() {
        this.getScanBatch().then(() => {
          this.$Message.info({
            content: '已刷新',
          })
        })
      },
      changeCurrentPage(currentPage) {
        this.currentPage = currentPage
        this.getScanBatch()
      },
      handleTableRowClick(row) {
        if (!row.batchId) {
          return
        }
        let nextActiveBatch = this.batches.find(x => x.batchId == row.batchId)
        this.changeActiveBatch(nextActiveBatch)
      },

      /**
       * 其他
       */
      changeActiveBatch(batch) {
        this.$emit('change-active-batch', batch)
      },
      rowClassName(row) {
        return this.activeBatch && this.activeBatch.batchId == row.batchId ? 'active' : ''
      },
    },
  }
</script>

<style lang="scss" scoped>
  .panel-batch {
    padding-right: 16px;
    padding-left: 16px;
    border-right: 1px solid $color-border;

    .panel-header {
      .search-room-no {
        width: 200px;
        margin-left: 10px;
      }

      .btn-refresh {
        margin-left: auto;
      }
    }

    :deep(tr.ivu-table-row) {
      cursor: pointer;

      &.active td {
        background-color: $color-iview-table-active-row;
      }
    }
  }
</style>
