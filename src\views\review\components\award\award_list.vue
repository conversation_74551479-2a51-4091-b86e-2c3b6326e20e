<template>
  <div class="container-award-list">
    <div class="section-filter-bar">
      <div class="box-bar-left">
        <div class="filter-school">
          <Select
            v-model="currentCategoryId"
            style="width: 200px"
            clearable
            placeholder="评审类别"
            @on-change="onChangeCategory"
          >
            <Option v-for="item in categories" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="filter-school">
          <Select
            v-model="currentSchoolId"
            style="width: 200px"
            clearable
            filterable
            placeholder="学校"
            @on-change="onChangeSchool"
          >
            <Option v-for="item in schoolListForSelect" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="filter-search">
          <Input
            v-model="keyword"
            :placeholder="participantIdentity === 'student' ? '学生姓名' : '教师姓名 / 手机号码'"
            clearable
            transfer
            suffix="md-search"
            style="width: 200px"
            @on-change="onKeywordChange"
          />
        </div>
      </div>
      <div class="box-bar-right">
        <Button :disabled="!awardList.length" @click="onExport">导出</Button>
      </div>
    </div>
    <div class="section-table">
      <Table :data="awardList" :columns="tableColumns"></Table>
      <!-- <div class="box-page">
        <Page
          v-model="pageNum"
          :total="total"
          :page-size="pageSize"
          show-total
          show-sizer
          show-elevator
          :page-size-opts="[15, 30, 60, 100]"
          @on-page-size-change="changePageSize"
          @on-change="changePage"
        />
      </div> -->
    </div>

    <Drawer v-model="awardDetailVisible" class-name="drawer-detail-award" title="评审结果详情" closable :width="600">
      <div v-if="currentAwardItem" class="container-drawer-content">
        <div class="box-score">
          <div class="title">{{ currentAwardItem.workTitle }}</div>
          <div class="score">
            <span class="score-text">{{ currentAwardItem.score }}</span>
            <span>分</span>
          </div>
          <div class="all-score">
            <div class="tips">以下是各评委打分</div>
            <div class="all-score-text">{{ allScoreText }}</div>
            <div class="tips">说明：共设 {{ currentAwardItem.scores.length }} 名评委</div>
          </div>
          <div v-if="commtentList.length" class="box-comment">
            <div class="header">评委点评：</div>
            <div class="body">
              <div v-for="item in commtentList" :key="item.judgeId" class="comment-item">
                <div class="judge-name">{{ item.judgeName }}：</div>
                <div class="comment">{{ item.comment }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="box-work-info">
          <div class="title">参评信息</div>
          <div class="work-info">
            <el-form :show-message="false" label-width="140px" label-position="top">
              <el-form-item label="参评类别" required>
                <el-input v-model="currentAwardItem.categoryName" readonly></el-input>
              </el-form-item>
              <el-form-item label="作品名称" required>
                <el-input v-model="currentAwardItem.workTitle" readonly></el-input>
              </el-form-item>
              <el-form-item label="学校">
                <el-input v-model="currentAwardItem.schoolName" readonly></el-input>
              </el-form-item>
              <el-form-item label="班级">
                <el-input v-model="currentAwardItem.className" readonly></el-input>
              </el-form-item>
              <el-form-item label="参评人" required>
                <el-input v-model="currentAwardItem.userName" readonly></el-input>
              </el-form-item>
              <el-form-item label="文件上传" required>
                <div class="file-box">
                  <div
                    v-for="file in currentAwardItem.files"
                    :key="file.fileName"
                    class="file-item"
                    @click="onPreview(file)"
                  >
                    <template v-if="file.fileType === 'pdf'">
                      <div class="icon-box">
                        <img class="icon" src="../../../../assets/images/review/icon_pdf.png" alt="" />
                      </div>
                      <div class="file-info">
                        <div>{{ file.fileName }}</div>
                      </div>
                    </template>
                    <template v-else-if="file.fileType === 'image'">
                      <div class="icon-box">
                        <img class="icon" src="../../../../assets/images/review/icon_image.png" alt="" />
                      </div>
                      <div class="file-info">
                        <div>{{ file.fileName }}</div>
                      </div>
                    </template>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </Drawer>

    <Modal
      v-model="modalFileVisible"
      class="modal-preview-file"
      title="文件"
      footer-hide
      mask-closable
      :width="800"
      style="top: 30px"
    >
      <div v-if="currentFile" class="container-modal-content">
        <div class="file">
          <template v-if="currentFile.fileType === 'image'">
            <img class="image" :src="currentFile.filePath" alt="" />
          </template>
          <template v-else-if="currentFile.fileType === 'pdf'">
            <object
              class="pdf-viewer-object"
              :data="currentFile.filePath"
              type="application/pdf"
              width="100%"
              height="800px"
            >
              <p>您的浏览器不支持查看PDF文件，请<a :href="currentFile.filePath">下载PDF文件</a>查看。</p>
            </object>
          </template>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { ElForm, ElFormItem, ElInput } from 'element-plus'
  import 'element-plus/es/components/form/style/css'
  import 'element-plus/es/components/form-item/style/css'
  import 'element-plus/es/components/input/style/css'
  import TextButton from '@/components/text_button'
  import { apiGetAwardList, apiExportAwardList } from '@/api/review/activity'
  import RevewAwardStatusEnum from '@/enum/review/review_award_status'
  import { debounce } from '@/utils/function'
  import { groupArray } from '@/utils/array'
  import { exportExcel } from '@/utils/excel_export'
  import { downloadBlob } from '@/utils/download'

  export default {
    components: {
      'el-form': ElForm,
      'el-form-item': ElFormItem,
      'el-input': ElInput,
    },
    data() {
      return {
        keyword: '',
        currentSchoolId: '',
        currentCategoryId: '',
        awardList: [],
        awardDetailVisible: false,
        currentAwardItem: null,
        modalFileVisible: false,
        currentFile: null,
        pageNum: 1,
        pageSize: 100000,
        total: 0,
      }
    },
    computed: {
      ...mapGetters('review', [
        'isAdmin',
        'isRegisterAuditor',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'enableSchoolAudit',
        'isMultipleSchool',
        'userList',
        'categories',
        'participantIdentity',
        'activityId',
        'schoolList',
        'currentActivity',
      ]),
      tableColumns() {
        let columns = [
          {
            key: 'score',
            title: '得分',
            align: 'center',
          },
          {
            key: '',
            title: '评委打分',
            align: 'center',
            render: (h, params) => {
              return h('span', {}, params.row.scores.map(item => item.score).join('，'))
            },
          },
          {
            key: 'awardName',
            title: '奖项',
            align: 'center',
          },
          {
            key: 'schoolName',
            title: '学校',
            align: 'center',
          },
          {
            key: 'className',
            title: '班级',
            align: 'center',
          },
          {
            key: 'userName',
            title: '参评人',
            align: 'center',
          },
          {
            key: 'categoryName',
            title: '参评类别',
            align: 'center',
          },
          {
            key: 'workTitle',
            title: '作品名称',
          },
          {
            key: 'action',
            title: '操作',
            align: 'center',
            render: (h, params) => {
              return h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => {
                    this.onShowDetail(params.row)
                  },
                },
                () => '查看'
              )
            },
          },
        ]

        return columns
      },
      activitySchoolId() {
        return this.$store.getters['review/currentActivity']?.schoolId
      },
      activitySchoolName() {
        return this.$store.getters['review/currentActivity']?.schoolName
      },
      userSchoolId() {
        return this.$store.getters['user/info'].schoolId
      },
      schoolListForSelect() {
        let list = this.schoolList.slice()
        // 评审管理员可选所有评审学校及评审创建学校
        // if (this.isAdmin) {
        //   if (list.every(s => s.id != this.activitySchoolId)) {
        //     list.unshift({
        //       id: this.activitySchoolId,
        //       name: this.activitySchoolName,
        //     })
        //   }
        //   return list
        // }
        // // 学校管理员可选本校
        // else if (this.isSchoolAdmin) {
        //   return list.filter(s => s.id == this.userSchoolId)
        // } else {
        //   return []
        // }
        return list
      },
      allScoreText() {
        return this.currentAwardItem?.scores.map(item => item.score).join('，')
      },
      commtentList() {
        if (!this.currentAwardItem || !this.currentAwardItem.scores) return []
        return this.currentAwardItem.scores.filter(item => item.comment)
      },
    },
    watch: {
      currentActivity: {
        handler: function (newVal) {
          this.awardList = []
          if (newVal?.awardStatus === RevewAwardStatusEnum.AwardSuccess.id) {
            this.fetchAwardList()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      resetStore() {
        this.awardList = []
      },
      onKeywordChange: debounce(function (e) {
        this.resetStore()
        this.fetchAwardList()
      }, 600),
      onChangeCategory() {
        this.resetStore()
        this.fetchAwardList()
      },
      onChangeSchool() {
        this.resetStore()
        this.fetchAwardList()
      },
      fetchAwardList() {
        return apiGetAwardList({
          activityId: this.activityId,
          categoryId: this.currentCategoryId || undefined,
          schoolId: this.currentSchoolId || undefined,
          keyword: this.keyword || undefined,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        }).then(res => {
          this.awardList = res.list
          this.total = Number(res.total)
        })
      },
      onShowDetail(item) {
        this.currentAwardItem = item
        this.awardDetailVisible = true
      },
      onPreview(item) {
        this.modalFileVisible = true
        this.currentFile = item
      },
      changePage(page) {
        this.pageNum = page
        this.fetchAwardList()
      },
      changePageSize(size) {
        this.pageNum = 1
        this.pageSize = size
        this.fetchAwardList()
      },
      onExport() {
        // const sheets = groupArray(this.awardList, k => k.categoryName).map(item => {
        //   return {
        //     sheetName: item.key,
        //     rows: item.group,
        //     columns: [
        //       {
        //         key: 'score',
        //         title: '得分',
        //       },
        //       {
        //         title: '评委打分',
        //         key: row => {
        //           return row.scores.map(s => s.score).join('，')
        //         },
        //       },
        //       {
        //         key: 'awardName',
        //         title: '奖项',
        //       },
        //       {
        //         key: 'schoolName',
        //         title: '学校',
        //       },
        //       {
        //         key: 'className',
        //         title: '班级',
        //       },
        //       {
        //         key: 'userName',
        //         title: '参评人',
        //       },
        //       {
        //         key: 'categoryName',
        //         title: '参评类别',
        //       },
        //       {
        //         key: 'workTitle',
        //         title: '名称',
        //       },
        //     ],
        //   }
        // })
        // exportExcel(sheets, `${this.currentActivity.name}评审结果.xlsx`)

        apiExportAwardList({
          activityId: this.activityId,
        }).then(res => {
          downloadBlob(res, `${this.currentActivity.name}评审结果.xlsx`)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-award-list {
    .section-filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 10px;
    }

    .box-bar-left {
      @include flex(row, flex-start, center);
    }

    .filter-school {
      margin-right: 15px;
    }

    .box-page {
      @include flex(row, flex-end, center);
      margin-top: 20px;
    }
  }
</style>

<style lang="scss">
  .drawer-detail-award {
    .ivu-drawer-body {
      background-color: #ebebeb;
    }

    .box-score {
      margin-bottom: 16px;
      padding-bottom: 15px;
      background-color: #fff;

      .title {
        padding: 15px;
        border-bottom: 1px solid #dfdfdf;
        font-weight: 600;
        font-size: 16px;
      }

      .score {
        padding: 20px 0;
        color: $color-warning;
        text-align: center;

        .score-text {
          font-size: 30px;
        }
      }

      .all-score {
        .tips {
          color: #999;
          font-size: 12px;
          text-align: center;
        }

        .all-score-text {
          margin-top: 15px;
          margin-bottom: 20px;
          color: $color-info;
          font-size: 16px;
          text-align: center;
        }
      }

      .box-comment {
        margin-top: 30px;
        padding: 0 15px;

        .header {
          margin-bottom: 10px;
          padding-bottom: 10px;
          border-bottom: 1px solid #dfdfdf;
        }

        .comment-item {
          &:not(:last-child) {
            margin-bottom: 8px;
          }

          .comment {
            line-height: 1.6;
          }
        }
      }
    }

    .box-work-info {
      background-color: #fff;

      .el-form-item:last-child {
        margin-bottom: 0;
      }

      .title {
        padding: 15px;
        font-weight: 600;
        font-size: 16px;
      }

      .work-info {
        padding: 0 15px 16px;
      }

      .file-box {
        padding: 15px;
        border: 1px dashed #dfdfdf;
        border-radius: 8px;
      }

      .file-item {
        @include flex(row, flex-start, center);
        cursor: pointer;

        &:not(:last-child) {
          margin-bottom: 10px;
        }
      }

      .icon-box {
        @include flex(row, center, center);
        margin-right: 8px;

        .icon {
          width: 40px;
        }
      }

      .file-info {
        font-size: 14px;
        line-height: 1;
      }
    }
  }

  .modal-preview-file {
    .container-modal-content {
      .image {
        width: 100%;
      }
    }
  }
</style>
