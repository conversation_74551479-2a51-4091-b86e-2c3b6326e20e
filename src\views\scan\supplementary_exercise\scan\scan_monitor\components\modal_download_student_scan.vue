<template>
  <Modal
    class="modal-download-student-scan"
    :model-value="modelValue"
    :mask-closable="false"
    :closable="!downloading"
    title="导出扫描考生"
    @on-visible-change="handleVisibleChange"
  >
    <div class="tip">请选择要导出考生的扫描状态：</div>
    <RadioGroup v-model="selectedSignText">
      <Radio
        v-for="r in radios"
        :key="r.signText"
        class="radio-item"
        :label="r.signText"
        :disabled="downloading"
      ></Radio>
    </RadioGroup>
    <template #footer>
      <Button type="text" :disabled="downloading" @click="handleCancel">取消</Button>
      <Button type="primary" :loading="downloading" @click="handleOK">{{ downloading ? '导出中' : '导出' }}</Button>
    </template>
  </Modal>
</template>

<script>
  import ScanStatusEnum from '@/enum/emarking/scan_status'

  export default {
    props: {
      modelValue: Boolean,
      downloading: Boolean,
      initSignText: String,
    },
    emits: ['update:modelValue', 'ok'],
    data() {
      return {
        radios: [
          {
            signText: '全部',
            signs: [
              ScanStatusEnum.NotScanned.id,
              ScanStatusEnum.Absent.id,
              ScanStatusEnum.Normal.id,
              ScanStatusEnum.MarkAbsent.id,
            ],
          },
          {
            signText: '已扫',
            signs: [ScanStatusEnum.Absent.id, ScanStatusEnum.Normal.id],
          },
          {
            signText: '扫描缺考',
            signs: [ScanStatusEnum.Absent.id],
          },
          {
            signText: '标记缺考',
            signs: [ScanStatusEnum.MarkAbsent.id],
          },
          {
            signText: '未扫',
            signs: [ScanStatusEnum.NotScanned.id],
          },
        ],
        selectedSignText: '',
      }
    },
    computed: {
      selectedRadio() {
        return this.radios.find(r => r.signText == this.selectedSignText)
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init()
        }
      },
    },
    methods: {
      init() {
        if (!this.downloading) {
          if (this.radios.some(r => r.signText == this.initSignText)) {
            this.selectedSignText = this.initSignText
          } else {
            this.selectedSignText = ''
          }
        }
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      handleCancel() {
        if (this.downloading) {
          return
        }
        this.$emit('update:modelValue', false)
      },
      handleOK() {
        if (this.downloading) {
          return
        }
        if (!this.selectedRadio) {
          this.$Message.warning({
            content: '请先选择考生扫描状态',
          })
          return
        }
        let params = {
          ...this.selectedRadio,
        }
        if (params.signText == '全部') {
          params.signText = '全部状态'
        }
        this.$emit('ok', params)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tip {
    margin-bottom: 10px;
  }

  .radio-item {
    margin-right: 20px;
  }
</style>
