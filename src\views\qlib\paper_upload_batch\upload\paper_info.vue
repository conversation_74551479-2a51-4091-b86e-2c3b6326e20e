<template>
  <div class="container-paper-upload-batch-paper-info">
    <div v-for="field in fields" :key="field.label" class="form-item" :class="field.name">
      <span class="form-item-label">{{ field.label }}</span>

      <div v-if="field.name === 'region'" class="selector-region">
        <Select
          class="selector-province"
          :model-value="paperInfoRegion[0]"
          placeholder="省份"
          :multiple="false"
          :disabled="props.disabled"
          @on-change="changeProvince"
        >
          <Option v-for="p in provinces" :key="p.value" :value="p.value">{{ p.label }}</Option>
        </Select>
        <Select
          class="selector-city"
          :model-value="paperInfoRegion[1]"
          placeholder="地市"
          :multiple="false"
          :disabled="props.disabled"
          @on-change="changeCity"
        >
          <Option v-for="c in cities" :key="c.value" :value="c.value">{{ c.label }}</Option>
        </Select>
      </div>
      <div v-else-if="field.name === 'grade'" class="selector-grade-term">
        <Select
          class="selector-grade"
          :model-value="field.value"
          placeholder="年级"
          :multiple="false"
          :disabled="props.disabled"
          @on-change="changeField(field, $event)"
        >
          <Option v-for="item in field.data" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
        <Select
          class="selector-term"
          :model-value="paperInfo.term && paperInfo.term.id"
          placeholder="学期"
          :multiple="false"
          :disabled="props.disabled"
          @on-change="changeTerm"
        >
          <Option v-for="t in terms" :key="t.id" :value="t.id">{{ t.name }}</Option>
        </Select>
      </div>
      <Select
        v-else
        class="form-item-select"
        :model-value="field.value"
        :multiple="false"
        :disabled="props.disabled"
        @on-change="changeField(field, $event)"
      >
        <Option v-for="item in field.data" :key="item.id" :value="item.id">{{ item.name }}</Option>
      </Select>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import iView from '@/iview'
  import store from '@/store'
  import { PaperInfo } from '@/helpers/qlib/paper_info'
  import { groupArray, unGroupArray } from '@/utils/array'

  const props = defineProps({
    paperInfo: {
      type: Object,
      required: true,
    },
    disabled: Boolean,
  })

  const currentStageSubject = computed(() => {
    return store.getters['qlib/currentStageSubject']
  })
  const stages = computed(() => {
    return store.getters['qlib/stages']()
  })
  const paperInfoRegion = computed(() => {
    let region = props.paperInfo.region.map(r => r.value)
    let regionLength = region.length
    if (regionLength === 0) {
      return [0, 0]
    } else if (regionLength === 1) {
      return [region[0], 0]
    } else {
      return region
    }
  })
  const regionsByProvinceCity = computed(() => {
    return store.getters['common/regionsByProvinceCity']()
  })
  const provinces = computed(() => {
    let result = regionsByProvinceCity.value.map(p => ({
      value: p.value,
      label: p.label,
    }))
    result.unshift({
      value: 0,
      label: '不限省份',
    })
    return result
  })
  const cities = computed(() => {
    let province = regionsByProvinceCity.value.find(p => p.value === paperInfoRegion.value[0])
    let result = ((province && province.children) || []).map(c => ({
      label: c.label,
      value: c.value,
    }))
    result.unshift({
      value: 0,
      label: '不限地市',
    })
    return result
  })
  const grades = computed(() => {
    let grades = store.getters['qlib/gradesByStage'](props.paperInfo.stage.id)
    grades.unshift({
      id: 0,
      name: '不限',
    })
    return grades
  })
  const terms = computed(() => {
    if (!props.paperInfo.grade.id) {
      return [
        {
          id: 0,
          name: '不限',
        },
      ]
    } else {
      let terms = store.getters['qlib/termsByStage'](props.paperInfo.stage.id) || []
      terms.unshift({
        id: 0,
        name: '不限',
      })
      return terms
    }
  })
  const subjects = computed(() => {
    let subjects = store.getters['qlib/subjectsByStage'](props.paperInfo.stage.id)
    return subjects
  })
  const paperTypes = computed(() => {
    let paperTypes = store.getters['qlib/paperTypesByStage'](props.paperInfo.stage.id)
    return paperTypes
  })
  const years = computed(() => {
    let years = []
    let thisYear = new Date().getFullYear()
    for (let i = 0; i < 10; i++) {
      years.push({
        id: thisYear - i,
        name: (thisYear - i).toString(),
      })
    }
    return years
  })
  const fields = computed(() => {
    let _fields = [
      {
        label: '学段',
        name: 'stage',
        data: stages.value,
        value: props.paperInfo.stage.id,
      },
      {
        label: '年级',
        name: 'grade',
        data: grades.value,
        value: props.paperInfo.grade.id,
      },
      {
        label: '学科',
        name: 'subject',
        data: subjects.value,
        value: props.paperInfo.subject.id,
      },
      {
        label: '类型',
        name: 'paperType',
        data: paperTypes.value,
        value: props.paperInfo.paperType.id,
      },
      {
        label: '年份',
        name: 'year',
        data: years.value,
        value: props.paperInfo.year,
      },
      {
        label: '地区',
        name: 'region',
      },
    ]

    return _fields
  })

  function changeProvince(pValue) {
    props.paperInfo.changeRegion(pValue)
  }

  function changeCity(cValue) {
    if (cValue) {
      props.paperInfo.changeRegion(cValue)
    } else {
      let province = props.paperInfo.region[0]
      props.paperInfo.changeRegion((province && province.value) || 0)
    }
  }
  function changeTerm(t) {
    props.paperInfo.changeTerm(t)
  }
  function changeField(field, value) {
    let actionName = `change${field.name[0].toUpperCase()}${field.name.substring(1)}`
    if (typeof props.paperInfo[actionName] !== 'function') {
      return
    }

    props.paperInfo[actionName](value)
  }

  // 解析文件名获取试卷信息
  function getPaperInfoFromFileName(fileName) {
    let paperInfo = new PaperInfo()
    paperInfo.changeStage(currentStageSubject.value.stageId)
    paperInfo.changeSubject(currentStageSubject.value.subjectId)

    fileName = fileName.slice(0, fileName.lastIndexOf('.'))

    // 年份
    let year = years.value.find(y => fileName.indexOf(y.name) >= 0)
    paperInfo.changeYear((year && year.id) || new Date().getFullYear())

    // 地区-省
    let province = regionsByProvinceCity.value.find(p => fileName.indexOf(p.label.slice(0, 2)) >= 0)
    let city = ((province && province.children) || []).find(c => fileName.indexOf(c.label.slice(0, 2)) >= 0)
    if (province) {
      if (city) {
        paperInfo.changeRegion(city.value)
      } else {
        paperInfo.changeRegion(province.value)
      }
    }

    let stages = store.state.qlib.stages.filter(s => s.id === currentStageSubject.value.stageId)

    // 试卷类型
    fileName = fileName.replace(/[一二三]模/, '模拟')
    let paperTypes = groupArray(
      unGroupArray(stages, s =>
        s.paperTypes.map(p => ({
          paperTypeId: p.id,
          paperTypeName: p.name,
          stageId: s.id,
          stageName: s.name,
        }))
      ),
      p => p.paperTypeId
    ).map(g => ({
      paperTypeId: g.group[0].paperTypeId,
      paperTypeName: g.group[0].paperTypeName,
      stages: g.group.map(item => ({
        stageId: item.stageId,
        stageName: item.stageName,
      })),
    }))
    let shortPaperTypes = groupArray(paperTypes, p => p.paperTypeName.slice(0, 2)).map(g => ({
      name: g.key,
      paperTypes: g.group,
    }))
    let shortPaperType = shortPaperTypes.find(p => fileName.indexOf(p.name) >= 0)
    if (shortPaperType) {
      let paperType
      if (shortPaperType.paperTypes.length === 1) {
        paperType = shortPaperType.paperTypes[0]
      } else {
        // 小升初、中考、高考
        paperType = shortPaperType.paperTypes.find(p => fileName.indexOf(p.paperTypeName.slice(-2)) >= 0)
        if (!paperType) {
          paperType = shortPaperType.paperTypes.find(p => ['真卷', '真题'].includes(p.paperTypeName.slice(-2)))
        }
      }
      if (!paperType) {
        paperType = paperTypes.find(p => p.paperTypeName === '其他')
      }
      if (paperType) {
        paperInfo.changePaperType(paperType.paperTypeId)
      }
    }

    // 学段年级
    let grades = unGroupArray(stages, s =>
      s.grades.map(g => ({
        gradeId: g.id,
        gradeName: g.name,
        stageId: s.id,
        stageName: s.name,
      }))
    )
    let grade = grades.find(g => fileName.indexOf(g.gradeName) >= 0)
    if (grade) {
      paperInfo.changeGrade(grade.gradeId)
    }

    // 学期
    if (grade) {
      fileName = fileName.replace(
        new RegExp(`${grade.gradeName}[(（]([上下])[)）]`),
        (match, p1) => `${grade.gradeName}${p1}学期`
      )
    }
    let terms = stages[0].terms || []
    let term = terms.find(t => fileName.includes(t.name))
    if (term) {
      paperInfo.changeTerm(term.id)
    }

    return paperInfo
  }

  // 检查试卷信息
  function checkPaperInfo(showMessage = true) {
    let message = check()
    if (message) {
      if (showMessage) {
        iView.Message.warning({
          content: message,
        })
      }
      return false
    } else {
      return true
    }
  }

  function check() {
    if (!(props.paperInfo.stage.id > 0)) {
      return '请选择学段'
    }
    if (!(props.paperInfo.subject.id > 0)) {
      return '请选择学科'
    }
    if (!(props.paperInfo.year > 0)) {
      return '请选择年份'
    }
    if (!(props.paperInfo.paperType.id > 0)) {
      return '请选择试卷类型'
    }
    if (props.paperInfo.term.id > 0 && !(props.paperInfo.grade.id > 0)) {
      return '请选择年级'
    }
  }

  defineExpose({ getPaperInfoFromFileName, checkPaperInfo })
</script>

<style lang="scss" scoped>
  .container-paper-upload-batch-paper-info {
    display: grid;
    grid-template-columns: 3fr 5fr;
    gap: 16px;
    padding: 16px 8px;

    .form-item {
      @include flex(row, flex-start, center);

      .form-item-label {
        flex-shrink: 0;
        margin-right: 4px;
      }

      .form-item-select {
        flex: 1 1 0;
      }

      .selector-region {
        @include flex(row, flex-start, center);
        flex: 1 1 0;
        gap: 10px;

        .selector-province,
        .selector-city {
          flex: 1 1 0;
        }
      }

      .selector-grade-term {
        @include flex(row, flex-start, center);
        flex: 1 1 0;
        gap: 10px;

        .selector-grade,
        .selector-term {
          flex: 1 1 0;
        }
      }
    }
  }
</style>
