<script>
  import RadioGroup from '@/components/radio_group'

  import { apiChangeActivityStatus } from '@/api/review'

  import { formatDateStartTime, formatDateEndTime } from '@/utils/date'

  import StatusEnum from '@/enum/review/review_project_status'

  export default {
    components: {
      's-radio-group': RadioGroup,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        selectedStatusId: 0,
        registerStartTime: null,
        registerEndTime: null,
        submitEndTime: null,
      }
    },

    computed: {
      currentActivity() {
        return this.$store.getters['review/currentActivity']
      },

      currentActivityStatusName() {
        return this.currentActivity?.statusName || ''
      },

      activityId() {
        return this.$store.getters['review/activityId']
      },

      statusRelationList() {
        return StatusEnum.getEntries().map(s => {
          const TransableStatus = []
          const NeedParams = []
          if (s.key === 'WaitingRegister') {
            TransableStatus.push('RegisterOngoing')
            NeedParams.push(
              {
                name: 'registerStartTime',
                label: '报名开始时间',
                required: true,
              },
              {
                name: 'registerEndTime',
                label: '报名截止时间',
                required: false,
              },
              {
                name: 'submitEndTime',
                label: '提交截止时间',
                required: false,
              }
            )
          } else if (s.key === 'RegisterOngoing') {
            TransableStatus.push('WaitingRegister', 'RegisterEnded')
            NeedParams.push(
              {
                name: 'registerEndTime',
                label: '报名截止时间',
                required: true,
              },
              {
                name: 'submitEndTime',
                label: '提交截止时间',
                required: true,
              }
            )
          } else if (s.key === 'RegisterEnded') {
            TransableStatus.push('RegisterOngoing', 'UnderReview')
          } else if (s.key === 'UnderReview') {
            TransableStatus.push('ReviewPaused', 'ActivityEnded')
          } else if (s.key === 'ReviewPaused') {
            TransableStatus.push('RegisterOngoing', 'RegisterEnded', 'UnderReview')
          } else if (s.key === 'ActivityEnded') {
            TransableStatus.push('UnderReview')
          }

          return {
            id: s.id,
            name: s.name,
            key: s.key,
            transableStatus: TransableStatus,
            needParams: NeedParams,
          }
        })
      },

      currentActivityStatus() {
        return this.statusRelationList.find(s => s.name === this.currentActivityStatusName)
      },

      selectableStatusList() {
        return this.currentActivityStatus
          ? this.statusRelationList.filter(s => this.currentActivityStatus.transableStatus.includes(s.key))
          : []
      },

      selectedStatus() {
        return this.selectedStatusId && this.selectableStatusList.length
          ? this.selectableStatusList.find(s => s.id === this.selectedStatusId)
          : null
      },

      selectedStatusNeedParams() {
        return (this.selectedStatus && this.selectedStatus.needParams) || []
      },

      selectedStatusRequiredParams() {
        return this.selectedStatusNeedParams.filter(s => s.required)
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.selectedStatusId = 0
          this.registerStartTime = this.currentActivity?.registerStartTime
            ? new Date(this.currentActivity.registerStartTime)
            : null
          this.registerEndTime = this.currentActivity?.registerEndTime
            ? new Date(this.currentActivity.registerEndTime)
            : null
          this.submitEndTime = this.currentActivity?.submitEndTime ? new Date(this.currentActivity.submitEndTime) : null
        } else {
          this.closeModal()
        }
      },

      checkParamsOK() {
        if (!this.selectedStatusId) {
          return '尚未选择要更改的活动状态'
        }

        for (let i = 0; i < this.selectedStatusRequiredParams.length; i++) {
          if (!this[this.selectedStatusRequiredParams[i].name]) {
            return '尚未选择【' + this.selectedStatusRequiredParams[i].label + '】'
          }
        }

        if (this.selectedStatusRequiredParams.length) {
          const RegisterStartTimeValue = this.registerStartTime
            ? new Date(formatDateStartTime(this.registerStartTime)).getTime()
            : null
          const RegisterEndTimeValue = this.registerEndTime
            ? new Date(formatDateEndTime(this.registerEndTime)).getTime()
            : null
          const SubmitEndTimeValue = this.submitEndTime
            ? new Date(formatDateEndTime(this.submitEndTime)).getTime()
            : null
          if (RegisterStartTimeValue && RegisterEndTimeValue && RegisterStartTimeValue >= RegisterEndTimeValue) {
            return '【报名截止时间】应在【报名开始时间】之后'
          }
          if (RegisterStartTimeValue && SubmitEndTimeValue && RegisterStartTimeValue >= SubmitEndTimeValue) {
            return '【提交截止时间】应在【报名开始时间】之后'
          }
        }

        return ''
      },

      commitNewStatus() {
        const CheckParams = this.checkParamsOK()
        if (CheckParams) {
          this.$Message.warning({
            duration: 4,
            content: CheckParams,
          })
          return
        }

        apiChangeActivityStatus({
          activityId: this.activityId,
          currentStatus: this.currentActivityStatus.id,
          newStatus: this.selectedStatusId,
          registerStartTime:
            this.selectedStatusRequiredParams.length &&
            this.selectedStatusNeedParams.some(s => s.name === 'registerStartTime') &&
            this.registerStartTime
              ? formatDateStartTime(this.registerStartTime)
              : undefined,
          registerEndTime:
            this.selectedStatusRequiredParams.length &&
            this.selectedStatusNeedParams.some(s => s.name === 'registerEndTime') &&
            this.registerEndTime
              ? formatDateEndTime(this.registerEndTime)
              : undefined,
          submitEndTime:
            this.selectedStatusRequiredParams.length &&
            this.selectedStatusNeedParams.some(s => s.name === 'submitEndTime') &&
            this.submitEndTime
              ? formatDateEndTime(this.submitEndTime)
              : undefined,
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '修改状态成功',
            })

            return this.$store.dispatch('review/fetchActivityDetail', this.activityId)
          })
          .then(() => this.closeModal())
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" title="活动状态设置" width="550" @on-visible-change="handleModalVisibleChange">
    <div class="modal-panel">
      <div class="form-item">
        <div class="form-label">当前状态</div>
        <div class="form-value">
          <span v-show="currentActivityStatusName" class="status-tag">{{ currentActivityStatusName }}</span>
        </div>
      </div>
      <div class="form-item">
        <div class="form-label required-params">设置状态为</div>
        <div class="form-value">
          <s-radio-group v-model="selectedStatusId" :radioes="selectableStatusList"></s-radio-group>
        </div>
      </div>
      <template v-if="selectedStatusNeedParams.length">
        <div v-for="p of selectedStatusNeedParams" :key="p.name" class="form-item">
          <div class="form-label" :class="{ 'required-params': p.required }">{{ p.label }}</div>
          <div class="form-value">
            <DatePicker
              v-if="p.name === 'registerStartTime'"
              v-model="registerStartTime"
              :placeholder="p.label"
              type="date"
            ></DatePicker>
            <DatePicker
              v-else-if="p.name === 'registerEndTime'"
              v-model="registerEndTime"
              :placeholder="p.label"
              type="date"
            ></DatePicker>
            <DatePicker
              v-else-if="p.name === 'submitEndTime'"
              v-model="submitEndTime"
              :placeholder="p.label"
              type="date"
            ></DatePicker>
          </div>
        </div>
      </template>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button :disabled="!selectedStatusId" type="primary" @click="commitNewStatus">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-panel {
    .form-item {
      @include flex(row, flex-start, center);
      padding: 6px 8px;
      font-size: $font-size-medium;
      line-height: 1.5;

      &:not(:first-child) {
        margin-top: 8px;
      }

      .form-label {
        width: 12em;
        margin-right: 1.6em;
        text-align: right;
      }

      .required-params::before {
        display: inline-block;
        margin-right: 4px;
        color: #ed4014;
        font-size: $font-size-medium;
        font-family: 'SimSun';
        line-height: 1;
        content: '*';
      }

      .status-tag {
        height: 25px;
        padding: 4px 10px 4px 10px;
        border: 1px solid $color-primary;
        border-color: $color-primary-light;
        border-radius: 2px;
        color: $color-primary-light;
        font-size: 12px;
        background-color: #f0f9eb;
        user-select: none;
      }
    }
  }
</style>
