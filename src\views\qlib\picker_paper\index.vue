<template>
  <div class="container-qlib-bypaper">
    <div class="section-search">
      <Input
        :model-value="control.keyword"
        placeholder="请输入试卷标题关键字"
        maxlength="50"
        clearable
        @on-change="changeKeyword"
      >
        <template #prepend><span>搜索试卷</span></template></Input
      >
    </div>

    <com-paper-control-card
      class="section-control"
      :control="control"
      :paper-total-count="paperTotalCount"
      :show-paper-count="false"
      @change="changeControl"
    ></com-paper-control-card>

    <div v-show="papers.length" class="section-papers">
      <com-paper-card
        v-for="paper of papers"
        :key="paper.id"
        class="paper-item"
        :paper-info="paper"
        :tags="paperTags"
        :actions="paperActions"
        bank="public"
        disable-view
      ></com-paper-card>
    </div>

    <div v-show="papers.length" class="section-pagination">
      <Page
        :total="paperTotalCount"
        :page-size="pageSize"
        :model-value="currentPage"
        show-elevator
        @on-change="changePage"
      ></Page>
    </div>

    <com-paper-download-dialog
      v-model="showDownloadDialog"
      :paper-info="downloadPaperInfo"
      category="uploaded"
      bank="public"
    ></com-paper-download-dialog>
  </div>
</template>

<script>
  import comPaperControlCard from '../components/paper_control_card.vue'
  import comPaperCard from '../components/paper/paper_card.vue'
  import comPaperDownloadDialog from '../components/paper/paper_download_dialog.vue'

  import { apiGetPapers, apiFavorPaper, apiUnFavorPaper } from '@/api/qlib'

  import PageCache from '@/utils/page_cache'
  import { scrollToTop } from '@/utils/scroll'
  import { debounce } from '@/utils/function'

  const uniquePageName = 'qlib-bypaper'
  const paperTypes = [
    {
      id: 21,
      name: '小升初真题',
    },
    {
      id: 22,
      name: '小升初模拟',
    },
    {
      id: 23,
      name: '小升初复习',
    },
    {
      id: 41,
      name: '中考真卷',
    },
    {
      id: 42,
      name: '中考模拟',
    },
    {
      id: 43,
      name: '中考复习',
    },
    {
      id: 61,
      name: '高考真卷',
    },
    {
      id: 62,
      name: '高考模拟',
    },
    {
      id: 63,
      name: '高考复习',
    },
    {
      id: 64,
      name: '学业水平试卷',
    },
    {
      id: 65,
      name: '入学试卷',
    },
  ]

  export default {
    components: {
      comPaperControlCard: comPaperControlCard,
      comPaperCard: comPaperCard,
      comPaperDownloadDialog: comPaperDownloadDialog,
    },
    props: {
      paperList: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['add-to-collection', 'del-from-collection'],
    data() {
      return {
        papers: [],
        paperTotalCount: 0,
        pageSize: 10,
        currentPage: 1,
        control: {
          keyword: '',
          paperType: 0,
          grade: 0,
          year: 0,
          region: [],
          sortBy: 'time',
          term: 0,
        },
        changeKeyword: () => {},
        paperTags: () => {
          return ['grade', 'year', 'region', 'viewTimes', 'downloadTimes']
        },
        paperActions: p => {
          let r = [
            {
              name: 'check',
              label: '查看试卷',
              buttonType: 'info',
              handler: this.toPaperDetail,
            },
          ]

          if (this.paperList.some(paper => paper.paperId == p.id)) {
            r.push({
              name: 'del',
              label: '从合集移除',
              buttonType: 'warning',
              handler: this.delPaperFromCollection,
            })
          } else {
            r.push({
              name: 'add',
              label: '添加进合集',
              buttonType: 'success',
              handler: this.addPaperToCollection,
            })
          }

          // if (p.favorite === true) {
          //   r.push({
          //     name: 'unFavor',
          //     label: '取消收藏',
          //     buttonType: 'warning',
          //     handler: this.unFavorPaper,
          //   })
          // } else {
          //   r.push({
          //     name: 'favor',
          //     label: '收藏',
          //     buttonType: 'info',
          //     handler: this.favorPaper,
          //   })
          // }

          return r
        },

        showDownloadDialog: false,
        downloadPaperInfo: {},
      }
    },

    computed: {
      currentStageSubject() {
        return this.$store.getters['qlib/currentStageSubject']
      },
    },

    watch: {
      currentStageSubject() {
        this.loadPapers()
      },
    },

    created() {
      this.changeKeyword = debounce(e => {
        this.control.keyword = e.target.value.trim()
        this.currentPage = 1
        this.loadPapers()
      }, 500)

      let lastPageCacheData = PageCache.fetch(uniquePageName)
      let lastStageSubject = lastPageCacheData && lastPageCacheData.currentStageSubject
      if (
        lastStageSubject &&
        lastStageSubject.stageId === this.currentStageSubject.stageId &&
        lastStageSubject.subjectId === this.currentStageSubject.subjectId
      ) {
        Object.keys(lastPageCacheData).forEach(key => {
          if (key in this.$data && key != 'currentStageSubject') {
            this[key] = lastPageCacheData[key]
          }
        })
      }

      this.setParams()
      this.loadPapers()
    },

    beforeUnmount() {
      this.control.keyword = ''
      this.control.paperType = 0
      PageCache.save(uniquePageName, {
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        control: this.control,
        currentStageSubject: this.currentStageSubject,
      })
    },

    methods: {
      setParams() {
        const { keyword } = this.$route.params
        const { paperTypeName } = this.$route.query

        if (keyword) {
          this.control.keyword = keyword
        }

        if (paperTypeName) {
          if (paperTypeName.includes('模拟')) {
            if (this.currentStageSubject.stageId == 1) {
              this.control.paperType = 22
            } else if (this.currentStageSubject.stageId == 2) {
              this.control.paperType = 42
            } else {
              this.control.paperType = 62
            }
            return
          }

          const thePaperType = paperTypes.find(p => p.name === paperTypeName)
          if (thePaperType) {
            this.control.paperType = thePaperType.id
          }
        }
      },
      loadPapers() {
        let params = {
          stage: this.currentStageSubject.stageId,
          subject: this.currentStageSubject.subjectId,
          pageSize: this.pageSize,
          currentPage: this.currentPage,
        }
        Object.keys(this.control).forEach(key => {
          params[key] = this.control[key]
        })

        scrollToTop()

        apiGetPapers(params).then(data => {
          this.papers = data.papers
          this.paperTotalCount = data.total
        })
      },

      changeControl(changeData) {
        this.control[changeData.name] = changeData.value
        if (changeData.name === 'grade' && !changeData.value) {
          this.control['term'] = 0
        }
        this.currentPage = 1
        this.loadPapers()
      },

      changePage(page) {
        this.currentPage = page
        scrollToTop()
        this.loadPapers()
      },

      downloadPaper(p) {
        this.downloadPaperInfo = p
        this.showDownloadDialog = true
      },
      favorPaper(p) {
        apiFavorPaper(p.id).then(() => {
          let thisPaper = this.papers.find(x => x.id === p.id)
          if (thisPaper) {
            thisPaper.favorite = true
          }
        })
      },
      unFavorPaper(p) {
        apiUnFavorPaper(p.id).then(() => {
          let thisPaper = this.papers.find(x => x.id === p.id)
          if (thisPaper) {
            thisPaper.favorite = false
          }
        })
      },
      addPaperToCollection(p) {
        this.$emit('add-to-collection', p)
      },
      delPaperFromCollection(p) {
        this.$emit('del-from-collection', p)
      },
      toPaperDetail(p) {
        let routeData = this.$router.resolve({
          name: 'qlib-examPaperView',
          params: {
            paperid: p.id,
          },
          query: {
            cannotEdit: Boolean(p.feedbackSheetDownloadCount) || undefined,
          },
        })

        window.open(routeData.href, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-qlib-bypaper {
    margin-top: 10px;
  }

  .section-search {
    margin-bottom: 10px;
  }

  .paper-item {
    margin-top: 10px;
  }

  .section-pagination {
    @include flex(row, flex-end, center);
    margin-top: 20px;
  }
</style>
