<script>
  import { apiGetSubjectCompare } from '@/api/report'

  import { mapGetters } from 'vuex'
  import { groupArray } from '@/utils/array'
  import { deepCopy } from '@/utils/object'
  import { retainTwoDecimalPlaces } from '@/utils/math'
  import { exportExcel } from '@/utils/excel_export'
  import echarts from '@/utils/echarts'

  const TableColumnStatic = [
    {
      title: '学科',
      key: 'subjectName',
    },
    {
      title: '实考人数',
      key: 'examedStudent',
      renderHeader: h =>
        h(
          'div',
          {
            title:
              '实考人数：本次考试各科目的实际参加人数，总分的参加人数以参加过任一科目考生人数为准，具体科目以非缺考人数为准',
          },
          '实考人数'
        ),
    },
    {
      title: '缺考人数',
      key: 'absentStudent',
      renderHeader: h =>
        h(
          'div',
          {
            title: '缺考人数：未参加考试或答卷未扫描的考生人数',
          },
          '缺考人数'
        ),
    },
    {
      title: '统计人数',
      key: 'statCount',
      renderHeader: h =>
        h(
          'div',
          {
            title: '统计人数：成绩参与排名及平均分等指标计算的考生人数',
          },
          '统计人数'
        ),
    },
    {
      title: '满分',
      key: 'totalFullScore',
    },
    {
      title: '最高分',
      key: 'maxScore',
    },
    {
      title: '最低分',
      key: 'minScore',
    },
    {
      title: '平均分',
      key: 'avgScore',
      sortable: true,
      getData: row => row.avgScore && Number(retainTwoDecimalPlaces(row.avgScore)),
      render: (h, params) => h('span', {}, params.row.avgScore && Number(retainTwoDecimalPlaces(params.row.avgScore))),
    },
  ]

  export default {
    props: {
      isParamsExplainShowed: {
        type: Boolean,
        default: false,
      },
      disableDownloadExcel: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['change-disable-download-excel-status'],

    data() {
      return {
        tableData: [],

        modalSelectIndicatorShown: false,
        selectedIndicatorOptions: [
          {
            id: '最高分',
            value: '最高分',
            code: 'maxScore',
          },
          {
            id: '平均分',
            value: '平均分',
            code: 'avgScore',
          },
        ],
        tempIndicatorOptions: [],

        // 排序 - 图表同步
        sortTitle: ' ',
        sortKey: ' ',
        sortOrder: 'normal', // normal | asc | desc

        latestTableDataRequestTime: 0,
      }
    },

    computed: {
      ...mapGetters('report', [
        'template',
        'isMultipleSchool',
        'currentLevelName',
        'isAdministrator',
        'currentReport',
        'currentParams',
        'currentSchoolId',
        'currentSchoolName',
        'currentClassId',
        'currentClassName',
        'examId',
        'examName',
        'templateId',
        'currentInstitutionId',
        'isSeniorHighSchoolSubjectCombinationSelectableProject',
        'subjectCombinationCode',
        'seniorHighSchoolSubjectCombinations',
        'subjectCombinationName',
        'templateSubjects',
      ]),

      // 是否显示排名
      // 考试管理员默认可见，其他老师需明确配置可见
      showSchoolRegionRank() {
        return (
          this.isMultipleSchool &&
          this.currentLevelName === 'school' &&
          (this.isAdministrator ||
            (this.currentReport &&
              this.currentReport.rolePositions.some(r => r.detail && r.detail.showSchoolRegionRank)))
        )
      },
      showClassRegionRank() {
        return (
          this.isMultipleSchool &&
          this.currentLevelName === 'class' &&
          (this.isAdministrator ||
            (this.currentReport &&
              this.currentReport.rolePositions.some(r => r.detail && r.detail.showClassRegionRank)))
        )
      },
      showClassSchoolRank() {
        return (
          this.currentLevelName === 'class' &&
          (this.isAdministrator ||
            (this.currentReport &&
              this.currentReport.rolePositions.some(r => r.detail && r.detail.showClassSchoolRank)))
        )
      },

      /* 用户配置项 */
      templateParametersScorerates() {
        const List = []

        if (this.template && this.template.parameters && this.template.parameters.scoreRates) {
          let operList = this.template.parameters.scoreRates
          operList.forEach(x => {
            x.sign = x.statsWay.toString()
            x.items.forEach(y => {
              x.sign += y.name + y.range
            })
          })

          operList = groupArray(operList, x => x.sign)
          operList.forEach(x => {
            List.push({
              statsWay: x.group[0].statsWay,
              subjectIds: x.group.map(x => x.subjectId),
              subjectNames: x.group.map(x => x.subjectName),
              items: x.group[0].items,
            })
          })
        }

        return List
      },

      // 显示标准差：有数据才显示
      showSD() {
        return this.tableData.some(row => row.sd != null)
      },

      // 显示标准分
      showZScore() {
        if (!this.isMultipleSchool || this.currentLevelName != 'school') {
          return false
        }
        return Boolean(this.template?.parameters?.misc?.calcSchoolZScore)
      },

      /* HTML 说明 */
      rawHTML() {
        let dynamicConfigHTML = ''
        const _WriteExplainItem = (statsWay, items) => {
          switch (statsWay) {
            case 1: {
              // 具体分数
              items.forEach(item => {
                dynamicConfigHTML +=
                  `<li class="item"><strong>${item.name}：</strong>` +
                  `本单位（联考全体 | 机构 | 学校 | 班级）分数${item.minValueIncluded ? '大于等于' : '大于'}` +
                  `${item.minValue}分，${item.maxValueIncluded ? '小于等于' : '小于'}` +
                  `${item.maxValue}分的考生人数 / 实考人数 × 100%</li>`
              })
              break
            }
            case 2: {
              // 分数比例
              items.forEach(item => {
                dynamicConfigHTML +=
                  `<li class="item"><strong>${item.name}：</strong>` +
                  `本单位（联考全体 | 机构 | 学校 | 班级）分数处于科目满分的` +
                  `${item.minValue * 100}%${item.minValueIncluded ? '（含）' : '（不含）'}` +
                  `至科目满分的${item.maxValue * 100}%${item.maxValueIncluded ? '（含）' : '（不含）'}` +
                  `的考生人数 / 实考人数 × 100%</li>`
              })
              break
            }
            case 3: {
              // 具体排名
              items.forEach(item => {
                dynamicConfigHTML +=
                  `<li class="item"><strong>${item.name}：</strong>` +
                  `本单位（联考全体 | 机构 | 学校 | 班级）排名在第${item.minValue}名` +
                  `${item.minValueIncluded ? '（含）' : '（不含)'}至第` +
                  `${item.maxValue}名${item.maxValueIncluded ? '（含）' : '（不含）'}` +
                  `的考生人数 / 实考人数 × 100%</li>`
              })
              break
            }
            case 4: {
              // 排名比例
              items.forEach(item => {
                dynamicConfigHTML +=
                  `<li class="item"><strong>${item.name}：</strong>` +
                  `本单位（联考全体 | 机构 | 学校 | 班级）排名在前${item.minValue * 100}%` +
                  `${item.minValueIncluded ? '（含）' : '（不含）'}至` +
                  `${item.maxValue * 100}%${item.maxValueIncluded ? '（含）' : '（不含）'}` +
                  `的考生人数 / 实考人数 × 100%</li>`
              })
              break
            }
          }
        }

        if (this.templateParametersScorerates.length) {
          if (this.templateParametersScorerates.length === 1) {
            _WriteExplainItem(this.templateParametersScorerates[0].statsWay, this.templateParametersScorerates[0].items)
          } else {
            this.templateParametersScorerates.forEach(x => {
              dynamicConfigHTML += `<div class="different-config-title">${x.subjectNames.toString()}</div>`
              _WriteExplainItem(x.statsWay, x.items)
            })
          }
        }

        return (
          '<li class="item"><strong>实考人数：</strong>本次考试各科目的实际参加人数，总分的参加人数以参加过任一科目考生人数为准，具体科目以非缺考人数为准</li>' +
          '<li class="item"><strong>缺考人数：</strong>未参加考试或答卷未扫描的考生人数</li>' +
          '<li class="item"><strong>统计人数：</strong>成绩参与排名及平均分等指标计算的考生人数</li>' +
          dynamicConfigHTML
        )
      },

      tableColumns() {
        const Columns = deepCopy(TableColumnStatic)

        // 标准差
        if (this.showSD) {
          Columns.push({
            title: '标准差',
            key: 'sd',
            sortable: true,
            getData: row => (row.sd == null ? '' : Number(retainTwoDecimalPlaces(row.sd))),
            render: (h, params) =>
              h('span', {}, params.row.sd == null ? '' : Number(retainTwoDecimalPlaces(params.row.sd))),
          })
        }

        // 标准分
        if (this.showZScore) {
          Columns.push({
            title: '标准分',
            key: 'zScore',
            sortable: true,
            getData: row => (row.zScore == null ? '' : Number(retainTwoDecimalPlaces(row.zScore))),
            render: (h, params) =>
              h('span', {}, params.row.sd == null ? '' : Number(retainTwoDecimalPlaces(params.row.zScore))),
          })
        }

        // 成绩率
        if (this.tableData.length) {
          const SampleRowRates = this.tableData[0].rates || []

          SampleRowRates.forEach(x => {
            Columns.push({
              title: x.id,
              sortable: true,
              getData: row => {
                let rate = row.rates.find(f => f.id === x.id)
                let rateStr = ''
                if (rate && rate.name) {
                  rateStr = retainTwoDecimalPlaces(+rate.name * 100) + '%'
                }
                return rateStr
              },
              sortMethod: this.getRateSortFunction(x.id),
              render: (h, params) => {
                let rate = params.row.rates.find(f => f.id === x.id)
                let rateStr = ''
                if (rate && rate.name) {
                  rateStr = retainTwoDecimalPlaces(+rate.name * 100) + '%'
                }

                return h('span', {}, rateStr)
              },
            })
          })
        }

        // 排名
        if (this.showSchoolRegionRank) {
          Columns.push({
            title: '联考排名',
            key: 'regionRank',
            align: 'center',
          })
        } else {
          if (this.showClassRegionRank) {
            Columns.push({
              title: '联考排名',
              key: 'regionRank',
              align: 'center',
            })
          }
          if (this.showClassSchoolRank) {
            Columns.push({
              title: '校排名',
              key: 'schoolRank',
              align: 'center',
            })
          }
        }

        Columns.forEach(x => {
          x.align = 'center'
        })

        return Columns
      },

      tableHeight() {
        return window.innerHeight - 140
      },

      /* 选择指标 */
      indicatorOptions() {
        const Options = [
          {
            id: '最高分',
            value: '最高分',
            code: 'maxScore',
          },
          {
            id: '最低分',
            value: '最低分',
            code: 'minScore',
          },
          {
            id: '平均分',
            value: '平均分',
            code: 'avgScore',
          },
        ]

        if (this.tableData.length) {
          ;(this.tableData[0].rates || []).forEach(x => {
            Options.push({
              id: x.id,
              value: x.id,
              code: 'rates',
            })
          })
        }

        return Options
      },

      /* 画图参数 */
      chartOption() {
        const Option = {}

        if (this.tableData && this.tableData.length) {
          const AxisYAll = [
            {
              name: '分',
              type: 'value',
              min: 0,
              axisTick: {
                show: false,
              },
            },
            {
              name: '%',
              type: 'value',
              max: 100,
              min: 0,
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
            },
          ]

          let tableDataWithoutTotalScore = this.tableData.filter(item => item.subjectName !== '总分')
          if (this.sortOrder !== 'normal') {
            if (this.sortKey === 'rates') {
              tableDataWithoutTotalScore = tableDataWithoutTotalScore.sort((a, b) => {
                if (this.sortOrder === 'asc') {
                  return (
                    +a.rates.find(rate => rate.id === this.sortTitle).name -
                    +b.rates.find(rate => rate.id === this.sortTitle).name
                  )
                } else {
                  return (
                    +b.rates.find(rate => rate.id === this.sortTitle).name -
                    +a.rates.find(rate => rate.id === this.sortTitle).name
                  )
                }
              })
            } else {
              tableDataWithoutTotalScore = tableDataWithoutTotalScore.sort((a, b) => {
                return this.sortOrder === 'asc' ? a[this.sortKey] - b[this.sortKey] : b[this.sortKey] - a[this.sortKey]
              })
            }
          }

          const OptionAxisY = []
          const OptionSeries = []
          let optionSeriesData = []
          const ContainPoint = this.selectedIndicatorOptions.some(x =>
            TableColumnStatic.some(column => column.title === x.id)
          )
          const ContainRate = this.selectedIndicatorOptions.some(x =>
            TableColumnStatic.every(column => column.title !== x.id)
          )

          if (ContainPoint && ContainRate) {
            OptionAxisY.push(...AxisYAll)
            this.selectedIndicatorOptions.forEach(x => {
              let optionIndexY = 0
              if (TableColumnStatic.some(column => column.title === x.id)) {
                optionSeriesData = tableDataWithoutTotalScore.map(y => Math.round(y[x.code] * 100) / 100)
                optionIndexY = 0
              } else {
                optionSeriesData = tableDataWithoutTotalScore.map(
                  y => Math.round(Number((y.rates.find(z => z.id === x.value) || { name: '0' }).name) * 10000) / 100
                )
                optionIndexY = 1
              }
              OptionSeries.push({
                type: 'bar',
                name: x.value,
                data: optionSeriesData,
                barGap: '45%',
                barMaxWidth: '25%',
                barCategoryGap: '25%',
                yAxisIndex: optionIndexY,
              })
            })
          } else if (ContainPoint) {
            OptionAxisY.push(AxisYAll[0])
            this.selectedIndicatorOptions.forEach(x => {
              optionSeriesData = tableDataWithoutTotalScore.map(y => Math.round(y[x.code] * 100) / 100)
              OptionSeries.push({
                type: 'bar',
                name: x.value,
                data: optionSeriesData,
                barGap: '45%',
                barMaxWidth: '23%',
                barCategoryGap: '25%',
              })
            })
          } else if (ContainRate) {
            AxisYAll[1].splitLine.show = true
            OptionAxisY.push(AxisYAll[1])
            this.selectedIndicatorOptions.forEach(x => {
              optionSeriesData = tableDataWithoutTotalScore.map(
                y => Math.round(Number((y.rates.find(z => z.id === x.value) || { name: '0' }).name) * 10000) / 100
              )
              OptionSeries.push({
                type: 'bar',
                name: x.value,
                data: optionSeriesData,
                barGap: '45%',
                barMaxWidth: '23%',
                barCategoryGap: '25%',
              })
            })
          }

          // set Option params
          Option.legend = {
            type: 'plain',
            data: this.selectedIndicatorOptions.map(x => x.value),
            textStyle: {
              fontSize: '15',
            },
            top: '4%',
          }
          Option.grid = {
            top: '15%',
            bottom: '15%',
            left: '8%',
            right: '8%',
          }
          Option.dataZoom = {
            type: 'slider',
            show: true,
          }
          Option.xAxis = {
            type: 'category',
            axisTick: {
              inside: true,
            },
            data: tableDataWithoutTotalScore.map(item => item.subjectName),
          }
          Option.tooltip = {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: params => {
              let result = params[0].name + '<br>'
              params.forEach(param => {
                result += param.marker + param.seriesName + ': ' + param.value
                if (param.seriesName.endsWith('率')) {
                  result += ' %<br>'
                } else {
                  result += '<br>'
                }
              })
              return result
            },
          }
          Option.yAxis = OptionAxisY
          Option.series = OptionSeries
        }

        return Option
      },
    },

    watch: {
      currentParams() {
        this.fetchData()
      },

      selectedIndicatorOptions() {
        this.drawChart()
      },
    },

    mounted() {
      this.refChart = echarts.init(this.$refs.chartPanel)
    },

    methods: {
      fetchData() {
        if (
          (this.currentLevelName !== 'multipleSchool' && !this.currentSchoolId) ||
          (this.currentLevelName === 'class' && !this.currentClassId)
        ) {
          this.tableData = []
          this.$Message.warning({
            content: '未获取到具体的学校或班级，请稍后重试',
          })
        } else {
          this.$TransparentSpin.show()

          this.$emit('change-disable-download-excel-status', true)
          const RequestTime = Date.now()
          apiGetSubjectCompare({
            examId: this.examId,
            templateId: this.templateId,
            classId: this.currentLevelName === 'class' ? this.currentClassId : undefined,
            schoolId: this.currentLevelName === 'multipleSchool' ? undefined : this.currentSchoolId,
            organizationId: this.currentLevelName === 'multipleSchool' ? this.currentInstitutionId : undefined,
            reportName:
              this.currentLevelName === 'class'
                ? 'cls_overview'
                : this.currentLevelName === 'school'
                  ? 'sch_overview'
                  : 'union_overview',
            subjectCategory: this.isSeniorHighSchoolSubjectCombinationSelectableProject
              ? this.subjectCombinationCode
              : undefined,
          })
            .then(data => {
              if (RequestTime > this.latestTableDataRequestTime) {
                this.latestTableDataRequestTime = RequestTime
              } else {
                return
              }

              if (!data) {
                data = []
              }

              data.forEach(x => {
                if (x.subjName) {
                  x.subjectName = x.subjName
                } else if (x.subjId == 0) {
                  x.subjectName = '总分'
                } else {
                  x.subjectName = (
                    this.templateSubjects.find(f => f.subjectId === x.subjId) || {
                      subjectName: '',
                    }
                  ).subjectName
                }

                x.examedStudent = x.present
                x.absentStudent = x.absent
              })

              data.sort((a, b) => a.subjId - b.subjId)

              this.tableData = data
              if (this.indicatorOptions.length > 3) {
                this.selectedIndicatorOptions = this.indicatorOptions.filter(x => x.code !== 'minScore')
              }
              this.drawChart()
            })
            .catch(err => {
              this.tableData = []
              throw err
            })
            .finally(() => {
              this.$TransparentSpin.hide()
              this.$emit('change-disable-download-excel-status', false)
            })
        }
      },

      getRateSortFunction(rateId) {
        return (a, b, type) => {
          let rateA = a.find(item => item.id === rateId)
          if (rateA) {
            rateA = Number(rateA.name)
          }
          let rateB = b.find(item => item.id === rateId)
          if (rateB) {
            rateB = Number(rateB.name)
          }
          if (type === 'asc') {
            return rateA - rateB
          } else if (type === 'desc') {
            return rateB - rateA
          }
        }
      },

      sortTableByColumns(sortInfo) {
        this.sortOrder = sortInfo.order
        this.sortKey = sortInfo.key
        this.sortTitle = sortInfo.column.title
        this.drawChart()
      },

      showSelectIndicatorModal() {
        this.tempIndicatorOptions = this.selectedIndicatorOptions.map(x => x.id)
        this.modalSelectIndicatorShown = true
      },

      checkIndicatorSelected() {
        if (!this.tempIndicatorOptions.length) {
          this.$Message.warning({
            content: '请至少选择一项指标',
          })
          return
        }

        this.selectedIndicatorOptions = this.indicatorOptions.filter(x => this.tempIndicatorOptions.includes(x.id))
        this.modalSelectIndicatorShown = false
      },

      drawChart() {
        if (this.refChart) {
          this.refChart.setOption(this.chartOption, true)
        }
      },

      exportExcelFile() {
        if (this.disableDownloadExcel) {
          this.$Message.warning({
            content: '数据加载中，请等数据加载完毕后重试',
            duration: 5,
          })
          return
        }
        if (!this.tableData.length) {
          this.$Message.info({
            content: '暂无数据',
          })
          return
        }

        let fileName = `${this.examName}_考情概览_${
          this.currentLevelName === 'multipleSchool'
            ? '联考'
            : this.currentLevelName === 'class'
              ? this.currentSchoolName + '_' + this.currentClassName
              : this.currentSchoolName
        }`
        if (this.isSeniorHighSchoolSubjectCombinationSelectableProject) {
          if (this.subjectCombinationCode) {
            fileName += '_' + this.subjectCombinationName
          }
        }
        fileName += '.xlsx'

        exportExcel(
          [
            {
              sheetName: '考情概览',
              rows: this.tableData,
              columns: deepCopy(this.tableColumns).map((x, xIndex) => {
                if (!xIndex) {
                  x.width = 'auto'
                }
                if (['平均分', '标准差', '标准分'].includes(x.title)) {
                  x.key = x.getData
                }
                if (!x.key) {
                  x.key = row => {
                    const Rate = row.rates.find(r => r.id == x.title)
                    return (Rate && Rate.name && Number(Rate.name)) || 0
                  }
                  x.cellNumberFormat = value =>
                    isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%'
                }

                return x
              }),
            },
          ],
          fileName
        )
      },
    },
  }
</script>

<template>
  <div class="component-subject-compare">
    <Alert v-show="isParamsExplainShowed" class="params-explain" type="info">
      <div class="params-explain-panel">
        <ul class="info" v-html="rawHTML"></ul>
      </div>
    </Alert>

    <Table
      border
      :columns="tableColumns"
      :data="tableData"
      :max-height="tableHeight"
      @on-sort-change="sortTableByColumns"
    />

    <Divider dashed style="margin-bottom: 18px" />

    <div style="margin-bottom: 10px">
      <Button type="primary" ghost @click="showSelectIndicatorModal">选择指标</Button>
    </div>

    <div ref="chartPanel" style="height: 500px"></div>

    <Modal v-model="modalSelectIndicatorShown" width="500" title="选择指标">
      <CheckboxGroup v-model="tempIndicatorOptions">
        <Checkbox
          v-for="(item, index) in indicatorOptions.slice(0, 3)"
          :key="'lo0' + index"
          class="check-box"
          :label="item.id"
        >
          {{ item.value }}
        </Checkbox>

        <Divider dashed style="margin: 15px 0" />

        <Checkbox
          v-for="(item, index) in indicatorOptions.slice(3)"
          :key="'lo1' + index"
          class="check-box"
          :label="item.id"
        >
          {{ item.value }}
        </Checkbox>
      </CheckboxGroup>

      <template #footer>
        <div>
          <Button type="text" @click="modalSelectIndicatorShown = false">取消</Button>
          <Button type="primary" @click="checkIndicatorSelected">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<style lang="scss" scoped>
  .component-subject-compare {
    .params-explain {
      max-height: 160px;
      padding: 10px 4px 10px 50px;
      overflow-y: auto;
      font-size: $font-size-small;
      user-select: none;

      .params-explain-panel {
        min-height: 18px;
      }

      .different-config-title {
        margin-top: 10px;
        font-size: $font-size-medium;
      }

      .info :not(:last-child) {
        margin-bottom: 6px;
      }
    }

    :deep(.ivu-alert) {
      line-height: 18px;
    }

    .check-box {
      margin-right: 20px;
    }
  }
</style>
