<template>
  <div class="container-question">
    <div class="question-header">
      <div class="name" :data-difficulty="question.difficulty">
        {{ question.questionName }}
      </div>
      <div v-if="question.originalQuestion" class="actions"></div>
      <div class="stats-avg-score">
        <div class="stats-item">
          <span class="stats-item-label">满分：</span>
          <span class="stats-item-data">{{ question.fullScore }}分</span>
        </div>
        <div v-if="showGradeAvgScore" class="stats-item">
          <span class="stats-item-label">年级均分/得分率：</span>
          <span class="stats-item-data">{{ question.gradeAvgScore }}分/{{ question.gradeScoreRate }}%</span>
        </div>
        <div class="stats-item">
          <span class="stats-item-label">班级均分/得分率：</span>
          <span class="stats-item-data">{{ question.classAvgScore }}分/{{ question.classScoreRate }}%</span>
        </div>
      </div>
    </div>
    <div ref="questionPanel" class="question-panel">
      <div v-if="question.originalQuestion" class="question-content">
        <QuestionCardBody :question="question.originalQuestion" :show-code="false" show-alias></QuestionCardBody>
      </div>
      <div class="question-body">
        <div class="section-stats">
          <div v-if="question.originalQuestion" class="section-stats-title">【统计】</div>
          <div class="section-stats-body">
            <div v-if="branchNames.length > 1" class="selector-branch">
              <div
                v-for="name in branchNames"
                :key="name"
                class="branch-name-item"
                :class="{ active: name === activeBranchName }"
                @click="changeActiveBranch(name)"
              >
                {{ name }}
              </div>
            </div>
            <div v-if="activeBranch && branchNames.length > 1" class="stats-avg-score">
              <div class="stats-item">
                <span class="stats-item-label">满分：</span>
                <span class="stats-item-data">{{ activeBranch.fullScore }}分</span>
              </div>
              <div v-if="showGradeAvgScore" class="stats-item">
                <span class="stats-item-label">年级均分/得分率：</span>
                <span class="stats-item-data"
                  >{{ activeBranch.gradeAvgScore }}分/{{ activeBranch.gradeScoreRate }}%</span
                >
              </div>
              <div class="stats-item">
                <span class="stats-item-label">班级均分/得分率：</span>
                <span class="stats-item-data"
                  >{{ activeBranch.classAvgScore }}分/{{ activeBranch.classScoreRate }}%</span
                >
              </div>
            </div>
            <div class="stats-answer-score">
              <div v-if="statsByAnswer.length > 0" class="stats-answer">
                <div
                  v-for="item in statsByAnswer"
                  :key="item.answer"
                  class="stats-answer-item"
                  :class="{
                    correct: activeBranch && activeBranch.standardAnswer === item.answer,
                    'stu-list': scoreDetailVisible,
                  }"
                  @click="showObjectiveStudentList(item)"
                >
                  <div class="label">{{ item.answer || '未选' }}</div>
                  <Progress class="progress" :percent="item.percentage" status="normal" hide-info> </Progress>
                  <div class="info" @click="showObjectiveStudentList(item)">
                    {{ item.percentage }}%，{{ item.studentCount }}人
                  </div>
                  <div class="tip">{{ item.students.length > 0 ? '点击查看学生名单' : '' }}</div>
                </div>
              </div>
              <div v-if="statsByScore.length > 0" class="stats-score">
                <div
                  v-for="item in statsByScore"
                  :key="item.intervalName"
                  class="stats-score-item"
                  :class="{ 'stu-list': scoreDetailVisible }"
                  @click="showSubjectiveStudentList(item)"
                >
                  <div class="label">{{ item.intervalName }}</div>
                  <Progress class="progress" :percent="item.percentage" hide-info> </Progress>
                  <div class="info" @click="showSubjectiveStudentList(item)">
                    {{ item.percentage }}%，{{ item.studentCount }}人
                  </div>
                  <div class="tip">点击查看学生名单</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="question.originalQuestion" class="section-question-detail">
          <QuestionCardAnswer :question="question.originalQuestion" show-alias></QuestionCardAnswer>
        </div>
      </div>
    </div>

    <Modal
      v-model="showModalObjectiveStudentList"
      class="modal-objective"
      :title="modalObjectiveStudentList.title"
      footer-hide
      width="600"
    >
      <span v-for="s in modalObjectiveStudentList.students" :key="s.studentId" class="student-name">{{
        s.studentName
      }}</span>
    </Modal>
    <Modal
      v-model="showModalSubjectiveStudentList"
      class="modal-report-lecture-subjective"
      :title="modalSubjectiveStudentList.title"
      footer-hide
      width="800"
    >
      <div class="modal-subjective-container">
        <div class="name-list">
          <div
            v-for="s in modalSubjectiveStudentList.students"
            :key="s.studentId"
            class="student-item"
            :class="{
              current:
                s.studentId ===
                (modalSubjectiveStudentList.currentStudent && modalSubjectiveStudentList.currentStudent.studentId),
            }"
            @click="changeModalSubjectiveCurrentStudent(s)"
          >
            <div class="student-name">{{ s.studentName }}</div>
            <div class="student-score">{{ s.score }}分</div>
          </div>
        </div>
        <div class="images">
          <canvas ref="canvas" class="question-image"></canvas>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
  import QuestionCardBody from '@/views/qlib/components/question/question_card_body.vue'
  import QuestionCardAnswer from '@/views/qlib/components/question/question_card_answer.vue'

  import { drawBlockComments } from '@/helpers/report/comment'
  import { loadImage } from '@/utils/promise'

  export default {
    components: {
      QuestionCardBody,
      QuestionCardAnswer,
    },
    props: {
      question: Object,
      showGradeAvgScore: {
        type: Boolean,
        default: true,
      },
      showAllBranch: {
        type: Boolean,
        default: true,
      },
      scoreDetailVisible: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        activeBranchName: undefined,
        showModalObjectiveStudentList: false,
        showModalSubjectiveStudentList: false,
        modalObjectiveStudentList: {
          title: '',
          students: [],
        },
        modalSubjectiveStudentList: {
          title: '',
          students: [],
          currentStudent: null,
        },
        canvas: null,
      }
    },
    computed: {
      branchNames() {
        let names = this.question.branches.map(b => b.branchName)
        if (this.showAllBranch && this.question.branches.length > 1) {
          names.unshift('全部小题')
        }
        return names
      },
      activeBranch() {
        if (this.activeBranchName === '全部小题') {
          return this.question
        } else {
          return this.question.branches.find(b => b.branchName === this.activeBranchName)
        }
      },
      statsByAnswer() {
        return (this.activeBranch && this.activeBranch.statsByAnswer) || []
      },
      statsByScore() {
        return (this.activeBranch && this.activeBranch.statsByScore) || []
      },
    },
    watch: {
      question() {
        this.changeActiveBranch(this.branchNames[0])
      },
    },
    created() {
      this.changeActiveBranch(this.branchNames[0])
    },
    mounted() {
      this.canvas = this.$refs['canvas']
    },
    methods: {
      changeActiveBranch(name) {
        this.activeBranchName = name
      },
      showObjectiveStudentList(item) {
        if (!this.scoreDetailVisible) {
          return
        }
        if (item.students.length === 0) {
          return
        }

        this.modalObjectiveStudentList = {
          title: `${this.activeBranch.branchName || this.activeBranch.questionName}题——${item.answer}选项（${
            item.studentCount
          }人，${item.percentage}%）`,
          students: item.students,
        }
        this.showModalObjectiveStudentList = true
      },
      showSubjectiveStudentList(item) {
        if (!this.scoreDetailVisible) {
          return
        }
        if (item.students.length === 0) {
          return
        }

        this.modalSubjectiveStudentList = {
          title: `${this.activeBranch.branchName || this.activeBranch.questionName}题——${item.intervalName}（${
            item.studentCount
          }人，${item.percentage}%）`,
          students: item.students,
          currentStudent: item.students[0],
        }
        this.showModalSubjectiveStudentList = true
        this.changeModalSubjectiveCurrentStudent(this.modalSubjectiveStudentList.students[0])
      },
      changeModalSubjectiveCurrentStudent(s) {
        this.modalSubjectiveStudentList.currentStudent = s
        this.drawImages(s)
      },
      async drawImages(s) {
        if (!this.canvas) {
          this.canvas = this.$refs.canvas
        }

        // 清除画布
        let ctx = this.canvas.getContext('2d')
        ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

        // 加载图片
        let imageElements = []
        try {
          let blockImageUrls = s.blocks.map(x => x.src).filter(Boolean)
          if (blockImageUrls.length == 0) {
            return
          }
          imageElements = await Promise.all(blockImageUrls.map(loadImage))
        } catch (err) {
          return
        }

        // 设置宽高
        this.canvas.width = Math.max(...imageElements.map(x => x.naturalWidth))
        this.canvas.height = imageElements.reduce((a, c) => a + c.naturalHeight, 0)

        // 分题块
        let offsetX = 0
        let offsetY = 0

        for (let idx = 0; idx < s.blocks.length; idx++) {
          let item = s.blocks[idx]
          let el = imageElements[idx]
          // 原图
          ctx.drawImage(el, offsetX, offsetY, el.naturalWidth, el.naturalHeight)

          // 整理批注
          let comments = []
          item.comments.forEach(s => {
            try {
              let teacherComments = JSON.parse(s)
              teacherComments.forEach(c => comments.push(c))
            } catch (err) {
              // do nothing
            }
          })

          // 绘制
          await drawBlockComments(ctx, comments, offsetX, offsetY)

          // 调整offset
          offsetY += el.naturalHeight
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-question {
    background-color: white;
  }

  .question-header {
    @include flex(row, space-between, center);
    padding-right: 20px;
    border-bottom: 1px solid #f0f0f0;

    .name {
      display: inline-block;
      min-width: 40px;
      height: 40px;
      padding-right: 8px;
      padding-left: 8px;
      color: white;
      font-weight: bold;
      font-size: $font-size-large;
      line-height: 40px;
      text-align: center;
      background-color: $color-primary;

      &[data-difficulty='easy'] {
        background-color: $color-success;
      }

      &[data-difficulty='normal'] {
        background-color: #2db7f5;
      }

      &[data-difficulty='hard'] {
        background-color: $color-error;
      }
    }
  }

  /* .question-content {
    padding: 0 20px;
  } */

  .question-panel {
    padding-top: 20px;
  }

  .question-belt {
    @include flex(row, flex-end, center);
    clear: both;
    height: 40px;
    margin-top: 20px;
  }

  .question-body {
    clear: both;
    padding: 0 20px 10px 20px;

    .section-stats {
      @include flex(row, flex-start, flex-start);
      margin-top: 20px;
      margin-bottom: 20px;

      .section-stats-title {
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 0.5em;
        margin-left: -0.5em;
        line-height: 30px;
      }

      .section-stats-body {
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .branch-name-item {
      display: inline-block;
      min-width: 30px;
      height: 30px;
      margin-right: 5px;
      margin-bottom: 5px;
      padding-right: 8px;
      padding-left: 8px;
      border: 1px solid $color-primary;
      border-radius: 3px;
      line-height: 28px;
      text-align: center;
      cursor: pointer;
      user-select: none;

      &:hover {
        color: $color-primary;
      }

      &.active {
        color: white;
        background-color: $color-primary;
      }
    }
  }

  .stats-avg-score {
    line-height: 30px;

    .stats-item {
      display: inline-block;
      margin-left: 10px;
      font-size: $font-size-small;

      &:first-child {
        margin-left: 0;
      }

      .stats-item-data {
        color: $color-warning;
      }
    }
  }

  .stats-answer-item,
  .stats-score-item {
    @include flex(row, flex-start, center);
    position: relative;
    line-height: 30px;

    .tip {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1;
      display: none;
      font-size: $font-size-small;
      line-height: 30px;
      text-align: center;
    }

    .label {
      flex-grow: 0;
      flex-shrink: 0;
      margin-right: 20px;
    }

    .progress {
      flex-grow: 1;
      flex-shrink: 1;
    }

    &.correct .progress {
      :deep(.ivu-progress-bg) {
        background-color: $color-success;
      }
    }

    .info {
      flex-grow: 0;
      flex-shrink: 0;
      min-width: 6em;
      margin-left: 20px;
    }

    &.stu-list:hover {
      cursor: pointer;

      .tip {
        display: block;
      }
    }
  }

  .stats-answer-item .label {
    min-width: 1.5em;
  }

  .stats-score-item .label {
    min-width: 4.5em;
  }

  .modal-objective {
    .student-name {
      display: inline-block;
      min-width: 3em;
      margin: 5px 10px;
    }
  }

  .modal-report-lecture-subjective {
    :deep(.ivu-modal-body) {
      padding: 0;
    }
  }

  .modal-subjective-container {
    @include flex(row, flex-start, stretch);
    height: 400px;
    height: calc(100vh - 200px);

    .name-list {
      flex-grow: 0;
      flex-shrink: 0;
      width: 8em;
      border: 1px solid #ccc;
      overflow-y: auto;
      background-color: #f0f0f0;

      .student-item {
        padding: 10px 20px;
        border-bottom: 1px solid #ccc;
        line-height: 1.2;
        cursor: pointer;
        user-select: none;

        &.current {
          color: $color-primary;
          background-color: white;
        }
      }
    }

    .images {
      flex-grow: 1;
      flex-shrink: 1;
      overflow-y: auto;

      .question-image {
        width: 100%;
      }
    }
  }
</style>
