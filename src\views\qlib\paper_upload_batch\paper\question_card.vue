<template>
  <div class="question-card">
    <div class="panel-top">
      <com-question-card-body class="q-body" :question="props.question" show-code-color></com-question-card-body>
      <com-transition-collapse :duration="0.3">
        <com-question-card-answer
          v-show="!collapsed"
          class="q-answer"
          :question="props.question"
          show-question-type
          :show-chapter="false"
          style="padding-top: 15px"
        ></com-question-card-answer>
      </com-transition-collapse>
    </div>

    <div v-if="props.enableChange" class="panel-bottom">
      <div class="question-footer">
        <span class="question-actions">
          <Button
            v-if="props.showBtnExpandBranch && props.question.branches.length > 1"
            class="btn-text btn-expand-branch"
            type="text"
            @click="toggleExpandBranches"
            >{{ props.question.code == 0 ? '合成一题' : '转为多题' }}</Button
          >
          <Button class="btn-text btn-collapse" type="text" @click="toggleCollapse">
            {{ collapsed ? '查看解析' : '收起解析' }}
          </Button>
          <Poptip
            confirm
            transfer
            :title="`确定删除第${props.question.getQuestionCodeString()}题？`"
            @on-ok="removeQuestion"
          >
            <Button class="btn-text btn-remove" type="text">删除</Button>
          </Poptip>
          <Button class="btn-edit" type="primary" @click="editQuestion">编辑</Button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import comQuestionCardBody from '../../components/question/question_card_body.vue'
  import comQuestionCardAnswer from '../../components/question/question_card_answer.vue'
  import comTransitionCollapse from '@/components/transition_collapse.vue'

  const props = defineProps({
    question: {
      type: Object,
      default() {
        return {}
      },
    },
    showBtnExpandBranch: Boolean,
    enableChange: Boolean,
  })
  const emits = defineEmits(['remove', 'edit', 'toggle-expand-branch'])
  const collapsed = ref(false)

  function toggleCollapse() {
    collapsed.value = !collapsed.value
  }
  function setCollapsed(value) {
    collapsed.value = value
  }
  function removeQuestion() {
    emits('remove')
  }
  function editQuestion() {
    emits('edit')
  }
  function toggleExpandBranches() {
    emits('toggle-expand-branch')
  }

  defineExpose({ setCollapsed })
</script>

<style lang="scss" scoped>
  .question-card {
    background-color: white;
  }

  .panel-top {
    padding: 10px;
  }

  .question-footer {
    @include flex(row, flex-end, center);
    clear: both;
    height: 30px;
    padding: 10px;

    button:focus {
      box-shadow: none;
    }

    .btn-text {
      color: $color-info;
      background: none !important;
      cursor: pointer;
    }

    .btn-remove {
      margin-right: 20px;
      color: $color-error;
    }

    .btn-basket {
      margin-left: 2em;
    }
  }

  .q-info-item {
    margin-right: 20px;
  }

  .q-info-item-number {
    margin: 0 3px;
    color: $color-warning;
  }
</style>
