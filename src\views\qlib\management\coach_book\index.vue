<template>
  <component
    :is="activeComponent"
    :book="currentBook"
    :initial-params="bookPageParams"
    :semesters="semesters"
    @change-book="currentBook = $event"
    @change-params="bookPageParams = $event"
    @switch="switchComponent"
  ></component>
</template>

<script>
  import ComBook from './book'
  import ComPaper from './paper'
  import ComCustomPaper from './custom_paper'
  import ComponentShareList from './share_list'

  import { apiGetSchoolSemesters } from '@/api/user'

  import Store from '@/store/index'

  import PageCache from '@/utils/page_cache'

  const UniquePageName = 'qlib-management-supplementarybook'

  export default {
    beforeRouteEnter(to, from, next) {
      Store.dispatch('emarking/ensureBasicData').finally(() => next())
    },
    data() {
      return {
        activeComponentName: 'book',
        currentBook: null,
        bookPageParams: null,
        semesters: [],
      }
    },
    computed: {
      activeComponent() {
        if (this.activeComponentName == 'paper') {
          return ComPaper
        } else if (this.activeComponentName == 'book') {
          return ComBook
        } else if (this.activeComponentName === 'customPaper') {
          return ComCustomPaper
        } else {
          return ComponentShareList
        }
      },
      currentStageSubject() {
        return this.$store.getters['qlib/currentStageSubject']
      },
    },
    created() {
      this.getPageCache()
      if (!this.semesters || !this.semesters.length) {
        this.fetchSemesters()
      }
    },
    beforeUnmount() {
      PageCache.save(UniquePageName, {
        activeComponentName: this.activeComponentName,
        currentBook: this.currentBook,
        bookPageParams: this.bookPageParams,
      })
    },
    methods: {
      getPageCache() {
        let lastPageCacheData = PageCache.fetch(UniquePageName)
        if (
          lastPageCacheData &&
          this.currentStageSubject &&
          lastPageCacheData.currentBook &&
          lastPageCacheData.currentBook.stageId === this.currentStageSubject.stageId &&
          lastPageCacheData.currentBook.subjectId === this.currentStageSubject.subjectId
        ) {
          this.switchComponent(lastPageCacheData.activeComponentName || '')
          this.currentBook = lastPageCacheData.currentBook || null
          this.bookPageParams = lastPageCacheData.bookPageParams || null
        }
      },
      switchComponent(componentName) {
        this.activeComponentName = componentName || 'book'
      },
      fetchSemesters() {
        return apiGetSchoolSemesters()
          .then(
            response =>
              (this.semesters = response.map(r => ({
                id: r.semesterId,
                name: r.semesterName,
              })))
          )
          .catch(() => (this.semesters = []))
      },
    },
  }
</script>
