<script>
  import SupplementaryExercise from './components/supplementary_exercise.vue'
  import FileDownload from './components/file_download.vue'

  import iView from '@/iview'
  import Store from '@/store/index'

  import { apiCheckUserDownloadPaperFilePermission } from '@/api/user/download_asset_config'

  export default {
    components: {
      'supplementary-exercise': SupplementaryExercise,
      'file-download': FileDownload,
    },

    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      Store.dispatch('emarking/ensureBasicData').finally(() => {
        iView.LoadingBar.finish()
        next()
      })
    },

    data() {
      return {
        canDownloadFile: false,
        activeTab: 'supplementaryExercise', // "supplementaryExercise" | "fileDownload"
        tabFileDownloadShowOnce: false,
      }
    },

    created() {
      this.fetchUserDownloadPaperFilePermission()
    },

    methods: {
      fetchUserDownloadPaperFilePermission() {
        return apiCheckUserDownloadPaperFilePermission().then(response => (this.canDownloadFile = response || false))
      },
      handleTabChanged() {
        if (this.activeTab === 'fileDownload') {
          this.tabFileDownloadShowOnce = true
        }
      },
    },
  }
</script>

<template>
  <div class="container-supplementary-exercise">
    <div class="wrap-tabs">
      <Tabs v-model="activeTab" :animated="false" @on-click="handleTabChanged">
        <TabPane label="数智教辅" name="supplementaryExercise">
          <supplementary-exercise></supplementary-exercise>
        </TabPane>
        <TabPane v-if="canDownloadFile" label="资料下载" name="fileDownload">
          <file-download v-if="tabFileDownloadShowOnce"></file-download>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .container-supplementary-exercise {
    background-color: white;

    .wrap-tabs {
      padding: 10px 0 25px;

      :deep(.ivu-tabs-nav .ivu-tabs-tab) {
        padding-bottom: 14px;
      }

      :deep(.ivu-tabs-bar) {
        margin: 0 20px 16px;
      }
    }
  }
</style>
