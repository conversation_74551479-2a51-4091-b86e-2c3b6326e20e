<template>
  <div class="container-coach-book-ingestion">
    <PageHeader @back="back"></PageHeader>
    <component :is="currentComponent"></component>
  </div>
</template>

<script setup>
  import { computed, onBeforeMount } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'
  import iView from '@/iview'

  import PageHeader from './page_header.vue'
  import StepUpload from './step_upload.vue'

  import { apiGetCoachBookInfo } from '@/api/qlib/coach_book'
  import { apiGetCoachBookIngestionInfo, apiCreateCoachBookIngestion } from '@/api/qlib/coach_book_ingestion'

  import { backOrDefault } from '@/utils/router'

  const route = useRoute()
  const router = useRouter()
  const ingestionStore = useCoachBookIngestionStore()

  /**
   * 当前步骤组件
   */
  const currentComponent = computed(() => {
    if (ingestionStore.currentStep == 'upload') {
      return StepUpload
    } else {
      return null
    }
  })

  /**
   * 获取教辅信息和录入信息
   */
  onBeforeMount(async () => {
    try {
      iView.LoadingBar.start()
      await init()
    } catch {
      iView.LoadingBar.error()
      setTimeout(() => {
        back()
      }, 1000)
    } finally {
      iView.LoadingBar.finish()
    }
  })

  async function init() {
    let coachBookId = route.params.coachBookId
    ingestionStore.reset()
    let [coachBookInfo, ingestionInfo] = await Promise.all([
      apiGetCoachBookInfo(coachBookId),
      apiGetCoachBookIngestionInfo(coachBookId),
    ])

    if (ingestionInfo == null) {
      ingestionInfo = await apiCreateCoachBookIngestion({
        coachBookId,
        fileType: 'pdf',
      })
    }

    ingestionStore.setCoachBookInfo(coachBookInfo)
    ingestionStore.setIngestionInfo(ingestionInfo)
  }

  function back() {
    backOrDefault(router, {
      name: 'qlib-management-supplementaryBook',
    })
  }
</script>
