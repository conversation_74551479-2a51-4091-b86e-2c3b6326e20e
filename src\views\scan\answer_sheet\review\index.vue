<template>
  <div v-if="hasPermissionReview" class="container-scan-review">
    <PageHeader
      class="section-header"
      :show-btn-back="false"
      title-prefix="扫描复核"
      :enable-change-view-mode="false"
      :enable-select-scan-user="false"
      show-btn-close
      @close="closeWindow"
    ></PageHeader>

    <Tabs v-model="activeTab" class="section-tabs" :animated="false">
      <TabPane
        v-for="(tab, tabIndex) of tabs"
        :key="tab.label"
        :name="tab.name"
        :label="tab.label"
        :index="tabIndex + 1"
      ></TabPane>
    </Tabs>

    <Component :is="currentComponent" class="review-content"></Component>
  </div>
</template>

<script>
  import PageHeader from '../components/page_header.vue'
  import EvaluationMultiFeeding from './multi_feeding/index.vue'

  import { mapGetters } from 'vuex'
  import iView from '@/iview'
  import store from '@/store/index'

  export default {
    components: {
      PageHeader,
      EvaluationMultiFeeding,
    },
    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      store
        .dispatch('scan/loadExamSubjectScanRangeQuestionTemplate', {
          examId: to.params.examId,
          examSubjectId: to.params.examSubjectId,
        })
        .then(() => {
          next()
        })
        .catch(() => {
          iView.LoadingBar.error()
        })
        .finally(() => {
          iView.LoadingBar.finish()
        })
    },
    data() {
      return {
        activeTab: 'multi_feeding',
      }
    },
    computed: {
      ...mapGetters('scan', ['hasPermissionReview']),
      tabs() {
        return [
          {
            label: '重张进纸',
            name: 'multi_feeding',
            component: EvaluationMultiFeeding,
          },
          // {
          //   label: '客观题偏移检查',
          //   name: 'scan_objective',
          //   // component: ScanRecords,
          // },
        ]
      },
      currentComponent() {
        return this.tabs.find(t => t.name == this.activeTab)?.component
      },
    },
    created() {
      if (!this.hasPermissionReview) {
        this.$Message.error({
          content: '无扫描复核权限',
          duration: 5,
          closable: true,
          onClose: () => {
            this.closeWindow()
          },
        })
      }
    },
    beforeUnmount() {
      this.$store.commit('scan/clear')
    },
    methods: {
      closeWindow() {
        window.close()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-scan-review {
    @include flex(column, flex-start, stretch);
    height: 100vh;
    background-color: white;

    :deep(.ivu-tabs-bar) {
      margin-bottom: 0;
    }

    .section-header,
    .section-tabs {
      flex-grow: 0;
      flex-shrink: 0;
    }

    .review-content {
      flex-shrink: 1;
    }
  }
</style>
