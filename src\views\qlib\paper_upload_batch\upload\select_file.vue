<template>
  <div class="select-file">
    <Button type="primary" long @click="openFolderDialog">请先选择试卷Word文档</Button>
    <input
      ref="folderInputRef"
      type="file"
      multiple
      :accept="WORD_EXTENSIONS.join(',')"
      style="display: none"
      @change="handleFilesSelection"
    />
  </div>
</template>

<script setup>
  import { useTemplateRef } from 'vue'
  import iView from '@/iview'

  const emits = defineEmits(['selected'])

  const folderInputRef = useTemplateRef('folderInputRef')

  const WORD_EXTENSIONS = ['.doc', '.docx']

  function openFolderDialog() {
    if (folderInputRef.value) {
      folderInputRef.value.value = ''
      folderInputRef.value.click()
    }
  }

  function handleFilesSelection(event) {
    const allFiles = event.target.files

    if (!allFiles || allFiles.length === 0) {
      iView.Message.warning({
        content: '您没有选择任何文件',
        duration: 5,
      })
      return
    }

    let wordFiles = Array.from(allFiles).filter(file => {
      const fileNameLower = file.name.toLowerCase()
      return WORD_EXTENSIONS.some(ext => fileNameLower.endsWith(ext))
    })
    if (wordFiles.length === 0) {
      iView.Message.warning({
        content: '您没有选择 Word 文件 (.doc, .docx)',
        duration: 5,
      })
      return
    }

    emits('selected', wordFiles)
  }
</script>
