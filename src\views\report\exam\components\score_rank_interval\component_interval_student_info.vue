<script>
  import { apiGetScoreIntervalStudentInfo, apiGetRankIntervalStudentInfo } from '@/api/report'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      params: {
        type: Object,
        default: () => {},
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        tableData: [],
        maxTableHeight: window.innerHeight - 300,

        currentPage: 1,
        pageSize: 10,
        dataTotal: 0,
      }
    },

    computed: {
      isTypeScore() {
        return this.params && this.params.type == 'score'
      },

      modalTitle() {
        const PropsParams = this.params
        let _info = ''

        if (PropsParams) {
          if (PropsParams.type) {
            _info += (this.isTypeScore ? '分数段' : '名次段') + ' '
          }
          if (PropsParams.range) {
            _info += PropsParams.range + ' '
          }
        }

        return `学生信息${_info ? ' —— ' + _info : ''}`
      },

      tableColumns() {
        const Result = [
          {
            title: '准考号',
            key: 'admissionNum',
            align: 'center',
          },
          {
            title: '姓名',
            key: 'studentName',
            align: 'center',
          },
          {
            title: '学校',
            key: 'schoolName',
            align: 'center',
          },
          {
            title: '班级',
            key: 'className',
            align: 'center',
          },
        ]

        if (this.isTypeScore) {
          Result.push({
            title: '分数',
            key: 'score',
            align: 'center',
          })
        } else {
          Result.push({
            title: '排名',
            key: 'rank',
            align: 'center',
          })
        }

        return Result
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },

      handleModalVisibleChanged(visibility) {
        if (visibility) {
          this.currentPage = 1
          this.pageSize = 10
          this.pageTotal = 0

          this.fetchStudentInfos()
        } else {
          this.closeModal()
        }
      },

      changePage(page = 1) {
        this.currentPage = page
        this.fetchStudentInfos()
      },

      changePageSize(size = 10) {
        this.pageSize = size
        this.changePage()
      },

      fetchStudentInfos() {
        const Request = this.isTypeScore ? apiGetScoreIntervalStudentInfo : apiGetRankIntervalStudentInfo

        this.$TransparentSpin.show()
        return Request({
          ...this.params,
          pageSize: this.pageSize,
          currentPage: this.currentPage,
        })
          .then(response => {
            if (response) {
              this.dataTotal = response.total || 0
              this.tableData = response.list || []
            }
          })
          .catch(() => {
            this.dataTotal = 0
            this.tableData = []
          })
          .finally(() => this.$TransparentSpin.hide())
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :width="70"
    :title="modalTitle"
    footer-hide
    @on-visible-change="handleModalVisibleChanged"
  >
    <div class="modal-panel">
      <Table :columns="tableColumns" :data="tableData" :max-height="maxTableHeight" border></Table>

      <div class="pager-bar">
        <Page
          :total="dataTotal"
          :page-size="pageSize"
          :model-value="currentPage"
          :show-sizer="true"
          :page-size-opts="[10, 20, 50, 100]"
          show-total
          show-elevator
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        ></Page>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-panel {
    min-height: 100px;

    .pager-bar {
      margin-top: 16px;
      margin-bottom: 6px;
      text-align: right;
    }
  }
</style>
