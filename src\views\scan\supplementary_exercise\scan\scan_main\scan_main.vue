<template>
  <div class="scan-main-container">
    <ScanMainStatusStatsStart
      :scanner-status-id="scannerStatusId"
      :current-batch-status="currentBatchStatus"
      :stats="stats"
      :top="top"
      :fixed-height="fixedBarHeight"
      @refresh="handleBtnRefreshClick"
      @start-scan="beginScan"
      @to-monitor="gotoScanMonitor"
    ></ScanMainStatusStatsStart>

    <div class="scan-result">
      <div class="radio" :style="resultRadioStyle">
        <span
          v-for="tab in resultTabs"
          :key="tab.id"
          class="label"
          :class="{ active: tab.id == activeResultTab, disabled: !hasExam && tab.id != 'image' }"
          @click="changeActiveTab(tab.id)"
          >{{ tab.name }}</span
        >
      </div>
      <div class="content">
        <ScanMainImage
          v-show="activeResultTab == 'image'"
          class="image"
          :scan-image-left="scanImageLeft"
          :scan-image-right="scanImageRight"
          :top="resultTop"
        ></ScanMainImage>
        <ScanMainRoomStudent
          v-show="activeResultTab == 'room'"
          class="room"
          :student-status-id="studentStatusId"
          :room-students="roomStudents"
          :room-no="roomNo"
          :top="resultTop"
          @change-status="changeStudentStatus"
          @refresh="getScanBatch"
        ></ScanMainRoomStudent>
        <ScanMainBatchRoom
          class="batch"
          :batch-rooms="batchRooms"
          :batch-count="batchCount"
          :current-page="batchCurrentPage"
          :page-size="batchPageSize"
          :room-no="roomNo"
          :top="resultTop"
          @change-room="changeRoom"
          @change-page="changeBatchPage"
        ></ScanMainBatchRoom>
      </div>
    </div>

    <ModalInstance
      v-model="showModalDownloadClient"
      :width="450"
      type="warning"
      title="更新扫描客户端"
      show-cancel
      ok-text="下载安装"
      @cancel="showModalDownloadClient = false"
      @ok="handleModalDownloadClientOK"
    >
      <strong style="display: block; line-height: 2.5">当前同级生扫描客户端版本过低，请下载安装最新版！</strong>
    </ModalInstance>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'

  import ScanMainStatusStatsStart from './components/scan_main_status_stats_start.vue'
  import ScanMainImage from './components/scan_main_image.vue'
  import ScanMainRoomStudent from './components/scan_main_room_student.vue'
  import ScanMainBatchRoom from './components/scan_main_batch_room.vue'
  import ModalInstance from '@/components/modal_instance.vue'

  import { apiGetScanClientInfo, apiGetScannerStatus, apiStartScan, apiPreScan } from '@/api/scan'
  import {
    apiGetScanBatchInfo,
    apiGetPersonalBatchRoomStatsPage,
    apiGetScanUserBatchRoomStatsPage,
    apiGetScanStationBatchRoomStatsPage,
    apiGetSubjectBatchRoomStatsPage,
    apiGetRoomStudents,
  } from '@/api/scan/scan_batch'
  import { apiGetPersonalScanStats, apiGetStationScanStats, apiGetUserScanStats } from '@/api/scan/scan_stats'

  import { randomString } from '@/utils/string'
  import { downloadUrl } from '@/utils/download'

  import ScannerStatusEnum from '@/enum/emarking/scanner_status'
  import { ScanClientWebSocketHost } from '@/config/config.js'
  import { apiSetInstitutionScanDeviceLisence } from '../../../../../api/user/scan_device_lisence'

  export default {
    components: {
      ScanMainStatusStatsStart,
      ScanMainImage,
      ScanMainRoomStudent,
      ScanMainBatchRoom,
      ModalInstance,
    },
    props: {
      top: Number,
    },
    emits: ['to-detail', 'to-monitor'],
    data() {
      return {
        // 定时器
        scannerStatusTimer: 0,
        scanStatsBatchTimer: 0,
        currentBatchInfoTimer: 0,

        // 扫描仪状态
        scannerStatusId: 'connecting',
        // 最后一次扫描中的时间
        lastScanningTimeStamp: Number.MAX_SAFE_INTEGER,

        // 正在开启扫描
        startingScan: false,

        // 当前批次
        currentBatchId: '',
        currentBatchInfo: null,

        // 扫描统计
        stats: null,

        // 显示的tab
        resultTabs: [
          {
            id: 'image',
            name: '实时图像',
          },
          {
            id: 'room',
            name: '考场考生',
          },
        ],
        activeResultTab: 'image',

        // 图像
        scanImageLeft: '',
        scanImageRight: '',
        nextImagePosition: 'left',
        socket: null,

        // 批次
        batchRooms: [],
        batchCount: 0,
        batchCurrentPage: 1,
        batchPageSize: 10,

        // 考生
        roomNo: 0,
        roomStudents: [],
        // 考生状态：all-全部; scanned-已扫; markAbsent-标记缺考; unScan-未扫
        studentStatusId: 'all',

        // 固定的状态、统计高度
        fixedBarHeight: 64,
        // 实时图像、考场考生切换高度
        resultRadioHeight: 34,

        // 下载扫描端弹窗
        showModalDownloadClient: false,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'viewMode', 'hasExam', 'isPostScan']),
      /**
       * 样式
       */
      resultRadioStyle() {
        return {
          height: this.resultRadioHeight + 'px',
          paddingBottom: '8px',
          borderBottomWidth: '1px',
          lineHeight: `${this.resultRadioHeight - 8 - 1}px`,
        }
      },
      resultTop() {
        return this.top + this.fixedBarHeight + this.resultRadioHeight
      },
      currentBatchStatus() {
        // 无批次
        if (!this.currentBatchId) {
          return 'noBatch'
        }
        // 批次上传中（刚开始扫描，服务端尚未接收到批次）
        else if (!this.currentBatchInfo) {
          return 'uploading'
        }
        // 确定科目或创建考试中
        else if (!this.currentBatchInfo.examSubjectId) {
          return 'creatingExam'
        }
        // 无法确定科目或创建考试失败
        else if (this.currentBatchInfo.examSubjectId == '-1') {
          return 'createExamFailed'
        }
        // 创建考试成功
        else {
          return 'createExamSucceeded'
        }
      },
      currentBatchStatusCreateExamFailed() {
        return this.currentBatchInfo && this.currentBatchInfo.examSubjectId == '-1'
      },
      // 当前批次状态-创建考试成功
      currentBatchStatusCreateExamSucceeded() {
        return (
          this.currentBatchInfo && this.currentBatchInfo.examSubjectId && this.currentBatchInfo.examSubjectId != '-1'
        )
      },
    },
    created() {
      this.resetStats()

      // 扫描仪状态
      this.getScannerStatus()
      this.startStatusTimer()

      // 扫描统计及批次信息
      this.getScanStats()
      this.getScanBatch()
      this.startStatsBatchTimer()
    },
    beforeUnmount() {
      clearTimeout(this.scannerStatusTimer)
      clearTimeout(this.scanStatsBatchTimer)
      clearTimeout(this.currentBatchInfoTimer)
      // 设为0则请求完成后不再设置定时任务
      this.scannerStatusTimer = 0
      this.scanStatsBatchTimer = 0
      this.currentBatchInfoTimer = 0
      if (this.socket) {
        this.socket.close()
      }
    },
    methods: {
      /**
       * 重置数据
       */
      resetStats() {
        this.stats = {
          scanPaperCount: 0,
          uploadPaperCount: 0,
          scanStuCount: 0,
          stuCount: 0,
          markAbsentStuCount: 0,
          waitRecogCount: 0,
          imageEx: 0,
          locateEx: 0,
          subjectEx: 0,
          cornerEx: 0,
          admissionEx: 0,
          duplicateEx: 0,
          missingEx: 0,
          absentEx: 0,
          objectiveEx: 0,
          subjectiveEx: 0,
          selectEx: 0,
          existsMyScanEx: false,
        }
      },
      resetExamSubject(examId, examSubjectId) {
        this.resetStats()

        this.batchRooms = []
        this.batchCount = 0
        this.batchCurrentPage = 1
        this.batchPageSize = 10

        this.roomNo = 0
        this.roomStudents = []
        this.studentStatusId = 'all'

        if (examId && examSubjectId) {
          this.$router.replace({
            name: 'scan-supplementary-exercise',
            params: {
              examId,
              examSubjectId,
            },
          })
          this.$store.dispatch('scan/loadExamSubjectScanRangeQuestionTemplateAsync', {
            examId,
            examSubjectId,
          })
        }
      },
      resetScanRealTimeImage() {
        this.activeResultTab = 'image'
        this.scanImageLeft = ''
        this.scanImageRight = ''
        this.nextImagePosition = 'left'
        if (this.socket) {
          this.socket.close()
        }
        this.socket = null
      },
      resetCurrentBatch() {
        this.currentBatchId = ''
        this.currentBatchInfo = null
      },

      /**
       * 请求数据
       */
      // 扫描仪状态
      getScannerStatus() {
        return apiGetScannerStatus()
          .then(statusId => {
            this.scannerStatusId = statusId
            if (statusId == ScannerStatusEnum.Scanning.id) {
              this.lastScanningTimeStamp = Date.now()
            }
          })
          .catch(() => {
            // 此接口不报错
            this.scannerStatusId = ScannerStatusEnum.NoConnection.id
          })
      },
      // 当前批次信息
      getCurrentBatchInfo() {
        // 无批次Id
        if (!this.currentBatchId) {
          this.currentBatchInfo = null
        }
        let batchId = this.currentBatchId
        let timer = true
        return apiGetScanBatchInfo(this.currentBatchId)
          .then(batchInfo => {
            // 不是当前批次，忽略
            if (this.currentBatchId != batchId) {
              return
            }
            this.currentBatchInfo = batchInfo
            // 科目创建成功，页面跳转到该科目
            if (this.currentBatchStatus == 'createExamSucceeded') {
              timer = false
              if (this.currentBatchInfo.examSubjectId != this.examSubjectId) {
                this.$Message.info({
                  content: `识别到教辅章节：${this.currentBatchInfo.examName}`,
                  duration: 5,
                  closable: true,
                })
                this.resetExamSubject(this.currentBatchInfo.examId, this.currentBatchInfo.examSubjectId)
              }
            }
            // 科目创建失败
            if (this.currentBatchStatus == 'createExamFailed') {
              timer = false
              this.$Modal.error({
                title: '出错了',
                content: '无法识别教辅作业所属章节，或创建教辅作业项目失败',
              })
            }
            // 扫描结束5秒后仍然没有批次，则放弃本批次
            if (this.currentBatchStatus == 'uploading' && Date.now() - this.lastScanningTimeStamp > 1000 * 5) {
              timer = false
              this.currentBatchId = ''
              this.$Message.warning({
                content: '本批次未扫描纸张',
                duration: 5,
                closable: true,
              })
            }
          })
          .finally(() => {
            if (timer) {
              this.currentBatchInfoTimer = setTimeout(() => {
                this.getCurrentBatchInfo()
              }, 1000)
            } else {
              this.currentBatchInfoTimer = 0
            }
          })
      },
      // 科目扫描统计
      getScanStats() {
        if (!this.hasExam) {
          return Promise.resolve()
        }
        let { isAdmin, scanStationId, scanUserId } = this.viewMode
        let api
        if (!isAdmin) {
          api = apiGetPersonalScanStats
        } else if (scanUserId) {
          api = apiGetUserScanStats
        } else {
          api = apiGetStationScanStats
        }
        let examSubjectId = this.examSubjectId
        return api({
          examSubjectId: this.examSubjectId,
          scanStationId,
          scanUserId,
        }).then(stats => {
          if (examSubjectId != this.examSubjectId) {
            return
          }
          this.stats = {
            ...stats,
            ...stats.exception,
          }
        })
      },
      // 科目扫描批次
      async getScanBatch() {
        if (!this.hasExam) {
          return
        }
        let { isAdmin, scanStationId, scanUserId } = this.viewMode
        let api
        if (!isAdmin) {
          api = apiGetPersonalBatchRoomStatsPage
        } else if (scanUserId) {
          api = apiGetScanUserBatchRoomStatsPage
        } else if (scanStationId) {
          api = apiGetScanStationBatchRoomStatsPage
        } else {
          api = apiGetSubjectBatchRoomStatsPage
        }
        try {
          let examSubjectId = this.examSubjectId
          let res = await api({
            examSubjectId: this.examSubjectId,
            scanStationId,
            scanUserId,
            currentPage: this.batchCurrentPage,
            pageSize: this.batchPageSize,
          })
          if (examSubjectId != this.examSubjectId) {
            return
          }
          this.batchRooms = res.records
          this.batchCount = res.total
        } catch (err) {
          this.batchRooms = []
          this.batchCount = 0
          this.roomNo = 0
          this.roomStudents = []
          throw err
        }
        await this.getRoomStudents()
      },
      // 科目考生考生
      async getRoomStudents() {
        if (!this.hasExam) {
          return
        }
        if (this.activeResultTab != 'room') {
          this.roomStudents = []
          return
        }
        if (this.batchRooms.every(batch => batch.rooms.every(room => room.roomNo != this.roomNo))) {
          this.roomStudents = []
          let firstBatch = this.batchRooms.find(batch => batch.rooms.length > 0)
          if (firstBatch) {
            this.roomNo = firstBatch.rooms[0].roomNo
          } else {
            this.roomNo = 0
          }
        }
        if (!this.roomNo) {
          this.roomStudents = []
          return
        }
        try {
          let examSubjectId = this.examSubjectId
          let roomStudents = await apiGetRoomStudents({
            examSubjectId: this.examSubjectId,
            roomNo: this.roomNo,
          })
          if (examSubjectId != this.examSubjectId) {
            return
          }
          this.roomStudents = roomStudents
        } catch (err) {
          this.roomStudents = []
          throw err
        }
      },

      /**
       * 定时刷新
       */
      startStatusTimer() {
        this.scannerStatusTimer = setTimeout(() => {
          this.getScannerStatus().finally(() => {
            if (this.scannerStatusTimer) {
              this.startStatusTimer()
            }
          })
        }, 1000)
      },
      startStatsBatchTimer() {
        this.scanStatsBatchTimer = setTimeout(() => {
          Promise.all([this.getScanStats(), this.getScanBatch()]).finally(() => {
            if (this.scanStatsBatchTimer) {
              this.startStatsBatchTimer()
            }
          })
        }, 2000)
      },

      /**
       * 操作响应
       */
      handleBtnRefreshClick() {
        this.scannerStatusId = ScannerStatusEnum.Connecting.id
        Promise.all([this.getScannerStatus(), this.getScanStats(), this.getScanBatch()]).then(() => {
          this.$Message.info({
            content: '已刷新',
          })
        })
      },
      async beginScan() {
        if (this.startingScan) {
          return
        }
        this.startingScan = true

        // 重置批次信息
        this.resetCurrentBatch()

        // 扫描新批次前检查并获取开启扫描参数
        let scanClientInfo
        try {
          scanClientInfo = await apiGetScanClientInfo()
        } catch (err) {
          this.showModalDownloadClient = true
          this.startingScan = false
          return
        }

        let params = {
          examSubjectId: '',
          userId: this.$store.getters['user/info'].userId,
          secretId: (scanClientInfo && scanClientInfo.clientId) || undefined,
          nonce: randomString(6),
          timeStamp: Math.floor(Date.now() / 1000),
          clientId: (scanClientInfo && scanClientInfo.clientId) || undefined,
          clientVersion: (scanClientInfo && scanClientInfo.clientVersion) || undefined,
          deviceName: (scanClientInfo && scanClientInfo.deviceName) || undefined,
          deviceNo: (scanClientInfo && scanClientInfo.deviceNo) || undefined,
          deviceManufacturer: (scanClientInfo && scanClientInfo.deviceManufacturer) || undefined,
          isPostScan: this.hasExam ? this.isPostScan : this.$route.params.examId == 'newpostscan',
          clientHandwritingSupported: scanClientInfo?.clientHandwritingSupported,
        }
        try {
          const PreScanResponse = await apiPreScan(params)
          params.signature = PreScanResponse.signature
          params.token = PreScanResponse.token
          params.isLocalRecognition = PreScanResponse.isLocalRecognition || false
          params.pixelType = 'RGB'
        } catch (err) {
          this.startingScan = false
          if (err && err.code == 2001) {
            this.showModalDownloadClient = true
            return
          } else if (err && err.code == 2002) {
            this.$Modal.error({
              title: '扫描前检查不通过',
              content: '当前扫描仪尚未授权，是否立即授权？',
              onOk: () => {
                apiSetInstitutionScanDeviceLisence({
                  deviceNo: params.deviceNo || '1251',
                  deviceName: params.deviceName || '虚拟扫描仪v1',
                })
                  .then(() => {
                    this.$Message.success({
                      duration: 4,
                      content: '已成功授权',
                    })
                  })
                  .catch(err => {
                    if (err && err.code && err.code === 9999) {
                      this.$Modal.error({
                        title: '扫描前检查不通过',
                        content: err.msg,
                      })
                    }
                  })
              },
            })
            return
          } else {
            this.$Modal.error({
              title: '扫描前检查不通过',
              content: (err && err.msg) || '',
            })
            throw err
          }
        }

        // 开启扫描
        try {
          this.currentBatchId = await apiStartScan(params)
        } catch (err) {
          this.$Modal.error({
            title: '开启扫描失败',
            content: (err && err.msg) || '',
          })
          this.startingScan = false
          throw err
        }

        // 查询批次信息
        this.getCurrentBatchInfo()

        // 显示实时图像
        this.resetScanRealTimeImage()
        this.startWebsocket()

        // 立即刷新扫描状态
        this.getScannerStatus()

        this.startingScan = false
      },
      startWebsocket() {
        // 连接已关闭或者没有连接成功
        if (!this.socket || this.socket.readyState == 3) {
          this.socket = new WebSocket(ScanClientWebSocketHost)
          this.socket.addEventListener('message', e => {
            let scanData = JSON.parse(e.data)
            if (scanData && scanData.type === 'image') {
              let imageData = JSON.parse(scanData.data)
              // 可能其他页面正在扫描其他科目
              if (imageData) {
                if (this.nextImagePosition == 'right') {
                  this.scanImageRight = imageData.image
                  this.nextImagePosition = 'left'
                } else {
                  this.scanImageLeft = imageData.image
                  this.nextImagePosition = 'right'
                }
              }
            }
          })
          this.socket.addEventListener('close', () => {
            this.socket = null
          })
        }
      },
      gotoScanMonitor(status) {
        this.$emit('to-monitor', status)
      },
      changeActiveTab(tab) {
        if (!this.hasExam && tab.id != 'image') {
          return
        }
        this.activeResultTab = tab
        if (tab == 'room') {
          this.getRoomStudents()
        }
      },
      changeRoom({ roomNo, status }) {
        let roomNoChanged = this.roomNo != roomNo
        this.roomNo = roomNo
        if (status) {
          this.studentStatusId = status
        }
        if (roomNoChanged) {
          this.roomStudents = []
          this.getRoomStudents()
        }
        this.activeResultTab = 'room'
      },
      changeBatchPage(page) {
        this.batchCurrentPage = page
        this.getScanBatch()
      },
      changeStudentStatus(status) {
        if (status) {
          this.studentStatusId = status
        }
      },
      handleModalDownloadClientOK() {
        downloadUrl('/file/同级生扫描客户端.exe')
        this.showModalDownloadClient = false
      },
    },
  }
</script>

<style scoped lang="scss">
  .scan-main-container {
    padding-right: 16px;
    padding-left: 16px;
    overflow-y: scroll;
  }

  .scan-result {
    margin-top: 30px;

    .radio {
      border-bottom-color: $color-border;
      border-bottom-style: solid;

      .label {
        margin-right: 20px;
        font-weight: bold;
        font-size: $font-size-medium-x;
        cursor: pointer;
        user-select: none;

        &.active {
          color: $color-primary;
        }

        &.disabled {
          color: $color-disabled;
          cursor: not-allowed;
        }
      }
    }

    .content {
      @include flex(row, flex-start, stretch);

      .image,
      .room {
        flex: 1 1 0;
      }

      .batch {
        flex: 0 0 520px;
        margin-left: 8px;
      }
    }
  }
</style>
