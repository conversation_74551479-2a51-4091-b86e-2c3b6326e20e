import ajax from '@/api/ajax'
import { groupArray } from '@/utils/array'
import { transformExam } from '@/api/emarking'
import { sortQuestionFunction } from '../../helpers/emarking/miscellaneous'

/**
 * 报告基础数据
 */
export function apiGetExamSchoolClasses(examId) {
  return ajax
    .get({
      url: 'report/score/classList2',
      params: {
        examId,
      },
      requestName: '获取学校班级',
    })
    .then(list => {
      if (!list) {
        list = []
      }

      return groupArray(list, cls => cls.schoolId).map(sch => {
        let classes = sch.group
          .map(c => ({
            classId: c.classId,
            className: c.className,
            sortCode: c.sortCode,
            classType: c.type,
            teachingSubjectIds: c.subjects || [],
          }))
          .sort((a, b) => a.sortCode - b.sortCode)

        return {
          schoolId: sch.group[0].schoolId,
          classes,
        }
      })
    })
}

export function apiGetSysSemster() {
  return ajax
    .get({
      url: 'report/score/sysSemester',
      requestName: '获取学期时间',
    })
    .then(data => {
      return data
    })
}

/**
 * 报表生成控制
 */
export function apiGenerateSubjectReport(params) {
  return ajax.put({
    url: 'report/score/scoreReport',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      skipWarn: params.skipWarn || false,
    },
    // requestName: '生成科目报表',
  })
}

/**
 * 查询分析报告
 */
export function apiGetReportExams({
  semesterId,
  term,
  gradeId,
  examType,
  examScope,
  beginTime,
  endTime,
  keyword,
  currentPage,
  pageSize,
}) {
  return ajax
    .get({
      url: 'report/score/reportList',
      params: {
        semesterId,
        term,
        grade: gradeId,
        type: examType,
        scope: examScope,
        beginTime,
        endTime,
        key: keyword,
        current: currentPage,
        size: pageSize,
      },
      requestName: '获取分析报告',
    })
    .then(data => ({
      currentPage: data.current,
      pageSize: data.size,
      total: data.total,
      exams: (data.records || []).map(resExam => {
        let exam = transformExam(resExam)
        exam.templates = (resExam.temps || []).map(x => ({
          templateId: x.templateId || '',
          templateName: x.templateName || '',
          reCalSign: x.reCalSign,
          sign: x.sign || 0,
          hasViewReportPermission: x.hasViewReportPermission || false,
          isShow: 'isShow' in x ? x.isShow : true,
          isDefault: x.isDefault,
          type: x.type,
        }))
        return exam
      }),
    }))
}

/**
 * 报表大题配置
 */

export function apiGetTopicConfig(params) {
  return ajax.get({
    url: 'report/score/topicDefine',
    params: {
      templateId: params.templateId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取报表大题配置',
  })
}

export function apiSaveTopicConfig(data) {
  return ajax.post({
    url: 'report/score/topicDefine',
    data: {
      examId: data.examId,
      templateId: data.templateId,
      examSubjectId: data.examSubjectId,
      topics: data.topics,
    },
    // requestName: '保存报表大题配置',
  })
}

/**
 * 报表数据
 */
// 获取学生成绩
export function apiGetStudentScore(params) {
  return ajax.get({
    url: 'report/score/students',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      key: params.keyword || null,
      orgSchId: params.organizationId || null,
      size: params.pageSize,
      current: params.currentPage,
      reportName: params.reportName,
      includeTeachingPoint: params.includeTeachingPoint,
    },
    requestName: '获取学生成绩',
  })
}
export function apiGetStudentScoreOfSeniorHigh(requestParams) {
  return ajax.get({
    url: 'report/score/students/v2',
    params: {
      categoryId: requestParams.categoryId,
      classId: requestParams.classId,
      schoolId: requestParams.schoolId,
      examId: requestParams.examId,
      foreignSubjectId: requestParams.foreignSubjectId,
      key: requestParams.keyword,
      orgSchId: requestParams.organizationId,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      reportName: requestParams.reportName,
      subjectCode: requestParams.subjectCode,
      templateId: requestParams.templateId,
      orderFiled: requestParams.orderFiled, // String ('score' || 'admissionNum')
      asc: requestParams.asc, // Boolean 是否升序
    },
    requestName: '获取学生成绩（高中选科）',
  })
}

// 分数段统计
export function apiGetScoreInterval(params) {
  return ajax.get({
    url: 'report/score/schScoreInterval',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      interval: params.scoreInterval,
      reportName: params.reportName,
    },
    requestName: '获取分数段统计',
  })
}

export function apiGetUnionScoreInterval(params) {
  return ajax.get({
    url: 'report/score/areaScoreInterval',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      interval: params.scoreInterval,
      reportName: 'union_scoreInterval',
    },
    requestName: '获取分数段统计（联考）',
  })
}

// 名次段统计
export function apiGetRankInterval(params) {
  return ajax.get({
    url: 'report/score/schRankInterval',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      interval: params.rankInterval,
      reportName: params.reportName,
    },
    requestName: '获取名次段统计',
  })
}

export function apiGetUnionRankInterval(params) {
  return ajax.get({
    url: 'report/score/areaRankInterval',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      interval: params.rankInterval,
      reportName: 'union_rankInterval',
    },
    requestName: '获取名次段统计（联考）',
  })
}

/*
	ranges: [
		{
			name: 'A'(string),
			range: '[80, 100]'(string),
			sortCode: 1(number)
		}
	]
*/
export function apiFetchScoreInterval(requestParams) {
  return ajax.post({
    url: 'report/score/schScoreInterval',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges || [],
    requestName: '获取分数段统计',
  })
}
export function apiFetchScoreIntervalOfSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/score/schScoreInterval/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取分数段统计（高中选科）',
  })
}

export function apiFetchUnionScoreInterval(requestParams) {
  return ajax.post({
    url: 'report/score/areaScoreInterval',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges || [],
    requestName: '获取分数段统计（联考）',
  })
}
export function apiFetchUnionScoreIntervalForSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/score/areaScoreInterval/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取分数段统计（联考-高中选科）',
  })
}

export function apiFetchRankInterval(requestParams) {
  return ajax.post({
    url: 'report/score/schRankInterval',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges || [],
    requestName: '获取名次段统计',
  })
}
export function apiFetchRankIntervalOfSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/score/schRankInterval/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      orgSchId: requestParams.organizationId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取名次段统计',
  })
}

export function apiFetchUnionRankInterval(requestParams) {
  return ajax.post({
    url: 'report/score/areaRankInterval',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    data: requestParams.ranges || [],
    requestName: '获取名次段统计（联考）',
  })
}
export function apiFetchUnionRankIntervalOfSeniorHigh(requestParams) {
  return ajax.post({
    url: 'report/score/areaRankInterval/v2',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      categoryId: requestParams.categoryId,
      subjectCode: requestParams.subjectCode,
    },
    data: requestParams.ranges || [],
    requestName: '获取名次段统计（联考-高中选科）',
  })
}

// 试卷讲评统计
export function apiGetPaperComment(params) {
  return ajax
    .get({
      url: 'report/score/paperComment',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId || null,
        templateId: params.templateId,
        schoolId: params.schoolId,
        classId: params.classId,
        reportName: params.reportName,
      },
      requestName: '获取试卷讲评统计数据',
    })
    .then(data => {
      // 没有小题的，转换为只有一个小题，小题号为0
      // 客观题均无小题
      let objectiveQuestions = data.objects.map(q => {
        let questionName = q.quesName || String(q.quesCode)
        let branchName = questionName
        return {
          questionName,
          questionCode: q.quesCode,
          branchName,
          branchCode: 0,
          branchTypeId: q.quesType,
          fullScore: q.fullScore,
          optionCount: q.optionCount,
          standardAnswer: q.answer,
          gradeAvgScore: q.graAvg,
          classAvgScore: q.claAvg,
          originalQuestionId: q.quesId,
          originalBranchId: q.branchId,
          isObjective: true,
        }
      })
      let subjectiveQuestions = data.subjects.map(q => {
        let questionName = q.quesName || String(q.quesCode)
        let branchCode = data.subjects.length > 1 ? q.branchCode : 0
        let branchName = q.branchName || (branchCode == 0 ? questionName : `${questionName}.${branchCode}`)
        return {
          questionName,
          questionCode: q.quesCode,
          branchName,
          branchCode,
          fullScore: q.fullScore,
          originalQuestionId: q.quesId,
          originalBranchId: q.branchId,
          gradeAvgScore: q.graAvg,
          classAvgScore: q.claAvg,
          isObjective: false,
        }
      })

      let questions = [...objectiveQuestions, ...subjectiveQuestions]
      questions.sort(sortQuestionFunction)

      return {
        paperId: !data.paperId || data.paperId == 0 ? null : data.paperId,
        questions,
      }
    })
}

// 试卷讲评分数
export function apiGetPaperCommentStudentDetail(params) {
  return ajax
    .get({
      url: 'report/score/studentAnswers',
      params: {
        examId: params.examId,
        examSubjectId: params.examSubjectId,
        templateId: params.templateId,
        schoolId: params.schoolId,
        classId: params.classId,
        reportName: params.reportName,
      },
      requestName: '获取试卷讲评学生分数',
    })
    .then(data =>
      data.map(branch => {
        let answers = branch.answers.map(s => ({
          studentId: s.stuId,
          studentName: s.stuName,
          answer: s.stuAnswer,
          score: s.stuScore,
        }))
        return {
          questionCode: branch.code,
          branchCode: branch.branchCode,
          blockId: branch.blockId,
          answers,
        }
      })
    )
}

export function apiGetPaperObjectiveComment(params) {
  return ajax
    .get({
      url: 'report/score/paperObjectiveComment',
      params: {
        examSubjectId: params.examSubjectId || null,
        classId: params.classId,
      },
      requestName: '获取客观题讲评统计数据',
    })
    .then(data => {
      // 没有小题的，转换为只有一个小题，小题号为0
      // 客观题均无小题
      let objectiveQuestions = data.objects.map(q => {
        let questionName = q.quesName || String(q.quesCode)
        let branchName = questionName
        return {
          questionName,
          questionCode: q.quesCode,
          branchName,
          branchCode: 0,
          branchTypeId: q.quesType,
          fullScore: q.fullScore,
          optionCount: q.optionCount,
          standardAnswer: q.answer,
          gradeAvgScore: q.graAvg,
          classAvgScore: q.claAvg,
          originalQuestionId: q.quesId,
          originalBranchId: q.branchId,
          isObjective: true,
        }
      })

      let questions = [...objectiveQuestions]
      questions.sort(sortQuestionFunction)

      return {
        paperId: !data.paperId || data.paperId == 0 ? null : data.paperId,
        studentAnswers: data.studentAnswers.map(branch => {
          let answers = branch.answers.map(s => ({
            studentId: s.stuId,
            studentName: s.stuName,
            answer: s.stuAnswer,
            score: s.stuScore,
          }))
          return {
            questionCode: branch.code,
            branchCode: branch.branchCode,
            blockId: branch.blockId,
            answers,
          }
        }),
        questions,
      }
    })
}

export function apiGetStudentPaperScore(params) {
  return ajax.get({
    url: 'report/score/stuPaperScore',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      admissionNo: params.admissionNum,
    },
    requestName: '获取考生卷面成绩',
  })
}

export function apiGetSubjectClassList(params) {
  return ajax.get({
    url: 'report/score/subjectClassList',
    params: {
      examId: params.examId,
    },
    requestName: '查询班级和班级下的科目列表',
  })
}

export function apiChangeStudentObjective(params) {
  return ajax.put({
    url: 'report/score/stuObj',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      studentId: params.studentId,
    },
    data: {
      code: params.code,
      answer: params.answer,
      score: params.score == null ? undefined : params.score,
    },
    requestName: '修改考生客观题作答',
  })
}

export function apiChangeStudentSubjective(params) {
  return ajax.put({
    url: 'report/score/stuSubj',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      studentId: params.studentId,
    },
    data: {
      blockId: params.blockId,
      score: params.score,
      subScore: params.subScore,
    },
    requestName: '修改考生主观题得分',
  })
}

export function apiChangeStudentSign(params) {
  return ajax.put({
    url: 'report/score/stuSign',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      studentId: params.studentId,
      sign: params.sign,
    },
    requestName: '修改考生缺考',
  })
}

export function apiChangeStudentOtherScore(params) {
  return ajax.put({
    url: 'report/score/stuOtherScore',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      studentId: params.studentId,
      otherScore: params.otherScore,
    },
    requestName: '修改考生其他分',
  })
}

export function apiChangeStudentWrittenScore(params) {
  return ajax.put({
    url: 'report/score/stuWrittenScore',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      studentId: params.studentId,
      writtenScore: params.writtenScore,
    },
    requestName: '修改考生笔试分',
  })
}

export function apiModifyStudentFeedbackScore(requestParams) {
  return ajax.put({
    url: 'report/score/updateStuScore',
    data: {
      examSubjectId: requestParams.examSubjectId,
      studentId: requestParams.studentId,
      objectives: requestParams.objectives, // [{ answer: [string], code: [number], score: [number] }...]
      subjectives: requestParams.subjectives, // [{ blockId: [string], score: [number], subScore: [string] }...]
    },
    requestName: '修改错题反馈学生成绩',
  })
}

export function apiDownloadClassesRankIntervalExcel(requestParams) {
  return ajax.download({
    url: 'report/score/exportClsRankInterval',
    params: {
      templateId: requestParams.templateId,
    },
    data: requestParams.ranges || [],
    requestName: '下载班级名次段统计报表',
  })
}

export function apiDownloadClassesScoreIntervalExcel(requestParams) {
  return ajax.download({
    url: 'report/score/exportClsScoreInterval',
    params: {
      templateId: requestParams.templateId,
    },
    data: requestParams.ranges || [],
    requestName: '下载班级分数段统计报表',
  })
}

export function apiGetReportComparableExamTemplates(examId) {
  return ajax.get({
    url: 'report/score/compareExams',
    params: {
      examId: examId, // * String
    },
    requestName: '获取项目可对比考试模板列表',
  })
}

export function apiGetReportExamCombinationsOfInstitutions(requestParams) {
  return ajax.get({
    url: 'report/score/categorySubjects',
    params: {
      entityId: requestParams.entityId, // String*  机构Id | 学校Id | 班级Id
      templateId: requestParams.templateId, // String*
    },
    requestName: '获取当前机构的考情概览学生成绩科目组合',
  })
}

export function apiGetIntervalReportExamCombinationsOfInstitutions(requestParams) {
  return ajax.get({
    url: 'report/score/interval/categorySubjects',
    params: {
      templateId: requestParams.templateId,
      orgSchoolId: requestParams.organizationId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
    },
    requestName: '获取当前机构高中分数段名次段报表科目组合',
  })
}

export function apiGetScoreIntervalStudentInfo(requestParams) {
  return ajax.get({
    url: 'report/score/listScoreIntervalStudents/v2',
    params: {
      dataType: requestParams.dataType, // String('eduSchool', 'school', 'class')
      categoryId: requestParams.categoryId, // Number
      examId: requestParams.examId, // String,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      range: requestParams.range
        ? encodeURIComponent(requestParams.range).replace(/\(/g, '%28').replace(/\)/g, '%29')
        : undefined, // String
      schoolId: requestParams.schoolId, // String
      templateId: requestParams.templateId, // String
      subjectCode: requestParams.subjectCode, // String
    },
    requestName: '高中报表获取指定分数段学生信息',
  })
}

export function apiGetRankIntervalStudentInfo(requestParams) {
  return ajax.get({
    url: 'report/score/listRankIntervalStudents/v2',
    params: {
      dataType: requestParams.dataType, // String('eduSchool', 'school', 'class')
      categoryId: requestParams.categoryId, // Number
      examId: requestParams.examId, // String,
      pageNum: requestParams.currentPage || 1,
      pageSize: requestParams.pageSize || 10,
      range: requestParams.range
        ? encodeURI(requestParams.range).replace(/\(/g, '%28').replace(/\)/g, '%29')
        : undefined, // String
      schoolId: requestParams.schoolId, // String
      templateId: requestParams.templateId, // String
      subjectCode: requestParams.subjectCode, // String
    },
    requestName: '高中报表获取指定名次段学生信息',
  })
}

export function apiGetClassStudentScoreDetail(requestParams) {
  return ajax.get({
    url: 'report/score/classStuScoreDetail',
    params: {
      templateId: requestParams.templateId, // *String
      examSubjectId: requestParams.examSubjectId, // *String
      classId: requestParams.classId, // *String
      reportName: requestParams.reportName, // String
    },
    requestName: '查询班级学生成绩',
  })
}

export function apiSearchStudentsByKeyword(requestParams) {
  return ajax.get({
    url: 'report/score/searchStudent',
    params: {
      examSubjectId: requestParams.examSubjectId, // *String
      keyword: requestParams.keyword, // *String
    },
    requestName: '根据准考号或者姓名查考生',
  })
}
