<template>
  <div class="container-step-subjective">
    <PaneImage class="pane" :src="currentPageImage.src">
      <template #header>
        <div class="tip">
          <span>说明：框选对错框打分区域</span>
        </div>
        <div class="actions">
          <Divider type="vertical"></Divider>
          <span class="toggle">
            <span>显示所有对错框</span>
            <i-switch v-model="showAllAreas"></i-switch>
          </span>
        </div>
      </template>
      <MaskRect
        ref="mask"
        :rects="rects"
        :draw="canAdd"
        @add="handleAddRect"
        @remove="confirmDeleteAreaRect"
      ></MaskRect>
    </PaneImage>
    <div class="pane pane-right">
      <Table
        size="small"
        :columns="tableColumns"
        :data="tableData"
        :row-class-name="rowClassName"
        @on-row-click="handleTableRowClick"
      ></Table>
    </div>

    <Modal v-model="showModalAddScoreArea" title="选择打分区域对应填空">
      <Select v-if="newArea" v-model="newArea.areaId">
        <Option v-for="a in noRectAreas" :key="a.id" :value="a.id">{{ a.blankFullName }}</Option>
      </Select>
      <template #footer>
        <Button type="text" @click="handleModalAddScoreAreaCancel">取消</Button>
        <Button type="primary" @click="handleModalAddScoreAreaOK">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import PaneImage from './pane_image'
  import MaskRect from './mask_rect'

  import { mapGetters, mapMutations } from 'vuex'
  import { pixel2MM } from '@/helpers/scan_template/image_processing'
  import ScoreTypeEnum from '@/enum/answer_sheet/score_type'

  export default {
    components: {
      PaneImage,
      MaskRect,
    },
    emits: ['show-demo'],
    data() {
      return {
        // 显示添加打分区域弹窗
        showModalAddScoreArea: false,
        // 新区域
        newArea: null,
        // 编辑的区域
        areaInModalEdit: null,
        // 显示所有区域
        showAllAreas: true,
        // 表格选中区域
        activeAreaId: '',
      }
    },
    computed: {
      ...mapGetters('scanTemplate', [
        'pages',
        'currentPageIndex',
        'currentPage',
        'currentPageImage',
        'subjectSubjectives',
      ]),

      scoreAreaIdInfoMap() {
        let map = new Map()
        this.subjectSubjectives.forEach(q => {
          if (q.scoreType != ScoreTypeEnum.TrueFalse.id) {
            return
          }
          let branchName = q.branchCode == 0 ? q.questionCode : `${q.questionCode}.${q.branchCode}`
          q.scoreAreaIds.forEach((areaId, idx) => {
            let blankCount = q.scoreAreaIds.length
            let blankIndex = idx + 1
            let blankName = blankCount > 1 ? `${branchName}-${blankIndex}` : branchName
            let blankFullName = blankCount > 1 ? `${branchName}题第${blankIndex}空` : `${branchName}题`
            map.set(areaId, {
              branchName,
              blankCount,
              blankIndex,
              blankName,
              blankFullName,
            })
          })
        })
        return map
      },

      // 显示新增弹窗时不能框选区域
      canAdd() {
        return !this.showModalAddScoreArea
      },

      // 所有矩形框
      rects() {
        let list = []
        // 正在添加的区域
        if (this.showModalAddScoreArea && this.newArea && this.newArea.pageIndex == this.currentPageIndex) {
          list.push({
            ...this.newArea.rect,
          })
        }
        // 其他已有区域
        let labelStyle = {
          position: 'absolute',
          left: '-2px',
          top: '-20px',
          padding: '3px',
          lineHeight: '14px',
          color: 'white',
          backgroundColor: 'red',
          fontSize: '14px',
          fontWeight: 'normal',
          whiteSpace: 'nowrap',
        }
        this.currentPage.trueFalseScoreAreas.forEach(area => {
          if (!this.showAllAreas && this.activeAreaId != area.id) {
            return
          }
          if (!area.rect) {
            return
          }

          let info = this.scoreAreaIdInfoMap.get(area.id)
          list.push({
            ...area.rect,
            areaId: area.id,
            info,
            removable: true,
            pageIndex: this.currentPageIndex,
            title: {
              text: info?.blankName || '',
              style: labelStyle,
            },
          })
        })

        return list
      },
      tableColumns() {
        return [
          {
            title: '小题',
            key: 'branchName',
            minWidth: 60,
          },
          {
            title: '填空序号',
            key: 'blankIndex',
            align: 'center',
            minWidth: 60,
            render: (h, { row }) => {
              return h('span', {}, row?.blankCount > 1 ? `第 ${row.blankIndex} 空` : '-')
            },
          },
          {
            title: '区域',
            minWidth: 60,
            align: 'center',
            render: (h, { row }) => {
              let exist = Boolean(row.rect)
              return h(
                TextButton,
                {
                  type: exist ? 'primary' : 'warning',
                  onClick: () => {
                    this.changeCurrentPageIndex(row.pageIndex)
                  },
                },
                () => (exist ? '有' : '无')
              )
            },
          },
          {
            title: '操作',
            minWidth: 60,
            align: 'center',
            render: (h, params) => {
              let btns = []
              if (params.row.rect) {
                btns.push(
                  h(
                    TextButton,
                    {
                      type: 'warning',
                      onClick: () => {
                        this.confirmDeleteAreaRect(params.row)
                      },
                    },
                    () => '删除'
                  )
                )
              }
              return h('div', {}, btns)
            },
          },
        ]
      },
      tableData() {
        let list = []
        this.pages.forEach(page => {
          page.trueFalseScoreAreas.forEach(area => {
            let blankInfo = this.scoreAreaIdInfoMap.get(area.id)
            list.push({
              ...area,
              areaId: area.id,
              pageIndex: page.index,
              pageName: page.name,
              branchName: blankInfo?.branchName || '',
              blankCount: blankInfo?.blankCount || 0,
              blankIndex: blankInfo?.blankIndex || 0,
              blankName: blankInfo?.blankName || '',
              blankFullName: blankInfo?.blankFullName || '',
            })
          })
        })
        return list
      },
      noRectAreas() {
        return this.tableData.filter(x => !x.rect)
      },
    },
    mounted() {
      if (this.rects.length > 0) {
        this.activeAreaId = this.rects[0].areaId
      }
    },
    methods: {
      ...mapMutations('scanTemplate', ['changeCurrentPageIndex', 'changeTrueFalseScoreAreaRect']),
      rowClassName(row) {
        if (row.areaId == this.activeAreaId) {
          return 'row-active'
        }
      },
      handleTableRowClick(row) {
        if (row.areaId) {
          this.activeAreaId = row.areaId
          this.changeCurrentPageIndex(row.pageIndex)
        }
      },

      /**
       * 添加
       */
      handleAddRect(rect) {
        if (!this.canAdd) {
          return
        }
        let widthMM = pixel2MM(rect.width)
        let heightMM = pixel2MM(rect.height)
        if (widthMM < 5 || heightMM < 3) {
          return
        }
        this.newArea = {
          areaId: '',
          rect,
          pageIndex: this.currentPageIndex,
        }
        this.showModalAddScoreArea = true
      },
      handleModalAddScoreAreaCancel() {
        this.newArea = null
        this.showModalAddScoreArea = false
      },
      handleModalAddScoreAreaOK() {
        if (!this.newArea?.areaId) {
          return
        }
        this.changeTrueFalseScoreAreaRect({
          pageIndex: this.newArea.pageIndex,
          area: {
            id: this.newArea.areaId,
            rect: this.newArea.rect,
          },
        })
        this.activeAreaId = this.newArea.areaId
        this.showModalAddScoreArea = false
        this.newArea = null
      },

      /**
       * 删除
       */
      confirmDeleteAreaRect({ areaId }) {
        let area = this.tableData.find(x => x.areaId == areaId)
        if (!area) {
          return
        }
        this.changeTrueFalseScoreAreaRect({
          pageIndex: area.pageIndex,
          area: {
            id: areaId,
            rect: null,
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tip {
    flex-grow: 1;
  }

  .btn-sub-image {
    margin-left: 10px;
    padding-right: 0;
  }

  .toggle {
    margin-right: 10px;
    line-height: 22px;
  }

  .pane-right {
    width: 360px;
    padding: 10px;

    .warning {
      height: 20px;
      margin-bottom: 10px;
      overflow: hidden;
      color: $color-warning;
      line-height: 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    :deep(.row-active td) {
      background-color: $color-iview-table-active-row;
    }
  }
</style>
