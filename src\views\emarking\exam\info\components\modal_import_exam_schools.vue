<script>
  import ComponentUploader from '@/components/upload.vue'

  import { apiImportExamSchools } from '@/api/emarking'

  export default {
    components: {
      'component-uploader': ComponentUploader,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      examId: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue', 'update-school-list'],

    data() {
      return {
        importFile: null,
        importResult: null,
        uploadingFile: false,
      }
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChanged(visibility) {
        if (visibility) {
          this.importFile = null
          this.importResult = null
          this.uploadingFile = false
        } else {
          this.closeModal()
        }
      },

      handleModalBtnConfirmClick() {
        if (!this.importFile) {
          this.$Message.warning({
            duration: 4,
            content: '未选择文件',
          })
          return
        }
        if (!this.examId) {
          this.$Message.warning({
            duration: 4,
            content: '参数加载失败，请尝试重新打开弹窗',
          })
          return
        }

        this.uploadingFile = true
        apiImportExamSchools({
          examId: this.examId,
          file: this.importFile,
        })
          .then(successMessage => {
            this.importResult = {
              success: true,
              detail: successMessage || '导入成功',
            }
          })
          .catch(err => {
            if (err.code === -1 && err.msg === '网络连接超时') {
              this.importResult = {
                success: false,
                detail: err.msg + ' 或 文件在被选择后非法修改，请重新选择文件后进行导入操作',
              }
            } else {
              this.importResult = {
                success: false,
                detail: err.msg,
              }
            }
          })
          .finally(() => {
            this.uploadingFile = false
            this.$emit('update-school-list')
          })
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    class="modal-import-exam-schools"
    width="900"
    title="批量导入学校"
    :closable="!uploadingFile"
    @on-visible-change="handleModalVisibleChanged"
  >
    <div>
      <div class="section-tips">
        <Alert type="info" class="cpn-alert">
          <ul class="tips">
            <li>Excel中需包含以下列：学校名称、考试编码</li>
          </ul>
        </Alert>
      </div>

      <component-uploader
        :accepts="['.xls', '.xlsx']"
        :file-name="importFile && importFile.name"
        @on-selected="importFile = $event"
      ></component-uploader>

      <div v-if="!uploadingFile && importResult" class="section-result">
        <div class="status">
          <div v-if="importResult.success" class="status-success">
            <Icon type="md-checkmark-circle"></Icon>
          </div>
          <div v-else class="status-fail">
            <Icon type="md-close-circle"></Icon>
          </div>
        </div>
        <div class="detail" v-html="importResult && importResult.detail"></div>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" :disabled="!importFile" :loading="uploadingFile" @click="handleModalBtnConfirmClick"
        >导入</Button
      >
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-import-exam-schools {
    .section-tips {
      margin-bottom: 10px;

      .tips {
        padding-left: 10px;
        line-height: 1.5;
      }
    }

    .section-result {
      margin-top: 18px;
      margin-bottom: 10px;
      border-top: 1px solid $color-border;

      .status {
        font-size: 40px;
        text-align: center;

        .status-success {
          color: $color-success;
        }

        .status-fail {
          color: $color-error;
        }
      }

      .detail {
        max-height: 160px;
        overflow-y: auto;
        white-space: pre;
        text-align: center;
      }
    }
  }
</style>
