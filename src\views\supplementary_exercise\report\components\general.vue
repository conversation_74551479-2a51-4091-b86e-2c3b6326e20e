<template>
  <div class="container-coach-report-general">
    <Indicators :class-detail="classDetail" class="main-indicators"></Indicators>
    <Grades :class-detail="classDetail" class="card card-grades"></Grades>
    <Groups :class-detail="classDetail" class="card card-groups"></Groups>
    <Advance :class-detail="classDetail" class="card card-advance"></Advance>
    <Topics :class-detail="classDetail" class="card card-topics"></Topics>
    <Questions :class-detail="classDetail" class="card card-questions" @to-question="toQuestion"></Questions>
  </div>
</template>

<script>
  import Indicators from './general_indicators.vue'
  import Grades from './general_grades.vue'
  import Groups from './general_groups.vue'
  import Advance from './general_advance.vue'
  import Topics from './general_topics.vue'
  import Questions from './general_questions.vue'

  export default {
    components: {
      Indicators,
      Grades,
      Groups,
      Advance,
      Topics,
      Questions,
    },
    props: {
      classDetail: Object,
      examInfo: Object,
    },
    emits: ['to-question'],
    methods: {
      toQuestion(q) {
        this.$emit('to-question', q)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .card {
    margin-top: 24px;
    padding: 24px;
    border-radius: 6px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);

    :deep(.card-title) {
      margin-bottom: 12px;
      font-size: $font-size-medium-x;
      line-height: 1;
    }

    :deep(.card-content) {
      @include flex(row, space-between, flex-start);

      .card-left,
      .card-right {
        flex-grow: 0;
        width: calc(50% - 12px);
      }
    }
  }
</style>
