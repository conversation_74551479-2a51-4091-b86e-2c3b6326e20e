<template>
  <div class="container-upload" :class="{ small: small }">
    <Button class="btn-select" :size="small ? 'small' : 'default'" type="primary" @click="selectFile">{{
      btnText
    }}</Button>
    <input ref="inputFile" class="input-file-self" type="file" :accept="accepts.join(',')" @change="checkFile" />
    <span v-if="showFileName" class="upload-file-name" :title="fileName">
      <span>{{ fileName }}</span>
      <Icon v-if="fileName" class="icon-clear" type="md-close-circle" @click="clear"></Icon>
    </span>
    <Button
      v-else
      :disabled="!fileName"
      class="btn-clear"
      :size="small ? 'small' : 'default'"
      type="warning"
      @click="clear"
      >清空</Button
    >
  </div>
</template>

<script>
  export default {
    props: {
      accepts: {
        type: Array,
        required: true,
      },
      fileName: {
        type: String,
        default: '',
      },
      maxSize: {
        type: Number,
        default: 10,
      },
      multiple: {
        type: Boolean,
        default: false,
      },
      small: <PERSON>olean,
      showFileName: {
        type: Boolean,
        default: true,
      },
      btnText: {
        type: String,
        default: '选择文件',
      },
    },

    emits: ['on-selected'],

    data() {
      return {}
    },

    methods: {
      selectFile() {
        this.$refs.inputFile.click()
      },

      checkFile() {
        let files = this.$refs.inputFile.files
        if (!this.multiple) {
          files = [files[0]]
        }

        for (let file of files) {
          if (!this.checkFileType(file)) {
            this.$Message.error({
              content: `请选择以下格式的文件：${this.accepts.join('，')}`,
              duration: 5,
            })
            return
          }

          if (!this.checkFileSize(file)) {
            this.$Message.error({
              content: `请选择不大于${
                this.maxSize >= 1 ? '' + this.maxSize + 'MB' : '' + this.maxSize * 1000 + 'KB'
              }的文件`,
              duration: 5,
            })
            return
          }
        }
        this.$refs.inputFile.value = null

        if (this.multiple) {
          this.$emit('on-selected', files)
        } else {
          this.$emit('on-selected', files[0])
        }
      },

      checkFileType(file) {
        let dotIndex = file.name.lastIndexOf('.')
        if (dotIndex <= 0) {
          return false
        }

        let extension = file.name.substring(dotIndex)
        return this.accepts.some(accept => accept.toLowerCase() == extension.toLowerCase())
      },

      checkFileSize(file) {
        return this.maxSize * 1024 * 1024 >= file.size
      },

      clear() {
        if (this.multiple) {
          this.$emit('on-selected', [])
        } else {
          this.$emit('on-selected', null)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-upload {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }

  .input-file-self {
    display: none;
  }

  .upload-file-name {
    position: relative;
    flex-grow: 1;
    height: 32px;
    margin-left: 10px;
    padding: 4px 7px;
    border: 1px solid $color-border;
    border-radius: 4px;
    overflow: hidden;
    line-height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;

    .icon-clear {
      position: absolute;
      top: 8px;
      right: 8px;
      cursor: pointer;
    }
  }

  .container-upload.small {
    .upload-file-name {
      height: 24px;
      margin-left: 10px;
      padding: 0 7px;
      border: 1px solid $color-border;
      border-radius: 4px;
      overflow: hidden;
      line-height: 22px;
    }

    .icon-clear {
      position: absolute;
      top: 4px;
      right: 8px;
      cursor: pointer;
    }
  }

  .btn-clear {
    margin-left: 0.6em;
  }
</style>
