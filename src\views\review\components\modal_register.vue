<script>
  import Uploader from './uploader.vue'

  import {
    apiCreateRegistrationRecord,
    apiEditRegistrationRecord,
    apiGetStudentRegistrationDetail,
    apiGetTeacherRegistrationDetail,
  } from '@/api/review'
  import { apiGetStudentList, apiGetTeacherList, apiGetSearchStudents, apiGetTeachers } from '@/api/user'

  import { mapGetters } from 'vuex'
  import { formatDateTime } from '@/utils/date'
  import { deepCopy } from '@/utils/object'

  export default {
    components: {
      'component-uploader': Uploader,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      activeCategoryId: {
        type: String,
        default: 'all',
      },
      isCreate: {
        type: Boolean,
        default: false,
      },
      user: {
        type: Object,
        default: () => null,
      },
    },

    emits: ['update:modelValue', 'refresh'],

    data() {
      return {
        staticFormValidate: {
          selectedCategoryId: '',
          userId: '', // 参赛者
          selectedSchoolId: '',
        },
        staticRuleValidate: {
          selectedCategoryId: [{ required: true, message: '请选择评审类别', trigger: 'change' }],
          userId: [{ required: true, message: '请选择参赛者', trigger: 'change' }],
          selectedSchoolId: [{ required: true, message: '请选择参赛学校', trigger: 'change' }],
        },
        dynamicFormValidate: {},
        dynamicRuleValidate: {},

        userSelectorIsLoading: false,
        userSelectorOptions: [],

        uploading: false,

        userDetail: null,

        closeModalAfterRequestSuccess: true,
      }
    },

    computed: {
      ...mapGetters('review', ['categories', 'activityId', 'schoolList', 'participantIdentity']),

      modalTitle() {
        return this.isCreate ? '新增报名' : '编辑报名信息'
      },

      currentUserInfo() {
        return this.$store.getters['user/info']
      },

      currentUserSchoolId() {
        return this.currentUserInfo?.schoolId || ''
      },

      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },

      categoryListForRadioGroup() {
        return this.categories.filter(c => c.id !== 'all')
      },

      selectedCategoryFields() {
        const Fileds = deepCopy(
          (
            (this.staticFormValidate.selectedCategoryId &&
              this.categoryListForRadioGroup.find(c => c.id === this.staticFormValidate.selectedCategoryId)) || {
              fields: [],
            }
          ).fields
        ).map(f => {
          f.extraObject = f.extraJson ? JSON.parse(f.extraJson) : {}
          if (f.fieldType === 'upload') {
            const NewAccepts = []
            if (f.extraObject.accepts.some(a => a.includes('image'))) {
              NewAccepts.push('.jpeg', '.jpg', '.png', '.gif')
            }
            if (f.extraObject.accepts.some(a => a.includes('pdf'))) {
              NewAccepts.push('.pdf')
            }
            f.extraObject.accepts = NewAccepts
            if (f.extraObject.maxFileSize) {
              f.extraObject.maxSize = f.extraObject.maxFileSize / 1024 / 1024
            }
          } else if (f.subType === 'mobile') {
            f.extraObject.maxLength = 11
          } else if (f.subType === 'idcard') {
            f.extraObject.maxLength = 18
          }

          return f
        })
        Fileds.sort((a, b) => a.sortOrder - b.sortOrder)

        return Fileds
      },

      isStudentPaticipant() {
        return this.participantIdentity === 'student'
      },

      userSelectorPlaceholder() {
        return this.isStudentPaticipant ? '学生姓名 / 学号' : '教师姓名 / 手机号码'
      },
    },

    watch: {
      selectedCategoryFields() {
        this.setDynamicValidate()
      },
    },

    methods: {
      closeModal() {
        this.resetStaticForm()
        this.resetDynamicForm()
        this.userSelectorOptions = []
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          if (this.isCreate) {
            this.closeModalAfterRequestSuccess = false
            this.initStaticFormValidateSelectedIdWhenIsCreateMode()
          } else {
            this.closeModalAfterRequestSuccess = true
            this.staticRuleValidate = {}
            this.staticFormValidate.selectedCategoryId = this?.user?.categoryId || ''
            this.staticFormValidate.selectedSchoolId = this?.user?.schoolId || ''
            this.fetchUserDetail()
          }
        } else {
          this.closeModal()
        }
      },
      setDynamicValidate() {
        const FormValidate = {}
        const RuleValidate = {}

        const UserForm = this.isCreate ? [] : this?.user?.form
        const ValidateAttributes = ['minLength', 'maxLength', 'accepts']
        this.selectedCategoryFields.forEach(f => {
          // part form
          const TargetUserFormItem = UserForm.find(uf => uf.fieldId === f.id)
          if (['text', 'textarea', 'radio'].includes(f.fieldType)) {
            FormValidate['d' + f.id] = TargetUserFormItem?.fieldValue || ''
          } else if (['checkbox', 'upload'].includes(f.fieldType)) {
            FormValidate['d' + f.id] = TargetUserFormItem?.fieldValue ? JSON.parse(TargetUserFormItem?.fieldValue) : []
          } else if (f.fieldType === 'date') {
            FormValidate['d' + f.id] = TargetUserFormItem?.fieldValue ? new Date(TargetUserFormItem.fieldValue) : null
          } else {
            FormValidate['d' + f.id] = null
          }

          // part rule
          const FieldExtraObject = f.extraObject || {}
          const FiltedFieldExtraObjectKeys = Object.keys(FieldExtraObject).filter(key =>
            ValidateAttributes.includes(key)
          )
          if (f.isRequired || FiltedFieldExtraObjectKeys.length) {
            const FieldTempRuleValidate = []
            if (f.isRequired) {
              let tipWord = ''
              if (['text', 'textarea'].includes(f.fieldType)) {
                tipWord = '请填写' + f.fieldLabel
              } else if (['radio', 'checkbox', 'date'].includes(f.fieldType)) {
                tipWord = '请选择' + f.fieldLabel
              } else if (f.fieldType === 'upload') {
                tipWord = '请上传' + f.fieldLabel + '的内容'
              }
              FieldTempRuleValidate.push({
                required: true,
                message: tipWord,
              })
            }
            RuleValidate['d' + f.id] = FieldTempRuleValidate
          }
        })

        this.dynamicFormValidate = FormValidate
        this.dynamicRuleValidate = RuleValidate
      },
      initStaticFormValidateSelectedIdWhenIsCreateMode() {
        this.staticFormValidate.selectedCategoryId =
          this.activeCategoryId === 'all'
            ? (this.categoryListForRadioGroup &&
                this.categoryListForRadioGroup[0] &&
                this.categoryListForRadioGroup[0].id) ||
              ''
            : this.activeCategoryId
        this.staticFormValidate.selectedSchoolId = this.isBureauInstitution ? '' : this.currentUserSchoolId
      },

      resetStaticForm() {
        this.staticFormValidate = {
          selectedCategoryId: '',
          userId: '', // 参赛者
          selectedSchoolId: '',
        }
        this.staticRuleValidate = {
          selectedCategoryId: [{ required: true, message: '请选择评审类别', trigger: 'change' }],
          userId: [{ required: true, message: '请选择参赛者', trigger: 'change' }],
          selectedSchoolId: [{ required: true, message: '请选择参赛学校', trigger: 'change' }],
        }
      },
      resetDynamicForm() {
        this.dynamicFormValidate = {}
        this.dynamicRuleValidate = {}
      },
      resetCategoryId() {
        this.staticFormValidate.selectedCategoryId = ''
      },
      resetSchoolId() {
        this.staticFormValidate.schoolId = ''
        if (this.staticFormValidate.userId) {
          this.resetUserId()
        }
      },
      resetUserId() {
        this.staticFormValidate.userId = ''
        this.userSelectorOptions = []
        this.resetDynamicForm()
        this.setDynamicValidate()
      },

      handleSelectedSchoolIdChanged() {
        if (this.staticFormValidate.userId) {
          this.resetUserId()
        }
      },
      handleUserIdChanged() {
        this.resetDynamicForm()
        this.setDynamicValidate()
      },
      handleFileSelected(files, id) {
        const TargetFormItem = this.dynamicFormValidate['d' + id]
        if (TargetFormItem) {
          if (
            TargetFormItem.some(item =>
              Array.from(files)
                .map(f => f.name)
                .includes(item.name || item.fileName)
            )
          ) {
            this.$Message.warning({
              duration: '4',
              content: '已存在相同文件名的文件',
            })
            return
          }
          TargetFormItem.push(...files)
        }
      },
      formItemUploadedFiles(id) {
        const TargetFormItem = this.dynamicFormValidate['d' + id]
        return (TargetFormItem || []).map(f => ({
          name: f.name || f.fileName,
          type: f.type || f.fileType,
          size: f.size || f.fileSize,
          path: f.filePath || '',
          file: f instanceof File ? f : null,
          isUploaded: Boolean(f.filePath),
        }))
      },
      handleFormItemDeleteFile(fileName, id) {
        const TargetFormItem = this.dynamicFormValidate['d' + id]
        if (TargetFormItem) {
          this.dynamicFormValidate['d' + id] = TargetFormItem.filter(f => f.name !== fileName && f.fileName != fileName)
        }
      },
      handleFormItemFileRename(id, fileChangeObject) {
        if (id && fileChangeObject && fileChangeObject.oldName && fileChangeObject.newName) {
          const TargetFormItem = this.dynamicFormValidate['d' + id]
          const TargetFileItem =
            TargetFormItem && TargetFormItem.find(item => item.fileName === fileChangeObject.oldName)
          if (TargetFileItem) {
            TargetFileItem.fileName = fileChangeObject.newName
          }
        }
      },
      handleFormItemFilesReset(id) {
        this.dynamicFormValidate['d' + id] = []
      },

      fetchUserDetail() {
        if (!(this.user && this.user.categoryId && this.user.schoolId && this.user.userId)) {
          this.$Message.warning({
            duration: 4,
            content: '请求所需数据不全，请刷新重试',
          })
          this.staticFormValidate.userId = this.user.userId
          return
        }

        this.fetchUsers(this.user.realName).finally(() => {
          this.staticFormValidate.userId = this.user.userId
          this.staticRuleValidate = {
            selectedCategoryId: [{ required: true, message: '请选择评审类别', trigger: 'change' }],
            userId: [{ required: true, message: '请选择参赛者', trigger: 'change' }],
            selectedSchoolId: [{ required: true, message: '请选择参赛学校', trigger: 'change' }],
          }
        })

        const Request = this.isStudentPaticipant ? apiGetStudentRegistrationDetail : apiGetTeacherRegistrationDetail
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.user.categoryId,
          schoolId: this.user.schoolId,
          userId: this.user.userId,
        }
        Request(RequestParams).then(response => (this.userDetail = response || null))
      },
      fetchUsers(query) {
        if (this.userSelectorIsLoading || this.userSelectorOptions.some(u => [u.id, u.name].includes(query))) {
          return Promise.resolve()
        }

        if (query && query.trim() === '') {
          this.userSelectorOptions = []
          return Promise.resolve()
        } else {
          const RequestParams = {
            currentPage: 1,
            pageSize: 10,
            current: 1,
            size: 10,
            schoolId: this.staticFormValidate.selectedSchoolId,
            keyword: query,
          }
          if (this.isBureauInstitution) {
            if (!RequestParams.schoolId) {
              this.userSelectorOptions = []
              return Promise.resolve()
            }
            if (this.isStudentPaticipant) {
              this.userSelectorIsLoading = true
              return apiGetStudentList(RequestParams)
                .then(response => {
                  this.userSelectorOptions = (response?.list || []).map(u => {
                    const TargetClass = (u.classes || []).find(cls => !cls.classType)
                    return {
                      id: u.studentId,
                      name: `${u.studentName} ( ${TargetClass ? TargetClass.className + '-' : ''}${u.studentNo} )`,
                    }
                  })
                })
                .finally(() => (this.userSelectorIsLoading = false))
            } else {
              this.userSelectorIsLoading = true
              return apiGetTeacherList(RequestParams)
                .then(response => {
                  this.userSelectorOptions = (response?.teachers || []).map(u => ({
                    id: u.userId,
                    name: `${u.realName} ( ${u.mobile || '-'} )`,
                  }))
                })
                .finally(() => (this.userSelectorIsLoading = false))
            }
          } else {
            if (this.isStudentPaticipant) {
              this.userSelectorIsLoading = true
              return apiGetSearchStudents(RequestParams)
                .then(
                  response =>
                    (this.userSelectorOptions = (response?.records || []).map(u => ({
                      id: u.studentId,
                      name: `${u.realName} ( ${u.className}-${u.studentNo} )`,
                    })))
                )
                .finally(() => (this.userSelectorIsLoading = false))
            } else {
              this.userSelectorIsLoading = true
              return apiGetTeachers(RequestParams)
                .then(
                  response =>
                    (this.userSelectorOptions = (response?.teachers || []).map(u => ({
                      id: u.userId,
                      name: `${u.realName} ( ${u.mobile || '-'} )`,
                    })))
                )
                .finally(() => (this.userSelectorIsLoading = false))
            }
          }
        }
      },

      async onRegister(submitAudit = false) {
        let validateOK = false
        const FormStatic = this.$refs.staticItemForm
        const FormDynamic = this.$refs.dynamicItemForm
        if (FormStatic && FormDynamic) {
          validateOK = (await FormStatic.validate()) && (await FormDynamic.validate())
        }

        if (validateOK) {
          const CreateRequestAPI = this.isCreate ? apiCreateRegistrationRecord : apiEditRegistrationRecord
          const RequestParams = {
            activityId: this.activityId,
            categoryId: this.staticFormValidate.selectedCategoryId,
            schoolId: this.staticFormValidate.selectedSchoolId,
            userId: this.staticFormValidate.userId,
            requestData: {},
            submit: submitAudit,
          }

          let fileIndex = 1
          const FormObjectList = []
          Object.keys(this.dynamicFormValidate).forEach(key => {
            const TargetField = this.selectedCategoryFields.find(item => item.id === key.slice(1, key.length))

            let value
            if (
              Array.isArray(this.dynamicFormValidate[key]) &&
              this.dynamicFormValidate[key].some(item => typeof item === 'object')
            ) {
              // TODO 相同文件如何才能只传一个？
              const Files = []
              this.dynamicFormValidate[key].forEach(item => {
                if (item.name) {
                  let formDataKey = 'file' + fileIndex
                  Files.push({
                    fileName: item.name,
                    formDataKey: formDataKey,
                  })
                  RequestParams.requestData[formDataKey] = item
                  fileIndex++
                } else {
                  Files.push({
                    fileName: item.fileName,
                    filePath: item.filePath,
                  })
                }
              })
              value = JSON.stringify(Files)
            } else if (this.dynamicFormValidate[key] instanceof Date) {
              value = formatDateTime(
                this.dynamicFormValidate[key],
                (TargetField?.extraObject?.format || '')
                  .replace(/[ydh]/g, match => match.toUpperCase())
                  .replace(/S/g, 's')
              )
            } else {
              value = this.dynamicFormValidate[key]
            }

            FormObjectList.push({
              label: TargetField.fieldLabel,
              value: value,
            })
          })
          RequestParams.requestData.formJson = JSON.stringify(FormObjectList)

          this.uploading = true
          CreateRequestAPI(RequestParams)
            .then(() => {
              this.$Message.success({
                duration: 3,
                content: `${this.isCreate ? '新增报名' : '修改报名信息'}成功`,
              })

              this.uploading = false
              this.$emit('refresh')

              if (this.closeModalAfterRequestSuccess) {
                this.closeModal()
              } else {
                this.resetStaticForm()
                this.resetDynamicForm()
                this.initStaticFormValidateSelectedIdWhenIsCreateMode()
              }
            })
            .catch(() => {
              this.uploading = false
              this.$emit('refresh')
            })
        }
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    :closable="!uploading"
    width="1000"
    @on-visible-change="handleModalVisibleChange"
  >
    <div class="modal-panel">
      <Form
        ref="staticItemForm"
        :label-width="200"
        :model="staticFormValidate"
        :rules="staticRuleValidate"
        label-position="right"
      >
        <FormItem v-if="categoryListForRadioGroup.length > 1" label="评审类别" prop="selectedCategoryId">
          <RadioGroup v-model="staticFormValidate.selectedCategoryId" type="button" button-style="solid">
            <Radio v-for="c of categoryListForRadioGroup" :key="c.id" :label="c.id" :disabled="!isCreate">{{
              c.name
            }}</Radio>
          </RadioGroup>
        </FormItem>

        <FormItem v-if="isBureauInstitution" label="参赛学校" prop="selectedSchoolId">
          <Select
            v-model="staticFormValidate.selectedSchoolId"
            :disabled="!isCreate"
            filterable
            clearable
            transfer
            style="width: 316px"
            @on-change="handleSelectedSchoolIdChanged"
          >
            <Option v-for="s of schoolList" :key="s.id" :value="s.id">{{ s.name }}</Option>
          </Select>
        </FormItem>

        <FormItem label="参赛者" prop="userId">
          <Select
            v-model="staticFormValidate.userId"
            :disabled="!isCreate"
            :remote-method="fetchUsers"
            :loading="userSelectorIsLoading"
            :placeholder="userSelectorPlaceholder"
            filterable
            clearable
            transfer
            style="width: 316px"
            @on-change="handleUserIdChanged"
          >
            <Option v-for="u of userSelectorOptions" :key="u.id" :value="u.id">{{ u.name }}</Option>
          </Select>
        </FormItem>
      </Form>

      <Form
        v-if="selectedCategoryFields.length"
        ref="dynamicItemForm"
        :label-width="200"
        :model="dynamicFormValidate"
        :rules="dynamicRuleValidate"
        label-position="right"
        style="margin-top: 24px"
      >
        <FormItem v-for="f of selectedCategoryFields" :key="f.id" :label="f.fieldLabel" :prop="'d' + f.id">
          <Input
            v-if="['text', 'textarea'].includes(f.fieldType)"
            v-model="dynamicFormValidate['d' + f.id]"
            :type="f.fieldType === 'textarea' ? 'textarea' : undefined"
            :placeholder="f.extraObject.placeholder"
            :maxlength="f.extraObject.maxLength"
            clearable
            style="width: 600px"
          ></Input>
          <RadioGroup
            v-else-if="f.fieldType === 'radio'"
            v-model="dynamicFormValidate['d' + f.id]"
            type="button"
            button-style="solid"
          >
            <Radio v-for="r of f.extraObject.options" :key="'radio' + r" :label="r">{{ r }}</Radio>
          </RadioGroup>
          <CheckboxGroup v-else-if="f.fieldType === 'checkbox'" v-model="dynamicFormValidate['d' + f.id]">
            <Checkbox v-for="c of f.extraObject.options" :key="'checkbox' + c" :label="c">{{ c }}</Checkbox>
          </CheckboxGroup>
          <DatePicker
            v-else-if="f.fieldType === 'date'"
            v-model="dynamicFormValidate['d' + f.id]"
            :placeholder="f.extraObject.placeholder"
            type="date"
          ></DatePicker>
          <component-uploader
            v-else-if="f.fieldType === 'upload'"
            :accepts="f.extraObject.accepts"
            :uploaded-files="formItemUploadedFiles(f.id)"
            :max-file-count="f.extraObject.maxFileCount"
            :max-size="f.extraObject.maxSize"
            tag-max-width="580px"
            style="width: 600px"
            @on-selected="handleFileSelected($event, f.id)"
            @delete-file="handleFormItemDeleteFile($event, f.id)"
            @reset-files="handleFormItemFilesReset(f.id)"
            @rename-file="handleFormItemFileRename(f.id, $event)"
          ></component-uploader>
        </FormItem>
      </Form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <Checkbox v-if="isCreate" v-model="closeModalAfterRequestSuccess">操作成功后关闭弹窗</Checkbox>
        <span v-else></span>
        <div>
          <Button type="text" :disabled="uploading" @click="closeModal">取消</Button>
          <Button type="primary" :loading="uploading" @click="onRegister(false)">保存</Button>
          <Button type="primary" :loading="uploading" @click="onRegister(true)">提交报名</Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-footer {
    @include flex(row, space-between, center);
    user-select: none;
  }
</style>
