<script>
  import ModalLeader from '../components/modal_leader.vue'
  import ModalBlockAnswer from '../components/modal_block_answer.vue'
  import ModalNormalBlockDetail from './components/modal_normal_block_detail.vue'
  import AffixAnswer from '../components/affix_answer.vue'

  import {
    apiGetExamSubjectInfo,
    apiGetSubjectMarkTasks,
    apiGetMyBlockLeaders,
    apiGetSubjectiveBlockSolutions,
    apiGetSubjectiveDefine,
    apiSubmitMarkBatchStudentBlocks,
  } from '@/api/emarking'
  import { apiGetBlockAIAssistMarkBatch } from '@/api/emarking/ai'

  import fscreen from 'fscreen'
  import { getBlockQuestionsDefineAndKeys } from '@/helpers/emarking/marking'
  import { sleep } from '@/utils/promise'
  import { scrollToTop } from '@/utils/scroll'
  import { deepCopy } from '@/utils/object'
  import { replaceBlockImageUrlPixelTypeKeyword } from '@/helpers/emarking/block_image'

  import MarkModeEnum from '@/enum/emarking/mark_mode'
  import AIMarkingScoreTypeEnum from '@/enum/emarking/ai_marking/ai_marking_score_type'
  import MarkingWayEnum from '@/enum/emarking/marking_way'
  // import AssignModeEnum from '@/enum/emarking/assign_mode'

  const MinSubmitPeriod = 300 // 最小提交时长，限制快速提交
  const MinUpdateBlockInfoInterval = 1000 * 60 // 更新题块定义的最小时间间隔：1分钟
  const PaperType_Normal = 0
  const PaperType_Abnormal = 1
  const SubmitInconsistentErrorCode = 4007
  const LoadMarkPaperErrorCode = {
    Prepare: 4000,
    Finish_All: 4001,
    Finish_Self: 4002,
    Finish_Wait_Other: 4003,
    NoTask: 4004,
    Suspended: 4005,
    NoPaper: 4006,
    Subject_Suspended: 4020,
    Subject_Finish: 4021,
  }

  export default {
    components: {
      'modal-leader': ModalLeader,
      'modal-block-answer': ModalBlockAnswer,
      'modal-normal-block-detail': ModalNormalBlockDetail,
      'affix-answer': AffixAnswer,
    },

    data() {
      return {
        isRehearsal: false,

        examSubjectInfo: {
          examId: '',
          examName: '',
          subjectName: '',
          examSubjectId: '',
          enableIncreaseMarkTaskNum: false,
          blocks: [],
        },

        blockInfo: {
          blockId: '',
          blockName: '',
          assignMode: {},
          markingWay: {},
          strictMark: false,
          leaders: [],
          questions: [],
          // batchScoreSize: 5,
          imgPixelType: '',
        },

        selectedAIScore: 0,

        setting: {
          customBatchSize: 5,
        },

        papers: [],

        // AI
        aiConfirmType: 'comfirmed', // String: 'comfirmed', 'unconfirmed'
        batchPlacementMode: 'row', // String: 'row'、'column'

        isModalLeadersShow: false,

        // 状态控制
        status: 0, // 当前状态：0，初始化；1，切换题块；2，获取试卷；3，加载图片；4，等待打分；5，提交分数
        markingType: 0, // 0-正评;1-回评
        loadMarkPaperErrorCode: 0, // 加载试卷失败时具体情况
        loadMarkPaperErrorMessage: '', // 加载试卷失败消息
        lastUpdateBlockInfoTimeStamp: 0, // 上一次更新评卷定义的时间

        // 题块答案
        blockAnswerImg: '',
        blockQuestions: [],
        blockAnswerModalShowed: false,
        isBlockAnswerLoading: false,
        affixImgShowed: false,

        // 显示控制
        rowGridItemCount: 3,
        maxRowGridItemCount: 3,
        isModalNormalBlockDetailShow: false,
        modalDetailPaper: null,
        columnImageWidthValue: 500,
        columnImagePanelWidth: Math.max(window.innerWidth, 1300) - 3 * 30 - 300,
        isColumnsImageWidthValueNeedInit: true,
      }
    },

    computed: {
      hasBlock() {
        return Boolean(this.blockInfo?.blockId)
      },
      hasPapers() {
        return Boolean(this.papers?.length)
      },
      isMarkingByClass() {
        return this.blockInfo?.markingWay?.id === MarkingWayEnum.Class.id
      },
      isBtnBackToMarkShow() {
        return this.hasPapers && this.markingType === 1
      },
      isBtnRefreshShow() {
        return (
          !this.hasPapers &&
          Object.keys(LoadMarkPaperErrorCode).some(key => LoadMarkPaperErrorCode[key] === this.loadMarkPaperErrorCode)
        )
      },
      isIgradeShowcaseSchool() {
        return this.$store.getters['user/isIgradeShowcaseSchool']
      },
      blockAIScores() {
        return this.blockInfo?.aiScoreGroupStat || []
      },
    },

    created() {
      this.examSubjectInfo.examSubjectId = this.$route.params?.examSubjectId || ''
      this.selectedAIScore = this.$route.params?.aiAssistQuestionsScore || 0

      if (this.examSubjectInfo.examSubjectId) {
        this.fetchExamSubjectInfo()
          .then(() => this.fetchTaskBlocks())
          .then(() => this.setCurrentBlockInfo(this.$route.params?.blockId))
      } else {
        this.$Message.warning({
          duration: 3,
          content: '页面参数错误：无科目信息',
        })
      }
    },

    beforeUnmount() {
      if (fscreen.fullscreenElement) {
        fscreen.exitFullscreen()
      }
    },

    methods: {
      /* fetch */
      fetchExamSubjectInfo() {
        return apiGetExamSubjectInfo(this.examSubjectInfo.examSubjectId).then(response => {
          this.examSubjectInfo.examId = response.examId || ''
          this.examSubjectInfo.examName = response.examName || ''
          this.examSubjectInfo.subjectName = response.subjectName || ''
          this.examSubjectInfo.enableIncreaseMarkTaskNum = response.enableIncreaseMarkTaskNum || false
        })
      },
      fetchTaskBlocks() {
        return apiGetSubjectMarkTasks(this.examSubjectInfo.examSubjectId).then(
          response =>
            (this.examSubjectInfo.blocks = (response || [])
              /* .filter(
              b =>
                b.markMode?.id &&
                b.markMode.id === MarkModeEnum.Single.id &&
                b.aiScoreType &&
                b.aiScoreType === AIMarkingScoreTypeEnum.AIAssist.id
            )
            */
              .map(b => {
                const Block = deepCopy(b)
                Block.isAIAssistMarkingMode =
                  b.markMode.id &&
                  b.markMode.id === MarkModeEnum.Single.id &&
                  b.aiScoreType &&
                  b.aiScoreType === AIMarkingScoreTypeEnum.AIAssist.id
                return Block
              }))
        )
      },
      fetchStudentPapers() {
        this.$TransparentSpin.show()
        return apiGetBlockAIAssistMarkBatch({
          subjectiveId: this.blockInfo.blockId,
          score: this.selectedAIScore,
          batchSize: this.setting.customBatchSize,
        })
          .then(async response => {
            this.papers = await Promise.all(
              (response || []).map(async (paper, pdx) => {
                const PaperSplitQuestions = (paper.aiSubScore || '').split(';').map(q => {
                  const QM = q.split('=')
                  return {
                    code: QM[0] || '-',
                    score: QM[1] ? QM[1] * 1 : 0,
                  }
                })
                const BlockQuestions = deepCopy(this.blockInfo?.questions || [])
                BlockQuestions.forEach(q => {
                  const TargetQuestion = PaperSplitQuestions.find(psq => psq.code == q.questionCode)
                  q.aiSubScore = TargetQuestion?.score || 0
                  q.score = q.aiSubScore
                  q.scoreInterval = q.scoreInterval || q.scoreInterval === 0 ? 1 * q.scoreInterval : 1
                })

                const AIScore = paper.aiScore || BlockQuestions.reduce((acc, cur) => acc + cur.score, 0) || 0

                const ImgUrl =
                  (this.isIgradeShowcaseSchool &&
                    replaceBlockImageUrlPixelTypeKeyword(paper.imgUrl, this.blockInfo?.imgPixelType)) ||
                  paper.imgUrl

                if (!pdx) {
                  const OriginPaperWidth = await this.getImageOriginWidthFromURL(ImgUrl)
                  const MonitorPaperWidth = OriginPaperWidth > 430 ? 430 : OriginPaperWidth
                  this.rowGridItemCount = Math.floor(
                    (Math.max(window.innerWidth, 1300) - 60) / (MonitorPaperWidth + 20)
                  )
                  this.maxRowGridItemCount = this.rowGridItemCount + 1
                  if (this.isColumnsImageWidthValueNeedInit) {
                    this.columnImageWidthValue = Math.max(Math.min(OriginPaperWidth, this.columnImagePanelWidth), 300)
                    this.isColumnsImageWidthValueNeedInit = false
                  }
                }

                let paperAIResultList = []
                if (paper.aiResultJson) {
                  try {
                    paperAIResultList = JSON.parse(paper.aiResultJson || '')
                  } catch {
                    paperAIResultList = []
                  }
                }

                return {
                  No: paper.No || pdx + 1,
                  imgUrl: ImgUrl,
                  questions: BlockQuestions,
                  score: AIScore,
                  aiScore: AIScore,
                  blockId: paper.blockId || this.blockInfo?.blockId,
                  studentBlockId: paper.studentBlockId || paper.subjectiveItemId,
                  secretNum: paper.secretNum,
                  type: paper.type,
                  readTime: null,
                  student: null,
                  image: {
                    loaded: false,
                    naturalWidth: 0,
                    naturalHeight: 0,
                    width: 800,
                    rotate: 0,
                  },
                  aiResultList: paperAIResultList,
                }
              })
            )

            // 更改状态
            this.markingType = 0 // 正评
            this.status = 3 // 加载图片
            this.loadMarkPaperErrorCode = 0
            this.isRehearsal = Boolean(response && response.length && response.some(question => question.isRehearsal))

            // // 设置打分板焦点
            // this.$nextTick().then(this.focusFirstQuestion)

            scrollToTop()
          })
          .catch(err => {
            // 处理特定类型错误
            if (
              err.code !== LoadMarkPaperErrorCode.Prepare &&
              Object.keys(LoadMarkPaperErrorCode).some(key => LoadMarkPaperErrorCode[key] == err.code)
            ) {
              this.papers = []
              this.loadMarkPaperErrorCode = err.code
              this.loadMarkPaperErrorMessage = err.msg
              // 如果是Finish_Wait_Other，则刷新统计
              // if (err.code === LoadMarkPaperErrorCode.Finish_Wait_Other) {
              //   return this.refreshStats()
              // }
            } else {
              throw err
            }
          })
          .catch(err => {
            // 一般错误
            this.papers = []
            this.$Modal.error({
              title: '取卷错误',
              content: `获取试卷出错了：${(err && err.msg) || ''} <br>请先返回至评卷任务列表页面再重新进入!`,
              onOk: () => {
                this.backToList()
              },
            })
            throw err
          })
          .finally(() => this.$TransparentSpin.hide())
      },

      /* function */
      backToList() {
        if (window.history?.state?.back) {
          this.$router.back()
        } else {
          this.$router.replace({
            name: 'emarking-subject-index',
            params: {
              isFromHomePage: this.$route?.params?.isTaskListFromHomePage || 0,
              examSubjectId: this.$route?.params?.examSubjectId,
            },
          })
        }
      },
      setCurrentBlockInfo(blockId) {
        const CurrentBlock = this.examSubjectInfo.blocks.find(b => b.blockId === blockId)
        if (CurrentBlock) {
          if (!CurrentBlock.aiScoreGroupStat.some(x => x.score == this.selectedAIScore)) {
            this.selectedAIScore = CurrentBlock.aiScoreGroupStat[0].score // TODO 如何选中未被分配完的分数
          }

          this.blockInfo = {
            blockId: CurrentBlock.blockId,
            blockName: CurrentBlock.blockName,
            assignMode: CurrentBlock.assignMode || {},
            markingWay: CurrentBlock.markingWay || {},
            strictMark: Boolean(CurrentBlock.strictMark),
            imgPixelType: CurrentBlock.imgPixelType,
            // batchScoreSize: CurrentBlock.batchScoreSize,
            leaders: [],
            aiScoreGroupStat: CurrentBlock.aiScoreGroupStat,
            questions: getBlockQuestionsDefineAndKeys(
              CurrentBlock.questions,
              CurrentBlock.strictMark
                ? ''
                : window.sessionStorage.getItem('customScoreIntervalStr_' + CurrentBlock.blockId)
            ),
          }
        } else {
          this.$Message.warning({
            duration: 3,
            content: '题块不存在',
            onClose: () => this.backToList(),
          })
          throw new Error('题块不存在')
        }

        this.loadMarkAIPapers()
      },
      loadMarkAIPapers() {
        this.status = 2

        return this.updateBlockInfo().finally(() => this.tryGetStudentsPapers(1, 2000))
      },
      tryGetStudentsPapers(maxRetryTimes = 3, duration = 5000) {
        let retryTimes = 0
        const _LoadMarkPapers = () =>
          this.fetchStudentPapers().catch(err => {
            if (err.code == LoadMarkPaperErrorCode.Prepare && retryTimes < maxRetryTimes) {
              retryTimes++
              return sleep(duration).then(_LoadMarkPapers)
            } else {
              throw err
            }
          })

        return _LoadMarkPapers()
      },
      loadBlockAnswer() {
        this.isBlockAnswerLoading = true
        this.$TransparentSpin.show()

        return apiGetSubjectiveBlockSolutions({
          blockId: this.blockInfo.blockId,
        })
          .then(response => {
            const QuestionDetailList = response.quesDetailList || []
            const QuestionList = response.quesList || []
            QuestionDetailList.forEach(q => {
              q.updateQuestionCodeAliasScore(q, QuestionList)
              q.showBranches = q.branches.filter(b => QuestionList.some(item => item.branchId === b.id))
            })

            this.blockAnswerImg = response.answerImgUrl
            this.blockQuestions = QuestionDetailList
          })
          .finally(() => {
            this.$TransparentSpin.hide()
            this.isBlockAnswerLoading = false
          })
      },
      updateBlockInfo() {
        if (Date.now() - this.lastUpdateBlockInfoTimeStamp < MinUpdateBlockInfoInterval) {
          return Promise.resolve()
        } else {
          return apiGetSubjectiveDefine(this.blockInfo.blockId).then(response => {
            // 更新各小题、是否规范给分
            const CustomScoreIntervalStr = window.sessionStorage.getItem(
              'customScoreIntervalStr_' + this.blockInfo.blockId
            )
            this.blockInfo.strictMark = response.strictMark
            this.blockInfo.questions = getBlockQuestionsDefineAndKeys(
              response.questions,
              this.blockInfo.strictMark ? '' : CustomScoreIntervalStr
            )
            this.lastUpdateBlockInfoTimeStamp = Date.now()
          })
        }
      },
      getImageOriginWidthFromURL(url) {
        if (url) {
          return new Promise(resolve => {
            const ImageObject = new Image()
            ImageObject.onload = () => resolve(ImageObject.naturalWidth)
            ImageObject.onerror = () => resolve(0)
            ImageObject.src = url
          })
        } else {
          return Promise.resolve(0)
        }
      },
      // scaleImage() {
      //   console.log('please believe me.')
      // },
      // scaleRowImage() {},
      // scaleColumnImage() {},
      zoomInImage() {
        if (this.batchPlacementMode === 'row') {
          this.zoomInRowImage()
        } else {
          this.zoomInColumnImage()
        }
      },
      zoomInRowImage() {
        if (this.rowGridItemCount > 2) {
          this.rowGridItemCount--
        }
      },
      zoomInColumnImage() {
        const CalcZoomInWidth = Math.floor(this.columnImageWidthValue * 1.2)
        this.columnImageWidthValue = Math.min(this.columnImagePanelWidth, CalcZoomInWidth)
      },
      zoomOutImage() {
        if (this.batchPlacementMode === 'row') {
          this.zoomOutRowIamge()
        } else {
          this.zoomOutColumnImage()
        }
      },
      zoomOutRowIamge() {
        if (this.rowGridItemCount < this.maxRowGridItemCount) {
          this.rowGridItemCount++
        }
      },
      zoomOutColumnImage() {
        const CalcZoomOutWidth = Math.floor(this.columnImageWidthValue / 1.2)
        this.columnImageWidthValue = Math.max(300, CalcZoomOutWidth)
      },

      checkAndSubmit() {
        // TODO
        this.submitMark()
      },
      submitMark() {
        // this.calcTotalScore()
        this.status = 5

        return Promise.all([
          apiSubmitMarkBatchStudentBlocks(
            this.papers.map(p => ({
              subjectiveId: p.blockId,
              subjectiveItemId: p.studentBlockId,
              score: p.type ? null : p.score,
              subScore: p.type ? null : (p.questions || []).map(pq => `${pq.questionCode}=${pq.score}`).join(';'),
            }))
          ),
          sleep(MinSubmitPeriod),
        ])
          .then(([statsData]) => {
            this.$Message.success({
              duration: 2,
              content: '成功提交分数',
            })
            this.updateStatsData(statsData)
            this.loadMarkAIPapers()
          })
          .catch(err => {
            this.status = 4 // 4 - 待评卷

            if (err.code === SubmitInconsistentErrorCode) {
              this.$Modal.error({
                title: '提交分数失败',
                content: `<br>${(err && err.msg) || ''}<br><br>请先返回至评卷任务列表页面再重新进入`,
                onOk: () => this.backToList(),
              })
            } else {
              this.$Message.error({
                content: `提交分数失败：${(err && err.msg) || ''}`,
                duration: 5,
                closable: true,
              })
            }

            throw err
          })
      },
      updateStatsData() {
        // 占位
      },
      resetHistoryParams() {},

      /* event */
      onDropdownBlockItemClick(blockId) {
        if (blockId && blockId !== this.blockInfo.blockId) {
          const TargetBlock = this.examSubjectInfo.blocks.find(b => b.blockId === blockId)

          if (TargetBlock.isAIAssistMarkingMode) {
            this.setCurrentBlockInfo(blockId)
          } else {
            this.$router.replace({
              name: (TargetBlock?.batchScoreSize || 0) > 1 ? 'emarking-mark-batch' : 'emarking-mark',
              params: {
                examSubjectId: this.examSubjectInfo.examSubjectId,
                blockId: blockId,
              },
            })
          }
        }
      },
      onDropdownBlockAIScoreItemClick(score) {
        this.selectedAIScore = score
        this.loadMarkAIPapers()
      },
      onDropdownBlockBatchCountItemClick(count) {
        this.setting.customBatchSize = count
        this.loadMarkAIPapers()
      },
      onBatchPlacementModeChange(mode) {
        this.batchPlacementMode = mode
      },
      onBtnLeadersClick() {
        apiGetMyBlockLeaders({
          examSubjectId: this.examSubjectInfo.examSubjectId,
          subjectiveId: this.blockInfo.blockId,
        })
          .then(data => (this.blockInfo.leaders = data || []))
          .finally(() => (this.isModalLeadersShow = true))
      },
      onBtnBlockAnswerClick() {
        this.loadBlockAnswer().finally(() => (this.blockAnswerModalShowed = true))
      },
      handleRemarkPaperTagAbnormalClick(paper) {
        const TargetPaper = this.papers.find(p => p.No === paper.No)
        if (TargetPaper) {
          TargetPaper.type = TargetPaper.type ? PaperType_Normal : PaperType_Abnormal
        }
      },
      onBtnRefreshClick() {
        this.loadMarkAIPapers().then(() => {
          this.$Message.info({
            duration: 3,
            content: '已刷新',
          })
        })
      },
      onBtnFullScreenClick() {
        if (fscreen.fullscreenElement) {
          fscreen.exitFullscreen()
        } else {
          fscreen.requestFullscreen(document.documentElement)
        }
      },
      onBranchScoreChange(value, paper, question) {
        const TargetPaper = this.papers.find(p => p.secretNum === paper.secretNum)
        if (TargetPaper) {
          const TargetQuestion = TargetPaper.questions.find(q => q.questionCode === question.questionCode)
          if (TargetQuestion) {
            TargetQuestion.score = value
            TargetPaper.score = TargetPaper.questions.reduce((acc, cur) => acc + cur.score, 0)
          }
        }
      },
      onBtnRemarkClick() {
        this.resetHistoryParams()
        this.loadHistory().then(() => {
          // if
        })
      },

      onNormalBlockItemClick(item) {
        this.modalDetailPaper = item
        this.isModalNormalBlockDetailShow = true
      },

      onCommitMarking() {
        setTimeout(this.checkAndSubmit, 0)
      },
    },
  }
</script>

<template>
  <div class="page-marking-ai-assist">
    <div class="section-header">
      <div class="nav">
        <TextButton type="primary" icon="md-arrow-back" @click="backToList">任务列表</TextButton>

        <span class="block-selector">
          当前题块：
          <Dropdown
            v-if="examSubjectInfo.blocks && examSubjectInfo.blocks.length > 1"
            transfer
            @on-click="onDropdownBlockItemClick"
          >
            <span>{{ blockInfo.blockName }}<Icon type="ios-arrow-down" /></span>
            <template #list>
              <DropdownMenu>
                <DropdownItem v-for="b of examSubjectInfo.blocks" :key="b.blockId" :name="b.blockId">{{
                  b.blockName
                }}</DropdownItem>
              </DropdownMenu>
            </template>
          </Dropdown>
          <span v-else>{{ blockInfo.blockName }}</span>
        </span>

        <span class="block-selector">
          分数：
          <Dropdown v-if="blockAIScores.length > 1" transfer @on-click="onDropdownBlockAIScoreItemClick">
            <span>{{ selectedAIScore }}<Icon type="ios-arrow-down" /></span>
            <template #list>
              <DropdownMenu>
                <DropdownItem v-for="b of blockAIScores" :key="'score-' + b.score" :name="b.score">{{
                  b.score
                }}</DropdownItem>
              </DropdownMenu>
            </template> </Dropdown
          ><span v-else>{{ selectedAIScore }}</span>
        </span>

        <span class="block-selector">
          批量份数：
          <Dropdown transfer @on-click="onDropdownBlockBatchCountItemClick">
            <span>{{ setting.customBatchSize }}<Icon type="ios-arrow-down" /></span>
            <template #list>
              <DropdownMenu>
                <DropdownItem v-for="c in 10" :key="'count-' + c" :name="c">{{ c }}</DropdownItem>
              </DropdownMenu>
            </template>
          </Dropdown>
        </span>

        <RadioGroup
          :model-value="batchPlacementMode"
          type="button"
          size="small"
          butto-style="solid"
          @on-change="onBatchPlacementModeChange"
        >
          <Radio label="row">平铺模式</Radio>
          <Radio label="column">单列模式</Radio>
        </RadioGroup>

        <Button type="primary" size="small" style="margin-left: 10px" @click="onCommitMarking">确认提交</Button>
      </div>

      <div>
        <template v-if="isRehearsal">
          <span>正处于试评模式中</span>
          <Divider type="vertical"></Divider>
        </template>

        <span>
          <TextButton v-if="hasBlock && !isMarkingByClass" type="primary" icon="md-contacts" @click="onBtnLeadersClick"
            >题组长信息</TextButton
          >
          <TextButton class="btn" type="primary" @click="onBtnBlockAnswerClick">题块答案</TextButton>

          <template v-if="!markingType">
            <Divider type="vertical" />
            <TextButton type="primary" @click="onBtnRemarkClick">进入回评</TextButton>
          </template>

          <template v-if="hasPapers">
            <Divider type="vertical"></Divider>
            <TextButton class="btn" type="default" title="放大图片" style="vertical-align: middle" @click="zoomInImage">
              <img src="@/assets/images/common/zoom_in.svg" style="width: 16px" />
            </TextButton>
            <TextButton
              class="btn"
              type="default"
              title="缩小图片"
              style="vertical-align: middle"
              @click="zoomOutImage"
            >
              <img src="@/assets/images/common/zoom_out.svg" style="width: 16px" />
            </TextButton>
          </template>
        </span>

        <Divider type="vertical" />

        <template v-if="isBtnRefreshShow">
          <span>
            <TextButton type="primary" @click="onBtnRefreshClick">刷新</TextButton>
          </span>
          <Divider type="vertical" />
        </template>
        <span>
          <TextButton type="default" icon="md-resize" title="全屏切换" @click="onBtnFullScreenClick"></TextButton>
        </span>
      </div>
    </div>

    <div class="section-body">
      <div v-if="hasPapers" class="marking-panel">
        <div v-if="batchPlacementMode === 'row'" class="row-block-panel">
          <div v-for="p of papers" :key="p.imgUrl" class="row-block-item">
            <div class="row-block-image">
              <img :src="p.imgUrl" style="width: 100%" />

              <div class="row-block-card" @click="onNormalBlockItemClick(p)">
                <div class="question-code-tag"></div>

                <div v-if="p.type" class="question-score-abnormal">问题卷</div>
                <div v-else :class="{ 'question-score-modified': p.score !== p.aiScore }" class="question-score">
                  {{ p.score }} 分
                </div>
              </div>
            </div>

            <div class="row-block-score-panel">
              <div class="panel-title">
                <div class="paper-no">第 {{ p.No }} 份</div>
                <div>
                  <Checkbox
                    :model-value="p.type === 1"
                    style="margin-right: 1px"
                    @click="handleRemarkPaperTagAbnormalClick(p)"
                  ></Checkbox>
                  <TextButton
                    :type="p.type ? 'warning' : 'gray'"
                    style="padding-right: 0; padding-left: 0"
                    @click="handleRemarkPaperTagAbnormalClick(p)"
                    >问题卷</TextButton
                  >
                </div>
              </div>
              <div class="score-item-panel">
                <div v-for="q of p.questions" :key="'row-q-' + q.questionCode" class="score-item">
                  {{ q.questionCode }} ({{ q.fullScore }} 分)
                  <InputNumber
                    :model-value="q.score"
                    :min="0"
                    :max="q.fullScore"
                    :step="q.scoreInterval"
                    :class="{ 'modified-branch-score': q.score !== q.aiSubScore }"
                    style="width: 60px"
                    @on-change="onBranchScoreChange($event, p, q)"
                  />
                  分
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="column-block-panel">
          <div v-for="p of papers" :key="p.imgUrl" class="column-block-item">
            <div class="column-block-image">
              <div :style="{ width: columnImageWidthValue + 'px' }" class="image-panel">
                <img :src="p.imgUrl" style="width: 100%" />

                <div class="column-block-card" @click="onNormalBlockItemClick(p)">
                  <div class="question-code-tag"></div>

                  <div v-if="p.type" class="question-score-abnormal">问题卷</div>
                  <div v-else :class="{ 'question-score-modified': p.score !== p.aiScore }" class="question-score">
                    {{ p.score }} 分
                  </div>
                </div>
              </div>
            </div>

            <div class="column-block-score-panel">
              <div class="panel-title">
                <div class="paper-no">第 {{ p.No }} 份</div>

                <div>
                  <Checkbox
                    :model-value="p.type === 1"
                    style="margin-right: 1px"
                    @click="handleRemarkPaperTagAbnormalClick(p)"
                  ></Checkbox>
                  <TextButton
                    :type="p.type ? 'warning' : 'gray'"
                    style="padding-right: 0; padding-left: 0"
                    @click="handleRemarkPaperTagAbnormalClick(p)"
                    >问题卷</TextButton
                  >
                </div>
              </div>

              <div v-for="q of p.questions" :key="'row-q-' + q.questionCode" class="column-score-item">
                {{ q.questionCode }} ({{ q.fullScore }} 分)
                <InputNumber
                  :model-value="q.score"
                  :min="0"
                  :max="q.fullScore"
                  :step="q.scoreInterval"
                  :class="{ 'modified-branch-score': q.score !== q.aiSubScore }"
                  style="width: 60px"
                  @on-change="onBranchScoreChange($event, p, q)"
                />
                分
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-else>
        <div class="message-box" v-html="loadMarkPaperErrorMessage"></div>
        <!-- <div v-show="showBtnMarkMore" class="btn-mark-more">
          <Button type="primary" @click="modalMoreTaskShowed = true">申请继续评卷</Button>
        </div> -->
      </template>
    </div>

    <!-- 弹窗 -->
    <modal-leader
      v-model="isModalLeadersShow"
      :title="'题组长（' + blockInfo.blockName + '）'"
      :leaders="blockInfo.leaders"
    ></modal-leader>

    <modal-block-answer
      :showed="blockAnswerModalShowed"
      :block-name="blockInfo.blockName"
      :is-block-answer-loading="isBlockAnswerLoading"
      :block-answer-img="blockAnswerImg"
      :block-questions="blockQuestions"
      :affix-img-showed="affixImgShowed"
      @handle-btn-affix-answer-img="affixImgShowed = !affixImgShowed"
      @close-modal="blockAnswerModalShowed = false"
    ></modal-block-answer>

    <affix-answer
      v-if="affixImgShowed && (blockAnswerImg || blockQuestions.length)"
      :is-block-answer-loading="isBlockAnswerLoading"
      :block-answer-img="blockAnswerImg"
      :block-questions="blockQuestions"
    ></affix-answer>

    <modal-normal-block-detail
      v-model="isModalNormalBlockDetailShow"
      :paper="modalDetailPaper"
    ></modal-normal-block-detail>
  </div>
</template>

<style lang="scss" scoped>
  .page-marking-ai-assist {
    position: relative;
    padding-top: 50px;
    user-select: none;

    .section-header {
      @include flex(row, space-between, center);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1;
      flex-wrap: wrap;
      width: 100%;
      height: 50px;
      padding-right: 50px;
      padding-left: 50px;
      white-space: nowrap;
      background-color: white;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

      .nav {
        @include flex(row, flex-start, center);

        .block-selector {
          margin: 0 1em 2px 1em;
        }
      }
    }

    .section-body {
      position: relative;
      z-index: 0;
      margin: 10px 30px;

      .marking-panel {
        width: 100%;
        min-height: 300px;

        .row-block-panel {
          display: grid;
          grid-template-rows: auto;
          grid-template-columns: repeat(v-bind(rowGridItemCount), 1fr);

          .row-block-item {
            position: relative;
            margin: 0 20px 40px 0;

            .row-block-image {
              width: 100%;

              .row-block-card {
                @include flex(row, space-between, stretch);
                position: absolute;
                top: 2px;
                left: 4px;
                width: 100%;
                height: 100%;
                color: red;
                font-weight: bold;
                font-size: $font-size-large-s;

                &:hover {
                  cursor: pointer;
                }

                .question-code-tag {
                  text-decoration: underline red;
                  text-underline-offset: 4px;
                }

                .question-score {
                  margin-right: 0.5em;
                  color: $color-content;
                }

                .question-score-modified {
                  color: $color-primary;
                }

                .question-score-abnormal {
                  z-index: 2;
                  height: fit-content;
                  padding: 5px 8px;
                  border: 1px solid $color-error;
                  border-radius: 5px;
                  color: $color-error;
                  font-size: $font-size-medium;
                  background-color: #ffefe6;
                  opacity: 0.7;
                }
              }
            }

            .row-block-score-panel {
              position: relative;
              width: 100%;
              margin-top: 4px;
              padding: 4px 8px 0 8px;
              background-color: #edf4e3;

              .panel-title {
                @include flex(row, space-between, center);
                margin-bottom: 2px;
              }

              .score-item-panel {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 4px;
                padding-bottom: 10px;

                .score-item {
                  width: fit-content;
                  white-space: nowrap;

                  .modified-branch-score {
                    :deep(.ivu-input-number-input) {
                      color: white;
                      font-weight: bold;
                      background-color: #76d7c4;
                    }
                  }
                }
              }
            }
          }
        }

        .column-block-panel {
          .column-block-item {
            @include flex(row, flex-end, stretch);

            &:not(:last-child) {
              margin-bottom: 20px;
            }

            .column-block-image {
              flex: 1;
              margin-right: 30px;
              text-align: right;

              .image-panel {
                position: relative;
                display: inline-block;

                .column-block-card {
                  @include flex(row, space-between, stretch);
                  position: absolute;
                  top: 2px;
                  left: 4px;
                  width: 100%;
                  height: 100%;
                  color: red;
                  font-weight: bold;
                  font-size: $font-size-large-s;

                  &:hover {
                    cursor: pointer;
                  }

                  .question-code-tag {
                    text-decoration: underline red;
                    text-underline-offset: 4px;
                  }

                  .question-score {
                    margin-right: 0.5em;
                    color: $color-content;
                  }

                  .question-score-modified {
                    color: $color-primary;
                  }

                  .question-score-abnormal {
                    z-index: 2;
                    height: fit-content;
                    padding: 5px 8px;
                    border: 1px solid $color-error;
                    border-radius: 5px;
                    color: $color-error;
                    font-size: $font-size-medium;
                    background-color: #ffefe6;
                    opacity: 0.7;
                  }
                }
              }
            }

            .column-block-score-panel {
              flex: none;
              width: 300px;
              height: fit-content;
              padding: 4px 8px;
              background-color: #edf4e3;

              .panel-title {
                @include flex(row, space-between, center);
                margin-bottom: 2px;
              }

              .column-score-item {
                margin-bottom: 4px;
                margin-left: 20px;

                .modified-branch-score {
                  :deep(.ivu-input-number-input) {
                    color: white;
                    font-weight: bold;
                    background-color: #76d7c4;
                  }
                }
              }
            }
          }
        }
      }

      .message-box {
        padding-top: 100px;
        font-weight: bold;
        font-size: 30px;
        text-align: center;
      }
    }
  }
</style>
