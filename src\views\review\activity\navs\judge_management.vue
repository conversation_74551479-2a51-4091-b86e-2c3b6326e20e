<template>
  <div class="page-judge-management">
    <div class="section-content">
      <JudgeAudit />
    </div>
  </div>
</template>

<script>
  import JudgeAudit from '../../components/judge_audit.vue'

  export default {
    components: {
      JudgeAudit,
    },
    data() {
      return {}
    },
  }
</script>

<style lang="scss" scoped>
  .page-judge-management {
    padding: 20px;
    background-color: #fff;

    .section-header {
      @include flex(row, space-between, center);
      margin-bottom: 16px;
    }
  }
</style>
