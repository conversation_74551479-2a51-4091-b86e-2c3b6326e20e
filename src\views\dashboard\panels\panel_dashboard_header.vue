<template>
  <div class="panel-dashboard-header">
    <div class="section-selector">
      <div class="selector-semester">
        <Select
          class="selector-scaled"
          :model-value="activeSemester"
          size="small"
          style="width: 130px"
          @on-change="onChangeSemester"
        >
          <Option v-for="item in schoolTerms" :key="item.semesterId" :value="item.semesterId">{{
            item.semesterName
          }}</Option>
        </Select>
        <Select
          class="selector-scaled"
          :model-value="activeTerm"
          clearable
          placeholder="全学年"
          size="small"
          style="width: 80px; margin-left: 10px"
          @on-change="onChangeTerm"
        >
          <Option v-for="item in terms" :key="item.value" :value="item.value">{{ item.name }}</Option>
        </Select>
      </div>
      <div class="selector-school">
        <Select
          class="selector-scaled"
          :model-value="activeSchool"
          filterable
          size="small"
          style="width: 220px"
          @on-change="onChangeSchool"
        >
          <Option v-for="item in schools" :key="item.schoolId" :value="item.schoolId">
            <span class="selector-school-name" :style="'padding-left: ' + item.level * 15 + 'px'">{{
              item.schoolName
            }}</span>
          </Option>
        </Select>
      </div>
    </div>
    <div class="section-title">
      <div class="dashboard-name">质量监测大数据中心</div>
      <div class="region-name">{{ regionName }}</div>
    </div>
    <div class="section-time">
      <span class="time">{{ currentDateTime }}</span>
      <span class="btn-full-screen" @click="handleFullScreen">
        <Icon type="md-expand" size="20" />
      </span>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue'
  import store from '@/store/index'
  import { useDashboardStore } from '@/store/dashboard'
  import { formatDateTime } from '@/utils/date'
  import PageCache from '@/utils/page_cache'

  const emits = defineEmits(['change-screen-mode'])

  const dashboardStore = useDashboardStore()
  const PageName = 'dashboard'
  const currentDateTime = ref('')
  const terms = [
    {
      value: 1,
      name: '上学期',
    },
    {
      value: 2,
      name: '下学期',
    },
  ]

  const userInfo = computed(() => {
    return store.getters['user/info']
  })
  const regionName = computed(() => {
    return userInfo.value.schoolName
  })
  const schoolTerms = computed(() => {
    let list = store.getters['school/schoolTerms']
    return list.filter((item, idx) => list.findIndex(x => x.semesterId === item.semesterId) === idx)
  })
  const activeSemester = computed(() => {
    return dashboardStore.currentSemester || ''
  })
  const activeTerm = computed(() => {
    return dashboardStore.currentTerm || ''
  })
  const schools = computed(() => {
    return dashboardStore.schools
  })
  const activeSchool = computed(() => {
    return dashboardStore.currentSchool?.schoolId || ''
  })

  function updateTime() {
    const now = new Date()
    const dayOfWeek = now.toLocaleString('zh-CN', { weekday: 'long' })
    currentDateTime.value = `${formatDateTime(now, 'YYYY-MM-DD HH:mm:ss')} ${dayOfWeek}`
    requestAnimationFrame(updateTime)
  }

  function savePageParams() {
    PageCache.save(PageName, {
      currentSemester: dashboardStore.currentSemester,
      currentTerm: dashboardStore.currentTerm,
      currentSchool: dashboardStore.currentSchool,
    })
  }

  function initPageParams() {
    const cacheData = PageCache.fetch(PageName)
    if (cacheData) {
      dashboardStore.currentSemester = cacheData.currentSemester || null
      dashboardStore.currentTerm = cacheData.currentTerm || null
      dashboardStore.currentSchool = cacheData.currentSchool || null
    }
  }

  const handleFullScreen = () => {
    emits('change-screen-mode')
  }

  const onChangeSemester = val => {
    dashboardStore.currentSemester = val
    savePageParams()
  }

  const onChangeTerm = val => {
    dashboardStore.currentTerm = val
    savePageParams()
  }

  const onChangeSchool = val => {
    const theSchool = schools.value.find(item => item.schoolId === val)
    dashboardStore.currentSchool = theSchool
    savePageParams()
  }

  watch(schoolTerms, newVal => {
    if (Array.isArray(newVal) && newVal.length) {
      const cacheData = PageCache.fetch(PageName)
      if (!cacheData || !cacheData.currentSemester) {
        dashboardStore.currentSemester = newVal[0].semesterId
        savePageParams()
      } else {
        initPageParams()
      }
    }
  })

  watch(schools, newVal => {
    if (Array.isArray(newVal) && newVal.length) {
      const cacheData = PageCache.fetch(PageName)
      if (!cacheData || !cacheData.currentSchool) {
        dashboardStore.currentSchool = newVal[0]
        savePageParams()
      } else {
        initPageParams()
      }
    }
  })

  onMounted(() => {
    updateTime()
  })
</script>

<style lang="scss" scoped>
  .panel-dashboard-header {
    @include flex(row, space-between, center);
    height: 60px;
    padding: 0 30px;
    color: white;

    :deep(.ivu-select-selection) {
      color: white;
      background-color: transparent;

      .ivu-select-arrow {
        color: #fff;
      }
    }

    :deep(.ivu-select-input) {
      color: #fff;
    }

    :deep(.ivu-select-placeholder) {
      color: #fff;
    }

    .section-selector {
      @include flex(row, flex-start, center);
      width: 450px;
    }

    .selector-semester {
      @include flex(row, flex-start, center);
      margin-top: 22px;
      margin-right: 10px;
      padding-left: 20px;
    }

    .selector-school {
      margin-top: 22px;
    }

    .section-title {
      @include flex(column, center, center);
      margin-top: -6px;

      .region-name {
        font-size: 12px;
      }

      .dashboard-name {
        margin-bottom: 4px;
        font-weight: 600;
        font-size: 20px;
        line-height: 1;
      }
    }

    .section-time {
      @include flex(row, flex-end, center);
      width: 450px;
      margin-top: 22px;

      .time {
        margin-right: 20px;
      }

      .btn-full-screen {
        cursor: pointer;
      }
    }
  }
</style>
