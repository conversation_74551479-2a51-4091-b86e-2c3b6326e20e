<template>
  <div class="class-students">
    <div class="class-students-header">
      <div>
        <Input v-model="searchStudentKeyword" class="class-students-filter" clearable placeholder="姓名 / 学籍号" />
        <span class="class-students-count">共 {{ students.length }} 名学生</span>
      </div>
      <Button v-show="schoolTeacherRegisterModuleOpened" type="primary" @click="openAddStudentModal">添加学生</Button>
    </div>

    <Table class="class-students-content" :columns="studentTableColumns" :data="filtedSelectedClassStudents"></Table>

    <modal-student-trnasfer-class
      v-model="modalStudentTransferClassShowed"
      :transfer-student="transferStudent"
      :teachers-class-list="teachersClassList"
      @refresh-student-list="refreshClass"
    ></modal-student-trnasfer-class>
  </div>
</template>

<script>
  import { Icon } from 'view-ui-plus'
  import TextButton from '@/components/text_button.vue'
  import ModalStudentTransferClass from './modal_student_transfer_class.vue'

  export default {
    components: {
      'modal-student-trnasfer-class': ModalStudentTransferClass,
    },
    props: {
      students: {
        type: Array,
        default: () => [],
      },
      schoolTeacherRegisterModuleOpened: {
        type: Boolean,
        default: false,
      },
      teachersClassList: {
        type: Array,
        defualt: () => [],
      },
      selectedClass: {
        type: Object,
        default: () => {},
      },
      isSelectedClassLeaders: {
        type: Boolean,
        default: false,
      },
    },
    emits: [
      'show-modal-add-student',
      'open-modal-edit-student',
      'refresh-class',
      'delete-student',
      'add-focus-student',
      'delete-focus-student',
    ],
    data() {
      return {
        searchStudentKeyword: '',

        modalStudentTransferClassShowed: false,
        transferStudent: {},
      }
    },
    computed: {
      // 关键字筛选后的学生列表
      filtedSelectedClassStudents() {
        let trimedSearchStudentKeyword = this.searchStudentKeyword.trim()
        if (trimedSearchStudentKeyword) {
          return this.students.filter(
            x => x.studentNo.includes(trimedSearchStudentKeyword) || x.realName.includes(trimedSearchStudentKeyword)
          )
        } else {
          return this.students
        }
      },
      // 是否有学生在教学班中
      hasStudentInTeachingClass() {
        return this.students.some(s => s.teachingClassNames)
      },
      isGradeHasForeignLanguages() {
        const SelectedClassGradeId = this.selectedClass?.gradeId || 0
        return SelectedClassGradeId > 9 && SelectedClassGradeId < 13
      },
      foreignLanguageSubjects() {
        return [
          {
            subjectId: 3,
            subjectName: '英语',
          },
          {
            subjectId: 16,
            subjectName: '日语',
          },
          {
            subjectId: 18,
            subjectName: '俄语',
          },
        ]
      },
      studentTableColumns() {
        let columns = [
          {
            title: '姓名',
            key: 'realName',
            align: 'center',
            width: 120,
          },
          {
            title: '性别',
            key: 'sex',
            align: 'center',
            width: 80,
          },
          {
            title: '学籍号',
            key: 'studentNo',
            align: 'center',
            sortable: true,
            minWidth: 130,
          },
          {
            title: '校内考试号',
            key: 'examNo',
            align: 'center',
            sortable: true,
            minWidth: 120,
          },
          {
            title: '所在班级',
            align: 'center',
            minWidth: 120,
            render: (h, params) => {
              let list = [params.row.className]
              if (params.row.teachingClassNames) {
                list.push(...params.row.teachingClassNames)
              }
              return h(
                'div',
                {
                  style: {
                    'margin-top': '1px',
                  },
                },
                list.map(s =>
                  h(
                    'p',
                    {
                      style: {
                        'text-align': 'center',
                        'line-height': '22px',
                        'margin-bottom': '1px',
                      },
                    },
                    s
                  )
                )
              )
            },
          },
          {
            title: '家长',
            align: 'center',
            minWidth: 120,
            render: (h, params) => {
              let parentStrs = (params.row.parents || []).map(x =>
                x.mobile ? `${x.realName}（${x.mobile.substring(0, 3) + '****' + x.mobile.substring(7)}）` : x.realName
              )
              return h(
                'div',
                {},
                parentStrs.map(s => h('p', {}, s))
              )
            },
          },
        ]

        if (this.isSelectedClassLeaders || this.schoolTeacherRegisterModuleOpened) {
          columns.push({
            title: '操作',
            align: 'center',
            width: this.schoolTeacherRegisterModuleOpened ? 240 : 90,
            render: (h, params) => {
              const OperateTextBtns = [
                h(
                  TextButton,
                  { type: 'primary', onClick: () => this.$emit('open-modal-edit-student', params.row) },
                  () => '编辑'
                ),
              ]

              if (this.schoolTeacherRegisterModuleOpened) {
                OperateTextBtns.push(
                  h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => {
                        this.transferStudent = params.row
                        this.modalStudentTransferClassShowed = true
                      },
                    },
                    () => '调班'
                  ),
                  h(
                    TextButton,
                    {
                      type: 'warning',
                      disabled: Boolean(params.row.subjects && params.row.subjects.length),
                      onClick: () => this.$emit('delete-student', params.row),
                    },
                    () => '删除'
                  )
                )
              }

              return h('div', {}, OperateTextBtns)
            },
          })
        }

        if (this.schoolTeacherRegisterModuleOpened) {
          columns.splice(5, 0, {
            title: '已激活科目',
            align: 'center',
            minWidth: 120,
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.subjects && params.row.subjects.length
                  ? params.row.subjects.map(s => s.subjectName).join('、')
                  : '-'
              ),
          })
          columns.unshift({
            title: '重点关注',
            align: 'center',
            width: 100,
            render: (h, params) => {
              let iconAttribute = {
                type: 'md-star-outline',
                style: 'font-size: 20px; cursor: pointer',
                onClick: () => {
                  if (params.row.focus) {
                    this.$emit('delete-focus-student', params.row.studentId)
                  } else {
                    this.$emit('add-focus-student', params.row.studentId)
                  }
                },
              }
              if (params.row.focus) {
                iconAttribute.type = 'md-star'
                iconAttribute.color = '#05c1ae'
              }
              return h(Icon, iconAttribute)
            },
          })
        }

        if (!this.hasStudentInTeachingClass) {
          columns = columns.filter(col => col.title != '所在班级')
        }

        if (this.isGradeHasForeignLanguages) {
          columns.splice(
            columns.findIndex(x => x.key === 'studentNo'),
            0,
            {
              title: '外语',
              align: 'center',
              width: 70,
              render: (h, params) => {
                let foreignSubjectName = (
                  this.foreignLanguageSubjects.find(x => x.subjectId === params.row.foreignSubjectId) || {
                    subjectName: '-',
                  }
                ).subjectName

                return h('span', {}, foreignSubjectName)
              },
            }
          )
        }

        if (this.filtedSelectedClassStudents.some(item => item.selectSubjectNames)) {
          const theColumnIdx = columns.findIndex(c => c.key === 'sex')
          if (theColumnIdx >= 0) {
            columns.splice(theColumnIdx + 1, 0, {
              title: '选科',
              width: 150,
              align: 'center',
              render: (h, params) => {
                if (params.row.selectSubjectNames) return h('span', {}, params.row.selectSubjectNames)
                return ''
              },
            })
          }
        }

        return columns
      },
    },
    methods: {
      openAddStudentModal() {
        this.$emit('show-modal-add-student')
      },
      refreshClass() {
        this.$emit('refresh-class')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .class-students {
    .class-students-header {
      @include flex(row, space-between, center);
      margin-bottom: 10px;

      .class-students-count {
        margin-left: 1em;
        user-select: none;
      }

      .class-students-filter {
        display: inline-block;
        width: 200px;
        margin-right: auto;
      }
    }
  }
</style>
