import ajax from '@/api/ajax'
import { transformExam } from '@/api/emarking'
import { ReportParameterConfig, ReportPermissionConfig } from '@/helpers/report/template'

/**
 * 分析报告
 */
export function apiGetReportTemplate(templateId) {
  return ajax
    .get({
      url: 'report/tem/temp',
      params: {
        templateId,
      },
      requestName: '获取报告配置',
    })
    .then(data => {
      let exam = transformExam(data.examVo)
      let parameters = ReportParameterConfig.import(exam, data.configJson)
      let permissions = ReportPermissionConfig.import(exam, data.templateId || '', data.permissions || [])

      return {
        exam,
        template: {
          templateId: data.templateId || '',
          templateName: data.templateName || '',
          sign: data.sign || 0,
          parameters,
          permissions,
          type: data.type || 0,
          schoolIds: data.schoolIds || [],
          isShow: 'isShow' in data ? data.isShow : true,
        },
      }
    })
}

export function apiDeleteReportTemplate(requestParams) {
  return ajax.delete({
    url: 'report/tem/delTemp',
    params: {
      templateId: requestParams.templateId,
    },
    requestName: '删除报告',
  })
}

export function apiGetNewReportTemplate(examId) {
  return ajax.get({
    url: 'report/tem/newTemp',
    params: {
      examId,
    },
    requestName: '获取新报告配置',
  })
}

export function apiGetExamSubjectsFullScore(examId) {
  return ajax
    .get({
      url: 'report/tem/subjScore',
      params: {
        examId,
      },
      requestName: '获取科目满分',
    })
    .then(data => {
      return (data.subjects || []).map(x => ({
        examSubjectId: x.examSubjectId,
        subjectId: x.subjectId,
        fullWrittenScore: x.writtenFullScore || 0,
        fullOtherScore: x.otherFullScore || 0,
      }))
    })
}

export function apiSaveReportTemplateParameter(exam, template, type = 0, schoolIds = [], isShow) {
  return ajax.put({
    url: 'report/tem/saveTempConfig',
    data: {
      examId: exam.examId,
      templateId: template.templateId,
      templateName: template.templateName,
      configJson: template.parameters.export(),
      isShow: isShow,
      type: type, // Number 0 - 普通报表 | 1 - 分区报表
      schoolIds: type ? schoolIds : undefined, // Array[String]
    },
  })
}

export function apiSaveReportTemplatePermission(exam, template, isShow) {
  return ajax.put({
    url: 'report/tem/saveTempPermissions',
    data: {
      examId: exam.examId,
      templateId: template.templateId,
      templateName: template.templateName,
      permissions: template.permissions.export(),
      isShow: isShow,
    },
    requestName: '保存报表权限配置',
  })
}

export function apiCreateNewExamTemplate(params) {
  return ajax.get({
    url: 'report/tem/newTemp',
    params: {
      examId: params.examId,
      userId: params.userId,
    },
    requestName: '添加考试报表模板',
  })
}

export function apiSaveNewExamTemplate(exam, template, type = 0, schoolIds = []) {
  return ajax.post({
    url: 'report/tem/create',
    data: {
      examId: exam.examId,
      templateId: template.templateId,
      templateName: template.templateName,
      configJson: template.parameters.export(),
      permissions: template.permissions.export(),
      type: type, // Number 0 - 普通报表 | 1 - 分区报表
      schoolIds: type ? schoolIds : undefined, // Array[String]
    },
    requestName: '保存新建考试报表模板',
  })
}

export function apiGetExamTemplates(data) {
  return ajax.get({
    url: 'report/tem/publishTem',
    params: {
      examId: data.examId,
    },
    requestName: '获取考试可用于发布的报表模板',
  })
}

export function apiPublishWechatMPTemplate(data) {
  return ajax.put({
    url: 'report/tem/publishTem',
    params: {
      examId: data.examId,
      templateId: data.templateId,
    },
    requestName: '发布微信公众号成绩模板',
  })
}

export function apiCancelWechatMPTemplate(data) {
  return ajax.delete({
    url: 'report/tem/publishTem',
    params: {
      examId: data.examId,
    },
    requestName: '取消发布微信公众号成绩模板',
  })
}

export function apiPublishAnalyseTemplate(data) {
  return ajax.put({
    url: 'report/tem/analyseTem',
    params: {
      examId: data.examId,
      templateId: data.templateId,
    },
    requestName: '发布精准分析成绩模板',
  })
}

export function apiCancelAnalyseTemplate(data) {
  return ajax.delete({
    url: 'report/tem/analyseTem',
    params: {
      examId: data.examId,
    },
    requestName: '取消发布精准分析成绩模板',
  })
}

export function apiGetWechatMPPushTask(requestParams) {
  return ajax.get({
    url: 'report/wxPushTask',
    params: {
      examId: requestParams.examId,
    },
    requestName: '查看公众号推送任务',
  })
}

export function apiPushWechatMPTask(requestParams) {
  return ajax.put({
    url: 'report/wxPushTask',
    params: {
      examId: requestParams.examId,
      status: requestParams.status,
      taskId: requestParams.taskId,
    },
    requestName: '推送公众号消息',
  })
}

export function apiCancelPushWechatMPTask(requestParams) {
  return ajax.put({
    url: 'report/wxPushTask/cancel',
    params: {
      examId: requestParams.examId,
      status: requestParams.status,
      taskId: requestParams.taskId,
    },
    requestName: '取消（中止）公众号推送',
  })
}

export function apiGetWechatMPSetting(requestParams) {
  return ajax.get({
    url: 'report/wxPushTask/detail',
    params: {
      examId: requestParams.examId,
    },
    requestName: '获取公众号配置详情',
  })
}

export function apiSetWechatMPSetting(requestParams) {
  return ajax.put({
    url: 'report/wxPushTask/detail',
    params: {
      examId: requestParams.examId,
    },
    data: requestParams.WechatMPTaskSetting,
    // WechatMPTaskSetting: [
    //   {
    //     examId: (stirng),
    //     examSubjectId: (string),
    //     subjectId: (number),
    //     showBlockScore: (boolean), 是否显示题块得分
    //     showMarkComment: (boolean), 是否显示评卷批注
    //     showMarker: (boolean), 是否显示评卷老师
    //     showPaper: (boolean), 是否显示原图
    //     showScaleScore: (boolean), 是否显示等级分
    //     showRank: (boolean), 是否显示排名，
    //     showUnion: (boolean), 是否显示联考数据
    //   }
    // ]
    requestName: '配置微信公众号',
  })
}

export function apiGetWechatMPPushConfig(requestParams) {
  return ajax.get({
    url: 'report/wxPushTask/getPushConfig',
    params: {
      examId: requestParams.examId,
    },
    requestName: '获取公众号推送配置',
  })
}

export function apiSaveWechatMPPushConfig(requestParams) {
  if (
    requestParams.subjects &&
    requestParams.subjects.length === 1 &&
    requestParams.subjects.some(subject => !subject.subjectId && subject.subjectName === '全部科目')
  ) {
    requestParams.subjects.push(requestParams.subjects[0])
    requestParams.subjects[0].subjectId = 0
    requestParams.subjects[0].subjectName = '全部科目'
  }

  return ajax.post({
    url: 'report/wxPushTask/savePushConfig',
    data: {
      examId: requestParams.examId,
      schools: requestParams.schools, // [{ isPublishWxMp: true, schoolId: string, schoolName: string }]
      subjects: requestParams.subjects, // [{examId: string, examSubjectId: string, showBlockScore: boolean,
      // showMarkComment: boolean, showMarker: boolean, showPaper: boolean, showRank: boolean, showScaleScore: boolean,
      // showUnion: boolean, subjectId: number(0) }]
      templateId: requestParams.templateId,
      templateName: requestParams.templateName,
    },
    requestName: '保存公众号推送配置',
  })
}

/**
 * 报表数据
 */
// 获取考试相关属性
export function apiGetExamAttributes(params) {
  return ajax.get({
    url: 'report/tem/examCalNum',
    params: {
      examId: params.examId,
      templateId: params.templateId,
    },
    requestName: '获取考试相关属性',
  })
}

// 考情概览
export function apiGetSubjectCompare(params) {
  return ajax.get({
    url: 'report/tem/subjCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      reportName: params.reportName,
      orgSchId: params.organizationId,
      subjectCategory:
        'subjectCategory' in params && !Number.isNaN(params.subjectCategory) ? params.subjectCategory : undefined, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取考情概览',
  })
}

// 学校成绩对比
export function apiGetSchoolCompare(params) {
  return ajax.get({
    url: 'report/tem/schCompare',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      subjectId: params.subjectId,
      templateId: params.templateId,
      reportName: params.reportName,
      orgSchId: params.organizationId,
    },
    requestName: '获取学校对比',
  })
}
export function apiGetSchoolCompareOfSeniorHigh(requestParams) {
  return ajax.get({
    url: 'report/tem/schCompare/v2',
    params: {
      examId: requestParams.examId, // String
      orgSchId: requestParams.organizationId, // String
      reportName: requestParams.reportName, // String
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
      subjectCode: requestParams.subjectCode, // String
      templateId: requestParams.templateId, // String
    },
    requestName: '获取高中学校成绩对比',
  })
}

// 班级成绩对比
export function apiGetClassCompare(params) {
  return ajax.get({
    url: 'report/tem/claCompare',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      subjectId: params.subjectId,
      schoolId: params.schoolId,
      templateId: params.templateId,
      reportName: params.reportName,
      orgSchId: params.organizationId,
    },
    requestName: '获取班级对比',
  })
}
export function apiGetClassCompareOfSeniorHigh(requestParams) {
  return ajax.get({
    url: 'report/tem/claCompare/v2',
    params: {
      examId: requestParams.examId, // String
      reportName: requestParams.reportName, // String
      schoolId: requestParams.schoolId, // String
      subjectCategory: requestParams.subjectCategory, // String
      subjectCode: requestParams.subjectCode, // String
      templateId: requestParams.templateId, // String
    },
    requestName: '获取高中班级成绩对比',
  })
}

// 小题分析
export function apiGetUnionQuestionAnalyse(params) {
  return ajax.get({
    url: 'report/tem/unionQuesAnalyse',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      orgSchId: params.organizationId,
      reportName: 'union_quesAnalyse',
    },
    requestName: '获取小题分析（联考）',
  })
}
export function apiGetSchoolQuestionAnalyse(params) {
  return ajax.get({
    url: 'report/tem/schQuesAnalyse',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      orgSchId: params.organizationId,
      reportName: 'sch_quesAnalyse',
    },
    requestName: '获取小题分析（校级）',
  })
}
export function apiGetClassQuestionAnalyse(params) {
  return ajax.get({
    url: 'report/tem/clsQuesAnalyse',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      classId: params.classId,
      orgSchId: params.organizationId,
      reportName: 'cls_quesAnalyse',
    },
    requestName: '获取小题分析（班级）',
  })
}
export function apiGetExpandQuestionAnalyse(requestParams) {
  return ajax.get({
    url: 'report/tem/quesScore',
    params: {
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      orgSchId: requestParams.organizationId,
      reportName: requestParams.reportName,
    },
    requestName: '获取小题分析（展开）',
  })
}

// export function apiGetUnionObjectiveAnalyse(params) { // deprecated 2024-6
//   return ajax.get({
//     url: 'report/tem/unionAnswer',
//     params: {
//       examId: params.examId,
//       examSubjectId: params.examSubjectId || null,
//       templateId: params.templateId,
//       schoolId: params.schoolId || null,
//       reportName: 'union_objAnalyse',
//       orgSchId: params.organizationId,
//     },
//     requestName: '获取客观题作答统计（联考）',
//   })
// }
export function apiGetUnionQuestionAnswerAnalyse(requestParams) {
  return ajax.post({
    url: 'report/tem/unionAnswer',
    params: {
      examId: requestParams.examId, // *String
      examSubjectId: requestParams.examSubjectId, // *String
      orgSchId: requestParams.organizationId, // *String
      templateId: requestParams.templateId, // *String
    },
    data: requestParams.customQuestionIntervals || undefined,
    /**
     * customQuestionIntervals,
     *  Array：
     *  [
     *    {
     *      branchCode: Number,
     *      questionCode: Number,
     *      ranges: Array[String]
     *    }
     *  ]
     */
    requestName: '联考作答统计分析',
  })
}

// export function apiGetSchoolObjectiveAnalyse(params) { // deprecated 2024-6
//   return ajax.get({
//     url: 'report/tem/schAnswer',
//     params: {
//       examId: params.examId,
//       examSubjectId: params.examSubjectId || null,
//       templateId: params.templateId,
//       schoolId: params.schoolId || null,
//       reportName: 'sch_objAnalyse',
//       orgSchId: params.organizationId,
//     },
//     requestName: '获取客观题作答统计（校级）',
//   })
// }
export function apiGetSchoolQuestionAnswerAnalyse(requestParams) {
  return ajax.post({
    url: 'report/tem/schAnswer',
    params: {
      examId: requestParams.examId, // *String
      examSubjectId: requestParams.examSubjectId, // *String
      orgSchId: requestParams.organizationId, // *String
      templateId: requestParams.templateId, // *String
      schoolId: requestParams.schoolId, // *String
    },
    data: requestParams.customQuestionIntervals || undefined,
    /**
     * customQuestionIntervals,
     *  Array：
     *  [
     *    {
     *      branchCode: Number,
     *      questionCode: Number,
     *      ranges: Array[String]
     *    }
     *  ]
     */
    requestName: '校级作答统计分析',
  })
}

// export function apiGetClassObjectiveAnalyse(params) { // deprecated 2024-6
//   return ajax.get({
//     url: 'report/tem/clsAnswer',
//     params: {
//       examId: params.examId,
//       examSubjectId: params.examSubjectId || null,
//       templateId: params.templateId,
//       schoolId: params.schoolId || null,
//       classId: params.classId || null,
//       reportName: 'cls_objAnalyse',
//       orgSchId: params.organizationId,
//     },
//     requestName: '获取客观题作答统计（班级）',
//   })
// }
export function apiGetClassQuestionAnswerAnalyse(requestParams) {
  return ajax.post({
    url: 'report/tem/clsAnswer',
    params: {
      examId: requestParams.examId, // *String
      examSubjectId: requestParams.examSubjectId, // *String
      orgSchId: requestParams.organizationId, // *String
      templateId: requestParams.templateId, // *String
      schoolId: requestParams.schoolId, // *String
      classId: requestParams.classId, // *String
    },
    data: requestParams.customQuestionIntervals || undefined,
    /**
     * customQuestionIntervals,
     *  Array：
     *  [
     *    {
     *      branchCode: Number,
     *      questionCode: Number,
     *      ranges: Array[String]
     *    }
     *  ]
     */
    requestName: '校级作答统计分析',
  })
}

// 知识点统计
export function apiGetKnowledge(params) {
  return ajax.get({
    url: 'report/tem/knowAnalyse',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId || null,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      reportName: params.reportName,
      orgSchId: params.organizationId,
    },
    requestName: '获取知识点统计',
  })
}

export function apiGetExpandKnowledge(requestParams) {
  return ajax.get({
    url: 'report/tem/knowAnalyse2',
    params: {
      classId: requestParams.classId || null,
      examId: requestParams.examId,
      examSubjectId: requestParams.examSubjectId || null,
      orgSchId: requestParams.organizationId,
      reportName: requestParams.reportName,
      schoolId: requestParams.schoolId || null,
      templateId: requestParams.templateId,
    },
    requestName: '获取知识点统计（展开）',
  })
}

// 分档上线 - 学科上线有效分
export function apiGetSubjectScoreLine(params) {
  return ajax.get({
    url: 'report/tem/subjScoreLine',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取学科上线有效分',
  })
}
// 分档上线 - 学科上线对比
export function apiGetSubjectLineCompare(params) {
  return ajax.get({
    url: 'report/tem/subjLineCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId || null,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取学科上线对比',
  })
}
// 分档上线 - 班级分档上线（校级报表）
export function apiGetClassTotalLineCompare(params) {
  return ajax.get({
    url: 'report/tem/clsTotalLineCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取班级分档上线',
  })
}
// 分档上线 - 班级学科上线对比（校级报表）
export function apiGetClassSubjectLineCompare(params) {
  return ajax.get({
    url: 'report/tem/clsSubjLineCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      schoolId: params.schoolId,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取班级学科上线分析',
  })
}

// 分档上线 - 联考学校分档上线对比
export function apiGetUnionTotalLineCompare(params) {
  return ajax.get({
    url: 'report/tem/unionTotalLineCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取联考学校分档上线对比',
  })
}

// 分档上线 - 联考学校学科上线对比
export function apiGetUnionSubjectLineCompare(params) {
  return ajax.get({
    url: 'report/tem/unionSubjLineCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      orgSchId: params.organizationId,
      subjectCategory: params.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取联考学校学科上线对比',
  })
}

// 大题分析
export function apiGetTopicAnalysis(params) {
  return ajax.get({
    url: 'report/tem/topicAnalyse',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      examSubjectId: params.examSubjectId || null,
      schoolId: params.schoolId || null,
      classId: params.classId || null,
      orgSchId: params.organizationId,
    },
    requestName: '获取大题分析',
  })
}

export function apiGetExpandTopicAnalysis(requestParams) {
  return ajax.get({
    url: 'report/tem/topicAnalyse2',
    params: {
      classId: requestParams.classId || null,
      schoolId: requestParams.schoolId || null,
      examSubjectId: requestParams.examSubjectId || null,
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
    },
    requestName: '获取大题分析（展开）',
  })
}

export function apiGetScoreGrades(params) {
  return ajax.get({
    url: 'report/tem/scoreLevels',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      classId: params.classId,
      orgSchId: params.organizationId,
      categoryId: params.categoryId, // Number
    },
    requestName: '获取成绩分档数据',
  })
}

export function apiGetScoreGradesCompare(params) {
  return ajax.get({
    url: 'report/tem/scoreLevelCompare',
    params: {
      examId: params.examId,
      templateId: params.templateId,
      examSubjectId: params.examSubjectId,
      schoolId: params.schoolId,
      orgSchId: params.organizationId,
      categoryId: params.categoryId, // Number
    },
    requestName: '获取成绩分档比较',
  })
}

export function apiGetCriticalExamCombinations(requestParams) {
  return ajax.get({
    url: 'report/tem/criticalStudent/categorySubjects',
    params: {
      templateId: requestParams.templateId, // String*
      schoolId: requestParams.schoolId, // String*
      classId: requestParams.classId, // String
    },
    requestName: '获取临界生统计查询大类科目列表',
  })
}

export function apiGetCriticalStudentCountByScore(requestParams) {
  return ajax.get({
    url: 'report/tem/criticalStudent/countByScore',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      floatingScore: requestParams.criticalValue, // 浮动分
      thresholdScore: requestParams.criticalLine, // 临界分
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取临界生统计（分数）',
  })
}

export function apiGetCriticalStudentCountByRank(requestParams) {
  return ajax.get({
    url: 'report/tem/criticalStudent/countByRank',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      floatingRank: requestParams.criticalValue,
      thresholdRank: requestParams.criticalLine,
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取临界生统计（名次）',
  })
}

export function apiGetCriticalStudentListByScore(requestParams) {
  return ajax.get({
    url: 'report/tem/criticalStudent/studentListByScore',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      floatingScore: requestParams.criticalValue,
      thresholdScore: requestParams.criticalLine,
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取临界生名单（分数）',
  })
}

export function apiGetCriticalStudentListByRank(requestParams) {
  return ajax.get({
    url: 'report/tem/criticalStudent/studentListByRank',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      examSubjectId: requestParams.examSubjectId,
      schoolId: requestParams.schoolId,
      classId: requestParams.classId,
      floatingRank: requestParams.criticalValue,
      thresholdRank: requestParams.criticalLine,
      subjectCategory: requestParams.subjectCategory, // Number(0 - 全体 | 1 - 历史类 | 2 - 物理类)
    },
    requestName: '获取临界生名单（名次）',
  })
}

// 科目组合对比
export function apiGetUnionSubjCombinations(requestParams) {
  return ajax.get({
    url: 'report/tem/unionSubjectCombination',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      reportName: 'union_schCompare',
    },
    requestName: '获取联考科目组合',
  })
}

export function apiGetSchoolSubjCombinationCompare(requestParams) {
  // 2024.9 设计了新的高中报表，暂时不使用该接口
  return ajax.get({
    url: 'report/tem/schSubjectCombinationCompare',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      orgSchId: requestParams.organizationId,
      subjectCode: requestParams.subjectCode,
      reportName: 'union_schCompare',
    },
    requestName: '获取学校科目组合成绩对比',
  })
}

export function apiGetSchoolSubjCombinations(requestParams) {
  return ajax.get({
    url: 'report/tem/schoolSubjectCombination',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      // reportName: requestParams.reportName,
      reportName: 'sch_clsCompare',
    },
    requestName: '获取学校科目组合',
  })
}

export function apiGetClassSubjCombinationCompare(requestParams) {
  return ajax.get({
    url: 'report/tem/claSubjectCombinationCompare',
    params: {
      examId: requestParams.examId,
      templateId: requestParams.templateId,
      schoolId: requestParams.schoolId,
      subjectCode: requestParams.subjectCode,
      // reportName: requestParams.reportName,
      reportName: 'sch_clsCompare',
    },
    requestName: '获取班级科目组合成绩对比',
  })
}

/* 手阅测试 */
export function apiGetTestReportStatus(requestParams) {
  return ajax.get({
    url: 'report/tem/getReportStatus',
    params: {
      examId: requestParams.examId,
    },
    requestName: '获取手阅测试报告的状态',
  })
}

/* 报表配置 - 不参与统计学校 */
export function apiAddNotStatSchools(requestParams) {
  // 被 apiSetReportTemplateNonCountingSchools 替代
  return ajax.post({
    url: 'report/tem/addNotStatSchool',
    params: {
      templateId: requestParams.templateId,
    },
    data: requestParams.schoolIds,
    requestName: '报表配置，新增不参与统计的学校',
  })
}
export function apiGetNotStatSchools(templateId) {
  // 被 apiGetReportTemplateNonCountingSchools 替代
  return ajax.get({
    url: 'report/tem/getNotStatSchool',
    params: {
      templateId: templateId,
    },
    requestName: '报表配置，获取不参与统计学校列表',
  })
}
export function apiDeleteNotStatSchools(requestParams) {
  // 被 apiRemoveReportTemplateNonContingStudents 替代
  return ajax.delete({
    url: 'report/tem/delNotStatSchool',
    params: {
      templateId: requestParams.templateId,
    },
    data: requestParams.schoolIds,
    requestName: '报表配置，删除不参与统计的学校',
  })
}

export function apiChangeTemplateAdminOnlyStatus(requestParams) {
  return ajax.put({
    url: 'report/tem/updateIsShow',
    params: {
      isShow: requestParams.isShow,
      templateId: requestParams.templateId,
    },
    requestName: '切换报表模板是否仅考试管理员可见状态',
  })
}

export function apiModifyReportTemplateName(objectIdName) {
  return ajax.put({
    url: 'report/tem/updateReportName',
    data: objectIdName,
    requestName: '修改报表模板名称',
  })
}

/**
 * 数据大屏发展分析（机构）
 */
export function apiGetDevelopAnalysisEduSchool({ semesterId, term, gradeId, schoolId }) {
  return ajax.get({
    url: 'report/tem/developAnalysis',
    params: {
      semesterId,
      term,
      gradeId,
      eduSchoolId: schoolId,
    },
    requestName: '获取发展分析',
  })
}

/**
 * 数据大屏发展分析（学校）
 */
export function apiGetDevelopAnalysisSchool({ semesterId, term, gradeId, schoolId }) {
  return ajax.get({
    url: 'report/tem/schoolDevelopAnalysis',
    params: {
      semesterId,
      term,
      gradeId,
      schoolId,
    },
    requestName: '获取发展分析',
  })
}
