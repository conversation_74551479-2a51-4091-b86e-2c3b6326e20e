import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import StepStatusEnum from '@/enum/qlib/upload_batch/step_status'

export const useUploadBatchStore = defineStore('upload-batch', () => {
  // 选择的文件
  const files = ref([])
  function changeFiles(wordFiles) {
    files.value = wordFiles.map((file, idx) => ({
      file,
      order: idx + 1,
      skiped: false,
      uploadStatus: StepStatusEnum.Ready.id,
      uploadFailReason: '',
      paperId: '',
      paperHTML: '',
      handlingPaperHTML: false,
      paper: null,
      saveQuestionsStatus: StepStatusEnum.Ready.id,
      saveQuestionsFailReason: '',
      messages: [],
    }))
  }
  const succeededFileCount = computed(() => {
    return files.value.filter(file => file.saveQuestionsStatus == StepStatusEnum.Succeeded.id).length
  })

  // 当前文件
  const currentFileIndex = ref(-1)
  const currentFile = computed(() => files.value[currentFileIndex.value])
  const unStarted = computed(() => {
    return currentFileIndex.value == -1
  })
  const finished = computed(() => {
    return files.value.length > 0 && currentFileIndex.value >= files.value.length
  })
  // 切换当前文件提示
  const changeCurrentFileTip = computed(() => {
    if (!currentFile.value) {
      return { error: '', warning: '' }
    }
    let { uploadStatus, handlingPaperHTML, paper, saveQuestionsStatus } = currentFile.value
    if (uploadStatus == StepStatusEnum.Processing.id) {
      return {
        error: '当前文件正在上传中',
      }
    }
    if (handlingPaperHTML) {
      return {
        error: '当前文件正在处理试卷内容中',
      }
    }
    if (saveQuestionsStatus == StepStatusEnum.Processing.id) {
      return {
        error: '当前试卷正在保存题目中',
      }
    }
    if (paper && paper.questionList.length > 0 && saveQuestionsStatus != StepStatusEnum.Succeeded.id) {
      return {
        warning: '当前试卷题目未保存',
      }
    }
    return { error: '', warning: '' }
  })
  // 更改当前文件
  function changeCurrentFileIndex(index) {
    if (changeCurrentFileTip.value.error) {
      return
    }
    // 不能略过未执行的文件
    for (let i = 0; i < index - 1; i++) {
      let preFile = files.value[i]
      let { skiped, uploadStatus, saveQuestionsStatus } = preFile
      if (
        !skiped &&
        (uploadStatus != StepStatusEnum.Succeeded.id || saveQuestionsStatus != StepStatusEnum.Succeeded.id)
      ) {
        return
      }
    }
    currentFileIndex.value = index
    // 清空试卷
    files.value.forEach(file => {
      file.paperHTML = ''
      file.paper = null
    })
  }
  // 当前文件完成进入下一个文件
  function nextFile() {
    if (currentFile.value?.saveQuestionsStatus != StepStatusEnum.Succeeded.id) {
      return
    }
    changeCurrentFileIndex(currentFileIndex.value + 1)
  }
  // 跳过当前文件
  function skipCurrentFile() {
    if (!currentFile.value) {
      return
    }
    if (currentFile.value.saveQuestionsStatus == StepStatusEnum.Succeeded.id) {
      return
    }
    currentFile.value.skiped = true
    changeCurrentFileIndex(currentFileIndex.value + 1)
  }

  // 上传文件
  function setCurrentFileUploading() {
    if (!currentFile.value) {
      return
    }
    currentFile.value.uploadStatus = StepStatusEnum.Processing.id
  }
  function setCurrentFileUploadSucceeded(paperId) {
    if (!currentFile.value) {
      return
    }
    currentFile.value.uploadStatus = StepStatusEnum.Succeeded.id
    currentFile.value.uploadFailReason = ''
    currentFile.value.paperId = paperId
  }
  function setCurrentFileUploadFailed(reason) {
    if (!currentFile.value) {
      return
    }
    currentFile.value.uploadStatus = StepStatusEnum.Failed.id
    currentFile.value.uploadFailReason = reason || ''
  }

  // 设置试卷
  function setCurrentFilePaper(data) {
    if (!currentFile.value) {
      return
    }
    currentFile.value.paperHTML = data.paperHTML
    currentFile.value.paper = data.paper
    if (data.paperHTML) {
      currentFile.value.uploadStatus = StepStatusEnum.Succeeded.id
      currentFile.value.uploadFailReason = ''
    }
    if (data.paper.questionList.length > 0) {
      currentFile.value.saveQuestionsStatus = StepStatusEnum.Succeeded.id
      currentFile.value.saveQuestionsFailReason = ''
    }
  }

  // 设置处理试卷内容中
  function setCurrentFileHandlingPaperHTML(value) {
    if (!currentFile.value) {
      return
    }
    currentFile.value.handlingPaperHTML = value
  }

  // 保存题目
  function setCurrentFileSavingQuestions() {
    if (!currentFile.value) {
      return
    }
    currentFile.value.saveQuestionsStatus = StepStatusEnum.Processing.id
  }
  function setCurrentFileSaveQuestionsSucceeded() {
    if (!currentFile.value) {
      return
    }
    currentFile.value.saveQuestionsStatus = StepStatusEnum.Succeeded.id
    currentFile.value.saveQuestionsFailReason = ''
    currentFile.value.skiped = false
  }
  function setCurrentFileSaveQuestionsFailed(reason) {
    if (!currentFile.value) {
      return
    }
    currentFile.value.saveQuestionsStatus = StepStatusEnum.Failed.id
    currentFile.value.saveQuestionsFailReason = reason || ''
  }

  // 执行消息
  function addCurrentFileMessage(message, type = 'info') {
    if (!currentFile.value) {
      return
    }
    currentFile.value.messages.push({
      message,
      type,
      time: new Date(),
    })
  }

  return {
    files,
    changeFiles,

    succeededFileCount,
    currentFileIndex,
    currentFile,
    unStarted,
    finished,
    changeCurrentFileIndex,
    changeCurrentFileTip,
    nextFile,
    skipCurrentFile,

    setCurrentFileUploading,
    setCurrentFileUploadSucceeded,
    setCurrentFileUploadFailed,

    setCurrentFilePaper,
    setCurrentFileHandlingPaperHTML,

    setCurrentFileSavingQuestions,
    setCurrentFileSaveQuestionsSucceeded,
    setCurrentFileSaveQuestionsFailed,

    addCurrentFileMessage,
  }
})
