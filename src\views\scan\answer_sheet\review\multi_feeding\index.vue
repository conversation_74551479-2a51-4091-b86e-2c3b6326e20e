<template>
  <div class="box-btn-refresh">
    <TextButton icon="md-refresh" type="primary" @click="onRefresh(true)">刷新</TextButton>
  </div>
  <div ref="container" class="container-evaluation-multi-feeding">
    <statistic :stat-data="statData" @on-scroll-to="onToDetail" />

    <ScanBatch ref="scanBatch" :stat-data="statData" @show-batch-detail="onShowBatchDetail" @on-refresh="onRefresh" />

    <div ref="wrapperCheckResult">
      <CheckResult
        ref="checkResult"
        :stat-data="statData"
        :selected-batch-id="selectedBatchId"
        :selected-status="selectedStatus"
        @on-refresh="onRefresh"
      />
    </div>
  </div>
</template>

<script>
  import statistic from './components/statistic.vue'
  import ScanBatch from './components/scan_batch.vue'
  import CheckResult from './components/check_result.vue'

  import { mapGetters } from 'vuex'
  import { apiGetMultiFeedingStat } from '@/api/scan/multi_feeding'

  export default {
    components: {
      statistic,
      ScanBatch,
      CheckResult,
    },
    inheritAttrs: false,
    data() {
      return {
        statData: {},
        selectedBatchId: '',
        selectedStatus: '',
        timer: null,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'viewMode']),
    },
    watch: {
      viewMode: {
        handler: function (newVal) {
          if (newVal) {
            this.fetchStat()
          }
        },
        deep: true,
      },
    },
    created() {
      this.fetchStat()
      this.timer = setInterval(() => {
        this.onRefresh()
      }, 60000)
    },
    beforeUnmount() {
      if (this.timer) clearInterval(this.timer)
    },
    methods: {
      onRefresh(showMessageSuccess = false) {
        Promise.all([this.fetchStat(), this.$refs.scanBatch.onRefresh(), this.$refs.checkResult.onRefresh()]).then(
          () => {
            if (showMessageSuccess) {
              this.$Message.success('已刷新')
            }
          }
        )
      },
      fetchStat() {
        const { examSubjectId, viewMode } = this

        return apiGetMultiFeedingStat({
          scanStationId: viewMode.scanStationId,
          examSubjectId,
        }).then(res => {
          this.statData = res
        })
      },
      onShowBatchDetail(data) {
        this.selectedBatchId = data
        this.scrollToCheckResult()
      },
      onToDetail(status) {
        this.selectedStatus = status
        this.scrollToCheckResult()
      },
      scrollToCheckResult() {
        let el = this.$refs.wrapperCheckResult
        let to = el.getBoundingClientRect().top - 87

        this.$ScrollTop(this.$refs.container, {
          to,
          time: 800,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .box-btn-refresh {
    position: absolute;
    top: 43px;
    right: 20px;
  }

  .container-evaluation-multi-feeding {
    padding: 16px 20px 20px;
    overflow: auto;

    :deep(.section-scan-batch) {
      .header {
        @include flex(row, space-between, center);
        margin-bottom: 10px;
      }

      .header-title {
        position: relative;
        padding-left: 10px;
        font-size: 16px;

        &::after {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 3px;
          height: 16px;
          margin: auto;
          background-color: $color-primary;
          content: '';
        }
      }

      .box-select {
        margin-left: 60px;
      }

      .box-page {
        @include flex(row, flex-end, center);
        padding: 15px 0 0;

        .summary {
          margin-right: 10px;
        }
      }
    }
  }
</style>
