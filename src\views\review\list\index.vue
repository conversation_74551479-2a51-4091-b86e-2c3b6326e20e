<script>
  import ReviewList from '../components/review_list.vue'
  import ReviewTask from '../components/reivew_task.vue'
  import PageCache from '@/utils/page_cache'

  const uniquePageName = 'review-home'

  export default {
    components: {
      ReviewList,
      ReviewTask,
    },

    data() {
      return {
        activeTab: 'review-list',
      }
    },

    computed: {
      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },
    },

    created() {
      let pageCacheData = PageCache.fetch(uniquePageName)
      if (pageCacheData) {
        this.activeTab = pageCacheData.activeTab
      }
    },

    beforeUnmount() {
      PageCache.save(uniquePageName, {
        activeTab: this.activeTab,
      })
    },

    methods: {
      onToCreate() {
        this.$router.push({
          name: 'review_create',
        })
      },

      onTabClick(tab) {
        this.activeTab = tab
      },
    },
  }
</script>

<template>
  <div class="page-review-home">
    <div class="title-bar">
      <div class="title">
        <div
          :class="['tab-item', activeTab === 'review-list' ? 'tab-item-active' : '']"
          @click="onTabClick('review-list')"
        >
          评审活动
        </div>
        <div
          :class="['tab-item', activeTab === 'review-task' ? 'tab-item-active' : '']"
          @click="onTabClick('review-task')"
        >
          评审任务
        </div>
      </div>
      <div class="operate-btns">
        <Button
          v-show="isBureauInstitution"
          class="btn-success btn-normal-height"
          type="primary"
          icon="md-add"
          ghost
          @click="onToCreate"
          >创建评审活动</Button
        >
      </div>
    </div>

    <template v-if="activeTab === 'review-list'">
      <ReviewList />
    </template>

    <template v-else>
      <ReviewTask />
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .page-review-home {
    padding: 25px;
    padding-top: 10px;
    background-color: #fff;

    .title-bar {
      @include flex(row, space-between, center);
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dfdfdf;

      .title {
        @include flex(row, flex-start, center);
        flex: 1;
        font-size: $font-size-medium-x;

        .tab-item {
          position: relative;
          cursor: pointer;

          &:not(:last-child) {
            margin-right: 30px;
          }
        }

        .tab-item-active {
          &::after {
            position: absolute;
            right: 0;
            bottom: -12px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: $color-primary;
            content: '';
          }
        }
      }

      .operate-btns {
        flex: none;

        .btn-normal-height {
          height: 28px;
          font-size: 14px;
        }

        .btn-success {
          border-color: #13ce66;
          color: #13ce66;
          background-color: #e8faf0;
        }
      }
    }

    .filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 16px;

      .keyword-serach {
        width: 250px;
      }
    }
  }
</style>
