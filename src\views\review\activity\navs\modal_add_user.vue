<template>
  <Modal :model-value="modelValue" title="添加人员" @on-visible-change="handleChangeVisibility">
    <Form>
      <FormItem label="角色">
        <RadioGroup v-model="role" type="button" button-style="solid">
          <Radio v-for="r of roleList" :key="r.id" :label="r.id">{{ r.name }}</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="showCategory" label="类别">
        <Select v-model="categoryId" class="select-category">
          <Option v-for="c of categoryList" :key="c.id" :value="c.id">{{ c.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="学校">
        <Select
          v-model="schoolId"
          class="select-school"
          :disabled="!isMultipleSchool || !isAdmin"
          placeholder="请选择学校"
        >
          <Option v-for="s of schoolListForSelect" :key="s.id" :value="s.id">{{ s.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="姓名">
        <Select v-model="teacherIds" class="select-teacher" filterable multiple clearable placeholder="请选择教师">
          <Option v-for="t of teacherList" :key="t.userId" :value="t.userId">{{ t.realName }}</Option>
        </Select>
      </FormItem>
    </Form>
    <template #footer>
      <div class="modal-footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK(false)">确定</Button>
        <Button type="primary" @click="handleOK(true)">确定并关闭</Button>
      </div>
    </template>
  </Modal>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import store from '@/store/index'
  import { Message } from 'view-ui-plus'

  import { apiGetSchoolTeachers } from '@/api/emarking'
  import { addActivityUsers } from '@/api/review/activity'

  import ActivityUserRoleEnum from '@/enum/review/activity_user_role'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue', 'on-add-users'])

  const activityId = computed(() => {
    return store.getters['review/activityId']
  })
  const categories = computed(() => {
    return store.getters['review/categories']
  })
  const schoolList = computed(() => {
    return store.getters['review/schoolList']
  })
  const activitySchoolId = computed(() => {
    return store.getters['review/currentActivity']?.schoolId
  })
  const activitySchoolName = computed(() => {
    return store.getters['review/currentActivity']?.schoolName
  })
  const isMultipleSchool = computed(() => {
    return store.getters['review/isMultipleSchool']
  })
  const isAdmin = computed(() => {
    return store.getters['review/isAdmin']
  })
  const isSchoolAdmin = computed(() => {
    return store.getters['review/isSchoolAdmin']
  })
  const userSchoolId = computed(() => {
    return store.getters['user/info'].schoolId
  })
  const userIsSystem = computed(() => {
    return store.getters['user/info'].isSystem
  })

  // 角色
  const role = ref('')
  const roleList = computed(() => {
    // 多校，评审管理员可加：评审管理员、报名审核员、学校负责人
    if (isMultipleSchool.value && isAdmin.value) {
      return [
        ActivityUserRoleEnum.Admin,
        ActivityUserRoleEnum.RegisterAuditor,
        ActivityUserRoleEnum.SchoolPersonInCharge,
      ]
    }
    // 多校，学校管理员可加：学校负责人
    else if (isMultipleSchool.value && isSchoolAdmin.value) {
      return [ActivityUserRoleEnum.SchoolPersonInCharge]
    }
    // 单校，评审管理员可加：评审管理员、报名审核员
    else if (!isMultipleSchool.value && isAdmin.value) {
      return [ActivityUserRoleEnum.Admin, ActivityUserRoleEnum.RegisterAuditor]
    }
    return []
  })

  // 类别
  const categoryId = ref('all')
  const categoryList = computed(() => {
    return [{ id: 'all', name: '不限类别' }, ...categories.value]
  })
  const showCategory = computed(() => {
    return [ActivityUserRoleEnum.RegisterAuditor.id].includes(role.value)
  })

  // 学校
  const schoolId = ref('')
  const schoolListForSelect = computed(() => {
    let list = schoolList.value.slice()
    // 评审管理员可选所有评审学校及评审创建学校
    if (isAdmin.value) {
      if (list.every(s => s.id != activitySchoolId.value)) {
        list.unshift({
          id: activitySchoolId.value,
          name: activitySchoolName.value,
        })
      }
      return list
    }
    // 学校管理员可选本校
    else if (isSchoolAdmin.value) {
      return list.filter(s => s.id == userSchoolId.value)
    } else {
      return []
    }
  })
  // 学校变化时，清空学校教师和已选教师，重新加载
  watch(schoolId, () => {
    teacherIds.value = []
    teacherList.value = []
    loadSelectedSchoolTeachers()
  })

  // 已选教师
  const teacherIds = ref([])
  // 学校教师列表
  const teacherList = ref([])
  async function loadSelectedSchoolTeachers() {
    if (!schoolId.value) {
      teacherIds.value = []
      teacherList.value = []
      return
    }
    let requestSchoolId = schoolId.value
    let teachers = await apiGetSchoolTeachers({
      schoolId: requestSchoolId,
    })
    // 系统内置人员才可见系统内置人员
    if (!userIsSystem.value) {
      teachers = teachers.filter(t => !t.isSystem)
    }
    // 若请求过程中切换学校，请求数据作废
    if (requestSchoolId == schoolId.value) {
      teacherList.value = teachers
    }
  }

  // 取消
  function handleCancel() {
    emit('update:modelValue', false)
  }
  // 关闭弹窗
  function handleChangeVisibility(visible) {
    if (!visible) {
      handleCancel()
    }
  }

  // 确定
  async function handleOK(close) {
    let params
    try {
      params = checkParams()
    } catch (e) {
      Message.warning({
        content: e,
        duration: 3,
      })
      return
    }

    try {
      await addActivityUsers(params)
      Message.success('已添加')
    } finally {
      emit('on-add-users')
    }

    if (close) {
      handleCancel()
    } else {
      teacherIds.value = []
      // 多校评审管理员，重置所选学校
      if (isAdmin.value && isMultipleSchool.value) {
        schoolId.value = ''
        teacherList.value = []
      }
    }
  }
  function checkParams() {
    if (!role.value) {
      throw '请选择角色'
    }
    // 仅报名审核员可选类别
    if (role.value != ActivityUserRoleEnum.RegisterAuditor.id) {
      categoryId.value = 'all'
    }
    if (!teacherIds.value.length) {
      throw '请选择教师'
    }
    return {
      activityId: activityId.value,
      users: teacherIds.value.map(id => ({
        role: role.value,
        categoryId: categoryId.value == 'all' ? '' : categoryId.value,
        userId: id,
        identity: 'teacher',
      })),
    }
  }

  // 显示弹窗时初始化
  watch(
    () => props.modelValue,
    visible => {
      if (visible) {
        initialize()
      }
    }
  )
  function initialize() {
    if (roleList.value.length == 1) {
      role.value = roleList.value[0].id
    } else {
      role.value = ''
    }
    categoryId.value = 'all'
    schoolId.value = ''
    teacherIds.value = []
    teacherList.value = []
    // 多校，学校管理员只能加学校负责人
    if (isMultipleSchool.value && !isAdmin.value) {
      role.value = ActivityUserRoleEnum.SchoolPersonInCharge.id
      schoolId.value = userSchoolId.value
    }
    // 单校，学校固定为本校
    else if (!isMultipleSchool.value) {
      schoolId.value = userSchoolId.value
    }
  }
</script>

<style lang="scss" scoped>
  .select-category,
  .select-school,
  .select-teacher {
    width: 400px;
  }
</style>
