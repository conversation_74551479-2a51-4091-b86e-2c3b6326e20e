<template>
  <Drawer
    :transfer="false"
    inner
    :model-value="props.show"
    :title="modalTitle"
    width="100%"
    :mask="false"
    @on-visible-change="handleVisibleChange"
  >
    <div class="drawer-inner">
      <div class="drawer-body">
        <QuestionEdit v-if="questionCopy" :question="questionCopy"></QuestionEdit>
      </div>

      <div class="drawer-footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK">确定</Button>
      </div>
    </div>
  </Drawer>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import iView from '@/iview'
  import QuestionEdit from '../../components/question/question_edit.vue'
  import { Question } from '@/helpers/qlib/question'

  const props = defineProps({
    show: Boolean,
    question: Object,
  })
  const emits = defineEmits(['change', 'cancel'])
  const questionCopy = ref(null)

  const modalTitle = computed(() => {
    if (props.question == null) {
      return '编辑题目'
    }
    return `编辑题目【${props.question.getQuestionCodeString()}题】`
  })

  watch(
    () => props.show,
    () => {
      if (props.show) {
        questionCopy.value = Question.copy(props.question)
      } else {
        setTimeout(() => {
          questionCopy.value = null
        }, 500)
      }
    }
  )

  function handleOK() {
    let checkResult = questionCopy.value.checkIntegrity({ noKnowledge: true })
    if (checkResult) {
      iView.Message.warning({
        content: checkResult,
      })
      return
    }
    emits('change', questionCopy.value)
  }
  function handleCancel() {
    emits('cancel')
  }
  function handleVisibleChange(visibility) {
    if (!visibility) {
      handleCancel()
    }
  }
</script>

<style lang="scss" scoped>
  .drawer-inner {
    @include flex(column, space-between, stretch);
    height: 100%;
    margin-right: -16px;
    margin-left: -16px;

    .drawer-body {
      flex-grow: 1;
      padding-right: 16px;
      padding-left: 16px;
      overflow: auto;
    }

    .drawer-footer {
      @include flex(row, flex-end, center);
      flex-shrink: 0;
      margin-top: 16px;
      padding-top: 16px;
      padding-right: 16px;
      padding-left: 16px;
      border-top: 1px solid $color-border;
      column-gap: 8px;
    }
  }
</style>
