<template>
  <Modal
    v-model="modelValue"
    class="container-modal-recent-exam"
    title="最新动态"
    :width="800"
    footer-hide
    style="top: 60px"
  >
    <div class="content-modal">
      <div class="section-selector">
        <span
          v-for="tab in tabList"
          :key="tab.value"
          :class="['tab-item', tab.value === activeTab ? 'active' : '']"
          @click="handleTabClick(tab.value)"
          >{{ tab.name }}</span
        >
      </div>
      <div class="section-list">
        <template v-if="showNormalAndUnionExam">
          <div class="box-list-header">
            <div class="item-school">
              {{ activeTab === 'normal' ? '学校' : activeTab === 'union' ? '机构' : '学校/机构' }}
            </div>
            <div class="item-grade">年级</div>
            <div class="item-name">考试</div>
            <div class="item-time">创建时间</div>
            <div class="item-time item-time-exam">考试时间</div>
          </div>
          <div v-if="list.length" class="box-list-body">
            <div v-for="item in list" :key="item.examId" class="list-item" :class="{ 'list-item-odd': item.isOdd }">
              <div class="item-school" :title="item.schoolName">{{ item.schoolName }}</div>
              <div class="item-grade" :title="item.grade?.name">{{ item.grade?.name }}</div>
              <div class="item-name" :title="item.examName">{{ item.examName }}</div>
              <div class="item-time" :title="item.createTimeTitle">{{ item.createTimeTitle }}</div>
              <div class="item-time item-time-exam" :title="`${item.beginTimeStr} 至 ${item.endTimeStr}`">
                {{ item.beginTimeStr }} 至 {{ item.endTimeStr }}
              </div>
            </div>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </template>
        <template v-else>
          <div class="box-list-header">
            <div class="item-school">学校/机构</div>
            <div class="item-name">考试</div>
            <div class="item-grade">在评人数</div>
            <div class="item-time">最近评卷</div>
          </div>
          <div v-if="list.length" class="box-list-body">
            <div v-for="item in list" :key="item.examId" class="list-item" :class="{ 'list-item-odd': item.isOdd }">
              <div class="item-school" :title="item.schoolName">{{ item.schoolName }}</div>
              <div class="item-name" :title="item.examName">{{ item.examName }}</div>
              <div class="item-grade" :title="item.userCount">{{ item.userCount }}</div>
              <div class="item-time" :title="item.recentMarkingTimeTitle">{{ item.recentMarkingTimeTitle }}</div>
            </div>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </template>
      </div>
    </div>
  </Modal>
</template>

<script setup>
  import { computed } from 'vue'

  const modelValue = defineModel({ type: Boolean, default: false })
  const props = defineProps({
    tabList: Array,
    activeTab: String,
    list: {
      type: Array,
      default: () => [],
    },
  })
  const emits = defineEmits(['on-change-tab'])

  const showNormalAndUnionExam = computed(() => {
    return ['normal', 'union'].includes(props.activeTab)
  })

  function handleTabClick(val) {
    emits('on-change-tab', val)
  }
</script>

<style lang="scss">
  .container-modal-recent-exam {
    box-shadow: 0 0 6px 1px #fff;

    .ivu-modal-content {
      background-color: #03064f;
    }

    .ivu-modal-body {
      padding: 15px 0 0;
    }

    .ivu-modal-header-inner {
      color: #fff;
    }

    .ivu-modal-close .ivu-icon-ios-close:hover {
      color: #fff;
    }

    .section-selector {
      // margin-bottom: 15px;
    }

    .tab-item {
      display: inline-block;
      min-width: 50px;
      height: 26px;
      padding: 0 10px;
      border: 1px solid #999;
      border-bottom: none;
      color: #fff;
      font-size: 13px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;

      &:not(:first-child) {
        margin-left: 6px;
      }

      &.active {
        border-color: #0069ea;
        background-color: #0069ea;
      }
    }

    .no-data {
      @include flex(row, center, center);
      width: 100%;
      min-height: 400px;
      color: #fff;
      font-size: 14px;
    }

    .section-list {
      @include flex(column, flex-start, flex-start);
      flex-grow: 1;
      width: 100%;
      border: 1px solid #999;
      border-top: none;
      overflow: hidden;

      ::-webkit-scrollbar {
        display: none;
      }

      .box-list-header {
        @include flex(row, flex-start, center);
        flex-shrink: 0;
        width: 100%;
        height: 30px;
        // border-bottom: 1px solid #999;
        color: #fff;
        font-size: 14px;
        background-color: #0069ea;
      }

      .box-list-body {
        width: 100%;
        height: 70vh;
        overflow: auto;
      }

      .list-item {
        @include flex(row, flex-start, center);
        height: 30px;
        color: #fff;
        font-size: 12px;
        background-color: transparent;
        user-select: none;
      }

      .list-item-odd {
        background-color: #0b5481;
      }

      .item-school {
        width: 160px;
        padding-left: 10px;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
      }

      .item-name {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
      }

      .item-grade {
        width: 60px;
        text-align: center;
      }

      .item-time {
        width: 120px;
        text-align: center;
      }

      .item-time-exam {
        width: 160px;
      }
    }
  }
</style>
