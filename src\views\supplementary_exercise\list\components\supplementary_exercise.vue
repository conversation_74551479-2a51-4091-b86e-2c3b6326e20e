<script>
  import NoData from '@/components/no_data'
  import SearchInput from '@/components/searchInput.vue'
  import { ElProgress } from 'element-plus'
  import 'element-plus/es/components/progress/style/css'

  import { apiTeacherGetClassList } from '@/api/user/class'
  import { apiGetTeachingGradeSubjectCoachBooks } from '@/api/qlib/coach_book'
  import { apiGetCoachHomeworkList } from '@/api/emarking'

  import { roundNumber } from '@/utils/math'

  import UploadModeEnum from '@/enum/emarking/upload_mode'

  export default {
    components: {
      'search-input': SearchInput,
      'no-data': NoData,
      'el-progress': ElProgress,
    },

    data() {
      return {
        // 选中学年学期名称
        currentTermValue: '',
        // 个人任教班级
        classList: [],
        // 选中班级
        currentClassId: '',
        // 选中学年学期任教相关教辅
        coachBooks: [],
        // 选中教辅
        currentCoachBookId: '',
        // 选中上传模式
        currentUploadModeId: '',
        // 关键词
        keyword: '',

        // 分页
        pageSize: 10,
        currentPage: 1,

        // 教辅作业项目
        exerciseList: [],
        total: 1,
        loading: true,
      }
    },

    computed: {
      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },
      terms() {
        return this.$store.getters['emarking/terms']() || []
      },
      selectedTerm() {
        let _term = null

        if (this.terms && this.terms.length) {
          _term = this.terms.find(item => item.termName === this.currentTermValue) || null
        }

        return _term
      },
      selectedClass() {
        return this.classList.find(cls => cls.classId == this.currentClassId)
      },
      filterCoachBooks() {
        if (this.selectedClass) {
          return this.coachBooks.filter(book => book.gradeId == this.selectedClass.gradeId)
        }
        return this.coachBooks
      },
      uploadModes() {
        return [
          {
            id: UploadModeEnum.PostScan.id,
            name: '先阅后扫',
          },
          {
            id: UploadModeEnum.Scan.id,
            name: '线上阅卷',
          },
        ]
      },
    },

    created() {
      if (this.terms.length) {
        this.currentTermValue = this.terms[0].termName
      }

      this.fetchTermClasses()
      this.fetchCoachBooks()
      this.fetchSupplementaryExerciseList()
    },

    methods: {
      getCustomColor(percentage) {
        if (percentage > 0 && percentage < 100) {
          return '#1890FF'
        } else {
          return '#19be6b'
        }
      },

      /**
       * router
       */
      // 进入扫描页面
      gotoScanPage({ exam, mode }) {
        let examId
        let examSubjectId
        if (exam) {
          examId = exam.examId
          examSubjectId = exam.examSubjectId
        } else if (mode == 'online') {
          examId = 'newonline'
          examSubjectId = 'newonline'
        } else {
          examId = 'newpostscan'
          examSubjectId = 'newpostscan'
        }

        this.$router.push({
          name: 'scan-supplementary-exercise',
          params: {
            examId,
            examSubjectId,
          },
        })
      },
      // 进入设置页面
      gotoSettingPage({ examId, examSubjectId }) {
        this.$router.push({
          name: 'supplementaryExerciseSetting',
          params: {
            examId,
            examSubjectId,
          },
        })
      },
      // 进入批改页面
      gotoMarkPage(e) {
        this.$router.push({
          name: 'emarking-subject-index',
          params: {
            examSubjectId: e.examSubjectId,
            isFromHomePage: 0,
          },
        })
      },
      // 进入报表页面
      gotoReportPage(e) {
        this.$router.push({
          name: 'supplementaryExerciseReport',
          params: {
            examId: e.examId,
          },
        })
      },
      // 点击进度条进监控
      handleProgressMarkingClick(exercise) {
        if (!this.isSchoolAdministrator) {
          return
        }

        const RouteData = this.$router.resolve({
          name: 'emarking-monitor',
          params: {
            examId: exercise.examId,
          },
        })
        window.open(RouteData.href, '_blank')
      },

      /**
       * component event
       */
      // 选择学期
      onChangeTerm() {
        Promise.all([this.fetchTermClasses(), this.fetchCoachBooks()]).then(() => {
          if (this.classList.every(cls => cls.classId != this.currentClassId)) {
            this.currentClassId = ''
          }
          if (this.filterCoachBooks.every(book => book.id != this.currentCoachBookId)) {
            this.currentCoachBookId = ''
          }
          this.onChangePage()
        })
      },
      // 选择班级
      onChangeClass() {
        if (this.filterCoachBooks.every(book => book.id != this.currentCoachBookId)) {
          this.currentCoachBookId = ''
        }
        this.onChangePage()
      },
      // 选择教辅
      onChangeCoachBook() {
        this.onChangePage()
      },
      // 选择阅卷模式
      onChangeUploadMode() {
        this.onChangePage()
      },
      // 修改关键词搜索
      onChangeKeyword() {
        this.onChangePage()
      },
      onChangePage(page = 1) {
        this.currentPage = page
        this.fetchSupplementaryExerciseList()
      },

      /**
       * fetch
       */
      // 查任教班级
      fetchTermClasses() {
        if (!this.selectedTerm) {
          return Promise.resolve()
        }
        return apiTeacherGetClassList(this.selectedTerm).then(classList => (this.classList = classList || []))
      },
      // 查相关教辅
      fetchCoachBooks() {
        if (!this.selectedTerm) {
          return Promise.resolve()
        }
        return apiGetTeachingGradeSubjectCoachBooks(this.selectedTerm).then(
          coachBooks => (this.coachBooks = coachBooks || [])
        )
      },
      // 查教辅作业项目
      async fetchSupplementaryExerciseList() {
        if (!this.selectedTerm) {
          return
        }

        this.loading = true
        let res
        try {
          res = await apiGetCoachHomeworkList({
            semesterId: this.selectedTerm.semesterId,
            termId: this.selectedTerm.term,
            classId: this.currentClassId,
            bookId: this.currentCoachBookId,
            uploadMode: this.currentUploadModeId,
            keyword: this.keyword,
            currentPage: this.currentPage,
            pageSize: this.pageSize,
          })
        } catch (err) {
          this.exerciseList = []
          this.total = 0
          throw err
        } finally {
          this.loading = false
        }

        const calcProgres = (part, total) => {
          if (!(total > 0)) {
            return 0
          }
          let progress = roundNumber(((part || 0) / total) * 100, 1)
          // 未完成的最多99.9%
          if (progress == 100 && part < total) {
            progress = 99.9
          }
          return progress
        }
        this.exerciseList = (res.list || []).map(item => {
          item.scanProgress = calcProgres(item.scanned, item.total)
          item.isPostScan = item.uploadMode == UploadModeEnum.PostScan.id
          item.uploadModeText = item.isPostScan ? '先阅后扫' : '线上阅卷'
          item.markProgress = item.isPostScan ? 0 : calcProgres(item.markedCount, item.totalMarkCount)
          return item
        })
        this.total = res.total || 0
      },
    },
  }
</script>

<template>
  <div class="component-supplementary-exercise">
    <div class="btns-area">
      <Poptip placement="bottom-end">
        <Button ghost type="primary" class="btn-normal-height btn-success" icon="md-qr-scanner">扫描教辅作业</Button>
        <template #content>
          <div class="select-scan-mode-container">
            <Button class="btn-scan" type="default" @click="gotoScanPage({ mode: 'postScan' })"
              >先阅后扫（老师在纸质答题卡上批改主观题）</Button
            >
            <Button class="btn-scan" type="default" @click="gotoScanPage({ mode: 'online' })"
              >线上阅卷（老师在PC端或小程序批改主观题）</Button
            >
          </div>
        </template>
      </Poptip>
    </div>

    <div class="filter-bar">
      <div class="filter-bar-left">
        <div class="filter-item">
          <span class="filter-item-label">学期</span>
          <Select v-model="currentTermValue" class="filter-item-body term" transfer @on-change="onChangeTerm">
            <Option v-for="t in terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">班级</span>
          <Select v-model="currentClassId" class="filter-item-body" clearable transfer @on-change="onChangeClass">
            <Option v-for="cls in classList" :key="cls.classId" :value="cls.classId">{{ cls.className }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">教辅图书</span>
          <Select
            v-model="currentCoachBookId"
            class="filter-item-body book"
            clearable
            transfer
            @on-change="onChangeCoachBook"
          >
            <Option v-for="book in filterCoachBooks" :key="book.id" :value="book.id">{{ book.bookName }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">阅卷模式</span>
          <Select
            v-model="currentUploadModeId"
            class="filter-item-body upload-mode"
            clearable
            transfer
            @on-change="onChangeUploadMode"
          >
            <Option v-for="m in uploadModes" :key="m.id" :value="m.id">{{ m.name }}</Option>
          </Select>
        </div>
      </div>
      <search-input
        v-model="keyword"
        class="filter-bar-right"
        placeholder="测验名称"
        clearable
        @on-change="onChangeKeyword"
      ></search-input>
    </div>

    <div v-if="total">
      <div v-for="e of exerciseList" :key="e.examId" class="exercise-item">
        <div class="box-icon">
          <img src="@/assets/images/supplementary_exercise/supplementary_exercise_item_icon.svg" alt="" />
        </div>
        <div class="info">
          <div class="info-top">
            <span class="test-title" @click="gotoReportPage(e)">{{ e.examName }}</span>
            <span class="grade-subject-text">{{ e.gradeName }}{{ e.subjectName }}</span>
            <span class="upload-mode" :class="{ 'post-scan': e.isPostScan }">{{ e.uploadModeText }}</span>
          </div>
          <div class="info-bottom">
            <div>已扫人数：{{ e.scanned || 0 }} / {{ e.total || 0 }}</div>
          </div>
        </div>
        <div class="text-operations">
          <Button class="btn btn-setting" @click="gotoSettingPage(e)">设置</Button>
          <Button ghost class="btn btn-info" @click="gotoScanPage({ exam: e })">扫描</Button>
          <el-progress
            class="progress-bar clickable-el-progress"
            :stroke-width="8"
            :percentage="e.scanProgress"
            :color="getCustomColor"
            @click="gotoScanPage({ exam: e })"
          ></el-progress>
          <template v-if="e.needMark">
            <Button ghost class="btn btn-success" @click="gotoMarkPage(e)">批改</Button>
            <el-progress
              :class="['progress-bar', isSchoolAdministrator ? 'clickable-el-progress' : '']"
              :stroke-width="8"
              :percentage="e.markProgress"
              :color="getCustomColor"
              title="批改进度监控"
              @click="handleProgressMarkingClick(e)"
            ></el-progress>
          </template>
          <Button ghost class="btn btn-text-color" @click="gotoReportPage(e)">报表</Button>
        </div>
      </div>

      <Page
        :model-value="currentPage"
        :total="total"
        :page-size="pageSize"
        show-total
        style="float: right; margin-top: 24px"
        @on-change="onChangePage"
      ></Page>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<style lang="scss" scoped>
  .component-supplementary-exercise {
    position: relative;
    min-height: 90px;
    padding: 0 20px;

    .btns-area {
      position: absolute;
      top: -56px;
      right: 20px;
      text-align: right;

      .btn-success {
        border-color: #13ce66;
        color: #13ce66;
        background-color: #e8faf0;
      }

      .btn-info {
        border-color: #409eff;
        color: #409eff;
        background-color: #ecf5ff;
      }

      .btn-normal-height {
        height: 28px;
        font-size: 14px;
      }

      .select-scan-mode-container {
        padding: 12px 8px 6px 8px;

        .btn-scan {
          display: block;

          &:not(:first-child) {
            margin-top: 8px;
          }
        }
      }
    }

    .filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 16px;

      .filter-bar-left {
        @include flex(row, flex-start, center);

        .filter-item {
          @include flex(row, flex-start, center);
          margin-right: 20px;

          .filter-item-label {
            flex-grow: 0;
            flex-shrink: 0;
            margin-right: 10px;
          }

          .filter-item-body {
            width: 100px;

            &.term {
              width: 250px;
            }

            &.book {
              width: 300px;
            }

            &.sort {
              width: 120px;
            }
          }
        }
      }
    }

    .exercise-item {
      @include flex(row, flex-start, center);
      padding: 24px;
      border: 1px solid transparent;
      border-radius: 4px;
      box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        border-color: $color-primary;
        transform: scale(1.02);
        transform-origin: center center;
      }

      &:not(:last-child) {
        margin-bottom: 24px;
      }

      .box-icon {
        flex-shrink: 0;
        margin-right: 16px;
        font-size: 0;

        img {
          width: 74px;
          height: 74px;
        }
      }

      .info {
        flex-grow: 1;
        flex-shrink: 1;
        margin-right: 16px;

        .info-top {
          min-height: 30px;
          margin-bottom: 16px;

          .test-title {
            font-weight: bold;
            font-size: $font-size-medium-x;

            &:hover {
              cursor: pointer;
            }
          }

          .grade-subject-text {
            display: inline-block;
            margin-left: 4px;
            padding: 0.3em 0.6em;
            border: 1px solid #36a390;
            border-radius: 1.2em;
            color: #36a390;
            line-height: 1.3;
            text-align: center;
          }

          .upload-mode {
            display: inline-block;
            margin-left: 4px;
            padding: 0.3em 0.6em;
            border: 1px solid #13ce66;
            border-radius: 4px;
            color: #13ce66;
            line-height: 1.3;
            text-align: center;
            background-color: #e8faf0;

            &.post-scan {
              border-color: $color-warning;
              color: $color-warning;
              background-color: #fff7eb;
            }
          }
        }

        .info-bottom {
          @include flex(row, flex-start, center);
          height: 20px;
          color: $color-icon;
          font-size: 12px;
        }
      }

      .text-operations {
        @include flex(row, flex-end, center);
        flex-shrink: 0;

        .btn-text-color {
          border-color: #dbdbdb;
          color: $color-content;
          background-color: rgba(255, 255, 255, 0.4);
        }

        .btn-setting {
          margin-right: 24px;
        }

        .btn-info {
          border-color: #409eff;
          color: #409eff;
          background-color: #ecf5ff;
        }

        .btn-success {
          border-color: #13ce66;
          color: #13ce66;
          background-color: #e8faf0;
        }

        .btn {
          height: 28px;
        }

        .progress-bar {
          width: 100px;
          margin-right: 20px;
          margin-left: 8px;

          :deep(.el-progress__text) {
            display: inline-block;
            min-width: 34px;
            font-size: 13px !important;
            text-align: center;
          }
        }

        .clickable-el-progress:hover {
          cursor: pointer;
        }
      }
    }
  }
</style>
