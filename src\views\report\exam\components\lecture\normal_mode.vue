<template>
  <div class="container-normal-mode">
    <template v-if="questions.length">
      <div class="panel-left">
        <QuestionCard
          v-for="q in questions"
          :ref="'question_' + q.questionName"
          :key="q.questionName"
          class="question-item"
          :question="q"
          :show-grade-avg-score="showGradeAvgScore"
          :show-all-branch="showAllBranch"
          :score-detail-visible="scoreDetailVisible"
        ></QuestionCard>
      </div>
      <Affix :offset-top="60" class="panel-right">
        <div class="panel-right-body">
          <div class="title">
            <span>题目列表</span>
            <Icon class="icon-full-screen" type="ios-expand" title="全屏" @click="lectureOpenFullScreen" />
          </div>
          <QuickSelectorQuestion :questions="questions" @on-change="scrollToQuestion" />
        </div>
      </Affix>
    </template>
    <div v-else class="no-data">暂无数据~</div>
  </div>
</template>

<script>
  import QuestionCard from './question_card.vue'
  import QuickSelectorQuestion from './quick_selector_question.vue'
  import { scrollInto } from '@/utils/scroll'

  export default {
    components: {
      QuestionCard,
      QuickSelectorQuestion,
    },
    props: {
      questions: {
        type: Array,
        default: () => [],
      },
      showGradeAvgScore: {
        type: Boolean,
        default: true,
      },
      showAllBranch: {
        type: Boolean,
        default: true,
      },
      scoreDetailVisible: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['on-change-mode'],
    methods: {
      lectureOpenFullScreen() {
        this.$emit('on-change-mode', 'full-screen')
      },
      scrollToQuestion(q) {
        let el = this.$refs['question_' + q.questionName]
        el = el && el[0] && el[0].$el
        if (!el) {
          return
        }

        scrollInto(el, -60)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-normal-mode {
    @include flex(row, flex-start, flex-start);

    .no-data {
      width: 100%;
      padding: 20px;
      color: #999;
      text-align: center;
      background-color: #fff;
    }
  }

  .panel-left {
    flex-grow: 1;
    flex-shrink: 1;

    .question-item {
      margin-bottom: 10px;
    }
  }

  .panel-right {
    flex-grow: 0;
    flex-shrink: 0;
    width: 250px;
    margin-left: 20px;
  }

  .panel-right-body {
    width: 250px;
    max-height: 400px;
    max-height: calc(100vh - 100px);
    padding: 20px;
    overflow-y: auto;
    background-color: white;

    .title {
      @include flex(row, space-between, center);
      height: 20px;
      font-weight: bold;
      font-size: $font-size-medium-x;
      line-height: 20px;

      .icon-full-screen {
        font-weight: bold;
        font-size: $font-size-large;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }
</style>
