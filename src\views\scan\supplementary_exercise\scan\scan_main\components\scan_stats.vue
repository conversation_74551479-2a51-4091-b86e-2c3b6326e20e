<template>
  <div class="scan-stats" :class="cls">
    <div
      v-for="item in statsList"
      :key="item.title"
      class="stats-item"
      :class="item.class || []"
      @click="handleClickStatsItem(item)"
    >
      <div class="stats-item-title">{{ item.title }}</div>
      <div class="stats-item-number" :class="{ underline: item.underline }">{{ item.number }}</div>
      <div v-if="item.remark" class="stats-item-remark" @click="item.remarkClickHandler">{{ item.remark }}</div>
      <div v-if="item.title === '定位及考号异常' && missingEx > 0" class="stats-item-remark">
        缺页异常：{{ missingEx }}
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    props: {
      stats: Object,
      inline: <PERSON>olean,
    },
    emits: ['to-monitor', 'to-exception'],
    computed: {
      ...mapGetters('scan', ['isSimpleScanner', 'viewMode']),
      cls() {
        return {
          inline: this.inline,
          card: !this.inline,
        }
      },
      // 未传张数
      unUploadCount() {
        return Math.max(0, this.stats.scanPaperCount - this.stats.uploadPaperCount)
      },
      // 未扫人数
      unScanStuCount() {
        return Math.max(0, this.stats.stuCount - this.stats.scanStuCount - this.stats.markAbsentStuCount)
      },
      // 定位及考号异常
      locateAndAdmissionNumExceptionCount() {
        let count = 0
        ;['imageEx', 'locateEx', 'subjectEx', 'cornerEx', 'admissionEx', 'duplicateEx', 'missingEx'].forEach(key => {
          if (this.stats[key] > 0) {
            count += this.stats[key]
          }
        })
        return count
      },
      // 缺页异常
      missingEx() {
        return this.stats['missingEx']
      },
      // 其他异常
      otherExceptionCount() {
        let count = 0
        ;['absentEx', 'objectiveEx', 'subjectiveEx', 'selectEx'].forEach(key => {
          if (this.stats[key] > 0) {
            count += this.stats[key]
          }
        })
        return count
      },
      statsList() {
        let list = [
          {
            title: '未传张数',
            class: ['un-upload-count'],
            number: this.unUploadCount,
          },
          {
            title: '已扫人数',
            class: ['scan-student-count', 'clickable'],
            number: this.stats.scanStuCount + this.stats.markAbsentStuCount,
            remark: this.stats.markAbsentStuCount > 0 ? `标记缺考：${this.stats.markAbsentStuCount}` : '',
            underline: this.stats.scanStuCount > 0,
            clickHandler: () => {
              this.gotoScanMonitor('scanned')
            },
            remarkClickHandler: () => {
              this.gotoScanMonitor('markAbsent')
            },
          },
          {
            title: '未扫人数',
            class: ['un-scan-student-count', 'clickable'],
            number: this.unScanStuCount,
            underline: this.unScanStuCount > 0,
            clickHandler: () => {
              this.gotoScanMonitor('notScan')
            },
          },
          {
            title: '正在识别',
            class: ['wait-recognize-count'],
            number: this.stats.waitRecogCount,
          },
          {
            title: '定位及考号异常',
            class: ['recognize-error', 'clickable'],
            number: this.locateAndAdmissionNumExceptionCount,
            underline: this.locateAndAdmissionNumExceptionCount > 0,
            clickHandler: () => {
              this.gotoException('locate')
            },
          },
        ]
        if (!this.isSimpleScanner) {
          list.push({
            title: '其他异常',
            class: ['recognize-warning', 'clickable'],
            number: this.otherExceptionCount,
            underline: this.otherExceptionCount > 0,
            clickHandler: () => {
              this.gotoException('other')
            },
          })
        }
        return list
      },
    },
    methods: {
      handleClickStatsItem(item) {
        if (item.clickHandler) {
          item.clickHandler()
        }
      },
      gotoScanMonitor(status) {
        this.$emit('to-monitor', status)
      },
      gotoException(tab) {
        this.$emit('to-exception', {
          tab,
          ...this.viewMode,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-stats {
    .stats-item {
      user-select: none;

      &.clickable:hover {
        cursor: pointer;
      }

      &.un-upload-count {
        color: #f78631;
      }

      &.scan-student-count {
        color: #3c996f;
      }

      &.un-scan-student-count {
        color: #b8b7b7;
      }

      &.wait-recognize-count {
        color: #5f89c2;
      }

      &.recognize-error {
        color: $color-error;
      }

      &.recognize-warning {
        color: $color-warning;
      }

      .stats-item-number {
        &.underline {
          text-decoration: underline;
        }
      }
    }

    &.inline {
      .stats-item {
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        margin-right: 20px;

        .stats-item-title {
          margin-right: 6px;
          color: inherit;
        }

        .stats-item-number {
          font-weight: bold;
          font-size: $font-size-large;
        }

        .stats-item-remark {
          display: none;
        }
      }
    }

    &.card {
      @include flex(row, space-between, center);

      .stats-item {
        position: relative;
        flex: 1 0 0;

        &:not(:last-child) {
          margin-right: 6px;
        }

        &.un-upload-count {
          background: no-repeat left top/cover url('~@/assets/images/scan/unscan_paper_count.svg');
        }

        &.scan-student-count {
          background: no-repeat left top/cover url('~@/assets/images/scan/scan_count.svg');
        }

        &.un-scan-student-count {
          background: no-repeat left top/cover url('~@/assets/images/scan/unscan_count.svg');
        }

        &.wait-recognize-count {
          background: no-repeat left top/cover url('~@/assets/images/scan/identifying.svg');
        }

        &.recognize-error {
          background: no-repeat left top/cover url('~@/assets/images/scan/identifying_anomalies.svg');
        }

        &.recognize-warning {
          background: no-repeat left top/cover url('~@/assets/images/scan/identifying_anomalies.svg');
        }

        .stats-item-title {
          padding: 18px 1em 8px 1em;
          color: $color-content;
          font-size: $font-size-medium;
          line-height: 1;

          &::before {
            display: inline-block;
            width: 14px;
            height: 14px;
            margin-right: 4px;
            vertical-align: top;
            background: no-repeat bottom/100% url('~@/assets/images/scan/triangle_sign.svg');
            content: '';
          }
        }

        .stats-item-number {
          height: 100px;
          font-size: 32px;
          line-height: 90px;
          text-align: center;
        }

        .stats-item-remark {
          position: absolute;
          bottom: 4px;
          left: 32px;
          color: #b8b7b7;
          font-size: $font-size-small;
          cursor: pointer;
        }
      }
    }
  }
</style>
