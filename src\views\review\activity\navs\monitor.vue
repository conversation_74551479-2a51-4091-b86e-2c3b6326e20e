<template>
  <div class="page-review-monitor">
    <div class="section-header-stat">
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">报名人数</div>
          <div class="value">{{ registrationApprovedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_purple.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">评委</div>
          <div class="value">{{ judgeCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_purple.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">任务</div>
          <div class="value">{{ taskAllCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_box_yellow.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">已完成</div>
          <div class="value">{{ taskSubmittedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_box_yellow.svg" />
        </div>
      </div>
      <!-- <div class="stat-item">
        <span class="name">报名人数</span>
        <span class="count">{{ registrationApprovedCount }}</span>
      </div>
      <div class="stat-item">
        <span class="name">评委</span>
        <span class="count">{{ judgeCount }}</span>
      </div>
      <div class="stat-item">
        <span class="name">任务</span>
        <span class="count">{{ taskAllCount }}</span>
      </div>
      <div class="stat-item">
        <span class="name">已完成</span>
        <span class="count">{{ taskSubmittedCount }}</span>
      </div> -->
    </div>
    <div class="section-progress">
      <div class="header">整体进度</div>
      <div class="body">
        <Table
          class="table-category"
          :data="overallProgress"
          :columns="tableCategoryColumns"
          @on-row-click="handleRowClick"
          @on-sort-change="handleSortChange"
        ></Table>
      </div>
    </div>
    <div class="section-progress">
      <div class="header">
        <span>评审员进度</span>
        <div class="box-category">
          <Select
            v-model="currentCategory"
            class="selector-block"
            style="min-width: 100px"
            @on-change="onCategoryChange"
          >
            <Option v-for="c in categoriesForSelect" :key="c.id" :value="c.id">{{ c.name }} </Option>
          </Select>
        </div>
        <span class="actions">
          <TextButton icon="md-list" @click="showScoreHistory = true">查看评审记录</TextButton>
        </span>
      </div>

      <div class="body">
        <Table
          class="table-user"
          :data="judgeProgress"
          :columns="tableUserColumns"
          @on-sort-change="handleSortChange"
        ></Table>
      </div>
    </div>

    <ComScoreHistory v-model="showScoreHistory" :category-id="currentCategory"></ComScoreHistory>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import CustomProgress from '@/views/emarking/monitor/components/progress'
  import TextButton from '@/components/text_button'
  import ComScoreHistory from '../../components/score_history.vue'
  import {
    apiGetActivityProgress,
    apiGetJudgeProgress,
    apiResetJudgeReview,
    apiReleaseJudgeReview,
  } from '@/api/review/review'
  import { roundNumber } from '../../../../utils/math'

  export default {
    components: {
      ComScoreHistory,
    },
    data() {
      return {
        overallProgress: [],
        currentCategory: '',
        judgeProgress: [],
        sortTypes: {
          progress: 'normal',
        },
        showScoreHistory: false,
      }
    },
    computed: {
      ...mapGetters('review', ['activityId', 'categories']),
      categoriesForSelect() {
        return [
          {
            id: '0',
            name: '不限',
          },
          ...this.categories,
        ]
      },
      registrationApprovedCount() {
        const { overallProgress } = this
        if (!overallProgress?.length) return 0

        return overallProgress.reduce((prev, cur) => {
          return prev + cur.registrationApprovedCount
        }, 0)
      },
      judgeCount() {
        const { overallProgress } = this
        if (!overallProgress?.length) return 0

        return overallProgress.reduce((prev, cur) => {
          return prev + cur.judges?.length
        }, 0)
      },
      taskAllCount() {
        const { overallProgress } = this
        if (!overallProgress?.length) return 0

        return overallProgress.reduce((prev, cur) => {
          return prev + cur.progress?.total
        }, 0)
      },
      taskSubmittedCount() {
        const { overallProgress } = this
        if (!overallProgress?.length) return 0

        return overallProgress.reduce((prev, cur) => {
          return prev + cur.progress?.submitted
        }, 0)
      },
      tableCategoryColumns() {
        let columns = [
          {
            title: '类别',
            maxWidth: 150,
            key: 'categoryName',
          },
          {
            title: '评审进度',
            align: 'center',
            width: 250,
            key: 'progress',
            sortable: true,
            sortMethod: this.sortMarkProgressMethod,
            sortType: this.sortTypes['progress'],
            render: (h, params) => {
              return h(CustomProgress, {
                total: params.row.progress?.total,
                current: params.row.progress?.submitted,
              })
            },
          },
          {
            title: '评审次数',
            key: 'reviewCount',

            align: 'center',
          },
          {
            title: '满分',
            // maxWidth: 80,
            align: 'center',
            key: 'fullScore',
          },
          {
            title: '平均分',
            // maxWidth: 80,
            align: 'center',
            key: 'avgScore',
            render: (h, params) => {
              return h('span', {}, params.row.avgScore != null ? roundNumber(params.row.avgScore, 2) : '')
            },
          },
          {
            title: '最高分',
            // maxWidth: 80,
            align: 'center',
            key: 'maxScore',
          },
          {
            title: '最低分',
            // maxWidth: 80,
            align: 'center',
            key: 'minScore',
          },
        ]

        return columns
      },
      tableUserColumns() {
        let columns = [
          {
            title: '类别',
            key: 'categoryName',
            sortable: true,
            // sortType: this.sortTypes['categoryName'],
          },
          {
            title: '评审员',
            key: 'realName',
            align: 'center',
            sortable: true,
            // sortType: this.sortTypes['realName'],
          },
          {
            title: '学校',
            key: 'schoolName',
            align: 'center',
            sortable: true,
            // sortType: this.sortTypes['schoolName'],
          },
          {
            title: '手机号码',
            key: 'mobile',
            align: 'center',
            minWidth: 100,
            maxWidth: 120,
            sortable: true,
            // sortType: this.sortTypes['mobile'],
          },
          {
            title: '分配任务',
            key: 'totalTask',
            align: 'center',
            sortable: true,
            minWidth: 100,
            maxWidth: 120,
            // render: (h, params) => {
            //   return h('span', {}, params.row.progress.total)
            // },
          },
          {
            title: '已评量',
            key: 'submittedTask',
            align: 'center',
            sortable: true,
            maxWidth: 90,
            // render: (h, params) => {
            //   return h('span', {}, params.row.progress.submitted)
            // },
          },
          {
            title: '在评量',
            key: 'markingTask',
            align: 'center',
            sortable: true,
            maxWidth: 90,
            // render: (h, params) => {
            //   return h('span', {}, Number(params.row.progress.unreviewed) - Number(params.row.progress.pending))
            // },
          },
          {
            title: '平均分',
            key: 'avgScore',
            align: 'center',
            sortable: true,
            maxWidth: 90,
            // sortType: this.sortTypes['avgScore'],
            render: (h, params) => {
              return h('span', {}, params.row.avgScore != null ? roundNumber(params.row.avgScore, 2) : '')
            },
          },
          {
            title: '最高分',
            key: 'maxScore',
            align: 'center',
            sortable: 'true',
            maxWidth: 90,
            // sortType: this.sortTypes['maxScore'],
          },
          {
            title: '最低分',
            key: 'minScore',
            align: 'center',
            sortable: true,
            maxWidth: 90,
            // sortType: this.sortTypes['minScore'],
          },
          {
            title: '操作',
            align: 'center',
            width: 120,
            render: (h, params) => {
              let operateButtons = []

              if (params.row.markingTask > 0) {
                operateButtons.push(
                  h(
                    TextButton,
                    {
                      type: 'warning',
                      onClick: () => {
                        this.releaseOccupiedPaper(params.row)
                      },
                    },
                    () => '回收'
                  )
                )
              }

              if (params.row.progress.submitted > 0) {
                operateButtons.push(
                  h(
                    TextButton,
                    {
                      type: 'error',
                      onClick: () => {
                        this.resetTeacherPaper(params.row)
                      },
                    },
                    () => '重置'
                  )
                )
              }

              return h('div', {}, operateButtons)
            },
          },
        ]

        return columns
      },
    },
    watch: {
      categoriesForSelect: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            this.currentCategory = val[0].id
            this.fetchCategoryProgress()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.fetchActivityProgress()
    },
    methods: {
      fetchActivityProgress() {
        const { activityId, categories } = this

        apiGetActivityProgress({
          activityId,
        }).then(res => {
          this.overallProgress = (res || []).map(item => {
            const theCategory = categories.find(c => c.id === item.categoryId)
            return {
              ...item,
              categoryName: theCategory && theCategory.name,
            }
          })
        })
      },
      fetchCategoryProgress() {
        const { activityId } = this

        apiGetJudgeProgress({
          activityId,
          categoryId: this.currentCategory != '0' ? this.currentCategory : undefined,
        }).then(res => {
          this.judgeProgress = (res || []).map(item => ({
            ...item,
            totalTask: item.progress?.total,
            submittedTask: item.progress?.submitted,
            markingTask: Number(item.progress?.unreviewed) - Number(item.progress?.pending),
          }))
        })
      },
      onCategoryChange() {
        this.fetchCategoryProgress()
      },
      sortMarkProgressMethod(a, b, type) {
        let percentA = a.total > 0 ? a.submitted / a.total : 0
        let percentB = b.total > 0 ? b.submitted / b.total : 0

        if (type === 'asc') {
          return percentA - percentB
        } else {
          return percentB - percentA
        }
      },
      handleSortChange(data) {
        if (!data && data.key && data.order) {
          return
        }

        Object.keys(this.sortTypes).forEach(key => {
          if (key === data.key) {
            this.sortTypes[key] = data.order
          } else {
            this.sortTypes[key] = 'normal'
          }
        })
      },
      handleRowClick(item) {
        this.currentCategory = item.categoryId
        this.onCategoryChange()
      },
      releaseOccupiedPaper(item) {
        if (!(item.markingTask > 0)) {
          return
        }

        this.$Modal.confirm({
          title: '回收未提交试卷',
          content: `回收试卷可能导致该评卷老师正在评的试卷提交不成功。<br><br>确认回收【${item.categoryName}】${item.realName}的${item.markingTask}份试卷？`,
          onOk: () => {
            apiReleaseJudgeReview({
              activityId: item.activityId,
              categoryId: item.categoryId,
              judgeId: item.judgeId,
            })
              .then(() => {
                this.$Message.success({
                  content: '已回收',
                })
              })
              .finally(() => {
                this.fetchActivityProgress()
                this.fetchCategoryProgress()
              })
          },
        })
      },
      resetTeacherPaper(item) {
        if (!(item.progress.submitted > 0)) {
          return
        }

        this.$Modal.confirm({
          title: '重置评审员评审',
          content: `<span style="color:red">重置评审员评审会清除该评审员该题块所有评审记录，且不可恢复。</span><br><br>确认重置${item.realName}在【${item.categoryName}】的所有评审记录？`,
          onOk: () => {
            setTimeout(() => {
              this.$Modal.confirm({
                title: '请再次确认',
                content: `<span style="color:red">将重置${item.realName}在【${item.categoryName}】的所有评审记录，重置后无法恢复。</span><br><br>确认继续？`,
                onOk: () => {
                  apiResetJudgeReview({
                    judgeId: item.judgeId,
                    categoryId: item.categoryId,
                  })
                    .then(() => {
                      this.$Message.success({
                        content: '重置成功',
                      })
                    })
                    .finally(() => {
                      this.fetchActivityProgress()
                      this.fetchCategoryProgress()
                    })
                },
              })
            }, 1000 * 0.4)
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-review-monitor {
    padding: 20px;
    background-color: #fff;

    .section-header-stat {
      @include flex(row, space-between, center);

      .stat-item {
        @include flex(row, flex-start, center);
        width: 300px;
        height: 100px;
        padding-left: 30px;
        border-radius: 4px;
        background-color: #fff;

        .name {
          margin-right: 60px;
          font-size: 18px;
        }

        .count {
          font-size: 20px;
        }
      }

      .statistic-card {
        @include flex(row, space-between, center);
        flex: 1;
        max-width: 370px;
        height: 108px;
        padding: 16px 18px;
        border-radius: 6px;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

        &:not(:first-child) {
          margin-left: 20px;
        }

        .card-text {
          @include flex(column, space-between, stretch);
          flex: 1;
          height: 100%;

          .name {
            flex: 1;
            font-size: $font-size-medium-x;
            user-select: none;
          }

          .value {
            flex: none;
            font-weight: 700;
            font-size: 28px;
          }
        }

        .card-icon {
          flex: none;
          user-select: none;
        }
      }
    }

    .section-progress {
      margin-bottom: 15px;
      padding: 15px 0;
      background-color: #fff;

      .header {
        @include flex(row, flex-start, center);
        position: relative;
        margin-bottom: 10px;
        padding-left: 16px;
        font-weight: 600;
        font-size: 15px;

        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 6px;
          height: $font-size-medium-x;
          margin: auto;
          background-color: $color-primary;
          content: '';
        }
      }

      .category-progress {
        @include flex(row, flex-start, center);
        padding: 15px 0;
        border-bottom: 1px solid #dfdfdf;

        .category-name {
          display: inline-block;
          width: 150px;
        }

        .category-progress-stat {
          display: inline-block;
          width: 150px;
        }

        .category-progress-bar {
          flex: 1;
        }
      }

      .box-category {
        margin-left: 20px;
        font-weight: 500;
      }

      .actions {
        flex-grow: 1;
        font-weight: 500;
        font-size: 14px;
        text-align: right;
      }
    }
  }
</style>
