<template>
  <div class="scan-monitor-container">
    <MonitorStats
      class="panel-stats"
      :stats="stats"
      :student-params="studentParams"
      :table-max-height="tableMaxHeight"
      @change-active-row="changeStatsActiveRow"
      @refresh="refreshAndMessage"
    ></MonitorStats>
    <MonitorStudent
      class="panel-student"
      :students="students"
      :total="studentTotal"
      :params="studentParams"
      :status-list="scanStatusList"
      :table-max-height="tableMaxHeight"
      @change-status="changeStudentStatus"
      @change-keyword="changeStudentKeyword"
      @change-page="changeStudentPage"
      @change-page-size="changeStudentPageSize"
      @refresh="refresh"
    ></MonitorStudent>
  </div>
</template>

<script>
  import MonitorStats from './components/monitor_stats.vue'
  import MonitorStudent from './components/monitor_student.vue'

  import { apiGetScanStudentStats, apiGetScanStudentPage } from '@/api/scan/scan_stats'

  import { mapGetters } from 'vuex'
  import PageStatusEnum from '@/enum/scan/page_status'

  // 各部分高度
  const PanelHeaderHeight = 60
  const PanelFooterHeight = 60

  export default {
    components: {
      MonitorStats,
      MonitorStudent,
    },
    props: {
      filterStatus: String,
      top: Number,
    },
    emits: ['to-student', 'clear-filter-status'],
    data() {
      return {
        // 统计
        stats: null,
        // 考生筛选
        studentParams: {
          roomNo: 0,
          schoolId: '',
          scanStationId: 0,
          status: 'total',
          keyword: '',
          currentPage: 1,
          pageSize: 10,
        },
        // 考生
        students: [],
        studentTotal: 0,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'exam', 'isInSchoolExam', 'viewMode']),
      viewModeScanStationId() {
        return this.viewMode.scanStationId
      },
      scanStatusList() {
        return [
          {
            id: 'total',
            name: '全部',
          },
          {
            id: 'scanned',
            name: '已扫',
          },
          {
            id: 'scanAbsent',
            name: '扫描缺考',
          },
          {
            id: 'markAbsent',
            name: '标记缺考',
          },
          {
            id: 'notScan',
            name: '未扫',
          },
        ]
      },
      // 表格高度
      tableMaxHeight() {
        return window.innerHeight - this.top - PanelHeaderHeight - PanelFooterHeight
      },
    },
    watch: {
      viewModeScanStationId() {
        this.stats = null
        this.studentParams = {
          roomNo: 0,
          schoolId: '',
          scanStationId: 0,
          status: 'total',
          keyword: '',
          currentPage: 1,
          pageSize: 10,
        }
        this.students = []
        this.studentTotal = 0
        this.loadStats()
      },
    },
    created() {
      this.loadStats()
    },
    methods: {
      /**
       * 加载数据
       */
      loadStats() {
        this.$TransparentSpin.show()
        return apiGetScanStudentStats({
          examSubjectId: this.examSubjectId,
          scanStationId: this.viewModeScanStationId,
        })
          .then(data => {
            // 补充主客观题待入库
            let calcUnUploadNum = row => {
              row.objectiveUnUploadNum = row.scanned - row.objectiveUploadNum
              row.subjectiveUnUploadNum = row.scanned - row.subjectiveUploadNum
            }
            data.rooms.forEach(room => {
              calcUnUploadNum(room.total)
              room.schools.forEach(sch => calcUnUploadNum(sch.total))
            })
            data.schools.forEach(sch => calcUnUploadNum(sch.total))
            data.scanStations.forEach(station => calcUnUploadNum(station.total))
            calcUnUploadNum(data.total)

            this.stats = data
          })
          .finally(() => {
            this.$TransparentSpin.hide()
          })
      },
      loadStudents() {
        if (!this.studentParams.roomNo && !this.studentParams.schoolId && !this.studentParams.scanStationId) {
          this.students = []
          this.studentTotal = 0
          return Promise.resolve()
        }

        let params = {
          examSubjectId: this.examSubjectId,
          roomNo: 0,
          schoolId: '',
          scanStationId: this.viewModeScanStationId,
          status: this.studentParams.status,
          key: this.studentParams.keyword || '',
          currentPage: this.studentParams.currentPage,
          pageSize: this.studentParams.pageSize,
        }

        let keys = ['roomNo', 'schoolId', 'scanStationId']
        if (keys.every(key => this.studentParams[key] != '合计')) {
          keys.forEach(key => {
            if (this.studentParams[key]) {
              params[key] = this.studentParams[key]
            }
          })
        }

        this.$TransparentSpin.show()
        return apiGetScanStudentPage(params)
          .then(data => {
            data.records.forEach(record => {
              let statusClass = ''
              let statusText = ''
              let { markAbsent, pageStatus, scanAbsent, scanned } = record
              if (!scanned) {
                statusText = markAbsent ? '标记缺考' : '未扫'
              } else if (pageStatus == PageStatusEnum.Duplicate.id) {
                statusText = '多页'
                statusClass = 'warning'
              } else if (pageStatus == PageStatusEnum.Missing.id) {
                statusText = '缺页'
                statusClass = 'warning'
              } else if (scanAbsent) {
                statusText = '扫描缺考'
              } else {
                statusText = '已扫'
              }
              record.statusText = statusText
              record.statusClass = statusClass
            })
            this.students = data.records
            this.studentTotal = data.total
          })
          .finally(() => {
            this.$TransparentSpin.hide()
          })
      },

      /**
       * 操作响应
       */
      changeStatsActiveRow(row) {
        this.studentParams.roomNo = row.roomNo
        this.studentParams.schoolId = row.schoolId
        this.studentParams.scanStationId = row.scanStationId
        this.studentParams.keyword = ''
        this.studentParams.currentPage = 1
        if (this.filterStatus) {
          this.studentParams.status = this.filterStatus
          this.$emit('clear-filter-status')
        } else {
          this.studentParams.status = row.status || 'total'
        }
        this.loadStudents()
      },
      changeStudentStatus(status) {
        this.studentParams.status = status
        this.studentParams.currentPage = 1
        this.loadStudents()
      },
      changeStudentKeyword(keyword) {
        this.studentParams.keyword = keyword
        this.studentParams.currentPage = 1
        this.loadStudents()
      },
      changeStudentPage(page) {
        this.studentParams.currentPage = page
        this.loadStudents()
      },
      changeStudentPageSize(pageSize) {
        this.studentParams.pageSize = pageSize
        this.studentParams.currentPage = 1
        this.loadStudents()
      },
      refresh() {
        return Promise.all([this.loadStudents(), this.loadStats()])
      },
      refreshAndMessage() {
        return this.refresh().then(() => {
          this.$Message.info({
            content: '已刷新',
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-monitor-container {
    @include flex(row, flex-start, stretch);
    overflow: hidden;

    .panel-stats,
    .panel-student {
      position: relative;
      flex-grow: 0;
      flex-shrink: 0;
      width: 50vw;
      height: 100%;
    }

    :deep(.panel-header) {
      @include flex(row, flex-start, center);
      height: 60px;
    }

    :deep(.panel-footer) {
      @include flex(row, flex-end, center);
      position: absolute;
      bottom: 0;
      left: 0;
      flex-wrap: wrap;
      width: 100%;
      height: 60px;
      padding-right: 16px;

      .summary {
        margin-right: 10px;
      }
    }
  }
</style>
