<template>
  <div class="container-scan">
    <div class="section-title">教辅扫描员配置</div>
    <div class="header">
      <Button type="primary" @click="handleBtnAddScannerClick">添加扫描员</Button>
    </div>
    <Table :columns="tableColumns" :data="scanners"></Table>

    <modal-scanner
      v-model="modalOperScannerShowed"
      :mode="modalScannerMode"
      :scanner-info="modalScannerInfo"
      @fetch-scanners="fetchScannerAndStartInterval"
    ></modal-scanner>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import ModalScanner from './components/modal_scanner.vue'

  import { apiGetReferenceBookScanenrList, apiDeleteReferenceBookScanner } from '@/api/user'

  export default {
    components: {
      'modal-scanner': ModalScanner,
    },

    data() {
      return {
        scanners: [],
        modalScannerMode: 'add',
        modalScannerInfo: null,
        tableColumns: [
          {
            title: '账号',
            key: 'userName',
            width: 300,
          },
          {
            title: '姓名',
            key: 'realName',
          },
          {
            title: '操作',
            align: 'center',
            width: 200,
            render: (h, params) => {
              return h('div', {}, [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => this.modifyScannerInfo(params.row),
                  },
                  () => '编辑'
                ),
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => this.modifyScannerPassword(params.row),
                  },
                  () => '修改密码'
                ),
                h(
                  TextButton,
                  {
                    type: 'warning',
                    onClick: () => this.deleteScanner(params.row.teacherId),
                  },
                  () => '删除'
                ),
              ])
            },
          },
        ],

        fetchScannerListIntervalId: null,

        modalOperScannerShowed: false,
      }
    },

    created() {
      this.fetchScannerAndStartInterval()
    },

    unmounted() {
      this.endFetchScannerInterval()
    },

    methods: {
      fetchScannerAndStartInterval() {
        this.fetchScannerListIntervalId = setInterval(this.fetchScannerList, 1000 * 60 * 1)
        this.fetchScannerList()
      },

      endFetchScannerInterval() {
        if (this.fetchScannerListIntervalId) {
          clearInterval(this.fetchScannerListIntervalId)
          this.fetchScannerListIntervalId = null
        }
      },

      fetchScannerList() {
        this.$TransparentSpin.show()
        apiGetReferenceBookScanenrList()
          .then(response => {
            this.scanners = response || []
          })
          .finally(() => this.$TransparentSpin.hide())
      },

      handleBtnAddScannerClick() {
        this.modalScannerMode = 'add'
        this.modalScannerInfo = null
        this.endFetchScannerInterval()
        this.modalOperScannerShowed = true
      },

      modifyScannerInfo(scanner) {
        this.modalScannerMode = 'modifyInfo'
        this.modalScannerInfo = scanner
        this.endFetchScannerInterval()
        this.modalOperScannerShowed = true
      },

      modifyScannerPassword(scanner) {
        this.modalScannerMode = 'modifyPassword'
        this.modalScannerInfo = scanner
        this.endFetchScannerInterval()
        this.modalOperScannerShowed = true
      },

      deleteScanner(teacherId) {
        const TargetScanner = (this.scanners || []).find(scanner => scanner.teacherId === teacherId)
        if (!TargetScanner) {
          return
        }
        this.$Modal.confirm({
          title: '删除扫描员',
          content: `是否确定删除扫描员【${TargetScanner.realName}】？`,
          onOk: () => {
            apiDeleteReferenceBookScanner(teacherId)
              .then(() => {
                this.$Message.success({
                  content: '成功删除教辅扫描员',
                })
              })
              .finally(this.fetchScannerList)
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-scan {
    padding: 20px;
    background-color: white;

    .section-title {
      margin-bottom: 10px;
      font-size: 30px;
    }
  }

  .header {
    margin-bottom: 20px;
    text-align: right;
  }
</style>
