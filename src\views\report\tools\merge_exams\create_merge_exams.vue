<script>
  import ComRadioGroup from '@/components/radio_group'
  import ModalSelectExams from './modal_select_exams'
  import ModalSubjectSetting from './modal_subject_setting'

  import {
    apiGetMergeableExamDetail,
    apiCreateMergeExam,
    apiGetMergeExamDetail,
    apiModifyMergeExamConfig,
  } from '@/api/emarking'

  import { formatDateTime } from '@/utils/date'
  import { deepCopy } from '@/utils/object'
  import { randomId } from '@/utils/string'

  import ExamTypeEnum from '@/enum/emarking/exam_type'
  import SubjectScoreCopyTypes from '@/enum/emarking/merge_exam/subject_score_copy_types'

  export default {
    components: {
      'com-radio-group': ComRadioGroup,
      'modal-select-exams': ModalSelectExams,
      'modal-subject-setting': ModalSubjectSetting,
    },

    data() {
      return {
        isModeCreate: true,
        editProjectId: '',

        isMerging: false,
        isModalSelectExamsVisible: false,
        isModalSubjectSettingVisible: false,
        modalSettingSubjectConfig: null, // 弹窗所选科目配置

        // 考试类型
        examTypeId: '',
        // 考试时间
        examDate: ['', ''],
        // 考试名称
        examName: '',
        // 学年学期名称
        termName: '',
        // 考试年级
        examGradeId: '',
        // 考试科目
        examSubjectIds: [],
        // 考试项目
        exams: [],
        // 合并源项目详情
        examDetails: [],
        // 合并项目科目配置
        examSubjectConfigs: [],
      }
    },

    computed: {
      pageTitle() {
        return `${this.isModeCreate ? '新建' : '编辑'}合并考试`
      },
      examTypes() {
        return ExamTypeEnum.getEntries().filter(
          t => ![ExamTypeEnum.ClassPractise.id, ExamTypeEnum.Feedback.id].includes(t.id)
        )
      },
      terms() {
        return this.$store.getters['emarking/terms']()
      },
      grades() {
        return this.$store.getters['emarking/gradeSubjects']()
      },
      subjects() {
        const TargetGrade = this.grades.find(g => g.id === this.examGradeId)
        return (TargetGrade && TargetGrade.subjects) || []
      },
      checkSubjectIndeterminate() {
        return this.examSubjectIds.length > 0 && this.examSubjectIds.length < this.subjects.length
      },
      checkSubjectAll() {
        return this.examSubjectIds.length === this.subjects.length
      },
      selectedTerm() {
        return (this.terms || []).find(t => t.termName === this.termName)
      },
      selectedGrade() {
        return (this.grades || []).find(g => g.id === this.examGradeId)
      },
    },

    watch: {
      examGradeId() {
        if (this.examGradeId) {
          this.exams = []
          this.examDetails = []
          this.examSubjectIds = []
        }
      },
    },

    created() {
      if (this.$route.name === 'report-tool-edit-merge-exam') {
        this.initEdit()
      } else {
        this.initCreate()
      }
    },

    methods: {
      initCreate() {
        const BeginTime = new Date()
        BeginTime.setHours(0, 0, 0, 0)

        const EndTime = new Date()
        EndTime.setDate(EndTime.getDate() + 30)
        EndTime.setHours(23, 59, 59, 999)

        this.examDate = [BeginTime, EndTime]

        if (!this.examTypeId) {
          this.examTypeId = (this.examTypes && this.examTypes[0] && this.examTypes[0].id) || ''
        }
        if (!this.termName) {
          this.termName = (this.terms && this.terms[0] && this.terms[0].termName) || ''
        }
      },
      async initEdit() {
        this.isModeCreate = false
        this.editProjectId = this.$route?.params?.id
        if (this.editProjectId) {
          const ProjectDetail = await apiGetMergeExamDetail(this.editProjectId)
          const Config = ProjectDetail?.configJson ? JSON.parse(ProjectDetail.configJson) : null
          if (Config) {
            this.examGradeId = Config.gradeId || ''

            this.$nextTick().then(() => {
              this.examName = Config.examName || ''
              this.examDate = [
                Config.beginTime ? new Date(Config.beginTime) : '',
                Config.endTime ? new Date(Config.endTime) : '',
              ]
              this.examTypeId = Config.examType || ''

              const TargetTerm = this.terms.find(t => t.semesterId === Config.semesterId && t.term === Config.term)
              if (TargetTerm) {
                this.termName = TargetTerm.termName || ''
              }
              this.exams = (Config.exams || []).map(e => ({ examId: e.examId }))
              this.updateExamsDetail().finally(() => {
                this.exams = this.examDetails.map(d => ({
                  examId: d.examId,
                  examName: d.examName,
                  schoolId: d.schoolId,
                  schoolName: d.schoolName,
                  semesterId: d.semesterId,
                  term: d.term,
                  gradeId: d.gradeId,
                  examScope: d.examScope,
                  examType: d.examType,
                  beginTime: d.beginTime,
                  endTime: d.endTime,
                }))
                this.examSubjectConfigs.forEach(sc => {
                  const TargetConfigSubject = (Config.subjects || []).find(cs => cs.subjectId === sc.subjectId)
                  if (TargetConfigSubject) {
                    sc.copyType = TargetConfigSubject.copyType
                    sc.foreignSubjectBranch = TargetConfigSubject.foreignSubjectBranch
                    sc.subjectType = TargetConfigSubject.subjectType
                    sc.writtenFullScore = TargetConfigSubject.writtenFullScore
                    sc.otherFullScore = TargetConfigSubject.otherFullScore
                    sc.objectives = deepCopy(TargetConfigSubject.objectives)
                    sc.subjectives = deepCopy(TargetConfigSubject.subjectives)
                    sc.projectSubjectSettings = this.examDetails.map(detail => {
                      const TargetConfigExam = (Config.exams || []).find(exam => exam.examId === detail.examId)
                      const TargetConfigExamSubject = (TargetConfigExam?.subjects || []).find(
                        s => s.subjectId === sc.subjectId
                      )
                      const SettingSubjectConfig = (detail.subjects || []).find(subj => subj.subjectId === sc.subjectId)
                      const SettingMapper = {
                        objectives: (TargetConfigExamSubject?.objectives || []).map(item => ({
                          ...item,
                          id: randomId(),
                          isEditing: false,
                        })),
                        subjectives: (TargetConfigExamSubject?.subjectives || []).map(item => ({
                          ...item,
                          id: randomId(),
                          isEditing: false,
                        })),
                      }

                      return {
                        id: detail.examId,
                        name: detail.examName,
                        subjectConfig: SettingSubjectConfig,
                        mapper: SettingMapper,
                        isConflictResolved:
                          (SettingSubjectConfig.objectives || []).every(q =>
                            (SettingMapper.objectives || []).some(r => r.srcQuestionCode === q.questionCode)
                          ) &&
                          (SettingSubjectConfig.subjectives || []).every(sq =>
                            (SettingMapper.subjectives || []).some(r =>
                              (r.srcQuestions || []).some(
                                mq => mq.questionCode === sq.questionCode && mq.branchCode === sq.branchCode
                              )
                            )
                          ),
                      }
                    })
                    sc.settingDone = !(
                      [
                        SubjectScoreCopyTypes.QuestionScoreOnly.id,
                        SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id,
                      ].includes(TargetConfigSubject.copyType) &&
                      sc.projectSubjectSettings.some(struct => !struct.isConflictResolved)
                    )
                  }
                })
              })
            })
          }
        }
      },

      handleCheckAllSubject() {
        if (this.checkSubjectAll) {
          this.examSubjectIds = []
        } else {
          this.examSubjectIds = this.subjects.map(s => s.id)
        }
      },
      changeCheckSubject(subjectId, checked) {
        const Index = this.examSubjectIds.indexOf(subjectId)
        if (checked && Index < 0) {
          this.examSubjectIds.push(subjectId)
        } else if (!checked && Index >= 0) {
          this.examSubjectIds.splice(Index, 1)
        }
      },
      updateExams(exams = []) {
        this.exams = exams
        this.updateExamsDetail()
      },
      updateExamsDetail() {
        this.examDetails = []
        return Promise.all(this.exams.map(e => apiGetMergeableExamDetail(e.examId)))
          .then(responses => {
            this.examDetails.push(...responses)
          })
          .finally(() => this.buildExamSubjectConfigs())
      },
      buildExamSubjectConfigs() {
        this.examSubjectConfigs = []

        const SubjectCounts = new Map()
        const ExamDetailsLength = this.examDetails.length

        this.examDetails.forEach(d => {
          const SubjectsInCurrentExam = new Set()
          ;(d.subjects || []).forEach(s => {
            if (!SubjectsInCurrentExam.has(s.subjectId)) {
              SubjectsInCurrentExam.add(s.subjectId)
              SubjectCounts.set(s.subjectId, (SubjectCounts.get(s.subjectId) || 0) + 1)
            }
          })
        })

        const CommonSubjectsMap = new Map()
        this.examDetails.forEach(d => {
          ;(d.subjects || []).forEach(s => {
            if (SubjectCounts.get(s.subjectId) === ExamDetailsLength && !CommonSubjectsMap.has(s.subjectId)) {
              CommonSubjectsMap.set(s.subjectId, {
                subjectId: s.subjectId,
                subjectName: s.subjectName,
                subjectType: s.subjectType || 0,
                copyType: '',
                foreignSubjectBranch: false,
                objectives: [],
                subjectives: [],
                otherFullScore: 0,
                writtenFullScore: 0,
                projectSubjectSettings: [],
                settingDone: false,
              })
            }
          })
        })

        this.examSubjectConfigs = Array.from(CommonSubjectsMap.values())
      },

      handleEditSubjectConfig(subjectConfig) {
        this.modalSettingSubjectConfig = subjectConfig
        this.isModalSubjectSettingVisible = true
      },
      updateExamSubjectConfig(newConfig) {
        if (newConfig) {
          const TargetExamSubjectConfig = this.examSubjectConfigs.find(c => c.subjectId === newConfig.subjectId)
          if (TargetExamSubjectConfig) {
            Object.keys(newConfig).forEach(key => {
              TargetExamSubjectConfig[key] = newConfig[key]
            })
          }
        }
      },

      cancelMerge() {
        this.$router.back()
      },
      saveMergeProject() {
        // let checkContent = ''
        let isConfigFinished = true
        if (!this.examDate || !this.examDate[0] || !this.examDate[1]) {
          // checkContent = '请选择考试时间'
          isConfigFinished = false
        } else if (!this.examTypeId) {
          // checkContent = '请选择考试类型'
          isConfigFinished = false
        } else if (!this.selectedTerm) {
          // checkContent = '请选择考试学期'
          isConfigFinished = false
        } else if (!this.examGradeId) {
          // checkContent = '请选择考试年级'
          isConfigFinished = false
        } else if (!this.exams) {
          // checkContent = '请选择要合并的考试项目'
          isConfigFinished = false
        } else if (!this.examName) {
          // checkContent = '请输入考试名称'
          isConfigFinished = false
        } else if (this.examSubjectConfigs.some(config => !config.settingDone)) {
          // checkContent = '尚有科目配置待处理'
          isConfigFinished = false
        }
        // if (checkContent) {
        //   this.$Message.warning({
        //     duration: 3,
        //     content: checkContent,
        //   })
        //   return
        // }

        const CopySubjectStructModes = [
          SubjectScoreCopyTypes.QuestionScoreOnly.id,
          SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id,
        ]
        const RequestParams = {
          isConfigFinished: isConfigFinished,
          beginTime: formatDateTime(this.examDate[0]),
          endTime: formatDateTime(this.examDate[1]),
          examName: this.examName,
          examType: this.examTypeId,
          gradeId: this.examGradeId,
          semesterId: this.selectedTerm && this.selectedTerm.semesterId,
          term: this.selectedTerm && this.selectedTerm.term,
          subjects: this.examSubjectConfigs.map(config => ({
            subjectId: config.subjectId,
            subjectType: config.subjectType,
            foreignSubjectBranch: config.foreignSubjectBranch,
            copyType: config.copyType,
            writtenFullScore: config.writtenFullScore,
            otherFullScore: config.otherFullScore,
            objectives: CopySubjectStructModes.includes(config?.copyType || 'nomap') ? config.objectives : undefined,
            subjectives: CopySubjectStructModes.includes(config?.copyType || 'nomap') ? config.subjectives : undefined,
          })),
          exams: this.exams.map((e, edx) => {
            return {
              examId: e.examId,
              order: edx + 1,
              subjects: this.examSubjectConfigs.map(config => {
                const TargetExamSubjectConfig = config.projectSubjectSettings.find(cpss => cpss.id === e.examId)
                return {
                  subjectId: config.subjectId,
                  objectives:
                    config.copyType && CopySubjectStructModes.includes(config.copyType)
                      ? (TargetExamSubjectConfig &&
                          TargetExamSubjectConfig.mapper &&
                          TargetExamSubjectConfig.mapper.objectives) ||
                        []
                      : undefined,
                  subjectives:
                    config.copyType && CopySubjectStructModes.includes(config.copyType)
                      ? (TargetExamSubjectConfig &&
                          TargetExamSubjectConfig.mapper &&
                          TargetExamSubjectConfig.mapper.subjectives) ||
                        []
                      : undefined,
                }
              }),
            }
          }),
        }

        let request = apiCreateMergeExam
        if (!this.isModeCreate) {
          request = apiModifyMergeExamConfig
          RequestParams.id = this.editProjectId
        }

        request(RequestParams).then(() =>
          this.$Message.success({
            duration: 3,
            content: '操作成功',
            onClose: () => this.cancelMerge(),
          })
        )
      },
    },
  }
</script>

<template>
  <div class="page-create-merge-exams">
    <div class="title">{{ pageTitle }}</div>

    <Form :label-width="120" class="form" label-position="left">
      <FormItem label="考试类型">
        <com-radio-group v-model="examTypeId" :radioes="examTypes" class="radiogroup"></com-radio-group>
      </FormItem>
      <FormItem label="考试时间">
        <DatePicker v-model="examDate" :editable="false" class="date-picker-exam" type="daterange" />
      </FormItem>
      <FormItem label="考试学期">
        <Select v-model="termName" transfer class="select-termname">
          <Option v-for="t of terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
        </Select>
      </FormItem>
      <FormItem label="考试年级">
        <com-radio-group v-model="examGradeId" :radioes="grades" class="radiogroup"></com-radio-group>
      </FormItem>
      <FormItem label="考试项目">
        <template v-if="examGradeId">
          <span class="tips"
            >已选 <span class="color-primary">{{ exams.length }}</span> 个考试项目</span
          >
          <Button type="primary" size="small" @click="isModalSelectExamsVisible = true">选择考试项目</Button>
        </template>
        <div v-else style="user-select: none">请先选择年级</div>
      </FormItem>
      <FormItem v-show="examDetails.length" label="科目配置">
        <Button
          v-for="s of examSubjectConfigs"
          :key="s.subjectId"
          :type="s.settingDone ? 'primary' : 'warning'"
          class="btn-subject-config"
          size="small"
          @click="handleEditSubjectConfig(s)"
          >{{ s.subjectName }}</Button
        >
      </FormItem>
      <FormItem label="考试名称">
        <Input v-model="examName" class="input-examname" clearable maxlength="45" placeholder="请输入考试名称" />
      </FormItem>
    </Form>

    <div class="btn-actions">
      <Button type="text" @click="cancelMerge">取消</Button>
      <Button type="primary" @click="saveMergeProject">{{ isModeCreate ? '新建' : '修改' }}</Button>
    </div>

    <modal-select-exams
      v-model="isModalSelectExamsVisible"
      :exams="exams"
      :selected-term="selectedTerm"
      :selected-grade="selectedGrade"
      @update-exams="updateExams"
    ></modal-select-exams>

    <modal-subject-setting
      v-model="isModalSubjectSettingVisible"
      :exam-details="examDetails"
      :modal-setting-subject-config="modalSettingSubjectConfig"
      @update-exam-subject-config="updateExamSubjectConfig"
    ></modal-subject-setting>
  </div>
</template>

<style lang="scss" scoped>
  .page-create-merge-exams {
    padding: 20px;
    background-color: white;

    .title {
      padding-bottom: 10px;
      border-bottom: 1px solid $color-border;
      font-weight: bold;
      font-size: $font-size-medium-x;
      line-height: 30px;
      user-select: none;
    }

    .form {
      margin: 20px 0;

      .radiogroup {
        padding-top: 4px;
        padding-bottom: 4px;
      }

      .date-picker-exam {
        width: 210px;
      }

      .select-termname {
        width: 210px;
      }

      .tips {
        display: inline-block;
        margin-right: 10px;
        user-select: none;

        .color-primary {
          color: $color-primary;
        }
      }

      .btn-subject-config:not(:first-child) {
        margin-left: 10px;
      }

      .checkbox-group-subjects {
        @include flex(row, flex-start, stretch);

        .checkbox-all {
          flex: none;
          white-space: nowrap;
        }

        .checkbox-group {
          flex: 1;
          margin-left: 24px;

          .checkbox-subject:not(:last-child) {
            margin-right: 20px;
          }
        }
      }

      .input-examname {
        width: 500px;
      }
    }

    .btn-actions {
      text-align: right;
    }
  }
</style>
