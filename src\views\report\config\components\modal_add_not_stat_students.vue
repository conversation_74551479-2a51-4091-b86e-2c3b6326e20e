<script>
  import TextButton from '@/components/text_button'

  import { apiGetExamSchoolCodes, apiGetExamStudentStatistic, apiGetExamStudents } from '@/api/emarking'
  import { apiSetReportTemplateNonCountingStudents } from '@/api/report'

  import { debounce } from '@/utils/function'
  import { groupArray } from '@/utils/array'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      examId: {
        type: String,
        default: '',
      },
      templateId: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue', 'refresh-not-stat-students'],

    data() {
      return {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        keyword: '',
        selectedSchoolId: '',
        selectedClassId: '',

        pageStudentList: [],
        filterTableColumns: [
          {
            type: 'selection',
            width: 40,
          },
          {
            title: '姓名',
            key: 'studentName',
          },
          {
            title: '学校',
            key: 'schoolName',
          },
          {
            title: '学籍号',
            key: 'studentCode',
          },
          {
            title: '班级',
            key: 'className',
          },
          {
            title: '准考号',
            key: 'admissionNumber',
          },
          {
            title: '操作',
            width: 100,
            align: 'center',
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'warning',
                  disabled: this.selectedNotStatStudentList.some(stu => stu.studentId === params.row.studentId),
                  onClick: () => this.addNotStatStudent(params.row),
                },
                () => '剔除'
              ),
          },
        ],

        examSchoolCodes: [],
        studentStatistic: [],

        tableMaxHeight: window.innerHeight - 200,
        tableSelectedRows: [],

        selectedNotStatStudentList: [],
        selectedNotStatStudentColumns: [
          // {
          //   type: 'selection',
          //   width: 40,
          // },
          {
            title: '姓名',
            key: 'studentName',
          },
          {
            title: '准考号',
            key: 'admissionNumber',
          },
          {
            title: '操作',
            width: 100,
            align: 'center',
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => this.recoverStatStudent(params.row),
                },
                () => '恢复'
              ),
          },
        ],
      }
    },

    computed: {
      statSchoolList() {
        const List = (this.studentStatistic || []).map(school => ({
          schoolId: school.schoolId,
          schoolName: school.schoolName,
          examSchoolCode: (
            this.examSchoolCodes.find(item => item.schoolId === school.schoolId) || { examSchoolCode: 0 }
          ).examSchoolCode,
        }))
        List.sort((a, b) => a.examSchoolCode - b.examSchoolCode)

        return List
      },
      selectSchoolClassList() {
        const TargetStatSchool = (this.studentStatistic || []).find(
          item => item.schoolId === (this.selectedSchoolId || '')
        )
        return (TargetStatSchool && TargetStatSchool.classes) || []
      },
    },

    methods: {
      // initial
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.currentPage = 1
          this.pageSize = 10
          this.total = 0
          this.keyword = ''
          this.selectedSchoolId = ''
          this.selectedClassId = ''
          this.pageStudentList = []
          this.examSchoolCodes = []
          this.studentStatistic = []
          this.tableSelectedRows = []
          this.selectedNotStatStudentList = []

          this.fetchExamStudentStatistic().finally(() => this.fetchPageExamStudents())
        } else {
          this.closeModal()
        }
      },

      // fetch
      fetchExamStudentStatistic() {
        return Promise.all([
          apiGetExamSchoolCodes(this.examId),
          apiGetExamStudentStatistic({
            examId: this.examId,
          }),
        ])
          .then(([responseExamSchoolCodes, responseStatistic]) => {
            this.examSchoolCodes = responseExamSchoolCodes || []
            this.studentStatistic = responseStatistic || []
          })
          .catch(() => {
            this.examSchoolCodes = []
            this.studentStatistic = []
          })
      },
      fetchPageExamStudents() {
        apiGetExamStudents({
          examId: this.examId,
          currentPage: this.currentPage || 1,
          schoolId: this.selectedSchoolId || undefined,
          classId: this.selectedClassId || undefined,
          keyword: (this.keyword && this.keyword.trim()) || '',
          pageSize: this.pageSize || 10,
        })
          .then(response => {
            this.pageStudentList = ((response && response.students) || []).map(stu => ({
              ...stu,
              schoolName:
                stu.schoolName ||
                (this.statSchoolList.find(item => item.schoolId === stu.schoolId) || { schoolName: '' }).schoolName,
            }))
            this.total = (response && response.total) || 0
          })
          .catch(() => {
            this.pageStudentList = []
            this.total = 0
          })
      },

      // component event
      onSeletedSchoolIdChanged(value) {
        this.selectedSchoolId = value || ''
        if (!this.selectSchoolClassList.some(cls => cls.classId === this.selectedClassId)) {
          this.selectedClassId = ''
        }
        this.onPageChange()
      },
      onSeletedClassIdChanged(value) {
        this.selectedClassId = value || ''
        this.onPageChange()
      },
      onKeywordChange: debounce(function () {
        this.onPageChange()
      }, 600),
      onPageChange(page = 1) {
        this.currentPage = page
        this.fetchPageExamStudents()
      },
      onPageSizeChange(size = 10) {
        this.pageSize = size
        this.onPageChange()
      },
      onTableSelectionChange(rows) {
        this.tableSelectedRows = (rows || []).filter(
          stu => !this.selectedNotStatStudentList.some(s => s.studentId === stu.studentId)
        )
      },
      addNotStatStudent(student) {
        if (!this.selectedNotStatStudentList.some(stu => stu.studentId === student.studentId)) {
          this.selectedNotStatStudentList.push(student)
        }
      },
      recoverStatStudent(student) {
        this.selectedNotStatStudentList = this.selectedNotStatStudentList.filter(
          stu => stu.studentId !== student.studentId
        )
      },
      addNotStatStudentBatch() {
        this.selectedNotStatStudentList.push(...this.tableSelectedRows)
        this.tableSelectedRows = []
      },

      // commit
      commitNotStatStudents() {
        if (this.selectedNotStatStudentList.length) {
          const GAStudent = groupArray(this.selectedNotStatStudentList, item => item.schoolId)
          Promise.all(
            GAStudent.map(item =>
              apiSetReportTemplateNonCountingStudents({
                templateId: this.templateId,
                schoolId: item.key,
                studentIds: item.group.map(s => s.studentId),
              })
            )
          ).then(() => {
            this.$Message.success({
              duration: 3,
              content: '操作成功',
            })
            this.$emit('refresh-not-stat-students')
            this.closeModal()
          })
        } else {
          this.$Message.warning({
            duration: 4,
            content: '请选择要剔除统计的学生',
          })
        }
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" fullscreen title="添加剔除学生" @on-visible-change="handleModalVisibleChange">
    <div class="modal-add-not-stat-student-panel">
      <div class="filter-part">
        <div class="filter-bar">
          <div class="selectors">
            <div class="selector-item">
              <span class="item-label">学校：</span>
              <Select
                :model-value="selectedSchoolId"
                filterable
                clearable
                style="width: 200px"
                @on-change="onSeletedSchoolIdChanged"
              >
                <Option v-for="sch of statSchoolList" :key="sch.schoolId" :value="sch.schoolId">{{
                  sch.schoolName
                }}</Option>
              </Select>
            </div>
            <div class="selector-item">
              <span class="item-label">班级：</span>
              <Select
                :model-value="selectedClassId"
                filterable
                clearable
                style="width: 120px"
                @on-change="onSeletedClassIdChanged"
              >
                <Option v-for="cls of selectSchoolClassList" :key="cls.classId" :value="cls.classId">{{
                  cls.className
                }}</Option>
              </Select>
            </div>
            <div class="selector-item">
              <Input
                v-model="keyword"
                clearable
                suffix="md-search"
                style="width: 300px"
                placeholder="姓名 / 准考号 / 学籍号"
                @on-change="onKeywordChange"
              />
            </div>
          </div>

          <div>
            <TextButton v-show="tableSelectedRows.length" type="warning" @click="addNotStatStudentBatch"
              >批量剔除已勾选学生</TextButton
            >
            <TextButton type="primary" icon="md-refresh" @click="fetchPageExamStudents">刷新</TextButton>
          </div>
        </div>

        <Table
          :columns="filterTableColumns"
          :data="pageStudentList"
          :max-height="tableMaxHeight"
          @on-selection-change="onTableSelectionChange"
        ></Table>

        <Page
          show-total
          show-elevator
          show-sizer
          style="float: right; margin-top: 10px"
          :total="total"
          :model-value="currentPage"
          :page-size="pageSize"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
        ></Page>
      </div>

      <div class="selected-part">
        <div class="part-title">剔除学生列表</div>

        <Table
          :columns="selectedNotStatStudentColumns"
          :data="selectedNotStatStudentList"
          :max-height="tableMaxHeight"
        ></Table>
      </div>
    </div>

    <template #footer>
      <Button type="default" @click="closeModal">取消</Button>
      <Button type="primary" @click="commitNotStatStudents">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-add-not-stat-student-panel {
    @include flex(row, flex-start, stretch);

    .filter-part {
      flex: 1;

      .filter-bar {
        @include flex(row, space-between, center);
        margin-bottom: 10px;

        .selectors {
          @include flex(row, flex-start, center);

          .selector-item {
            @include flex(row, flex-start, center);

            .item-label {
              white-space: nowrap;
              user-select: none;
            }

            &:not(:first-child) {
              margin-left: 1em;
            }
          }
        }
      }
    }

    .selected-part {
      flex: none;
      width: 500px;
      margin-left: 10px;
      padding-left: 10px;
      border-left: 1px dashed $color-border;

      .part-title {
        margin-bottom: 10px;
        color: $color-primary-dark;
        font-weight: bold;
      }
    }
  }
</style>
