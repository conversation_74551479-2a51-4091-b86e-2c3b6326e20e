<template>
  <div class="container-change-answer-sheet-block">
    <div class="container-header">
      <Icon class="icon-back" title="返回" type="ios-arrow-back" @click="back"></Icon>
      <span class="title">{{ title }}</span>
    </div>
    <div v-if="initialized" class="container-body">
      <div class="page-frame">
        <div class="frame-header">
          <BarSteps class="steps" :current="stepIndex" :steps="steps" @step-click="handleStepClick"></BarSteps>
          <div class="actions">
            <TextButton class="btn" type="primary" @click="showModalQuestionList = true">查看题目列表</TextButton>
            <TextButton v-if="templateBlocks.length > 0" class="btn" type="warning" @click="deleteAllBlocks"
              >删除所有题块</TextButton
            >
            <Divider type="vertical"></Divider>
            <Button class="btn-finish" type="primary" @click="handleBtnFinishClick">保存</Button>
          </div>
        </div>
        <Component
          :is="currentStep.component"
          :visible-mark-types="['blockDetect']"
          class="frame-body"
          @show-demo="handleShowDemo"
        ></Component>
      </div>
    </div>

    <ModalQuestionList v-model="showModalQuestionList" tab-name="subjective"></ModalQuestionList>

    <ModalDemo
      v-model="showModalDemo"
      :steps="steps"
      :init-step="currentStep.title"
      :init-tab="demoInitTab"
    ></ModalDemo>
  </div>
</template>

<script>
  import StepSubjective from './components/step_subjective'
  import StepScoreArea from './components/step_score_area'
  import StepMark from './components/step_mark'
  import BarSteps from './components/bar_steps'
  import ModalQuestionList from './components/modal_question_list'
  import ModalDemo from './components/modal_demo'

  import { apiGetAnswerSheetDetail, apiChangeAnswerSheetTemplateJson } from '@/api/emarking/answer_sheet'

  import { mapGetters } from 'vuex'
  import ObjectiveQuestion from '@/helpers/emarking/objective_question'
  import SubjectiveQuestion from '@/helpers/emarking/subjective_question'
  import Block from '@/helpers/emarking/block'
  import Template from '@/helpers/scan_template_define/template'
  import { deepCopy } from '@/utils/object'
  import StepStatusEnum from '@/enum/scan_template/step_status'

  export default {
    components: {
      StepSubjective,
      StepScoreArea,
      StepMark,
      BarSteps,
      ModalQuestionList,
      ModalDemo,
    },
    // 离开页面前检查是否有未保存修改
    beforeRouteLeave() {
      if (!this.initialized) {
        return
      }
      let newTemplate = this.getNewTemplate()
      if (JSON.stringify(newTemplate) == this.answerSheet.templateJson) {
        return
      }
      if (window.confirm('答题卡扫描模板已修改但尚未保存，是否放弃更改？')) {
        return
      } else {
        return false
      }
    },
    data() {
      return {
        // 答题卡
        answerSheet: null,
        // 答题卡客观题
        templateObjectives: [],
        // 是否已初始化
        initialized: false,
        // 当前步骤
        stepIndex: 0,
        // 显示题目列表
        showModalQuestionList: false,
        // 操作演示
        showModalDemo: false,
        demoInitTab: '',
      }
    },
    computed: {
      ...mapGetters('scanTemplate', [
        'pages',
        'subjectiveStatus',
        'selectGroups',
        'selectMarkStatus',
        'scoreAreaStatus',
        'templateBlocks',
      ]),
      sheetId() {
        return this.$route.params.id
      },
      title() {
        if (this.answerSheet?.name) {
          return `调整答题卡题块——${this.answerSheet?.name}`
        } else {
          return `调整答题卡题块`
        }
      },
      steps() {
        return [
          {
            title: '主观题',
            status: this.subjectiveStatus,
            component: StepSubjective,
          },
          {
            title: '打分框',
            status: this.scoreAreaStatus,
            component: StepScoreArea,
          },
          {
            title: '遮挡检测忽略区域',
            status: StepStatusEnum.Wait.id,
            component: StepMark,
          },
        ]
      },
      currentStep() {
        return this.steps[this.stepIndex]
      },
    },
    async created() {
      this.$Loading.start()
      this.$store.commit('scanTemplate/reset')
      try {
        await this.loadSheet()
        this.$Loading.finish()
      } catch {
        this.$Loading.error()
      }
    },
    methods: {
      // 加载答题卡
      async loadSheet() {
        this.answerSheet = await apiGetAnswerSheetDetail(this.sheetId)

        let templateObjectives = JSON.parse(this.answerSheet.objectivesJson)
        templateObjectives.forEach(obj => {
          Object.setPrototypeOf(obj, ObjectiveQuestion.prototype)
        })
        this.templateObjectives = templateObjectives

        // 从templateJson中提取题目打分框
        let templateSubjectives = []
        JSON.parse(this.answerSheet.templateJson).questions.subjectives.forEach(block => {
          templateSubjectives.push(...block.questions)
        })
        let templateBlocks = JSON.parse(this.answerSheet.blocksJson)
        templateBlocks.forEach(b => {
          b.questions.forEach(subj => {
            Object.setPrototypeOf(subj, SubjectiveQuestion.prototype)
            let templateSubj = templateSubjectives.find(
              x => x.questionCode == subj.questionCode && x.branchCode == subj.branchCode
            )
            if (templateSubj) {
              subj.scoreType = templateSubj.scoreType
              subj.scoreAreaIds = templateSubj.scoreAreaIds
            }
          })
          Object.setPrototypeOf(b, Block.prototype)
        })

        await this.$store.dispatch('scanTemplate/import', {
          imageUrls: this.answerSheet.imageUrls,
          templateJson: this.answerSheet.templateJson,
          subjectObjectives: templateObjectives,
          subjectBlocks: templateBlocks,
          enableEditQuestion: true,
          onlyEditBlock: true,
        })
        this.initialized = true
      },
      // 删除所有题块
      deleteAllBlocks() {
        if (this.templateBlocks.length == 0) {
          return
        }
        this.$Modal.confirm({
          title: '确认删除',
          content: '是否删除所有题块？（不会删除科目主观题）',
          onOk: () => {
            let batch = []
            this.pages.forEach(page => {
              page.subjectiveAreas.forEach(area => {
                batch.push({
                  pageIndex: area.pageIndex,
                  area,
                })
              })
            })
            this.$store.commit('scanTemplate/deleteSubjectiveAreaBatch', batch)
            if (this.selectGroups.length > 0) {
              let selectGroupNames = this.selectGroups.map(g => g.selectGroupName)
              this.$store.commit('scanTemplate/deleteSelectGroups', selectGroupNames)
            }
          },
        })
      },
      // 点击步骤
      handleStepClick(step) {
        let index = this.steps.findIndex(s => s.title === step.title)
        if (index >= 0) {
          this.stepIndex = index
        }
      },
      // 操作演示
      handleShowDemo(activeTab) {
        this.demoInitTab = activeTab || ''
        this.showModalDemo = true
      },
      // 获取新扫描模板
      getNewTemplate() {
        let template = JSON.parse(this.answerSheet.templateJson)
        let newTemplate
        try {
          newTemplate = this.$store.state.scanTemplate.scanTemplate.export()
        } catch (err) {
          let message = '导出模板失败'
          if (typeof err == 'string') {
            message += '：' + err
          }
          this.$Message.error(message)
          return
        }
        template.pages.forEach((page, idx) => {
          let newPage = newTemplate.pages[idx]
          // 主观题
          page.subjectiveAreas = newPage.subjectiveAreas
          // 选做题
          page.selectMarkAreas = newPage.selectMarkAreas
          // 对错打分框
          if (page.trueFalseScoreAreas) {
            page.trueFalseScoreAreas.forEach(area => {
              let newArea = newPage.trueFalseScoreAreas.find(x => x.id == area.id)
              if (newArea) {
                area.searchArea = newArea.searchArea
              }
            })
          }
          // 遮挡检测忽略区域
          page.pageParam.blockDetectSkipAreas = newPage.pageParam.blockDetectSkipAreas
        })
        template.questions.subjectives = newTemplate.questions.subjectives
        Object.setPrototypeOf(template, Template.prototype)
        return template
      },

      // 完成修改
      async handleBtnFinishClick() {
        if (!this.checkFinish()) {
          return
        }

        let template = this.getNewTemplate()
        if (!this.checkTemplate(template)) {
          return
        }

        try {
          this.$TransparentSpin.show()

          let templateJson = JSON.stringify(template)

          let blocks = deepCopy(this.templateBlocks)
          blocks.forEach(b => {
            b.questions.forEach(subj => {
              delete subj.scoreType
              delete subj.scoreAreaIds
            })
          })
          let blocksJson = JSON.stringify(blocks)

          await apiChangeAnswerSheetTemplateJson({
            id: this.answerSheet.id,
            templateJson,
            blocksJson,
          })
          this.answerSheet.templateJson = templateJson
          this.$Message.success('已保存')
          setTimeout(() => {
            this.back()
          }, 1000)
        } finally {
          this.$TransparentSpin.hide()
        }
      },
      checkFinish() {
        let message = ''
        if (this.subjectiveStatus != StepStatusEnum.Finish.id) {
          message = '尚有主观题未加入题块'
        } else if (this.selectMarkStatus != StepStatusEnum.Finish.id) {
          message = '尚有选做题未添加选做标记'
        } else if (this.scoreAreaStatus != StepStatusEnum.Finish.id) {
          message = '尚有对错打分框未添加'
        }
        if (message) {
          this.$Message.warning({
            content: message,
            duration: 5,
            closable: true,
          })
        }
        return !message
      },
      checkTemplate(template) {
        let { errors } = template.check({
          subjectObjectives: this.templateObjectives,
          subjectBlocks: this.templateBlocks,
          pageSizeList: this.pages.map(p => ({
            width: p.image.width,
            height: p.image.height,
          })),
        })
        // 只提示errors，跳过warnings
        if (errors.length > 0) {
          let content =
            '本扫描模板存在以下问题，请您检查修改：<br>' +
            errors.map((msg, idx) => `${idx + 1}. ${msg}`).join('<br>') +
            '<br>若本页面无法修改，请退出本页面再进入答题卡编辑页面进行操作'
          this.$Modal.error({
            title: '请您检查',
            content,
          })
        }
        return errors.length == 0
      },
      back() {
        this.$router.back()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-header {
    @include flex(row, flex-start, center);
    width: 100%;
    height: 40px;
    padding-right: 20px;
    padding-left: 20px;
    border-bottom: 1px solid $color-border;
    color: white;
    font-size: $font-size-medium-x;
    line-height: 40px;
    background-color: $color-primary-dark;

    .icon-back {
      margin-right: 10px;
      font-size: $font-size-large;
      cursor: pointer;
    }

    .title {
      flex-grow: 1;
      margin-right: auto;
    }
  }

  :deep(.page-frame) {
    background-color: white;

    .frame-header {
      @include flex(row, space-between, center);
      height: 40px;
      padding-right: 10px;
      padding-left: 20px;
      border-bottom: 1px solid $color-border;

      .steps {
        width: 400px;
        margin-right: 50px;
      }

      .actions {
        @include flex(row, flex-start, center);

        .btn {
          margin-right: 15px;
        }

        .btn-finish {
          margin-left: 20px;
        }
      }
    }

    .frame-body {
      @include flex(row, flex-start, flex-start);
      position: relative;
      height: calc(100vh - 40px - 40px);
      overflow: hidden;

      .pane {
        height: 100%;
        overflow: auto;

        &:first-child {
          flex-grow: 1;
          flex-shrink: 1;
        }

        &:not(:first-child) {
          flex-grow: 0;
          flex-shrink: 0;
          border-left: 1px solid $color-border;
        }

        .header {
          @include flex(row, flex-start, center);
          height: 36px;
          padding-left: 20px;
          border-bottom: 1px solid $color-border;

          .ivu-radio-wrapper:not(:first-child) {
            margin-left: 10px;
          }
        }

        .body {
          height: calc(100vh - 116px);
          overflow: auto;
        }
      }
    }
  }
</style>
