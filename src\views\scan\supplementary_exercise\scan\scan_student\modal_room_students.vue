<template>
  <Modal
    class="modal-scan-room-students"
    :model-value="modelValue"
    :mask-closable="false"
    title="查看考生答卷"
    fullscreen
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <ScanStudent
      v-if="showContent"
      :init-room-no="roomNo"
      :init-student-id="studentId"
      :init-unit-id="unitId"
      :top="51"
    ></ScanStudent>
  </Modal>
</template>

<script>
  import ScanStudent from './scan_student.vue'

  export default {
    components: {
      ScanStudent,
    },
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      roomNo: Number,
      studentId: String,
      unitId: String,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        showContent: false,
      }
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.showContent = true
        }
      },
    },
    methods: {
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.$emit('update:modelValue', false)
          setTimeout(() => {
            this.showContent = false
          }, 500)
        }
      },
    },
  }
</script>

<style lang="scss">
  .modal-scan-room-students {
    .ivu-modal-body {
      padding: 0;
    }
  }
</style>
