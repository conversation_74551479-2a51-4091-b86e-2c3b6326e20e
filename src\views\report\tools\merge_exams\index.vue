<script>
  import NoData from '@/components/no_data'
  import ModalMergeProgress from './modal_merge_progress.vue'

  import {
    apiGetPageMergedExamList,
    apiStartExamsMergeProgress,
    apiCopyMergeExam,
    apiDeleteMergeExam,
  } from '@/api/emarking'

  import { formatDateTime } from '@/utils/date'

  import MergeProjectStatuses from '@/enum/emarking/merge_exam/project_status.js'

  export default {
    components: {
      'no-data': NoData,
      'modal-merge-progress': ModalMergeProgress,
    },

    data() {
      return {
        mergedExamList: [],

        total: 0,
        currentPage: 1,
        pageSize: 10,

        // 复制合并考试项目
        isModalCopyProjectShow: false,
        modalCopyProjectId: '',
        modalCopyProjectName: '',

        // 查看项目合并进度
        isModalMergeProgressShow: false,
        modalProjectMergeProgress: null,
        modalProjectStatus: '',
      }
    },

    computed: {
      userInfo() {
        return this.$store.getters['user/info']
      },
      isSystemUser() {
        return (this.userInfo && this.userInfo.isSystem) || false
      },
      mergeProjectStatuses() {
        return MergeProjectStatuses.getEntries()
      },
    },

    created() {
      this.fetchPageMergeExamList()
    },

    methods: {
      routerBack() {
        if (this.$route && this.$route.redirectedFrom) {
          this.$router.replace({
            name: 'report-tools',
          })
        } else {
          this.$router.back()
        }
      },

      fetchPageMergeExamList() {
        if (this.isSystemUser) {
          this.$TransparentSpin.show()
          return apiGetPageMergedExamList({
            currentPage: this.currentPage,
            pageSize: this.pageSize,
          })
            .then(response => {
              this.total = (response && response.total) || 0
              this.mergedExamList = ((response && response.records) || []).map(r => {
                const CreateTime = (r.createTime && new Date(r.createTime)) || null
                const Config = JSON.parse(r.configJson)

                return {
                  ...r,
                  createTimeText: (CreateTime && formatDateTime(CreateTime)) || '',
                  statusText: this.mergeProjectStatuses.find(s => s.id === r.status)?.name || '',
                  isDeletable: [MergeProjectStatuses.Pending.id, MergeProjectStatuses.Failed.id].includes(r.status),
                  isMerging: [MergeProjectStatuses.Waiting.id, MergeProjectStatuses.InProgress.id].includes(r.status),
                  isSuccess: r.status === MergeProjectStatuses.Succeeded.id,
                  examNameText: r.examName || (Config && Config.examName),
                  config: Config,
                  progress: JSON.parse(r.progressJson),
                }
              })
            })
            .catch(() => {
              this.total = 0
              this.mergedExamList = []
            })
            .finally(() => {
              this.$TransparentSpin.hide()
            })
        } else {
          this.$Message.error({
            duration: 1.3,
            content: '权限不足',
            closable: true,
            onClose: () => this.routerBack(),
          })
          return Promise.resolve()
        }
      },
      checkMergeProgress(projectId) {
        const TargetProject = this.mergedExamList.find(e => e.id === projectId)
        if (TargetProject) {
          this.modalProjectMergeProgress = {
            id: projectId,
            name: TargetProject.examNameText,
            detail: TargetProject.config,
            progress: TargetProject.progress,
          }
          this.modalProjectStatus = TargetProject.status
          this.isModalMergeProgressShow = true
        }
      },
      startMerge(projectId) {
        return apiStartExamsMergeProgress(projectId)
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '合并考试项目已开始，请稍后查看进度',
            })
          })
          .finally(() => {
            this.fetchPageMergeExamList()
          })
      },
      deleteMergeProject(projectId) {
        this.$Modal.confirm({
          title: '删除合并考试项目',
          content: '确定要删除该合并考试项目吗？',
          onOk: () => {
            return apiDeleteMergeExam(projectId)
              .then(() => {
                this.$Message.success({
                  duration: 3,
                  content: '删除成功',
                })
              })
              .catch(() => {
                this.$Message.error({
                  duration: 1.3,
                  content: '删除失败，请稍后重试',
                })
              })
              .finally(() => this.fetchPageMergeExamList())
          },
        })
      },
      handleBtnCopyProjectClick(projectId) {
        this.modalCopyProjectId = projectId
        this.modalCopyProjectName = ''
        this.isModalCopyProjectShow = true
      },
      copyProject() {
        const TrimedModalCopyProjectName = this.modalCopyProjectName.trim()
        if (!TrimedModalCopyProjectName) {
          this.$Message.warning({
            duration: 3,
            content: '请输入考试项目名称',
          })
          return
        }

        if (!this.modalCopyProjectId) {
          this.$Message.warning({
            duration: 3,
            content: '复制失败，请稍后重试',
          })
          return
        }

        apiCopyMergeExam({
          id: this.modalCopyProjectId,
          examName: TrimedModalCopyProjectName,
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '复制成功',
            })
            this.isModalCopyProjectShow = false
          })
          .finally(() => this.onPageChange())
      },
      enterPageEidtProjectSettings(projectId) {
        this.$router.push({
          name: 'report-tool-edit-merge-exam',
          params: {
            id: projectId,
          },
        })
      },

      onPageChange(page = 1) {
        this.currentPage = page
        this.fetchPageMergeExamList()
      },
      onPageSizeChange(size = 10) {
        this.pageSize = size
        this.onPageChange()
      },

      entoCreateMergePage() {
        this.$router.push({
          name: 'report-tool-create-merge-exam',
        })
      },

      gotoSucceededProject(exam) {
        if (exam.status === MergeProjectStatuses.Succeeded.id) {
          this.$router.push({
            name: 'emarking-exam-info',
            params: {
              examId: exam.examId,
            },
          })
        }
      },

      updateModalProjectStatus(newStatus) {
        const TargetProject = this.mergedExamList.find(item => item.id === (newStatus?.id || ''))
        if (TargetProject) {
          TargetProject.status = newStatus.status
          TargetProject.statusText = this.mergeProjectStatuses.find(s => s.id === TargetProject.status)?.name || ''
          TargetProject.progressJson = newStatus.progressJson
          TargetProject.progress = JSON.parse(TargetProject.progressJson)
          TargetProject.isDeletable = [MergeProjectStatuses.Pending.id, MergeProjectStatuses.Failed.id].includes(
            TargetProject.status
          )
          TargetProject.isMerging = [MergeProjectStatuses.Waiting.id, MergeProjectStatuses.InProgress.id].includes(
            TargetProject.status
          )
          TargetProject.isSuccess = TargetProject.status === MergeProjectStatuses.Succeeded.id

          this.modalProjectMergeProgress.progress = TargetProject.progress
          this.modalProjectStatus = TargetProject.status
        }
      },
    },
  }
</script>

<template>
  <div v-if="isSystemUser" class="page-merge-exams">
    <div class="page-header">
      <div class="header-left">
        <TextButton type="primary" title="返回报表工具" icon="ios-arrow-back" @click="routerBack"></TextButton>
        <span>报表工具 - 合并考试项目</span>
      </div>

      <div>
        <Button ghost type="primary" icon="md-refresh" class="btn-refresh" @click="fetchPageMergeExamList">刷新</Button>
        <Button ghost type="primary" icon="md-add" class="btn-create-merge-exam" @click="entoCreateMergePage"
          >新建合并考试</Button
        >
      </div>
    </div>

    <div class="merged-exam-list-panel">
      <template v-if="total">
        <div class="exam-cards">
          <div v-for="e of mergedExamList" :key="e.id + e.examId || ''" class="exam-card">
            <div class="card-left">
              <div
                :class="{ 'title-router': e.status === 'succeeded' }"
                class="project-name"
                @click="gotoSucceededProject(e)"
              >
                {{ e.examNameText || '-' }}
                <div class="tag">{{ e.statusText }}</div>
              </div>
              <div class="project-info">
                <div class="info-item">
                  <span class="item-label">创建者：</span>
                  <span class="item-cotent">{{ e.createByRealName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="item-label">创建时间：</span>
                  <span class="item-content">{{ e.createTimeText || '-' }}</span>
                </div>
              </div>
            </div>

            <div class="card-right">
              <Button v-if="!e.isSuccess" ghost type="primary" class="action-btn" @click="checkMergeProgress(e.id)"
                >查看进度</Button
              >
              <Button ghost type="primary" class="action-btn" @click="handleBtnCopyProjectClick(e.id)">复制</Button>
              <Button v-if="!e.isMerging && !e.isSuccess" type="primary" class="action-btn" @click="startMerge(e.id)"
                >合并</Button
              >
              <Button v-if="!e.isMerging" type="primary" class="action-btn" @click="enterPageEidtProjectSettings(e.id)"
                >编辑</Button
              >
              <Button v-if="e.isDeletable" type="error" class="action-btn" @click="deleteMergeProject(e.id)"
                >删除</Button
              >
            </div>
          </div>
        </div>

        <div class="pager">
          <Page
            :model-value="currentPage"
            :total="total"
            :page-size="pageSize"
            :page-size-opts="[10, 20, 50]"
            show-total
            show-elevator
            show-sizer
            @on-change="onPageChange"
            @on-page-size-change="onPageSizeChange"
          ></Page>
        </div>
      </template>
      <no-data v-else class="no-data"
        >暂无合并的考试项目 <TextButton type="primary" @click="onPageChange">刷新</TextButton></no-data
      >
    </div>

    <Modal v-model="isModalCopyProjectShow" title="复制合并考试项目">
      <div class="modal-copy-project-panel">
        <div class="label">考试项目名称：</div>
        <Input
          v-model="modalCopyProjectName"
          clearable
          maxlength="45"
          style="width: 300px"
          placeholder="请输入考试名称"
        />
      </div>

      <template #footer>
        <Button type="text" @click="isModalCopyProjectShow = false">取消</Button>
        <Button type="primary" @click="copyProject">确定</Button>
      </template>
    </Modal>

    <modal-merge-progress
      v-model="isModalMergeProgressShow"
      :project-status="modalProjectStatus"
      :project-merge-progress="modalProjectMergeProgress"
      @update-project-status="updateModalProjectStatus"
    ></modal-merge-progress>
  </div>
</template>

<style lang="scss" scoped>
  .page-merge-exams {
    min-height: 500px;
    padding: 20px;
    background-color: white;

    .page-header {
      @include flex(row, space-between, center);
      margin-bottom: 20px;

      .header-left {
        font-size: $font-size-large;
        user-select: none;
      }

      .btn-refresh {
        height: 28px;
        font-size: $font-size-medium;
      }

      .btn-create-merge-exam {
        height: 28px;
        border-color: #13ce66;
        color: #13ce66;
        font-size: $font-size-medium;
        background-color: #e8faf0;
      }
    }

    .merged-exam-list-panel {
      padding: 0 10px;

      .exam-cards {
        .exam-card {
          @include flex(row, space-between, center);
          padding: 24px;
          border: 1px solid transparent;
          border-radius: 4px;
          box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          .card-left {
            flex: 1;

            .project-name {
              margin-bottom: 22px;
              font-weight: bold;
              font-size: $font-size-medium-x;
              vertical-align: middle;

              .tag {
                display: inline-block;
                margin-left: 10px;
                padding: 2px 10px;
                border: 1px solid $color-primary;
                border-color: #2fb0f1;
                border-radius: 2px;
                color: #2fb0f1;
                font-weight: normal;
                font-size: $font-size-small;
                vertical-align: middle;
              }
            }

            .title-router:hover {
              cursor: pointer;
            }

            .project-info {
              @include flex(row, flex-start, center);
              color: $color-icon;
              font-size: $font-size-small;
              line-height: 32px;

              .info-item:not(:first-child) {
                margin-left: 1.6em;
              }
            }
          }

          .card-right {
            flex: 0;
            white-space: nowrap;

            .action-btn:not(:first-child) {
              margin-left: 2px;
            }
          }

          &:hover {
            border-color: $color-primary;
            transform: scale(1.02);
          }

          &:not(:last-child) {
            margin-bottom: 24px;
          }
        }
      }

      .pager {
        margin-top: 20px;
        text-align: right;
      }

      .no-data {
        height: calc(80vh);
        user-select: none;
      }
    }
  }

  .modal-copy-project-panel {
    @include flex(row, flex-start, center);

    .label {
      margin-right: 1em;
    }
  }
</style>
