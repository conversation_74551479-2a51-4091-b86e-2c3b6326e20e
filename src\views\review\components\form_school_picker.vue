<template>
  <div class="container-form-base-info">
    <div class="header" :class="{ 'header-1': isDetail }">
      <div class="header-left">
        <span>选择参赛学校</span>
        <div class="box-stat">
          <span v-if="canEdit">共 {{ filterSchoolCount }} 所学校，</span>
          <span>已选 {{ checkedDataCount }} 所<span v-if="!canEdit">学校</span> </span>
          <Button v-if="canEdit" class="btn-save" type="primary" @click="onSave">保存</Button>
        </div>
      </div>
      <div v-if="isDetail" class="box-btn-edit">
        <Button type="primary" ghost @click="onEdit">{{ canEdit ? '取消编辑' : '编辑' }}</Button>
      </div>
    </div>
    <div class="body">
      <div class="box-school-tree">
        <div v-if="isDetail && !canEdit" class="box-selected-school">
          <div v-for="s in checkedData" :key="s.id" class="school-item">
            <Icon type="ios-checkbox-outline" size="18" color="#999" />
            <span style="margin-left: 4px">{{ s.name }}</span>
          </div>
        </div>
        <template v-else>
          <el-tree
            ref="tree"
            :data="filterSchoolTree"
            :check-strictly="false"
            show-checkbox
            default-expand-all
            node-key="schoolId"
            highlight-current
            :props="defaultProps"
            @check="onTreeCheck"
          >
          </el-tree>
        </template>
      </div>
      <div v-if="!isDetail" class="box-button">
        <Button @click="onPrev">上一步</Button>
        <Button type="primary" @click="onNext">下一步</Button>
      </div>
    </div>
  </div>
</template>

<script>
  import { nextTick } from 'vue'
  import { ElTree } from 'element-plus'
  import 'element-plus/es/components/tree/style/css'
  import 'element-plus/es/components/button/style/css'
  import { apiAddActivitySchools, apiDeleteActivitySchools } from '@/api/review/activity'

  export default {
    components: {
      'el-tree': ElTree,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      activityDetail: {
        type: Object,
        default: () => {},
      },
      checkedData: {
        type: Array,
        default: () => [],
      },
      schoolTree: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['on-next', 'on-prev', 'on-refresh'],
    data() {
      return {
        defaultProps: {
          label: 'schoolName',
          children: 'schools',
        },
        canEdit: false,
        checkedDataCount: 0,
        filterSchoolCount: 0,
        filterSchoolTree: [],
      }
    },
    computed: {
      // filterSchoolTree() {
      //   return this.schoolTree
      //     .filter(
      //       item =>
      //         (item.schoolType === 1 && Array.isArray(item.schools) && item.schools.length) || item.schoolType === 0
      //     )
      //     .map(item => {
      //       let schools = this.buildTree(item.schools)
      //       return {
      //         ...item,
      //         disabled: this.isDetail && !this.canEdit,
      //         schools,
      //       }
      //     })
      // },
    },
    watch: {
      checkedData: {
        handler: function (newVal) {
          if (Array.isArray(newVal) && newVal.length) {
            this.checkedDataCount = newVal.length
          }
        },
        immediate: true,
        deep: true,
      },
      schoolTree: {
        handler: function (newVal) {
          if (Array.isArray(newVal) && newVal.length) {
            let tree = this.schoolTree
              .filter(
                item =>
                  (item.schoolType === 1 && Array.isArray(item.schools) && item.schools.length) || item.schoolType === 0
              )
              .map(item => {
                let schools = this.buildTree(item.schools)
                return {
                  ...item,
                  disabled: false,
                  schools,
                }
              })
            let flatList = this.flattenArray(tree)

            if (Array.isArray(this.checkedData) && this.checkedData.length) {
              this.checkedData.forEach(item => {
                if (!flatList.some(s => s.schoolId === item.id)) {
                  tree.push({
                    schoolId: item.id,
                    schoolName: item.name,
                    schoolType: 0,
                    schools: [],
                  })
                }
              })
            }

            this.filterSchoolCount = flatList.length
            this.filterSchoolTree = tree
          }
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {
      this.setTreeChecked()
    },
    beforeUnmount() {
      let checkedNodes = this.$refs.tree.getCheckedNodes()
      this.$store.dispatch('review/setCheckedSchools', checkedNodes)
    },
    methods: {
      flattenArray(arr) {
        return arr.reduce((acc, val) => {
          if (val.schoolType === 0) {
            acc.push(val)
          }
          if (Array.isArray(val.schools)) {
            return acc.concat(this.flattenArray(val.schools))
          }
          return acc
        }, [])
      },
      onTreeCheck(node, { checkedNodes }) {
        this.checkedDataCount = checkedNodes.filter(s => s.schoolType === 0).length
      },
      setTreeChecked() {
        if (this.isDetail) {
          if (this.canEdit) {
            const checkedKeys = this.checkedData.map(s => s.id)

            nextTick(() => {
              this.$refs.tree.setCheckedKeys(checkedKeys)
            })
          }
        } else {
          nextTick(() => {
            let checkedSchools = this.$store.getters['review/checkedSchools']

            if (checkedSchools) {
              this.checkedDataCount = checkedSchools.filter(item => item.schoolType === 0).length
              this.$refs.tree.setCheckedNodes(checkedSchools)
            }
          })
        }
      },
      buildTree(data = []) {
        return data
          .filter(
            item =>
              (item.schoolType === 1 && Array.isArray(item.schools) && item.schools.length) || item.schoolType === 0
          )
          .map(item => {
            const children = item.schools ? this.buildTree(item.schools) : []

            return {
              ...item,
              disabled: false,
              schools: children,
            }
          })
      },
      onNext() {
        let checkedNodes = this.$refs.tree.getCheckedNodes()
        this.$store.dispatch('review/setCheckedSchools', checkedNodes)

        this.$emit('on-next')
      },
      onPrev() {
        let checkedNodes = this.$refs.tree.getCheckedNodes()
        this.$store.dispatch('review/setCheckedSchools', checkedNodes)

        this.$emit('on-prev')
      },
      onEdit() {
        this.canEdit = !this.canEdit
        nextTick(() => {
          this.setTreeChecked()
        })
      },
      onSave() {
        const oldCheckedKeys = this.checkedData.map(item => item.id)
        let newCheckedNodes = this.$refs.tree.getCheckedNodes()
        let newCheckedKeys = newCheckedNodes.filter(item => item.schoolType === 0).map(item => item.schoolId)
        let newKeys = []
        let deleteKeys = []
        let request = []

        newCheckedKeys.forEach(item => {
          if (!oldCheckedKeys.some(k => k === item)) {
            newKeys.push(item)
          }
        })

        oldCheckedKeys.forEach(item => {
          if (!newCheckedKeys.some(k => k === item)) {
            deleteKeys.push(item)
          }
        })

        if (newKeys?.length) {
          request.push(
            apiAddActivitySchools({
              activityId: this.activityDetail.id,
              schoolIds: newKeys,
            })
          )
        }

        if (deleteKeys?.length) {
          request.push(
            apiDeleteActivitySchools({
              activityId: this.activityDetail.id,
              schoolIds: deleteKeys,
            })
          )
        }

        if (request.length) {
          Promise.all(request)
            .then(() => {
              this.$Message.success('已保存')
            })
            .finally(() => {
              this.canEdit = false
              this.$emit('on-refresh')
            })
        } else {
          this.canEdit = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-form-base-info {
    .header {
      @include flex(row, space-between, center);
      position: sticky;
      top: 0;
      z-index: 10;
      padding: 10px 0;
      background-color: #fff;

      .header-left {
        @include flex(row, flex-start, center);
      }

      .box-stat {
        margin-left: 50px;
        font-size: 14px;
      }
    }

    .header-1 {
      top: 50px;
    }

    .box-school-tree {
      padding-left: 50px;
    }

    .box-selected-school {
      padding-left: 30px;

      .school-item {
        @include flex(row, flex-start, center);
        font-size: 14px;
        line-height: 2;
      }
    }

    .box-button {
      padding: 30px 50px;
    }

    .btn-save {
      margin-left: 30px;
    }
  }
</style>
