<template>
  <div class="card-general-groups">
    <div class="card-title">小组分析</div>
    <div class="card-content">
      <div class="card-left">
        <TextButton class="btn-goto-class" type="primary" @click="gotoClassPage">设置分组</TextButton>
        <Table class="table-groups" :columns="tableColumns" :data="groups"></Table>
      </div>
      <div ref="chart" class="card-right" :class="{ hidden: groups.length <= 1 }"></div>
    </div>
  </div>
</template>

<script>
  import { sumByDefaultZero, roundNumber } from '@/utils/math'
  import echarts from '@/utils/echarts'

  export default {
    props: {
      classDetail: Object,
    },
    data() {
      return {
        chartInstance: null,
      }
    },
    computed: {
      groups() {
        let list = []
        list.push({
          groupName: '全班',
          studentCount: this.classDetail.total,
          studentIds: this.classDetail.studentScoreList.map(stu => stu.studentId),
        })
        this.classDetail.studentGroups.forEach(g => {
          list.push({
            groupName: g.groupName,
            studentCount: g.studentList.length,
            studentIds: g.studentList.map(stu => stu.id),
          })
        })
        list.forEach(g => {
          let scannedStudents = this.classDetail.studentScoreList.filter(stu => g.studentIds.includes(stu.studentId))
          g.scannedCount = scannedStudents.length
          if (g.scannedCount > 0) {
            g.maxScore = Math.max(...scannedStudents.map(stu => stu.score))
            g.minScore = Math.min(...scannedStudents.map(stu => stu.score))
            g.avgScore = roundNumber(sumByDefaultZero(scannedStudents, stu => stu.score) / g.scannedCount, 1)
          } else {
            g.maxScore = ''
            g.minScore = ''
            g.avgScore = ''
          }
        })
        return list
      },
      tableColumns() {
        return [
          {
            title: '组名',
            key: 'groupName',
          },
          {
            title: '组人数',
            key: 'studentCount',
            align: 'center',
          },
          {
            title: '统计人数',
            key: 'scannedCount',
            align: 'center',
          },
          {
            title: '最高分',
            key: 'maxScore',
            align: 'center',
          },
          {
            title: '平均分',
            key: 'avgScore',
            align: 'center',
          },
          {
            title: '最低分',
            key: 'minScore',
            align: 'center',
          },
        ]
      },
      chartOption() {
        let groups = this.groups.slice(1)
        return {
          tooltip: {
            trigger: 'item',
          },
          grid: {
            top: 24,
            bottom: 24,
            right: 24,
          },
          legend: {
            show: true,
          },
          xAxis: {
            type: 'category',
            data: groups.map(g => g.groupName),
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              type: 'bar',
              data: groups.map(g => ({
                name: g.groupName,
                value: g.maxScore,
                itemStyle: {
                  color: '#3E70FF',
                },
              })),
              barWidth: 16,
            },
            {
              type: 'bar',
              data: groups.map(g => ({
                name: g.groupName,
                value: g.avgScore,
                itemStyle: {
                  color: '#3ECBC4',
                },
              })),
              barWidth: 16,
            },
            {
              type: 'bar',
              data: groups.map(g => ({
                name: g.groupName,
                value: g.minScore,
                itemStyle: {
                  color: '#FFC43F',
                },
              })),
              barWidth: 16,
            },
          ],
        }
      },
    },
    watch: {
      chartOption() {
        this.drawChart()
      },
    },
    mounted() {
      if (this.$refs.chart) {
        this.chartInstance = echarts.init(this.$refs.chart)
        this.drawChart()
      }
    },
    beforeUnmount() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
    },
    methods: {
      gotoClassPage() {
        let routeName
        if (this.$store.getters['user/isSchoolAdministrator'] || this.$store.getters['user/isSchoolLeader']) {
          routeName = 'teching_class'
        } else {
          routeName = 'teacherClasses'
        }
        this.$router.push({
          name: routeName,
        })
      },
      drawChart() {
        if (this.chartInstance) {
          this.chartInstance.setOption(this.chartOption, true)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .card-left {
    position: relative;

    .btn-goto-class {
      position: absolute;
      top: -27px;
      right: 0;
    }
  }

  .card-right {
    height: 300px;

    &.hidden {
      visibility: hidden;
    }
  }
</style>
