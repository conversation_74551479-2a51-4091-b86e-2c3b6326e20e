<template>
  <div class="container-review-task">
    <div class="section-action-bar">
      <!-- <div class="date-picker">
        <span>创建时间：</span>
        <DatePicker
          v-model="minCreateTime"
          type="date"
          placeholder="创建时间"
          style="width: 200px"
          @on-change="onChangeDate"
        />
      </div> -->
      <div class="search-bar">
        <Input
          :model-value="keyword"
          class="keyword-serach"
          clearable
          transfer
          placeholder="评审活动名称"
          suffix="md-search"
          style="width: 250px"
          @on-change="onChangeKeyword"
          @on-clear="onChangeKeyword"
        />
      </div>
    </div>
    <template v-if="taskList.length">
      <div class="section-list">
        <div v-for="item in taskList" :key="item.id" class="list-item">
          <div class="list-item-top">
            <div class="item-name" @click="onToReview(item)">{{ item.activityName }}</div>
            <div class="item-status">{{ item.statusText }}</div>
          </div>
          <div class="list-item-middle">
            <div class="item-category">{{ item.categoryName }}</div>
            <div class="item-info">满分：{{ item.fullScore }} 分</div>
            <div class="item-info">评审方式：{{ item.scoreModeName }}</div>
          </div>
          <div class="list-item-bottom">
            <div class="item-progress">
              <div class="item-progress-total">
                <div class="progress-bar">
                  <span>总体进度：</span>
                  <span class="progress">
                    <Progress class="progress-body" :percent="item.overallProgress.percent"></Progress>
                  </span>
                </div>
                <div class="progress-info">
                  <span class="progress-info-item">全部：{{ item.overallProgress.total }}</span>
                  <span class="progress-info-item">已评：{{ item.overallProgress.reviewed }}</span>
                  <span class="progress-info-item">待评：{{ item.overallProgress.unreviewed }}</span>
                </div>
              </div>
              <div class="item-progress-total">
                <div class="progress-bar">
                  <span>我的进度：</span>
                  <span class="progress">
                    <Progress class="progress-body" :percent="item.myProgress.percent"></Progress>
                  </span>
                </div>
                <div class="progress-info">
                  <span class="progress-info-item">全部：{{ item.myProgress.total }}</span>
                  <span class="progress-info-item">已评：{{ item.myProgress.reviewed }}</span>
                  <span class="progress-info-item">待评：{{ item.myProgress.unreviewed }}</span>
                </div>
              </div>
            </div>
            <div class="item-action">
              <Button type="primary" @click="onToReview(item)">进入评审</Button>
            </div>
          </div>
        </div>
      </div>
      <div class="section-page">
        <Page v-model="pageNum" :total="total" :page-size="pageSize" show-total show-elevator @on-change="changePage" />
      </div>
    </template>
    <NoData v-else />
  </div>
</template>

<script>
  import NoData from '@/components/no_data'
  import { apiGetReviewTaskList } from '@/api/review/review'
  import ReviewStatus from '@/enum/review/review_status'
  import { debounce } from '@/utils/function'
  import { formatDate } from '@/utils/date'

  export default {
    components: {
      NoData,
    },
    data() {
      return {
        minCreateTime: '',
        keyword: '',
        pageNum: 1,
        pageSize: 15,
        total: 0,
        taskList: [],
      }
    },
    created() {
      this.getTasks()
    },
    methods: {
      getTasks() {
        const { pageNum, pageSize, minCreateTime, keyword } = this

        apiGetReviewTaskList({
          page: pageNum,
          size: pageSize,
          minCreateTime: minCreateTime ? formatDate(minCreateTime) : undefined,
          keyword,
        }).then(res => {
          this.taskList = (res?.list || []).map(item => {
            let statusText = ReviewStatus.getNameById(item.evaluateStatus)
            let percentAll = 0
            let percentSelf = 0

            // 计算总体进度
            if (!(item.overallProgress?.total > 0 && item.overallProgress?.reviewed > 0)) {
              percentAll = 0
            } else if (item.overallProgress?.reviewed > item.overallProgress?.total) {
              percentAll = 100
            } else {
              percentAll = Math.floor((item.overallProgress?.reviewed / item.overallProgress?.total) * 100)
            }
            // 计算个人进度
            if (!(item.myProgress?.total > 0 && item.myProgress?.reviewed > 0)) {
              percentSelf = 0
            } else if (percentAll === 100 || item.myProgress?.reviewed > item.myProgress?.total) {
              percentSelf = 100
            } else {
              percentSelf = Math.floor((item.myProgress?.reviewed / item.myProgress?.total) * 100)
            }

            // 评卷任务个人待评不能超过总体待评
            item.myProgress.unreviewed = Math.min(item.overallProgress.unreviewed, item.myProgress.unreviewed)

            return {
              ...item,
              statusText,
              scoreModeName: item.scoreMode === 'single' ? '单项评分' : '多项评分',
              overallProgress: {
                ...item.overallProgress,
                percent: percentAll,
              },
              myProgress: {
                ...item.myProgress,
                percent: percentSelf,
              },
            }
          })
          this.total = Number(res.total)
        })
      },
      onChangeDate() {
        this.changePage(1)
      },
      onChangeKeyword: debounce(function (e) {
        this.keyword = (e.target.value || '').trim()
        this.changePage(1)
      }, 600),
      changePage(page = 1) {
        this.pageNum = page
        this.getTasks()
      },
      onToReview(item) {
        this.$router.push({
          name: 'review_mark',
          params: {
            categoryId: item.categoryId,
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-action-bar {
    @include flex(row, flex-end, center);
    margin-bottom: 20px;
  }

  .list-item {
    padding: 24px;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:not(:last-child) {
      margin-bottom: 24px;
    }

    &:hover {
      border-color: $color-primary;
      transform: scale(1.02);
    }

    .list-item-top {
      @include flex(row, flex-start, center);
      margin-bottom: 15px;

      .item-name {
        font-weight: 700;
        font-size: $font-size-medium-x;

        &:hover {
          color: $color-primary;
          cursor: pointer;
        }
      }

      .item-status {
        display: inline-block;
        margin-left: 15px;
        padding: 2px 10px;
        border: 1px solid $color-primary;
        border-radius: 2px;
        color: $color-primary;
        font-size: 12px;
        vertical-align: middle;
      }
    }

    .list-item-middle {
      @include flex(row, flex-start, center);
      margin-bottom: 15px;

      .item-category {
        margin-right: 20px;
        padding: 2px 10px;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;
        background-color: $color-primary;
      }

      .item-info {
        font-size: 12px;

        &:not(:last-child) {
          margin-right: 15px;
        }
      }
    }

    .list-item-bottom {
      @include flex(row, flex-start, center);
      font-size: 14px;

      .item-progress {
        flex: 1;
      }

      .item-progress-total {
        @include flex(row, flex-start, center);

        &:not(:last-child) {
          margin-bottom: 10px;
        }
      }

      .progress-info {
        @include flex(row, flex-start, center);
      }

      .progress-bar {
        @include flex(row, flex-start, center);
        width: 50%;

        .progress {
          flex: 1;
        }
      }

      .progress-info-item {
        min-width: 100px;

        &:not(:last-child) {
          margin-right: 15px;
        }
      }
    }
  }

  .section-page {
    @include flex(row, flex-end, center);
    margin-top: 20px;
  }
</style>
