import ajax from '@/api/ajax'

export function apiGetAiBlockPrompt({ examSubjectId, blockId }) {
  return ajax.get({
    url: 'mark/mark/ai/blockPrompt',
    params: { examSubjectId, subjectiveId: blockId },
    requestName: '获取AI阅卷题块提示词',
  })
}

export function apiGetAllAiBlockPrompt(examSubjectId) {
  return ajax.get({
    url: 'mark/mark/ai/allBlockPrompts',
    params: { examSubjectId },
    requestName: '获取AI阅卷题块提示词',
  })
}

export function apiModifyAiBlockPrompt({ examSubjectId, prompt }) {
  return ajax.put({
    url: 'mark/mark/ai/modifyPrompt',
    params: {
      examSubjectId,
    },
    data: prompt,
    requestName: '修改AI阅卷题块提示词',
  })
}

export function apiGenerateAiBlockPrompt({ examSubjectId, blockId }) {
  return ajax.put({
    url: 'mark/mark/ai/generatePrompt',
    params: {
      examSubjectId,
      subjectiveId: blockId,
    },
    requestName: '生成AI阅卷题块提示词',
  })
}

export function apiGenerateAllAiBlockPrompt(examSubjectId) {
  return ajax.put({
    url: 'mark/mark/ai/generateAllBlockPrompts',
    params: {
      examSubjectId,
    },
    requestName: '生成AI阅卷题块提示词',
  })
}

export function apiChangeToAiScore({ examSubjectId, blockIds }) {
  return ajax.put({
    url: 'mark/mark/ai/changeToAiScore',
    params: {
      examSubjectId,
    },
    data: blockIds,
    requestName: '转为AI阅卷',
  })
}

export function apiChangeToManualScore({ examSubjectId, blockIds }) {
  return ajax.put({
    url: 'mark/mark/ai/changeToManualScore',
    params: {
      examSubjectId,
    },
    data: blockIds,
    requestName: '转为人工阅卷',
  })
}

export function apiChangeAiScoreStatus({ examSubjectId, blockIds, aiScoreStatus }) {
  return ajax.put({
    url: 'mark/mark/ai/changeAiScoreStatus',
    params: {
      examSubjectId,
      aiScoreStatus,
    },
    data: blockIds,
    requestName: '更改AI阅卷状态',
  })
}

export function apiGetAIMarkingStat(requestParams) {
  return ajax.get({
    url: 'mark/mark/ai/stat/list',
    params: {
      examSubjectId: requestParams.examSubjectId, // *String
      subjectiveId: requestParams.subjectiveId, // String
    },
    requestName: '获取AI阅卷统计',
  })
}

export function apiGetAINormalMarkHistory(requestParams) {
  return ajax.get({
    url: 'mark/mark/ai/history/normal',
    params: {
      subjectiveId: requestParams.blockId, // *String
      minScore: requestParams.minScore, // Number
      maxScore: requestParams.maxScore, // Number
      beginTime: requestParams.beginTime, // String: yyyy-MM-dd HH:mm:ss
      endTime: requestParams.endTime, // String: yyyy-MM-dd HH:mm:ss
      corrected: requestParams.corrected, // Boolean 是否已纠正
      pageSize: requestParams.pageSize || 10, // Number
      pageNum: requestParams.currentPage || 1, // Number
    },
    requestName: '获取AI评分正常记录',
  })
}

export function apiGetAIAbnormalMarkHistory(requestParams) {
  return ajax.get({
    url: 'mark/mark/ai/history/abnormal',
    params: {
      subjectiveId: requestParams.blockId, // *String
      beginTime: requestParams.beginTime, // String: yyyy-MM-dd HH:mm:ss
      endTime: requestParams.endTime, // String: yyyy-MM-dd HH:mm:ss
      corrected: requestParams.corrected, // Boolean  是否已纠正
      pageSize: requestParams.pageSize || 10, // Number
      pageNum: requestParams.currentPage || 1, // Number
    },
    requestName: '获取AI评分异常记录',
  })
}

export function apiGetAIMarkTimesHistory(requestParams) {
  return ajax.get({
    url: 'mark/mark/ai/history/allTimes',
    params: {
      subjectiveId: requestParams.blockId, // *String
      subjectiveItemId: requestParams.subjectiveItemId, // *String
    },
    requestName: '获取AI评分多次记录',
  })
}

export function apiCorrectAIMark(requestParams) {
  return ajax.post({
    url: 'mark/mark/ai/history/correct',
    data: {
      score: requestParams.score, // *Number,
      subScore: requestParams.subScore, // *Number,
      subjectiveId: requestParams.subjectiveId, // *String
      subjectiveItemId: requestParams.subjectiveItemId, // *String
    },
    requestName: '纠正AI阅卷分数',
  })
}

export function apiRedoBlockAIMarking(subjectiveId) {
  return ajax.put({
    url: 'mark/mark/ai/redo/all',
    params: {
      subjectiveId, // *String
    },
    requestName: '重置题块AI评卷',
  })
}

export function apiGetBlockAIAssistMarkBatch(requestParams) {
  return ajax.get({
    url: 'mark/mark/ai/assistMarkBatch',
    params: {
      subjectiveId: requestParams.subjectiveId, // *String
      score: requestParams.score, // *Number
      batchSize: requestParams.batchSize, // *Number
    },
    requestName: 'AI辅助-正评评卷',
  })
}
