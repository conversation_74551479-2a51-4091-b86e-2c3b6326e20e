<template>
  <span class="btn-text" :class="btnClass" @click="handleClick">
    <Icon v-if="icon" :type="icon" class="icon"></Icon><slot></slot>
  </span>
</template>

<script>
  export default {
    props: {
      icon: String,
      type: {
        type: String,
        default: 'default',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      underline: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['click'],
    computed: {
      btnClass() {
        let cls = {}
        if (this.type) {
          cls[`btn-text-${this.type}`] = true
        }
        if (this.disabled) {
          cls.disabled = true
        }
        if (this.underline) {
          cls.underline = true
        }
        return cls
      },
    },
    methods: {
      handleClick() {
        if (this.disabled) {
          return
        }

        this.$emit('click')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .btn-text {
    padding: 5px;
    line-height: 1;
    cursor: pointer;
    user-select: none;

    &:hover {
      text-shadow: 0 0 1px;
    }

    &.disabled {
      color: $color-disabled !important;
    }

    &.disabled:hover {
      text-shadow: none;
    }

    &.underline {
      text-underline-offset: 0.25em;
      text-decoration-line: underline;
    }
  }

  .icon {
    margin-right: 4px;
  }

  .btn-text.btn-text-default:hover {
    color: $color-primary;
  }

  .btn-text.btn-text-defaulterror:hover {
    color: $color-error;
  }

  .btn-text-info {
    color: $color-info;
  }

  .btn-text-blue {
    color: $color-text-btn-blue;
  }

  .btn-text-primary {
    color: $color-primary;
  }

  .btn-text-primary-dark {
    color: $color-primary-dark;
  }

  .btn-text-success {
    color: $color-success;
  }

  .btn-text-warning {
    color: $color-warning;
  }

  .btn-text-error {
    color: $color-error;
  }

  .btn-text-content {
    color: $color-content;
  }

  .btn-text-gray {
    color: $color-shadow;
  }
</style>
