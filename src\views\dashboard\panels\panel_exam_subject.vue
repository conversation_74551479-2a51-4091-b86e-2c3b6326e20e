<template>
  <div class="panel-exam-subject">
    <div class="section-selector">
      <div
        v-for="item in examSubjects"
        :key="item.examSubjectId"
        :class="['selector-item', item.examSubjectId === activeSubjectId ? 'active' : '']"
        @click="handleSubjectSelect(item.examSubjectId)"
      >
        {{ item.subjectName }}
      </div>
    </div>
    <div v-if="examSubjects.length && paperCtt" class="section-paper-ctt">
      <div class="paper-ctt-item">
        <span class="name">难度：</span>
        <span class="num">{{ paperDifficulty }}</span>
      </div>
      <div class="paper-ctt-item">
        <span class="name">区分度：</span>
        <span class="num">{{ paperDiscrimination }}</span>
      </div>
    </div>
    <div class="section-chart">
      <div v-if="schoolCompareData.length" id="chart-bar-school" class="chart-bar"></div>
      <div v-else class="no-data">暂无数据</div>
    </div>
    <div class="section-knowledge">
      <list-knowledge :list="knowledgeList" />
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import echarts from '@/utils/echarts'
  import ListKnowledge from '../components/list_knowledge.vue'
  import { useDashboardStore } from '../../../store/dashboard'
  import { roundNumber } from '@/utils/math'
  import { UUID_ZERO } from '@/const/string'
  import { getInstitutionName } from '@/helpers/report/tools/tools'
  import { apiGetSchoolCompare, apiGetExpandKnowledge } from '@/api/report'
  import { apiGetPaperCttParam } from '@/api/report/paper_quality'

  const dashboardStore = useDashboardStore()
  const ReportStatus = {
    UnGenerate: 0,
    Generating: 1,
    GenerateSucceeded: 2,
    GenerateFailed: 3,
    WaitForGenerate: 4,
    WaitForSubject: 5,
  }
  const activeSubjectId = ref('')
  const schoolCompareData = ref([])
  const paperCtt = ref(null)
  const knowledgeList = ref([])
  let chartBar = null

  const currentExam = computed(() => {
    return dashboardStore.currentExam
  })
  const examSubjects = computed(() => {
    return currentExam.value?.subjects || []
  })
  const barOptions = computed(() => {
    if (!schoolCompareData.value.length) return null

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '0%',
        right: '10%',
        bottom: '4%',
        top: '4%',
        containLabel: true,
      },
      dataZoom: [
        // 滑动条
        {
          type: 'slider',
          show: true,
          yAxisIndex: 0,
          width: 16,
          right: '2%',
          startValue: 0,
          endValue: 9,
          // 滑动时坐标轴范围不变
          filterMode: 'empty',
          showDetail: false,
        },
        // 内置，支持滚轮缩放
        {
          type: 'inside',
          yAxisIndex: 0,
          startValue: 0,
          endValue: 9,
          filterMode: 'empty',
          zoomOnMouseWheel: false,
          moveOnMouseWheel: true,
          moveOnMouseMove: true,
        },
      ],
      yAxis: {
        type: 'category',
        axisLabel: { color: '#FFFFFF', fontSize: 12 },
        axisLine: { show: false },
        axisTick: { show: false },
        data: schoolCompareData.value.map(item => {
          if (item.schoolId === UUID_ZERO) {
            return `全体学校 (${schoolCompareData.value.length}所)`
          }
          return item.schoolName
        }),
        inverse: true,
      },
      xAxis: {
        name: '平均分',
        type: 'value',
        axisLabel: {
          color: '#FFFFFF',
        },
        splitLine: { show: true, lineStyle: { color: '#3C4061' } },
        position: 'top',
        nameTextStyle: {
          verticalAlign: 'bottom',
          color: '#7db7db',
          lineHeight: 28,
        },
      },
      series: [
        {
          data: schoolCompareData.value.map(item => item.avgScore),
          type: 'bar',
          barWidth: '40%',
          itemStyle: { borderRadius: [0, 20, 20, 0], color: '#05d1bc' },
        },
      ],
    }
  })
  const paperDifficulty = computed(() => {
    return paperCtt.value?.difficulty || ''
  })
  const paperDiscrimination = computed(() => {
    return paperCtt.value?.discrimination || ''
  })

  watch(examSubjects, newVal => {
    schoolCompareData.value = []
    paperCtt.value = null
    knowledgeList.value = []

    if (Array.isArray(newVal) && newVal.length) {
      activeSubjectId.value = newVal[0].examSubjectId
      loadCompare()
    }
  })

  function normalizeCttStats(ctt) {
    return {
      category: ctt.category,
      avgScore: roundNumber(ctt.avgScore, 2),
      calcNum: ctt.calcNum,
      coefficientOfVariation: roundNumber(ctt.coefficientOfVariation, 2),
      difficulty: roundNumber(ctt.difficulty, 2),
      discrimination: roundNumber(ctt.discrimination, 2),
      fullRate: roundNumber(ctt.fullRate, 4),
      maxScore: roundNumber(ctt.maxScore, 2),
      minScore: roundNumber(ctt.minScore, 2),
      reliability: roundNumber(ctt.reliability, 2),
      scoreRate: roundNumber(ctt.scoreRate, 4),
      standardDeviation: roundNumber(ctt.standardDeviation, 2),
      totalFullScore: roundNumber(ctt.totalFullScore, 2),
      variance: roundNumber(ctt.variance, 2),
      zeroRate: roundNumber(ctt.zeroRate, 4),
    }
  }

  function tileData(data) {
    let result = []

    data
      .sort((a, b) => a.sortCode - b.sortCode)
      .forEach(x => {
        x.childVos = x.childVos
          .sort((a, b) => b.sortCode - a.sortCode)
          .map((y, yIndex) => {
            return {
              knowledge: x.knowName || '-',
              instituId: y.schoolId,
              instituName: getInstitutionName(y.schoolId, y.classId, y.schoolName, y.className),
              fullScore: y.totalFullScore,
              examedStudentCount: y.countNum,
              maxScore: y.max && Number(y.max),
              minScore: y.min && Number(y.min),
              avgScore: y.avg && Number(y.avg),
              scoreRate: y.scoreRate,
              zeroRate: y.zeroRate,
              fullRate: y.fullRate,
              standardDeviation: y.sqrt && Number(y.sqrt),
              difficult: y.diff && Number(y.diff),
              differentCoefficient: y.cv && Number(y.cv),
              // 合并单元格
              isFirstKindItem: !yIndex,
              kindLength: x.childVos.length,
            }
          })
      })

    // data.forEach((x, idx) => {
    //   if (idx === 0) {
    //     x.names = data.map(item => item.knowName)

    //     x.childVos.forEach(y => {
    //       let list = data.map(z => {
    //         let theItem = z.childVos.find(item => item.instituId === y.instituId)
    //         return {
    //           knowledge: theItem.knowledge,
    //           fullScore: theItem.fullScore,
    //           avgScore: theItem.avgScore,
    //           scoreRate: theItem.scoreRate,
    //           zeroRate: theItem.zeroRate,
    //         }
    //       })

    //       y.list = list
    //     })

    //     result.push({
    //       sortCode: x.sortCode,
    //       childVos: x.childVos,
    //       names: x.names,
    //     })
    //   } else {
    //     return false
    //   }
    // })

    return data
  }

  function handleSubjectSelect(val) {
    activeSubjectId.value = val
    loadCompare()
  }

  async function loadCompare() {
    const templates = (currentExam.value?.templates || []).filter(
      t => t.hasViewReportPermission && t.sign === ReportStatus.GenerateSucceeded && t.isShow
    )
    const defaultTemplate = templates.find(t => t.isDefault) || (templates.length && templates[0])
    const templateId = defaultTemplate ? defaultTemplate.templateId : undefined

    if (!templateId) {
      schoolCompareData.value = []
      return
    }

    const res = await Promise.all([
      apiGetSchoolCompare({
        examId: currentExam.value?.examId,
        reportName: 'union_schCompare',
        templateId,
        examSubjectId: activeSubjectId.value,
      }),
      apiGetPaperCttParam({
        examId: currentExam.value?.examId,
        templateId,
        examSubjectId: activeSubjectId.value,
      }),
      apiGetExpandKnowledge({
        examId: currentExam.value?.examId,
        templateId,
        examSubjectId: activeSubjectId.value,
        reportName: 'union_knowAnalyse',
      }),
    ])

    if (res && res.length) {
      let list = (res[1] || []).map(normalizeCttStats)
      schoolCompareData.value = res[0].map(item => ({
        ...item,
        avgScore: roundNumber(item.avgScore, 2),
      }))
      paperCtt.value = list.length ? list[0] : null
      knowledgeList.value = tileData(res[2])

      nextTick(() => {
        drawBar()
      })
    }
  }

  function drawBar() {
    if (barOptions.value) {
      if (chartBar) {
        chartBar.dispose()
      }
      chartBar = echarts.init(document.getElementById('chart-bar-school'))
      chartBar.setOption(barOptions.value)
    }
  }
</script>

<style lang="scss" scoped>
  .panel-exam-subject {
    @include flex(column, flex-start, flex-start);
    padding: 0 10px;
    overflow: hidden;

    .no-data {
      @include flex(row, center, center);
      width: 100%;
      height: 180px;
      color: #fff;
      font-size: 14px;
    }

    .section-selector {
      @include flex(row, center, center);
      flex-shrink: 0;
      flex-wrap: nowrap;
      width: 100%;
      margin-bottom: 8px;
      overflow: auto;
      color: #fff;
      font-size: 13px;
      white-space: nowrap;

      .selector-item {
        position: relative;
        box-sizing: border-box;
        padding: 4px 10px;
        line-height: 20px;
        text-align: center;
        cursor: pointer;

        &.active::after {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          width: 40px;
          height: 3px;
          margin: auto;
          background-color: #05d1bc;
          content: '';
        }
      }
    }

    .section-paper-ctt {
      @include flex(row, center, center);
      flex-shrink: 0;
      width: 100%;
      margin-bottom: 4px;

      .paper-ctt-item {
        margin: 0 10px;
        line-height: 1;
      }

      .name {
        color: #fff;
        font-size: 12px;
      }

      .num {
        color: #fff;
        font-size: 12px;
      }
    }

    .section-divider {
      width: 100%;
      margin-bottom: 10px;
    }

    .section-chart {
      @include flex(row, center, center);
      flex-shrink: 0;
      width: 100%;
      height: 180px;
      margin-bottom: 10px;

      .chart-bar {
        width: 100%;
        height: 100%;
      }
    }

    .section-knowledge {
      flex-grow: 1;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      margin-bottom: 10px;
      overflow: hidden;

      // ::-webkit-scrollbar {
      //   display: none;
      // }
    }
  }
</style>
