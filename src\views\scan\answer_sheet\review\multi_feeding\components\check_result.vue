<template>
  <div class="section-scan-batch">
    <div class="header">
      <span class="header-title">检查结果</span>
      <div class="header-action">
        <Select v-model="activeBatchId" placeholder="不限批次" filterable clearable @on-change="onChangeBatch">
          <Option v-for="item in batchNames" :key="item.batchId" :value="item.batchId">{{
            item.batchNumberText
          }}</Option>
        </Select>
        <Button
          class="btn-export"
          type="primary"
          icon="md-cloud-download"
          ghost
          :disabled="batchTotal === 0"
          @click="onExport"
          >导出Excel</Button
        >
      </div>
    </div>
    <div class="body">
      <div class="box-switch-action-history">
        <Switch v-model="isShowActionHistory" @on-change="onChangeActionHistoryShow"></Switch>
        <span>显示操作记录</span>
      </div>
      <Tabs v-model="activeTab" @on-click="onTabClick">
        <TabPane v-for="tab in tabList" :key="tab.value" :label="tab.label" :name="tab.value"></TabPane>
      </Tabs>
      <div class="box-table">
        <Table :columns="checkResultTableColumns" :data="batches"></Table>
        <div v-if="batchTotal > 0" class="box-page">
          <span class="summary">共 {{ batchTotal }} 个结果</span>
          <Page :model-value="pageNum" :page-size="pageSize" :total="batchTotal" @on-change="changeCurrentPage"></Page>
        </div>
      </div>
    </div>

    <ModalCheckMultiFeeding
      v-model="modalCheckMultiFeedingVisible"
      :request-params="requestParams"
      :title="modalTitle"
      :init-id="modalInitId"
      @change-index="changePageToIndex"
      @on-refresh="refreshPage"
    ></ModalCheckMultiFeeding>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { Button } from 'view-ui-plus'
  import ModalCheckMultiFeeding from './modal_check_multi_feeding.vue'
  import { apiGetAllBatchNames, apiGetCheckResultPage, apiExportCheckResult } from '@/api/scan/multi_feeding'
  import MultiFeedingManualCheckResult from '@/enum/scan/multi_feeding_manual_check_result'
  import MultiFeedingCheckStatus, { getStatus } from '@/enum/scan/multi_feeding_check_status'

  import RescanCheckResult from '@/enum/scan/multi_feeding_rescan_check_result'
  import { formatDateTime } from '@/utils/date'
  import { downloadBlob } from '@/utils/download'
  import PageCache from '@/utils/page_cache'

  const ContainerName = 'check_result'

  export default {
    components: {
      ModalCheckMultiFeeding,
    },
    props: {
      statData: {
        type: Object,
        default: () => {},
      },
      selectedBatchId: {
        type: String,
        default: '',
      },
      selectedStatus: {
        type: String,
        default: '',
      },
    },
    emits: ['on-refresh'],
    data() {
      return {
        pageNum: 1,
        pageSize: 10,
        totalPages: 1,
        batchTotal: 0,
        batches: [],
        modalCheckMultiFeedingVisible: false,
        activeTab: 'item-init',
        activeBatchId: '',
        batchNames: [],
        modalInitId: '',
        isShowActionHistory: false,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'viewMode', 'examName', 'subjectName', 'scanStationName']),
      itemInitCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.init
      },
      itemNotRescan() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.notReScanned
      },
      itemRescanCheckResultInitCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.reScanCheckResultInit
      },
      itemMultiFeedingCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.multiFeeding
      },
      itemAllCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.total
      },
      tabList() {
        return [
          {
            value: 'item-init',
            name: '待人工确认',
            label: h => {
              return h('div', [h('span', '待人工确认'), h('span', {}, `（${this.itemInitCount}）`)])
            },
          },
          {
            value: 'item-not-rescan',
            name: '待重扫',
            label: h => {
              return h('div', [h('span', '待重扫'), h('span', {}, `（${this.itemNotRescan}）`)])
            },
          },
          {
            value: 'item-rescan-check-init',
            name: '待重扫复核',
            label: h => {
              return h('div', [h('span', '待重扫复核'), h('span', {}, `（${this.itemRescanCheckResultInitCount}）`)])
            },
          },
          {
            value: 'item-multi-feeding',
            name: '确认重张',
            label: h => {
              return h('div', [h('span', '确认重张'), h('span', {}, `（${this.itemMultiFeedingCount}）`)])
            },
          },
          {
            value: 'item-all',
            name: '全部',
            label: h => {
              return h('div', [h('span', '全部'), h('span', {}, `（${this.itemAllCount}）`)])
            },
          },
        ]
      },
      checkResultTableColumns() {
        let columns = [
          {
            title: '批次',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                [
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          className: 'td-text-main',
                          style: {
                            lineHeight: 1,
                            verticalAlign: 'middle',
                          },
                        },
                        `${params.row.scanClient}-${params.row.batchNo}`
                      ),
                    ]
                  ),
                  h(
                    'div',
                    {
                      className: 'td-text-sub',
                      style: {
                        marginTop: '8px',
                      },
                    },
                    params.row.scanStationName
                  ),
                ]
              )
            },
          },
          {
            title: '纸张',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                [
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          style: {
                            fontSize: '15px',
                            fontWeight: 600,
                            lineHeight: 1,
                            color: '#1c2a42',
                            verticalAlign: 'middle',
                          },
                        },
                        `${params.row.paperNo}`
                      ),
                    ]
                  ),
                  h(
                    'div',
                    {
                      style: {
                        marginTop: '8px',
                        fontSize: '13px',
                        color: '#6c7a91',
                      },
                    },
                    `第${params.row.sortPageNos}面`
                  ),
                ]
              )
            },
          },
          {
            title: '学生',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                [
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          className: 'td-text-main',
                          style: {
                            lineHeight: 1,
                            verticalAlign: 'middle',
                          },
                        },
                        params.row.studentName
                      ),
                    ]
                  ),
                  h(
                    'div',
                    {
                      className: 'td-text-sub',
                      style: {
                        marginTop: '8px',
                      },
                    },
                    params.row.schoolName
                  ),
                ]
              )
            },
          },
          {
            title: '准考号',
            key: 'admissionNum',
            render: (h, params) => {
              return h(
                'span',
                {
                  className: 'td-text-main',
                },
                params.row.admissionNum
              )
            },
          },
          {
            title: '考场（座位）',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                [
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          className: 'td-text-main',
                        },
                        `${params.row.roomNo}`
                      ),
                      h(
                        'div',
                        {
                          style: {
                            color: '#808695',
                          },
                        },
                        `（${params.row.seatNum}）`
                      ),
                    ]
                  ),
                ]
              )
            },
          },

          {
            title: '问题状态',
            render: (h, params) => {
              let status = getStatus(params.row)
              if (!status) {
                return null
              }
              return h(
                'div',
                {
                  className: 'tag-check-status',
                  style: {
                    backgroundColor: status.backgroundColor,
                    color: status.color,
                  },
                },
                status.name
              )
            },
          },

          {
            title: '操作',
            width: '120px',
            align: 'center',
            render: (h, params) => {
              let status = getStatus(params.row)
              if (!status) {
                return null
              }
              let btnType = 'default'
              let btnText = '进入查看'

              if (status.id === MultiFeedingCheckStatus.Init.id) {
                btnType = 'primary'
                btnText = '进入确认'
              } else if (status.id === MultiFeedingCheckStatus.ReScanCheckInit.id) {
                btnType = 'primary'
                btnText = '进入复核'
              }

              return h(
                Button,
                {
                  type: btnType,
                  size: 'small',
                  style: {
                    fontSize: '13px',
                  },
                  onClick: () => {
                    this.showModalCheckMultiFeeding(params.row)
                  },
                },
                () => btnText
              )
            },
          },
        ]

        if (this.isShowActionHistory) {
          columns.splice(6, 0, {
            title: '操作记录',
            render: (h, params) => {
              let dynamicComponent = []
              if (params.row.checkResultUpdateBy) {
                let actionTime = formatDateTime(new Date(params.row.checkResultUpdateTime), 'MM-DD\nHH:mm')
                let actionName = ''
                if (params.row.checkResult === MultiFeedingManualCheckResult.Normal.id) {
                  actionName = '确认正常'
                } else if (params.row.checkResult === MultiFeedingManualCheckResult.MultiFeeding.id) {
                  actionName = '确认重张'
                }

                dynamicComponent.push(
                  h('div', {}, `${params.row.checkResultUpdateByName}，${actionTime}，${actionName}`)
                )
              }
              if (params.row.reScanCheckResultUpdateBy) {
                let actionTime = formatDateTime(new Date(params.row.reScanCheckResultUpdateTime), 'MM-DD\nHH:mm')
                let actionName = ''
                if (params.row.reScanCheckResult === RescanCheckResult.Yes.id) {
                  actionName = '重扫复核通过'
                } else if (params.row.reScanCheckResult === RescanCheckResult.No.id) {
                  actionName = '重扫复核不通过'
                }

                dynamicComponent.push(
                  h('div', {}, `${params.row.reScanCheckResultUpdateByName}，${actionTime}，${actionName}`)
                )
              }

              return h(
                'div',
                {
                  style: {
                    minWidth: '200px',
                    color: '#6c7a91',
                    fontSize: '13px',
                  },
                },
                dynamicComponent.length ? dynamicComponent : '-'
              )
            },
          })
        }

        return columns
      },
      requestParams() {
        const { examSubjectId, viewMode, activeTab, activeBatchId } = this

        let scanStationId = viewMode.scanStationId
        let batchId = activeBatchId || undefined

        let checkResult = undefined
        let reScanned = undefined
        let reScanCheckResult = undefined
        if (activeTab === 'item-init') {
          checkResult = MultiFeedingManualCheckResult.Init.id
        } else if (activeTab === 'item-not-rescan') {
          checkResult = MultiFeedingManualCheckResult.MultiFeeding.id
          reScanned = false
        } else if (activeTab === 'item-rescan-check-init') {
          checkResult = MultiFeedingManualCheckResult.MultiFeeding.id
          reScanned = true
          reScanCheckResult = RescanCheckResult.Init.id
        } else if (activeTab === 'item-multi-feeding') {
          checkResult = MultiFeedingManualCheckResult.MultiFeeding.id
        }

        return {
          examSubjectId,
          scanStationId,
          batchId,
          checkResult,
          reScanned,
          reScanCheckResult,
        }
      },
      modalTitle() {
        let tab = (this.tabList || []).find(t => this.activeTab === t.value)
        let tabName = ''

        if (this.activeTab !== 'item-all') {
          tabName = `（${tab && tab.name}）`
        }
        return `重张检查结果${tabName}`
      },
    },
    watch: {
      viewMode: {
        handler: function (newVal) {
          if (newVal) {
            this.fetchAllBatchNames()
            this.fetchCheckResultPage()
          }
        },
        deep: true,
      },
      selectedBatchId(newVal) {
        if (newVal) {
          this.activeBatchId = newVal
          this.pageNum = 1
          if (newVal) {
            this.activeTab = 'item-all'
          }
          this.fetchCheckResultPage()
        }
      },
      selectedStatus(newVal) {
        if (newVal) {
          this.activeTab = newVal
          this.pageNum = 1
          this.activeBatchId = ''
          this.fetchCheckResultPage()
        }
      },
    },
    created() {
      let PageCacheData = PageCache.fetch(ContainerName)
      if (PageCacheData) {
        this.isShowActionHistory = PageCacheData.isShowActionHistory || false
      }
      this.fetchAllBatchNames()
      this.fetchCheckResultPage()
    },
    methods: {
      onRefresh() {
        return Promise.all([this.fetchAllBatchNames(), this.fetchCheckResultPage()])
      },
      fetchAllBatchNames() {
        const { examSubjectId, viewMode } = this

        return apiGetAllBatchNames({
          scanStationId: viewMode.scanStationId,
          examSubjectId,
        }).then(res => {
          this.batchNames = (res || []).map(item => ({
            ...item,
            batchNumberText: `${item.scanClient}-${item.batchNo}`,
          }))
        })
      },
      fetchCheckResultPage() {
        return apiGetCheckResultPage({
          ...this.requestParams,
          page: this.pageNum,
          size: this.pageSize,
        }).then(res => {
          if ((!res.records || !res.records.length) && this.pageNum !== 1) {
            // 当前不在第一页且无数据，则重置到第一页
            this.pageNum = 1
            this.fetchCheckResultPage()
            return
          }
          this.batches = res.records
          this.batchTotal = Number(res.total)
          this.totalPages = Number(res.pages)
        })
      },
      changeCurrentPage(pageNum) {
        this.pageNum = pageNum
        this.fetchCheckResultPage()
      },
      changeCheckResult() {
        this.pageNum = 1
        this.batchTotal = 0
        this.fetchCheckResultPage()
      },
      changePageToIndex(index) {
        let page = 1
        if (index != null) {
          page = Math.floor(index / this.pageSize) + 1
        }
        this.changeCurrentPage(page)
      },
      showModalCheckMultiFeeding(item) {
        this.modalInitId = item.id
        this.modalCheckMultiFeedingVisible = true
      },
      onTabClick() {
        this.pageNum = 1
        this.fetchCheckResultPage()
      },
      onChangeBatch() {
        this.pageNum = 1
        this.fetchCheckResultPage()
      },
      onExport() {
        const {
          examSubjectId,
          viewMode,
          activeTab,
          activeBatchId,
          examName,
          subjectName,
          batchNames,
          scanStationName,
          tabList,
        } = this
        let checkResult = undefined
        let reScanned = undefined
        let reScanCheckResult = undefined
        let fileName = `${examName}_${subjectName}`
        if (activeTab === 'item-init') {
          checkResult = MultiFeedingManualCheckResult.Init.id
        } else if (activeTab === 'item-rescan-check-init') {
          checkResult = MultiFeedingManualCheckResult.MultiFeeding.id
          reScanned = true
          reScanCheckResult = RescanCheckResult.Init.id
        } else if (activeTab === 'item-multi-feeding') {
          checkResult = MultiFeedingManualCheckResult.MultiFeeding.id
        }

        if (viewMode?.scanStationId != 0) {
          fileName = fileName + '_' + scanStationName
        }
        if (activeBatchId) {
          let theBatch = batchNames.find(b => b.batchId === activeBatchId)
          if (theBatch) {
            fileName = fileName + '_' + theBatch.batchNumberText
          }
        }
        if (activeTab !== 'item-all') {
          let theTab = tabList.find(t => t.value === activeTab)
          if (theTab) {
            fileName = fileName + '_' + theTab.name
          }
        }

        apiExportCheckResult({
          scanStationId: viewMode.scanStationId,
          batchId: activeBatchId || undefined,
          checkResult,
          reScanCheckResult,
          reScanned,
          examSubjectId,
        }).then(blob => {
          downloadBlob(blob, `${fileName}检查结果.xlsx`)
        })
      },
      onChangeActionHistoryShow() {
        PageCache.save(ContainerName, {
          isShowActionHistory: this.isShowActionHistory,
        })
      },
      refreshPage() {
        this.$emit('on-refresh')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-scan-batch {
    padding: 15px;
    border: 1px solid #e5e9f2;
    border-radius: 16px;
    background-color: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 33, 111, 0.05);

    .header {
      @include flex(row, space-between, center);

      .header-action {
        @include flex(row, flex-start, center);

        .btn-export {
          margin-left: 10px;
        }
      }
    }

    .body {
      position: relative;
    }

    .box-switch-action-history {
      position: absolute;
      top: 6px;
      right: 0;
      z-index: 999;
    }

    :deep(.ivu-tabs-bar) {
      margin-bottom: 10px !important;
    }

    :deep(.td-text-main) {
      font-weight: 600;
      font-size: 14px;
    }

    :deep(.td-text-sub) {
      color: $color-icon;
      font-size: $font-size-medium-s;
    }

    :deep(.tag-check-status) {
      display: inline-block;
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      background-color: #eee;
    }
  }
</style>
