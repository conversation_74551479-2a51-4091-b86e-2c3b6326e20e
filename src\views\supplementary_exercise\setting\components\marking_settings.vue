<script>
  import TextButton from '@/components/text_button.vue'
  import BlockDetail from '@/views/emarking/exam/task/components/block_detail.vue'
  import ModalMarkingWaySubject from '@/views/emarking/exam/task/components/modal_marking_way_subject'
  import ModalSettingBatchScoreSize from '@/views/emarking/exam/task/components/modal_setting_batch_score_size.vue'
  import ModalAssignSubject from '@/views/emarking/exam/task/components/modal_assign_subject'
  import ModalImport from '@/views/emarking/exam/task/components/modal_import'
  import ModalMarkMode from '@/views/emarking/exam/task/components/modal_mark_mode'
  import ModalAssignMode from '@/views/emarking/exam/task/components/modal_assign_mode'
  import ModalTeachers from '@/views/emarking/exam/task/components/modal_teachers'
  import ModalArbitrators from '@/views/emarking/exam/task/components/modal_arbitrators'
  import ModalLeaders from '@/views/emarking/exam/task/components/modal_leaders'
  import Mo<PERSON><PERSON><PERSON><PERSON>arker from '@/views/emarking/exam/task/components/modal_copy_marker'
  import Mo<PERSON><PERSON><PERSON>Leader from '@/views/emarking/exam/task/components/modal_copy_leader'

  import {
    apiStartMarking,
    apiSuspendMarking,
    apiGetBlockTasks,
    apiResetBlockMarking,
    apiDownloadMarkingTaskExcel,
    apiSyncTeacherWhenMarkingByClass,
    apiAddClassPrepareHeadAsLeader,
    apiExamSubjectAllowMarkerMarkMoreTask,
  } from '@/api/emarking'
  import { apiGetExamSubjectStudentCount } from '@/api/emarking/exam_student'

  import { sumByDefaultZero } from '@/utils/math'
  import { uniq } from '@/utils/array'
  import { downloadBlob } from '@/utils/download'

  import MarkingWayEnum from '@/enum/emarking/marking_way'
  import SubjectStatusEnum from '@/enum/emarking/subject_status'
  import MarkModeEnum from '@/enum/emarking/mark_mode'
  import AssignModeEnum from '@/enum/emarking/assign_mode'
  import { UUID_ZERO } from '@/const/string'

  export default {
    components: {
      'modal-marking-way-subject': ModalMarkingWaySubject,
      'modal-setting-batch-score-size': ModalSettingBatchScoreSize,
      'modal-assign-subject': ModalAssignSubject,
      'modal-import': ModalImport,
      'modal-mark-mode': ModalMarkMode,
      'modal-assign-mode': ModalAssignMode,
      'modal-teachers': ModalTeachers,
      'modal-arbitrators': ModalArbitrators,
      'modal-leaders': ModalLeaders,
      'modal-copy-marker': ModalCopyMarker,
      'modal-copy-leader': ModalCopyLeader,
    },

    props: {
      examId: {
        type: String,
        default: '',
      },
      currentExamSubjectId: {
        type: String,
        default: '',
      },
      currentExamSubjectName: {
        type: String,
        default: '',
      },
    },

    data() {
      return {
        blocks: [], // 科目下所有题块
        modalBlock: null, // 正在编辑的题块

        searchTeacherKeyword: '', // 搜索评卷员姓名或手机号码
        expandDetails: false, // 展开详细信息

        showModalMarkMode: false, // 评卷方式对话框
        showModalMarkingWay: false, // 发卷方式对话框
        showModalAssignMode: false, // 分配方式对话框
        showModalMarkingWaySubject: false, // 科目统一设置发卷方式对话框
        showModalSchoolAmount: false, // 设置学校任务量对话框
        showModalAssignSubject: false, // 科目统一设置分配方式对话框
        showModalTeachers: false, // 评卷员对话框
        showModalArbitrators: false, // 仲裁员对话框
        showModalLeaders: false, // 题组长对话框
        showModalImport: false, // 导入评卷员对话框
        showModalCopyMarker: false, // 复制评卷员弹窗
        showModalCopyLeader: false, // 复制题组长弹窗
        modalSettingBatchScoreSizeShowed: false, // 科目批量阅卷设置对话框
      }
    },

    computed: {
      maxTableHeight() {
        const MinHeight = 200
        const CalcHeight = window.innerHeight - 300
        return Math.max(MinHeight, CalcHeight)
      },

      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator'] || false
      },

      currentSchoolId() {
        return this.$store.getters['user/currentUserSchoolId'] || ''
      },

      isOnlyTaskMenuSubjectAdmin() {
        return this.$store.getters['emarking/isOnlyTaskMenuSubjectAdmin']
      },

      exam() {
        return this.$store.getters['emarking/exam']
      },

      // 当前科目
      currentSubject() {
        return this.exam.subjects.find(s => s.examSubjectId === this.currentExamSubjectId) || null
      },
      currentSubjectStudentNumber() {
        return (this.currentSubject && this.currentSubject.studentNum) || 0
      },
      markerCanMarkMoreTask() {
        // 科目评卷员是否可以在评完评卷任务量后自愿继续评卷
        return (this.currentSubject && this.currentSubject.enableIncreaseMarkTaskNum) || false
      },
      showBtnStartMarking() {
        return (
          this.currentSubject &&
          (this.currentSubject.status < SubjectStatusEnum.Started.id ||
            this.currentSubject.status === SubjectStatusEnum.Suspended.id)
        )
      },
      showBtnPauseMarking() {
        return this.currentSubject && this.currentSubject.status === SubjectStatusEnum.Started.id
      },

      // 所有题块发卷方式集合
      distinctBlockMarkingWays() {
        return uniq(this.blocks.map(block => block.markingWay.id))
          .map(id => MarkingWayEnum.getEntryById(id))
          .filter(Boolean)
      },

      // 整个科目统一的发卷方式，若存在多种发卷方式则Id为空
      subjectMarkingWay() {
        if (this.distinctBlockMarkingWays.length === 1) {
          return this.distinctBlockMarkingWays[0]
        }
        return {
          id: 0,
          key: '',
          name: '',
        }
      },

      // 是否分班阅卷
      isMarkingByClass() {
        return this.subjectMarkingWay.id === MarkingWayEnum.Class.id
      },

      // 科目考生人数
      // 分校阅卷学校管理员看到的是本校考生人数
      subjectStudentNumber() {
        const FirstBlock = this.blocks[0]
        if (!FirstBlock) {
          return 0
        }

        const CurrentSchool = (FirstBlock.groups || []).find(g => g.groupId === this.currentSchoolId)
        return CurrentSchool ? CurrentSchool.studentNum : 0
      },

      tableRows() {
        if (!this.searchTeacherKeyword) {
          return this.blocks
        }
        return this.blocks.filter(block => {
          return [...block.teachers, ...block.arbitrators, ...block.leaders].some(
            x =>
              x.realName.includes(this.searchTeacherKeyword.trim()) ||
              (x.mobile && x.mobile.includes(this.searchTeacherKeyword.trim()))
          )
        })
      },

      tableColumns() {
        const Columns = [
          {
            type: 'expand',
            width: 30,
            render: (h, params) =>
              h(BlockDetail, {
                block: params.row,
                searchTeacherKeyword: this.searchTeacherKeyword,
              }),
          },
          {
            title: '题块名称',
            key: 'blockName',
            minWidth: 100,
            ellipsis: true,
          },
          {
            title: '包含题目',
            minWidth: 120,
            ellipsis: true,
            render: (h, params) => h('span', {}, this.getQuestionNamesText(params.row.questions)),
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
            minWidth: 50,
            ellipsis: true,
          },
          {
            title: '评卷方式',
            align: 'center',
            minWidth: 116,
            render: (h, params) => {
              const Texts = this.getMarkModeTexts(params.row).map((text, idx) =>
                h(
                  'span',
                  {
                    class: {
                      'mark-mode-detail': idx > 0,
                    },
                  },
                  text
                )
              )

              // 分校阅卷管理员不可更改
              // 分班阅卷时评卷方式限定为单评，也不可修改
              if (this.isMarkingByClass || this.isOnlyTaskMenuSubjectAdmin) {
                return h('span', Texts)
              }

              return h(
                TextButton,
                {
                  type: params.row.markMode.name ? 'default' : 'warning',
                  class: {
                    btn: true,
                  },
                  style: {
                    display: 'block',
                  },
                  onMousedown: e => {
                    e.stopPropagation()
                    this.handleMarkModeClick(params.row)
                  },
                },
                () => Texts
              )
            },
          },
          {
            title: '发卷方式',
            align: 'center',
            minWidth: 102,
            render: (h, params) => {
              if (!this.canChangeBlockMarkingWaySeparately(params.row) || this.isOnlyTaskMenuSubjectAdmin) {
                return h('span', {}, params.row.markingWay.name)
              }

              const IsBlockNeedAddGroup =
                [MarkingWayEnum.Group.id, MarkingWayEnum.GroupAmount.id].includes(params.row.markingWay.id) &&
                params.row.groups.length == 0
              return h(
                'span',
                {
                  style: {
                    color: !params.row.markingWay.name || IsBlockNeedAddGroup ? '#f90' : '#ed4014',
                  },
                },
                params.row.markingWay.name || '请设置'
              )
            },
          },
          {
            title: '批量阅卷',
            align: 'center',
            minWidth: 100,
            render: (h, params) => h('span', {}, params.row.batchScoreSize || '-'),
          },
          {
            title: '分配方式',
            align: 'center',
            minWidth: 90,
            ellipsis: true,
            render: (h, params) => {
              if (this.isMarkingByClass) {
                return h('span', {}, params.row.assignMode.name || '-')
              }

              return h(
                TextButton,
                {
                  type: params.row.allocated ? 'default' : 'warning',
                  class: {
                    btn: true,
                  },
                  onMousedown: e => {
                    e.stopPropagation()
                    this.handleAssignModeClick(params.row)
                  },
                },
                () => params.row.assignMode.name || (this.currentSchoolId ? '' : '请设置')
              )
            },
          },
          {
            title: '评卷老师',
            align: 'center',
            minWidth: 100,
            ellipsis: true,
            render: (h, params) => {
              const TeacherText = this.getTeachersText(params.row.teachers)
              if (this.isMarkingByClass) {
                return h('span', {}, TeacherText)
              } else {
                return h(
                  TextButton,
                  {
                    type: params.row.teachers.length > 0 ? 'default' : 'warning',
                    class: {
                      btn: true,
                    },
                    onMousedown: e => {
                      e.stopPropagation()
                      this.handleTeachersClick(params.row)
                    },
                  },
                  () => TeacherText
                )
              }
            },
          },
          {
            title: '仲裁老师',
            align: 'center',
            minWidth: 100,
            ellipsis: true,
            render: (h, params) => {
              if (this.isBlockNeedArbitrate(params.row.markMode.id)) {
                return h(
                  TextButton,
                  {
                    type:
                      this.isBlockNeedArbitrate(params.row.markMode.id) && params.row.arbitrators.length === 0
                        ? 'warning'
                        : 'default',
                    class: {
                      btn: true,
                    },
                    onMousedown: e => {
                      e.stopPropagation()
                      this.handleArbitratorsClick(params.row)
                    },
                  },
                  () => this.getArbitratorsText(params.row)
                )
              } else {
                return h('span', {}, '/')
              }
            },
          },
          {
            title: '题块组长',
            align: 'center',
            minWidth: 100,
            ellipsis: true,
            render: (h, params) => {
              const BlockLeadersText = this.getLeadersText(params.row.leaders)
              if (this.isMarkingByClass) {
                return h('span', {}, BlockLeadersText)
              } else {
                return h(
                  TextButton,
                  {
                    type: params.row.leaders.length > 0 ? 'default' : 'warning',
                    class: {
                      btn: true,
                    },
                    onMousedown: e => {
                      e.stopPropagation()
                      this.handleLeadersClick(params.row)
                    },
                  },
                  () => BlockLeadersText
                )
              }
            },
          },
        ]

        // 评卷开始或暂停管理员可重置评卷
        if (
          this.isSchoolAdministrator &&
          this.currentSubject &&
          [SubjectStatusEnum.Started.id, SubjectStatusEnum.Suspended.id].includes(this.currentSubject.status)
        ) {
          Columns.push({
            title: '操作',
            align: 'center',
            width: 100,
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'warning',
                  onMousedown: e => {
                    e.stopPropagation()
                    this.resetMarking(params.row)
                  },
                },
                () => '重置评卷'
              ),
          })
        }

        return Columns
      },
    },

    created() {
      this.loadTask()
    },

    methods: {
      /**
       * 显示设置
       */
      handleTableRowClick(row) {
        let block = this.blocks.find(x => x.blockId === row.blockId)
        if (block) {
          block._expanded = !block._expanded
        }
      },
      handleChangeExpandDetails(value) {
        this.blocks.forEach(block => {
          block._expanded = value
        })
      },
      handleExamSubjectMarkerMarkMoreTaskStatus(value) {
        if (this.currentExamSubjectId) {
          apiExamSubjectAllowMarkerMarkMoreTask({
            enable: value || false,
            examSubjectId: this.currentExamSubjectId,
          })
            .then(() =>
              this.$Message.success({
                duration: 4,
                content: '设置成功',
              })
            )
            .finally(() => this.$store.dispatch('emarking/updateExam'))
        }
      },

      /**
       * 加载数据
       */
      loadTask() {
        if (!this.currentExamSubjectId) {
          this.blocks = []
          return Promise.resolve()
        }

        return Promise.all([
          apiGetBlockTasks(this.currentExamSubjectId),
          apiGetExamSubjectStudentCount({
            examSubjectId: this.currentExamSubjectId,
          }),
        ]).then(([responseBlocks, responseStudentInfo]) => {
          this.blocks = (responseBlocks || []).map(block => {
            if (block.markingWay.id == MarkingWayEnum.Normal.id) {
              // 混合时只有一个组，其Id为UUID_ZERO，所有评卷员都在此组
              block.groups.forEach(g => {
                g.groupId = UUID_ZERO
                if (responseStudentInfo.length > 0) {
                  g.scannedNum = responseStudentInfo[0].scannedNum
                }
              })
              block.teachers.forEach(t => (t.groupId = UUID_ZERO))
            }

            // 是否已分配任务
            let allocated = this.getBlockAllocated(block)

            // 是否展开
            let oldBlock = this.blocks.find(x => x.blockId === block.blockId)
            let expanded = oldBlock ? oldBlock._expanded : this.expandDetails

            return {
              ...block,
              allocated,
              _expanded: expanded,
            }
          })
        })
      },
      getBlockAllocated(block) {
        if (!block || !block.groups || !block.groups.length) {
          return false
        }

        // 效率优先
        if (block.assignMode.id != AssignModeEnum.Amount.id) {
          return block.teachers.length > 0
        }

        const IsMarkingWayAmount = [MarkingWayEnum.SchoolAmount.id, MarkingWayEnum.GroupAmount.id].includes(
          block.markingWay.id
        )

        // 检查各组评卷员任务量之和小于组任务量
        for (let group of block.groups) {
          if (this.currentSchoolId && this.currentSchoolId != group.groupId) {
            continue
          }
          // 双评比例
          let doubleRate = 0
          if (block.markMode.id >= MarkModeEnum.DoubleAverage.id) {
            if (block.doubleRate > 0 && block.doubleRate < 100) {
              doubleRate = block.doubleRate / 100
            } else {
              doubleRate = 1
            }
          }
          // 分校定量、分组定量按任务量；单校考试存在已扫按已扫人数；其他按学生人数
          let firstNum
          let secondNum
          if (IsMarkingWayAmount) {
            firstNum = group.amount
            secondNum = group.amount * doubleRate
          } else {
            firstNum = group.studentNum
            secondNum = group.studentNum * doubleRate
          }
          // 三评按双评计算总评卷量
          let groupTasks = Math.ceil(firstNum + secondNum)

          let teachers
          if (block.markingWay.id == MarkingWayEnum.Class.id) {
            teachers = block.teachers.filter(t => t.groupId.includes(group.groupId))
          } else {
            teachers = block.teachers.filter(t => t.groupId == group.groupId)
          }
          let teachersTasks = sumByDefaultZero(teachers, t => Math.min(firstNum, t.assignedNumber))

          if (teachersTasks < groupTasks) {
            return false
          }
        }

        // 各组任务量总和小于考生人数
        if (!this.currentSchoolId && IsMarkingWayAmount) {
          let totalAmount = sumByDefaultZero(block.groups, g => g.amount)
          if (totalAmount < this.currentSubjectStudentNumber) {
            return false
          }
        }

        return true
      },

      /**
       * 科目操作
       */
      gotoGroupSetting() {
        this.$router.replace({
          name: 'emarking-exam-task-group',
        })
      },
      addClassPrepareHeadAsLeader() {
        if (!this.currentExamSubjectId) {
          return
        }

        this.$Modal.confirm({
          title: '题块组长设置',
          content: '确定设置科目备课组长设为题块组长？',
          onOk: () => {
            apiAddClassPrepareHeadAsLeader(this.currentExamSubjectId).then(() => {
              this.loadTask()
              this.$Message.success({
                content: '操作成功',
                duration: 3,
              })
            })
          },
          onCancel: () => {
            // do nothing.
          },
        })
      },
      refreshTeacherWhenMarkingByClass() {
        if (!this.isMarkingByClass) {
          return
        }

        apiSyncTeacherWhenMarkingByClass({
          examId: this.exam.examId,
          examSubjectId: this.currentExamSubjectId,
        })
          .then(() => {
            this.$Message.success({
              content: '已同步任课教师',
            })
          })
          .finally(() => {
            this.loadTask()
          })
      },
      downloadTaskExcel() {
        apiDownloadMarkingTaskExcel({
          examId: this.examId,
          examSubjectId: this.currentExamSubjectId,
        }).then(blob =>
          downloadBlob(blob, `${this.exam.examName}${this.currentExamSubjectName}` + '已分配评卷任务表.xlsx')
        )
      },

      /**
       * 题块操作
       */
      handleMarkingWayClick(block) {
        // 只在发卷方式为混合或分组时可以设置
        if (
          ![MarkingWayEnum.Normal.id, MarkingWayEnum.Group.id, MarkingWayEnum.GroupAmount.id].includes(
            block.markingWay.id
          )
        ) {
          return
        }

        this.modalBlock = block
        this.showModalMarkingWay = true
      },
      handleMarkModeClick(block) {
        this.modalBlock = block
        this.showModalMarkMode = true
      },
      handleAssignModeClick(block) {
        if (this.isMarkingByClass) {
          return
        }
        this.modalBlock = block
        this.showModalAssignMode = true
      },
      handleTeachersClick(block) {
        // 分班阅卷不需要安排评卷员
        if (this.isMarkingByClass) {
          return
        }
        this.modalBlock = block
        this.showModalTeachers = true
      },
      handleArbitratorsClick(block) {
        if (!this.isBlockNeedArbitrate(block.markMode.id)) {
          return
        }

        this.modalBlock = block
        this.showModalArbitrators = true
      },
      handleLeadersClick(block) {
        // 分班阅卷不能提交问题卷，不需要题组长
        if (this.isMarkingByClass) {
          return
        }
        this.modalBlock = block
        this.showModalLeaders = true
      },
      resetMarking(block) {
        let doRest = () => {
          this.$Spin.show({
            render: h =>
              h(
                'div',
                {
                  style: {
                    fontSize: '24px',
                  },
                },
                `正在重置【${block.blockName}】评卷`
              ),
          })
          apiResetBlockMarking({
            examId: this.examId,
            examSubjectId: this.currentExamSubjectId,
            blockId: block.blockId,
          })
            .then(() => {
              this.$Message.success({
                content: `已重置【${block.blockName}】评卷`,
              })
            })
            .finally(() => {
              this.$Spin.hide()
              this.loadTask()
            })
        }

        let confirmMessage = `<span style="color:red">重置题块评卷将会清空本题块所有评卷数据，且不可恢复，请务必谨慎操作！</span><br><br>确定重置【${block.blockName}】？`
        this.$Modal.confirm({
          title: '重置题块评卷',
          content: confirmMessage,
          onOk: () => {
            setTimeout(() => {
              this.$Modal.confirm({
                title: '请再次确认',
                content: confirmMessage,
                onOk: () => {
                  doRest()
                },
              })
            }, 1000)
          },
        })
      },

      /**
       * 题块getters
       */
      canChangeBlockMarkingWaySeparately(block) {
        if (this.currentSchoolId) {
          return false
        }
        return [MarkingWayEnum.Normal.id, MarkingWayEnum.Group.id, MarkingWayEnum.GroupAmount.id].includes(
          block.markingWay.id
        )
      },
      isBlockNeedArbitrate(markModeId) {
        return [MarkModeEnum.DoubleArbitrate.id, MarkModeEnum.TripleArbitrate.id].includes(markModeId)
      },
      getQuestionNamesText(questions) {
        return questions.map(q => q.questionName).join('，')
      },
      getMarkModeTexts(block) {
        if (!block.markMode.id) {
          return ['请设置']
        }
        let list = [block.markMode.name]

        let detail = ''
        let showErrorScore = [
          MarkModeEnum.DoubleArbitrate.id,
          MarkModeEnum.TripleAverage.id,
          MarkModeEnum.TripleArbitrate.id,
        ].includes(block.markMode.id)
        if (showErrorScore) {
          detail = `误差${block.errorScore}分`
        }

        let showDoubleRate =
          [
            MarkModeEnum.DoubleAverage.id,
            MarkModeEnum.DoubleArbitrate.id,
            MarkModeEnum.TripleAverage.id,
            MarkModeEnum.TripleArbitrate.id,
          ].includes(block.markMode.id) &&
          block.doubleRate > 0 &&
          block.doubleRate < 100
        if (showDoubleRate) {
          if (detail) {
            detail += '，'
          }
          detail += `双评${block.doubleRate}%`
        }
        if (detail) {
          list.push(detail)
        }
        return list
      },
      getTeachersText(teachers) {
        if (teachers.length === 0) {
          return '请添加'
        } else {
          return `${teachers.length}人（${teachers.map(t => t.realName).join('，')}）`
        }
      },
      getArbitratorsText(block) {
        if (!this.isBlockNeedArbitrate(block.markMode.id)) {
          return '\\'
        } else if (block.arbitrators.length === 0) {
          return '请添加'
        } else {
          return `${block.arbitrators.length}人（${block.arbitrators.map(t => t.realName).join('，')}）`
        }
      },
      getLeadersText(teachers) {
        if (teachers.length === 0) {
          return '请添加'
        } else {
          return teachers.map(t => t.realName).join('，')
        }
      },

      // 评卷状态
      handleBtnStartMarkingClick() {
        this.startMarkingWithCheckConfirm(false)
      },
      startMarkingWithCheckConfirm(skipWarning) {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在开启评卷'
            ),
        })
        apiStartMarking({
          examId: this.examId,
          examSubjectId: this.currentExamSubjectId,
          skipWarn: skipWarning,
        })
          .then(() => {
            this.$Message.success({
              content: `已开启评卷`,
            })
          })
          .catch(err => {
            if (err.code == 2000) {
              this.$Modal.confirm({
                title: '警告',
                content: err.msg || '',
                onOk: () => {
                  this.startMarkingWithCheckConfirm(true)
                },
              })
            } else {
              this.$Modal.error({
                title: '开启评卷失败',
                content: err.msg || '',
              })
              throw err
            }
          })
          .finally(() => {
            this.$Spin.hide()
            this.$store.dispatch('emarking/updateExam')
          })
      },
      handleBtnSuspendMarkingClick() {
        this.$Modal.confirm({
          title: '暂停评卷',
          content: `是否确定暂停评卷？`,
          onOk: () => {
            this.$Spin.show({
              render: h =>
                h(
                  'div',
                  {
                    style: {
                      fontSize: '24px',
                    },
                  },
                  '正在暂停评卷'
                ),
            })
            apiSuspendMarking({
              examId: this.examId,
              examSubjectId: this.currentExamSubjectId,
            })
              .then(() => {
                this.$Message.success({
                  content: `评卷已暂停`,
                })
              })
              .finally(() => {
                this.$Spin.hide()
                this.$store.dispatch('emarking/updateExam')
              })
          },
        })
      },
    },
  }
</script>

<template>
  <div class="tab-supplementary-exercise-marking-settings">
    <div v-if="isSchoolAdministrator" class="main">
      <div class="first-tool-bar">
        <div>
          <div class="filter-teacher">
            <Input
              v-model="searchTeacherKeyword"
              class="input"
              placeholder="姓名 / 手机号码"
              maxlength="11"
              clearable
            ></Input>
          </div>

          <div class="setting-display">
            <i-switch v-model="expandDetails" @on-change="handleChangeExpandDetails"></i-switch>
            <span style="user-select: none">展开</span>
          </div>
        </div>

        <Button v-if="showBtnStartMarking" type="primary" @click="handleBtnStartMarkingClick">开启评卷</Button>
        <Button v-else-if="showBtnPauseMarking" type="warning" @click="handleBtnSuspendMarkingClick">暂停评卷</Button>
        <div v-else class="blank-div"></div>
      </div>

      <div class="second-tool-bar">
        <div class="second-tool-bar-left">
          <TextButton
            v-if="!isOnlyTaskMenuSubjectAdmin"
            type="primary"
            icon="md-settings"
            @click="showModalMarkingWaySubject = true"
            >设置发卷方式</TextButton
          >
          <TextButton type="primary" icon="md-settings" @click="modalSettingBatchScoreSizeShowed = true"
            >设置批量阅卷</TextButton
          >
          <TextButton v-if="isMarkingByClass" type="primary" icon="md-refresh" @click="refreshTeacherWhenMarkingByClass"
            >同步任课教师</TextButton
          >
          <TextButton v-if="!isMarkingByClass" type="primary" icon="md-settings" @click="showModalAssignSubject = true"
            >分配评卷量</TextButton
          >
          <TextButton v-if="!isMarkingByClass" type="primary" icon="md-copy" @click="showModalCopyMarker = true"
            >复制评卷员</TextButton
          >
          <TextButton v-if="!isMarkingByClass" type="primary" icon="md-copy" @click="showModalCopyLeader = true"
            >复制题组长</TextButton
          >
          <TextButton type="primary" icon="md-copy" @click="addClassPrepareHeadAsLeader"
            >备课组长设为题块组长</TextButton
          >
          <TextButton
            v-if="!isMarkingByClass && !isOnlyTaskMenuSubjectAdmin"
            type="primary"
            icon="md-cloud-upload"
            @click="showModalImport = true"
            >导入</TextButton
          >
          <TextButton type="primary" icon="md-download" @click="downloadTaskExcel">导出</TextButton>
        </div>
        <div class="second-tool-bar-right">
          <Switch
            :model-value="markerCanMarkMoreTask"
            size="small"
            @on-change="handleExamSubjectMarkerMarkMoreTaskStatus"
          />
          <span style="user-select: none">允许评卷员追加评卷量</span>
        </div>
      </div>

      <Table
        :data="tableRows"
        :columns="tableColumns"
        :max-height="maxTableHeight"
        @on-row-click="handleTableRowClick"
      ></Table>

      <modal-marking-way-subject
        v-model="showModalMarkingWaySubject"
        :current-marking-way-id="subjectMarkingWay.id"
        :blocks="blocks"
        :exam-subject-id="currentExamSubjectId"
        @change="loadTask"
      ></modal-marking-way-subject>

      <modal-setting-batch-score-size
        v-model="modalSettingBatchScoreSizeShowed"
        :blocks="blocks"
        :exam-subject-id="currentExamSubjectId"
        @refresh-list="loadTask"
      ></modal-setting-batch-score-size>

      <modal-assign-subject
        v-model="showModalAssignSubject"
        :exam-subject-id="currentExamSubjectId"
        :subject-name="currentExamSubjectName"
        :is-multiple-school="false"
        :subject-student-number="subjectStudentNumber"
        :subject-marking-way="subjectMarkingWay"
        :current-school-id="currentSchoolId"
        :groups="(blocks[0] && blocks[0].groups) || []"
        @change="loadTask"
      ></modal-assign-subject>

      <modal-import
        v-model="showModalImport"
        :current-school-id="currentSchoolId"
        :subject-name="currentExamSubjectName"
        :exam-subject-id="currentExamSubjectId"
        @change="loadTask"
      ></modal-import>

      <modal-mark-mode v-model="showModalMarkMode" :block="modalBlock" @change="loadTask"></modal-mark-mode>

      <modal-assign-mode
        v-model="showModalAssignMode"
        :exam-subject-id="currentExamSubjectId"
        :block="modalBlock"
        @change="loadTask"
      ></modal-assign-mode>

      <modal-teachers
        v-model="showModalTeachers"
        :exam-subject="currentSubject"
        :exam-subject-id="currentExamSubjectId"
        :block="modalBlock"
        :is-block-need-arbitrate="modalBlock && isBlockNeedArbitrate(modalBlock.markMode.id)"
        :current-school-id="currentSchoolId"
        @change="loadTask"
      ></modal-teachers>

      <modal-arbitrators
        v-model="showModalArbitrators"
        :exam-subject="currentSubject"
        :exam-subject-id="currentExamSubjectId"
        :block="modalBlock"
        :current-school-id="currentSchoolId"
        @change="loadTask"
      ></modal-arbitrators>

      <modal-leaders
        v-model="showModalLeaders"
        :exam-subject="currentSubject"
        :exam-subject-id="currentExamSubjectId"
        :block="modalBlock"
        :current-school-id="currentSchoolId"
        @change="loadTask"
      ></modal-leaders>

      <modal-copy-marker
        v-model="showModalCopyMarker"
        :subject-blocks="blocks"
        :current-school-id="currentSchoolId"
        @change="loadTask"
      ></modal-copy-marker>

      <modal-copy-leader
        v-model="showModalCopyLeader"
        :subject-blocks="blocks"
        :current-school-id="currentSchoolId"
        @change="loadTask"
      ></modal-copy-leader>
    </div>
    <div v-else class="no-permission">抱歉，由于您权限不足，无法设置评卷任务</div>
  </div>
</template>

<style lang="scss" scoped>
  .tab-supplementary-exercise-marking-settings {
    .main {
      background-color: white;

      .first-tool-bar {
        @include flex(row, space-between, center);

        .filter-teacher {
          display: inline-block;
          margin-right: 10px;
        }

        .setting-display {
          display: inline-block;
        }
      }

      .second-tool-bar {
        @include flex(row, space-between, center);
        margin: 12px 0;
        line-height: 1.5;

        .second-tool-bar-right {
          flex-grow: 0;
          flex-shrink: 0;
        }
      }
    }

    .no-permission {
      font-weight: bold;
      font-size: $font-size-large;
      line-height: 100px;
      text-align: center;
    }
  }

  :deep(.mark-mode-detail) {
    display: block;
    margin-top: 6px;
    font-size: 10px;
    line-height: 1;
  }
</style>
