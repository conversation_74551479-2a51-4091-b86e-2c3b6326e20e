// 是否高中
export function isSenior(gradeId) {
  return gradeId >= 10 && gradeId < 13
}

// 是否初中
export function isJunior(gradeId) {
  return gradeId >= 7 && gradeId <= 9
}

// 是否小学
export function isPrimary(gradeId) {
  return gradeId <= 6
}

export function checkReferenceBookScannerPasswordIsLegal(pwd) {
  function isIncrease(password) {
    for (let i = 0; i < password.length - 1; i++) {
      if (password.charCodeAt(i + 1) - password.charCodeAt(i) !== 1) {
        return false
      }
    }
    return true
  }

  function isDecrease(password) {
    for (let i = 0; i < password.length - 1; i++) {
      if (password.charCodeAt(i + 1) - password.charCodeAt(i) !== -1) {
        return false
      }
    }
    return true
  }

  function isSameCharacter(password) {
    return new Set(password).size === 1
  }

  return !(isIncrease(pwd) || isDecrease(pwd) || isSameCharacter(pwd))
}
