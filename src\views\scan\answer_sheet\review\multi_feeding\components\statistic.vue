<template>
  <div v-if="statData" class="section-stat">
    <div class="stat-block left-stat-block">
      <div class="main-stat-container">
        <Tooltip placement="bottom">
          <div :class="['stat-number', statData.batchTotal > 0 ? '' : 'color-normal']">
            {{ statData.batchTotal }}
          </div>
          <div class="stat-label">已扫描批次</div>
          <template #content>
            <strong style="margin-bottom: 8px">批次检查详情</strong>
            <p>已扫描：{{ statData.batchTotal }}</p>
            <p>已检查: {{ statData.batchCompleted }}</p>
            <p>待检查: {{ statData.batchInit }}</p>
            <p>检查中: {{ statData.batchRunning }}</p>
            <p>检查挂起: {{ statData.batchSuspended }}</p>
            <p>检查失败: {{ statData.batchFailed }}</p>
          </template>
        </Tooltip>
      </div>
      <div v-if="notFinishedBatchCount > 0" class="sub-stat-container warning-sub-stat">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
          <line x1="12" y1="9" x2="12" y2="13"></line>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
        尚有{{ notFinishedBatchCount }}个批次未完成检查
      </div>
    </div>

    <div class="vertical-divider"></div>

    <div class="stat-block center-stat-block">
      <div class="funnel-step interactive" @click="onToDetail('item-init')">
        <Tooltip placement="bottom">
          <div :class="['stat-number', statData.init > 0 ? 'color-primary' : 'color-normal']">
            {{ statData.init }}
          </div>
          <div class="stat-label">待人工确认</div>
          <template #content>
            <strong style="margin-bottom: 8px">人工确认详情</strong>
            <p>发现疑似: {{ statData.total }}</p>
            <p>待人工确认：{{ statData.init }}</p>
            <p>已确认正常: {{ statData.normal }}</p>
            <p>已确认重张: {{ statData.multiFeeding }}</p>
          </template>
        </Tooltip>
      </div>

      <div class="arrow">→</div>

      <div class="funnel-step interactive" @click="onToDetail('item-not-rescan')">
        <Tooltip placement="bottom">
          <div :class="['stat-number', statData.notReScanned > 0 ? 'color-warning-sub-stat' : 'color-normal']">
            {{ statData.notReScanned }}
          </div>
          <div class="stat-label">待重扫</div>
          <template #content>
            <strong>重扫详情</strong>
            <p>确认重张: {{ statData.multiFeeding }}</p>
            <p>未重扫：{{ statData.notReScanned }}</p>
            <p>已重扫: {{ statData.reScanned }}</p>
          </template>
        </Tooltip>
      </div>

      <div class="arrow">→</div>

      <div class="funnel-step interactive" @click="onToDetail('item-rescan-check-init')">
        <Tooltip placement="bottom">
          <div :class="['stat-number', statData.reScanCheckResultInit > 0 ? 'color-blue' : 'color-normal']">
            {{ statData.reScanCheckResultInit }}
          </div>
          <div class="stat-label">待重扫复核</div>
          <div v-if="statData.reScanCheckResultNo > 0" class="sub-stat-container secondary-warning-sub-stat">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M18 6L6 18" />
              <path d="M6 6l12 12" />
            </svg>
            {{ statData.reScanCheckResultNo }}个重扫复核未通过
          </div>
          <template #content>
            <strong>重扫复核详情</strong>
            <p>已重扫: {{ statData.reScanned }}</p>
            <p>待复核: {{ statData.reScanCheckResultInit }}</p>
            <p>已复核通过: {{ statData.reScanCheckResultYes }}</p>
            <p>复核不通过: {{ statData.reScanCheckResultNo }}</p>
          </template>
        </Tooltip>
      </div>
    </div>

    <div class="vertical-divider"></div>

    <div class="stat-block right-stat-block">
      <div class="summary-item">
        <div :class="['stat-number', statData.total > 0 ? '' : 'color-normal']">{{ statData.total }}</div>
        <div class="stat-label">发现疑似</div>
      </div>
      <div class="summary-item">
        <div :class="['stat-number', statData.multiFeeding > 0 ? 'color-red' : 'color-normal']">
          {{ statData.multiFeeding }}
        </div>
        <div class="stat-label">确认重张</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      statData: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['on-scroll-to'],
    computed: {
      notFinishedBatchCount() {
        return this.statData.batchTotal - this.statData.batchCompleted
      },
    },
    methods: {
      onToDetail(status) {
        this.$emit('on-scroll-to', status)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-stat {
    @include flex(row, space-between, center);
    gap: 24px;
    margin-bottom: 20px;
    padding: 24px 32px;
    border: 1px solid #e5e9f2;
    border-radius: 16px;
    background-color: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 33, 111, 0.05);

    .box-stat-item {
      @include flex(column, center, flex-start);
      width: 180px;
      height: 110px;
      margin: 0 30px;
      padding: 15px;
      border: 1px solid #dfdfdf;
      border-radius: 6px;
      line-height: 1.6;

      .label {
        display: inline-block;
        width: 90px;
      }
    }

    .vertical-divider {
      width: 1px;
      height: 80px;
      background-color: #f0f2f5;
    }

    .stat-block {
      text-align: center;
    }

    .main-stat-container {
      position: relative;
    }

    .stat-number {
      position: relative;
      color: $color-title;
      font-weight: 700;
      font-size: 48px;
      line-height: 1.2;
    }

    .stat-label {
      margin-top: 6px;
      color: #6c7a91;
      font-size: 14px;
    }

    .funnel-step:hover {
      .stat-number::after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0;
        border: 2px solid;
        content: '';
      }
    }

    .sub-stat-container {
      display: flex;
      gap: 6px;
      align-items: center;
      justify-content: center;
      margin-top: 8px;
      font-size: 13px;
    }

    .warning-sub-stat {
      color: #f39c12;
    }

    .secondary-warning-sub-stat {
      color: #e74c3c;
      font-size: 12px;
    }

    .center-stat-block {
      display: flex;
      gap: 4vw;
      align-items: flex-start;
    }

    .funnel-step {
      position: relative;
      transition: transform 0.2s ease-out;
    }

    .funnel-step.interactive {
      cursor: pointer;
    }

    .funnel-step.interactive:hover {
      transform: translateY(-5px);
    }

    .center-stat-block .arrow {
      margin-top: 12px;
      color: #c8d0e0;
      font-size: 24px;
    }

    .right-stat-block {
      display: flex;
      gap: 40px;
      align-items: flex-start;
    }

    .color-normal {
      color: #919bad !important;
    }

    .color-primary {
      color: #049587 !important;
    }

    .color-warning-sub-stat {
      color: #fa8c16 !important;
    }

    .color-blue {
      color: #007bff !important;
    }

    .color-red {
      color: #dc3545 !important;
    }
  }
</style>
