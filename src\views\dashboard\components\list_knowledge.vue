<template>
  <div class="container-knowledge">
    <div v-if="list.length" class="box-list">
      <div class="knowledge-item-header">
        <div class="name">知识点</div>
        <div v-for="child in list[0].childVos" :key="child.instituId" class="item-title">
          {{ child.instituName }}
        </div>
      </div>
      <div
        v-for="(item, idx) in list"
        :key="item.sortCode"
        class="knowledge-item"
        :class="{ 'knowledge-item-odd': idx % 2 === 1 }"
      >
        <div class="name">{{ item.knowName }}</div>
        <div v-for="child in item.childVos" :key="child.instituId" class="my-swiper-slide">{{ child.scoreRate }}</div>
      </div>
    </div>
    <div v-else class="no-data">暂无知识点</div>
  </div>
</template>

<script setup>
  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  })
</script>

<style lang="scss" scoped>
  .section-knowledge {
    flex-grow: 1;
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 10px;
    overflow: hidden;
  }

  .container-knowledge {
    @include flex(row, flex-start, stretch);
    width: 100%;
    height: 100%;

    .no-data {
      @include flex(row, center, center);
      width: 100%;
      height: 100%;
      color: #fff;
      font-size: 14px;
    }
  }

  .box-list {
    box-sizing: border-box;
    overflow: auto;
    color: #fff;
    font-size: 0;
    user-select: none;
  }

  .knowledge-item-header {
    position: sticky;
    top: -1px;
    z-index: 100;
    display: inline-flex;
    align-items: stretch;
    height: 28px;
    background-color: rgb(10, 115, 255);

    .name {
      position: sticky;
      left: -1px;
      flex-shrink: 0;
      width: 150px;
      padding: 0 10px;
      overflow: hidden;
      font-size: 16px;
      line-height: 28px;
      white-space: nowrap;
      background-color: rgb(10, 115, 255);
    }

    .item-title {
      width: 120px;
      margin-left: 10px;
      overflow: hidden;
      font-size: 14px;
      line-height: 28px;
      white-space: nowrap;
      text-align: center;
    }
  }

  .knowledge-item {
    display: inline-flex;
    align-items: stretch;
    height: 28px;
    background-color: transparent;

    .name {
      position: sticky;
      left: -1px;
      z-index: 99;
      flex-shrink: 0;
      width: 150px;
      padding-right: 10px;
      padding-left: 10px;
      overflow: hidden;
      font-size: 14px;
      line-height: 28px;
      white-space: nowrap;
      background-color: #03064f;
    }
  }

  .knowledge-item-odd {
    background-color: #0b5481;

    .name {
      background-color: #0b5481;
    }
  }

  .my-swiper {
    flex-grow: 1;
  }

  .my-swiper-slide {
    width: 120px;
    margin-left: 10px;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
  }

  .item-rate {
    overflow: hidden;
    font-size: 13px;
    line-height: 28px;
    white-space: nowrap;
  }
</style>
