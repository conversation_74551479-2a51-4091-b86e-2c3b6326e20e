<template>
  <div class="container-paper-upload-batch">
    <div class="header">
      <div class="title">批量上传试卷</div>
      <Cascader
        :data="stageSubjectCascaderData"
        :model-value="stageSubjectCascaderValue"
        size="small"
        :clearable="false"
        class="selector-stage-subject"
        transfer
        @on-change="changeStageSubject"
      ></Cascader>
    </div>
    <div class="main">
      <PanelUpload class="panel-upload"></PanelUpload>
      <PanelContent class="panel-content"></PanelContent>
      <PanelPaper class="panel-paper"></PanelPaper>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'

  import store from '@/store'

  import PanelUpload from './upload/panel_upload.vue'
  import PanelContent from './content/panel_content.vue'
  import PanelPaper from './paper/panel_paper.vue'

  // 学段学科
  const currentStageSubject = computed(() => store.getters['qlib/currentStageSubject'])
  const stages = computed(() => store.getters['qlib/stageSubjects']())
  const stageSubjectCascaderValue = computed(() => {
    return [currentStageSubject.value.stageId, currentStageSubject.value.subjectId]
  })
  const stageSubjectCascaderData = computed(() => {
    return stages.value.map(stage => ({
      value: stage.id,
      label: stage.name,
      children: stage.subjects.map(s => ({
        value: s.id,
        label: s.name,
      })),
    }))
  })
  function changeStageSubject([stageId, subjectId]) {
    let stage = stages.value.find(x => x.id == stageId)
    let subject = stage.subjects.find(x => x.id == subjectId)
    store.dispatch('qlib/updateCurrentStageSubject', {
      stageId: stage.id,
      stageName: stage.name,
      subjectId: subject.id,
      subjectName: subject.name,
    })
  }
</script>

<style lang="scss" scoped>
  .container-paper-upload-batch {
    @include flex(column, flex-start, stretch);
    height: 100vh;
    overflow: hidden;

    .header {
      @include flex(row, flex-start, center);
      flex-shrink: 0;
      gap: 8px;
      height: 40px;
      padding: 0 16px;
      color: white;
      background-color: $color-primary-dark;

      .title {
        margin-right: auto;
        font-size: $font-size-medium-x;
      }

      .selector-stage-subject {
        width: 150px;
      }
    }

    .main {
      @include flex(row, flex-start, stretch);
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
      background-color: white;

      .panel-upload {
        flex: 0 0 420px;
        overflow: hidden;
      }

      .panel-content,
      .panel-paper {
        flex: 1 1 0;
        border-left: 1px solid $color-border;
        overflow: hidden;
      }
    }
  }
</style>
