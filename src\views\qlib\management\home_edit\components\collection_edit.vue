<template>
  <div class="container-banner-edit">
    <div class="box-tool">
      <TextButton type="primary" icon="md-add" @click="onShowAddCollectionPaper">新增合集试卷</TextButton>
      <div class="tip-item">
        <Icon type="md-information-circle" class="tip-icon"></Icon>
        <span class="tip-text">可通过拖拽调整试卷合集顺序</span>
      </div>
    </div>
    <div class="section-body">
      <div class="outer-list-collection">
        <draggable
          v-model="collectionList"
          item-key="collectionName"
          :component-data="{ class: 'transition-group' }"
          ghost-class="ghost"
          @end="onCollectionDragEnd"
        >
          <template #item="{ element }">
            <div class="block-list">
              <div class="block-header">
                <span class="block-title">{{ element.collectionName }}</span>
                <div class="box-btn">
                  <TextButton type="primary" class="block-more" @click="onEditCollection(element)">编辑</TextButton>
                  <TextButton type="warning" class="block-more" @click="onDeleteCollection(element)">删除</TextButton>
                </div>
              </div>
              <div class="block-body">
                <div v-for="paper in element.list" class="list-item" :Key="paper.id" @click="onToPaper(paper)">
                  <span class="list-item-name">{{ paper.paperName }}</span>
                  <span class="list-item-count">{{ paper.browseCount }}</span>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <Modal
      v-model="modalEditViisible"
      class="modal-edit-paper"
      :title="modalEditTitle"
      :width="700"
      @on-visible-change="handleModalSaveCollectionVisibleChange"
    >
      <div class="content-modal">
        <Form :label-width="80">
          <FormItem label="合集名称">
            <div class="container-form-item">
              <!-- <Select v-model="currentCollection.collectionName" style="width: 200px">
                <Option v-for="c in collectionList" :value="c.collectionName" :key="c.collectionName">{{
                  c.collectionName
                }}</Option>
              </Select> -->
              <!-- <TextButton v-if="!isEditing" type="primary" @click="onNewCollection">新增合集名称</TextButton> -->
              <div class="box-new-collection">
                <Input
                  v-model="currentCollection.collectionName"
                  placeholder="请输入合集名称"
                  style="width: 200px; margin-right: 10px"
                ></Input>
                <!-- <Button icon="md-checkmark" type="primary" class="btn-add-name" @click="onAddCollectionName"></Button> -->
              </div>
            </div>
          </FormItem>
          <FormItem label="合集试卷">
            <div class="box-btn-add-paper">
              <TextButton type="primary" @click="onToAddPaper">添加试卷</TextButton>
              <div class="tip-item">
                <Icon type="md-information-circle" class="tip-icon"></Icon>
                <span class="tip-text">可通过拖拽调整试卷顺序</span>
              </div>
            </div>
            <div class="container-list-paper">
              <draggable
                v-model="currentCollection.list"
                item-key="id"
                :component-data="{ type: 'transition-group' }"
                ghost-class="ghost"
              >
                <template #item="{ element }">
                  <div class="list-item-paper">
                    <span>{{ element.paperName }}</span>
                    <TextButton type="warning" @click="onDeletePaper(element)">移除</TextButton>
                  </div>
                </template>
              </draggable>
            </div>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <div class="footer">
          <Button type="text" @click="handleSaveCollectionCancel">取消</Button>
          <Button type="primary" @click="handleSaveCollectionOK">确定</Button>
        </div>
      </template>
    </Modal>
    <Modal
      v-model="modalAddPaperViisible"
      class="modal-add-paper"
      title="添加合集试卷"
      fullscreen
      footer-hide
      @on-visible-change="handleAddPaperVisibleChange"
    >
      <PickerPaper
        :paper-list="currentCollection.list"
        @add-to-collection="onAddToCollection"
        @del-from-collection="onDelFromCollection"
      />
      <!-- <template #footer>
        <div class="footer">
          <Button type="text" @click="handleAddPaperCancel">取消</Button>
          <Button type="primary" @click="handleAddPaperOK">确定</Button>
        </div>
      </template> -->
    </Modal>
  </div>
</template>

<script>
  import draggable from 'vuedraggable'
  import {
    apiGetFrontPagePaperList,
    apiSavePapers,
    apiDeleteCollection,
    apiChangeCollectionOrder,
  } from '@/api/qlib/front_page'
  import { groupArray } from '@/utils/array'
  import PickerPaper from '../../../picker_paper/index.vue'
  import { deepCopy } from '@/utils/object'

  export default {
    components: {
      draggable,
      PickerPaper,
    },
    data() {
      return {
        collectionList: [],
        modalEditViisible: false,
        modalEditTitle: '编辑合集试卷',
        currentCollection: {
          collectionName: '',
          list: [],
        },
        isEditing: false,
        newName: '',
        modalAddPaperViisible: false,
      }
    },
    computed: {
      currentStageSubject() {
        return this.$store.getters['qlib/currentStageSubject']
      },
    },
    watch: {
      currentStageSubject() {
        this.getPaperList()
      },
    },
    created() {
      this.getPaperList()
    },
    methods: {
      getPaperList() {
        apiGetFrontPagePaperList({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
        }).then(res => {
          const result = groupArray(res || [], item => item.collectionName).map(g => ({
            collectionName: g.key,
            list: g.group,
            collectionSortCode: g.group[0].collectionSortCode,
          }))

          this.collectionList = result.sort((a, b) => a.collectionSortCode - b.collectionSortCode)
        })
      },
      onShowAddCollectionPaper() {
        this.modalEditTitle = '新增合集试卷'
        this.modalEditViisible = true
        const collectionLength = (this.collectionList && this.collectionList.length) || 0
        this.currentCollection = {
          collectionName: '',
          list: [],
          collectionSortCode: collectionLength + 1,
        }
      },
      onEditCollection(collection) {
        this.modalEditTitle = '编辑合集试卷'
        this.modalEditViisible = true
        this.currentCollection = deepCopy(collection)
      },
      onDeleteCollection(collection) {
        this.$Modal.confirm({
          title: '删除合集',
          content: '确定删除该试卷合集吗？',
          onOk: () => {
            apiDeleteCollection({
              stageId: this.currentStageSubject.stageId,
              subjectId: this.currentStageSubject.subjectId,
              collectionName: collection.collectionName,
            }).then(() => {
              this.getPaperList()
            })
          },
        })
      },
      onToPaper(paper) {
        this.$router.push({
          name: 'qlib-examPaperView',
          params: {
            paperid: paper.paperId,
          },
        })
      },
      handleModalSaveCollectionVisibleChange(visible) {
        if (!visible) {
          this.handleSaveCollectionCancel()
        }
      },
      handleAddPaperVisibleChange(visible) {
        if (!visible) {
          this.handleAddPaperCancel()
        }
      },
      handleSaveCollectionCancel() {
        this.modalEditViisible = false
      },
      handleAddPaperCancel() {
        this.modalAddPaperViisible = false
      },
      handleSaveCollectionOK() {
        const msg = this.check()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        const { collectionName, list, collectionSortCode } = this.currentCollection
        apiSavePapers({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
          collectionName,
          collectionSortCode,
          paperIds: list.map(item => item.paperId),
        }).then(() => {
          this.$Message.success('保存成功')
          this.handleSaveCollectionCancel()
          this.getPaperList()
        })
      },
      onCollectionDragEnd() {
        apiChangeCollectionOrder({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
          collectionNames: this.collectionList.map(item => item.collectionName),
        }).then(() => {
          this.getPaperList()
        })
      },
      // handleAddPaperOK() {},
      onToAddPaper() {
        this.modalAddPaperViisible = true
      },
      onAddPaper() {
        this.$router.push({
          name: 'qlib-examPaperList',
          query: {
            stageId: this.currentStageSubject.stageId,
            subjectId: this.currentStageSubject.subjectId,
          },
        })
      },
      check() {
        if (!this.currentCollection.collectionName) {
          return '请选择合集'
        }
        if (!this.currentCollection.list.length) {
          return '请添加试卷至合集'
        }
        return ''
      },
      onNewCollection() {
        this.isEditing = true
      },
      onAddCollectionName() {
        this.currentCollection.collectionName = this.newName
        this.collectionList.push({
          collectionName: this.newName,
          list: [],
        })
        this.isEditing = false
      },
      onDeletePaper(paper) {
        const { list = [] } = this.currentCollection
        const index = list.findIndex(item => item.paperId === paper.paperId)
        if (index > -1) {
          list.splice(index, 1)
        }
      },
      onAddToCollection(paper) {
        this.currentCollection.list.push({
          paperId: paper.id,
          paperName: paper.name,
        })
      },
      onDelFromCollection(paper) {
        const { list = [] } = this.currentCollection
        const index = list.findIndex(item => item.paperId == paper.id)

        if (index > -1) {
          list.splice(index, 1)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-banner-edit {
    .box-tool {
      @include flex(row, space-between, center);
      margin-bottom: 10px;
    }

    .section-body {
      background-color: #fff;
    }

    .outer-list-collection {
      .transition-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
      }

      .block-list {
        margin-bottom: 15px;
        padding: 0 15px;
      }

      .block-header {
        @include flex(row, space-between, center);
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dfdfdf;
      }

      .block-title {
        font-size: 20px;
      }

      .list-item {
        @include flex(row, space-between, center);
        cursor: pointer;

        &:not(:last-child) {
          margin-bottom: 12px;
        }

        .list-item-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &:hover {
          .list-item-name {
            color: $color-primary;
          }
        }
      }

      .list-item-count {
        color: #999;
      }
    }
  }

  .modal-edit-paper {
    .ivu-form-item {
      margin-bottom: 10px;
    }

    .box-new-collection {
      @include flex(row, flex-start, center);
      // margin-left: 10px;
    }

    .box-btn-add-paper {
      @include flex(row, space-between, center);
    }

    .container-form-item {
      @include flex(row, flex-start, center);
    }

    .list-item-paper {
      @include flex(row, space-between, center);
      user-select: none;
    }
  }
</style>
