<template>
  <el-checkbox-group v-model="activeCheckbox">
    <el-checkbox v-for="item in options.options.value" :label="item">{{ item }}</el-checkbox>
  </el-checkbox-group>
</template>

<script>
  import { ElCheckboxGroup, ElCheckbox } from 'element-plus'
  import 'element-plus/es/components/checkbox-group/style/css'
  import 'element-plus/es/components/checkbox/style/css'

  export default {
    components: {
      'el-checkbox-group': ElCheckboxGroup,
      'el-checkbox': ElCheckbox,
    },
    props: {
      options: {
        type: Object,
        default: () => ({
          options: {
            value: [],
          },
        }),
      },
    },
    data() {
      return {
        activeCheckbox: [],
      }
    },
  }
</script>
