<template>
  <div class="page-scan-index">
    <PageHeader
      class="section-header"
      show-btn-back
      :title-prefix="titlePrefix"
      :show-btn-close="false"
      :height="headerHeight"
      @back="back"
    ></PageHeader>

    <Tabs v-model="activeTab" class="section-tabs" :animated="false">
      <TabPane
        v-for="(tab, tabIndex) of tabs"
        :key="tab.label"
        :name="tab.name"
        :label="tab.label"
        :index="tabIndex + 1"
      ></TabPane>
    </Tabs>

    <Component
      :is="currentComponent"
      class="section-tab-content"
      :init-batch-id="initBatchId"
      :init-batch-page="initBatchPage"
      :init-room-no="initRoomNo"
      :init-student-id="initStudentId"
      :filter-status="filterStatus"
      :top="tabContentTop"
      :style="tabContentStyle"
      @to-detail="goToDetail"
      @clear-init="clearInit"
      @to-student="gotoStudent"
      @to-monitor="gotoMonitor"
      @clear-filter-status="clearFilterStauts"
    ></Component>
  </div>
</template>

<script>
  import PageHeader from '../../answer_sheet/components/page_header.vue'
  import ScanMain from './scan_main/scan_main.vue'
  import ScanRecords from './scan_records/scan_records.vue'
  import ScanMonitor from './scan_monitor/scan_monitor.vue'
  import ScanStudent from './scan_student/scan_student.vue'

  import iView from '@/iview'
  import store from '@/store/index'

  export default {
    components: {
      PageHeader,
    },
    beforeRouteEnter(to, from, next) {
      store.commit('scan/clear')
      let { examId, examSubjectId } = to.params
      let newIds = ['newonline', 'newpostscan']
      if (examId && examSubjectId && !newIds.includes(examId) && !newIds.includes(examSubjectId)) {
        iView.LoadingBar.start()
        store
          .dispatch('scan/loadExamSubjectScanRangeQuestionTemplateAsync', {
            examId: to.params.examId,
            examSubjectId: to.params.examSubjectId,
          })
          .then(() => {
            next()
          })
          .catch(() => {
            iView.LoadingBar.error()
          })
          .finally(() => {
            iView.LoadingBar.finish()
          })
      } else {
        next()
      }
    },
    data() {
      return {
        activeTab: 'scan_paper',

        // 扫描记录页面初始批次
        initBatchId: '',
        initBatchPage: 1,

        // 答卷浏览页面初始考场学生
        initRoomNo: 0,
        initStudentId: '',

        filterStatus: '',

        // 样式
        headerHeight: 36,
        tabsHeight: 36,
      }
    },
    computed: {
      hasExam() {
        return this.$store.getters['scan/hasExam']
      },
      isPostScan() {
        return this.$store.getters['scan/isPostScan']
      },
      tabs() {
        let tabs = [
          {
            label: '扫描答卷',
            name: 'scan_paper',
            component: ScanMain,
          },
        ]
        if (this.hasExam) {
          tabs.push(
            {
              label: '扫描记录',
              name: 'scan_record',
              component: ScanRecords,
            },
            {
              label: '扫描统计',
              name: 'scan_monitor',
              component: ScanMonitor,
            },
            {
              label: '答卷浏览',
              name: 'scan_student',
              component: ScanStudent,
            }
          )
        }
        return tabs
      },
      currentComponent() {
        let tab = this.tabs.find(t => t.name == this.activeTab)
        return tab && tab.component
      },
      tabContentTop() {
        return this.headerHeight + this.tabsHeight
      },
      tabContentStyle() {
        return {
          height: `calc(100vh - ${this.tabContentTop}px)`,
        }
      },
      titlePrefix() {
        let isPostScan = this.hasExam ? this.isPostScan : this.$route.params.examId == 'newpostscan'
        return `扫描教辅作业（${isPostScan ? '先阅后扫' : '线上阅卷'}）`
      },
    },
    beforeUnmount() {
      this.$store.commit('scan/clear')
    },
    methods: {
      back() {
        this.$router.go(-1)
      },
      goToDetail({ batchId, currentPage }) {
        this.initBatchId = batchId
        this.initBatchPage = currentPage
        this.activeTab = 'scan_record'
      },
      gotoMonitor(status) {
        this.activeTab = 'scan_monitor'
        this.filterStatus = status
      },
      gotoStudent({ roomNo, studentId }) {
        this.initRoomNo = roomNo
        this.initStudentId = studentId
        this.activeTab = 'scan_student'
      },
      clearInit() {
        this.initBatchId = ''
        this.initBatchPage = 1
        this.initRoomNo = 0
        this.initStudentId = ''
      },
      clearFilterStauts() {
        this.filterStatus = ''
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-scan-index {
    background-color: white;
  }

  .section-tabs {
    :deep(.ivu-tabs-bar) {
      margin-bottom: 0;
    }
  }
</style>
