<template>
  <Modal
    class="modal-raw-image"
    :model-value="modelValue"
    :mask-closable="false"
    :closable="false"
    fullscreen
    footer-hide
  >
    <template #header>
      <div class="header">
        <div class="title">{{ title }}</div>
        <div class="toolbar">
          <div class="btn" title="放大图片" @click="zoomImage('in')">
            <img src="@/assets/images/common/zoom_in.svg" style="width: 16px" />
          </div>
          <div class="btn" title="缩小图片" @click="zoomImage('out')">
            <img src="@/assets/images/common/zoom_out.svg" style="width: 16px" />
          </div>
          <div class="btn" title="还原" @click="resetImage">
            <Icon class="btn-icon-reset" type="md-refresh" :size="18"></Icon>
          </div>
        </div>
        <div class="paper-no">{{ paperNo }}</div>
        <Divider type="vertical"></Divider>
        <div class="stu-info">{{ stuInfo }}</div>
        <div class="nav">
          <TextButton type="primary" icon="ios-arrow-back" :disabled="!hasPrevious" @click="goPrevious"
            >上一张</TextButton
          >
          <TextButton type="primary" icon="ios-arrow-forward" :disabled="!hasNext" @click="goNext">下一张</TextButton>
        </div>
        <Icon class="btn-close" type="ios-close" @click="close"></Icon>
      </div>
    </template>
    <div class="img-container">
      <img v-if="imgLeftSrc" :src="imgLeftSrc" :style="imgStyle" /><img
        v-if="imgRightSrc"
        :src="imgRightSrc"
        :style="imgStyle"
      />
      <template v-if="imgOthers.length > 0">
        <img v-for="src in imgOthers" :key="src" :src="src" :style="imgStyle" />
      </template>
    </div>
  </Modal>
</template>

<script>
  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      activeBatch: Object,
      paper: Object,
      hasPrevious: Boolean,
      hasNext: Boolean,
    },
    emits: ['update:modelValue', 'go-previous', 'go-next'],
    data() {
      return {
        scale: 1,
      }
    },
    computed: {
      title() {
        if (!this.activeBatch) {
          return '查看原图'
        }
        return `查看原图（${this.activeBatch.batchNumber} 批次）`
      },
      paperNo() {
        return this.paper ? `第 ${this.paper.paperNo} 张` : ''
      },
      stuInfo() {
        if (!this.paper) {
          return ''
        }
        let info = ''
        let { admissionNum, studentName, statusText } = this.paper
        if (admissionNum) {
          info += admissionNum + '，'
        }
        if (studentName) {
          info += studentName + '，'
        }
        info += statusText
        return info
      },
      imgLeftSrc() {
        return this.paper ? this.paper.imgUrls[0] : ''
      },
      imgRightSrc() {
        return this.paper ? this.paper.imgUrls[1] : ''
      },
      // 一般只有两张图
      imgOthers() {
        return this.paper ? this.paper.imgUrls.slice(2) : []
      },
      imgStyle() {
        return {
          width: `${50 * this.scale}%`,
        }
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          document.body.addEventListener('keydown', this.handleKeydown)
        } else {
          document.body.removeEventListener('keydown', this.handleKeydown)
        }
      },
    },
    beforeUnmount() {
      document.body.removeEventListener('keydown', this.handleKeydown)
    },
    methods: {
      close() {
        this.$emit('update:modelValue', false)
      },
      goPrevious() {
        this.$emit('go-previous')
      },
      goNext() {
        this.$emit('go-next')
      },
      handleKeydown(e) {
        if (e.key == 'ArrowLeft' && this.hasPrevious) {
          this.goPrevious()
        } else if (e.key == 'ArrowRight' && this.hasNext) {
          this.goNext()
        }
      },
      zoomImage(option) {
        let nextScale = option === 'in' ? this.scale + 0.1 : this.scale - 0.1
        nextScale = Math.min(5, Math.max(0.1, nextScale))
        this.scale = nextScale
      },
      resetImage() {
        this.scale = 1
      },
    },
  }
</script>

<style lang="scss" scoped>
  .header {
    @include flex(row, flex-start, center);

    height: 24px;

    .title {
      margin-right: 50px;
      font-size: $font-size-medium-x;
    }

    .toolbar {
      @include flex(row, center, center);
      flex-grow: 1;

      .btn {
        margin-right: 20px;
        font-size: 0;
        cursor: pointer;
      }

      .btn-icon-reset {
        transform: scaleX(-1);
      }
    }

    .paper-no {
      margin-right: 10px;
      margin-left: 50px;
    }

    .stu-info {
      margin-right: 50px;
      margin-left: 10px;
    }

    .btn-close {
      margin-left: 50px;
      color: #999;
      font-size: 31px;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #444;
      }
    }
  }

  .img-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    text-align: center;
  }
</style>
