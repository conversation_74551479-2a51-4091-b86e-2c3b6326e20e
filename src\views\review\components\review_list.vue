<script>
  import NoData from '@/components/no_data'
  import RadioGroup from '@/components/radio_group'

  import { apiGetReviewActivityList } from '@/api/review'

  import { debounce } from '@/utils/function'

  import ReviewProjectStatus from '@/enum/review/review_project_status'

  const uniquePageName = 'review-list'

  export default {
    components: {
      'no-data': NoData,
      's-radio-group': RadioGroup,
    },

    data() {
      return {
        keyword: '',
        filterStatus: 'all',

        reviewList: [],
        currentPage: 1,
        pageSize: 10,
        total: 0,

        reviewListIntervalId: null,
      }
    },

    computed: {
      statusList() {
        const List = [
          {
            id: 'all',
            name: '全部',
          },
        ]

        List.push(
          ...(ReviewProjectStatus.getEntries() || []).map(s => ({
            id: s.key,
            name: s.name,
          }))
        )

        return List
      },
    },

    watch: {
      filterStatus() {
        this.changePage()
      },
    },

    created() {
      this.fetchReviewListAndSetInterval()
    },

    beforeUnmount() {
      this.clearFetchInterval()
    },

    methods: {
      enterActivity(item) {
        this.$router.push({
          name: 'review_activity',
          params: {
            activityId: item.id,
          },
        })
      },

      clearFetchInterval() {
        if (this.reviewListIntervalId) {
          clearInterval(this.reviewListIntervalId)
          this.reviewListIntervalId = null
        }
      },

      fetchReviewListAndSetInterval() {
        this.clearFetchInterval()
        this.fetchReviewList()
        this.reviewListIntervalId = setInterval(() => this.fetchReviewList(), 1000 * 60 * 5)
      },

      fetchReviewList() {
        const RequestParams = {
          name: this.keyword || undefined,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
        }

        if (this.filterStatus !== 'all' && this.filterStatus) {
          RequestParams.status = ReviewProjectStatus[this.filterStatus].id
        }

        apiGetReviewActivityList(RequestParams)
          .then(response => {
            this.total = (response && response.total && Number(response.total)) || 0
            this.reviewList = (response && response.list) || []
          })
          .catch(() => {
            this.total = 0
            this.reviewList = []
          })
      },

      changeFilterKeyword: debounce(function (e) {
        this.keyword = (e.target.value || '').trim()
        this.changePage()
      }, 600),

      changePage(page = 1) {
        this.currentPage = page
        this.fetchReviewListAndSetInterval()
      },
    },
  }
</script>

<template>
  <div class="container-review-list">
    <div class="filter-bar">
      <s-radio-group v-model="filterStatus" :radioes="statusList"></s-radio-group>

      <Input
        :model-value="keyword"
        class="keyword-serach"
        clearable
        transfer
        placeholder="评审活动名称"
        suffix="md-search"
        @on-change="changeFilterKeyword"
      />
    </div>

    <div v-if="reviewList && reviewList.length" class="review-list">
      <div v-for="item of reviewList" :key="item.id" class="review-item">
        <div class="review-item-left">
          <div class="item-title">
            <span class="item-name" @click="enterActivity(item)">{{ item.name }}</span>
            <Icon v-show="!item.isPublic" style="margin-left: 12px; font-size: 16px" type="md-eye-off" title="未发布" />
          </div>
          <div class="item-info">
            <span class="tag tag-running">{{ item.statusName }}</span>
            <span class="info-text">报名截止时间：{{ item.registerEndTime && item.registerEndTime.slice(0, 10) }}</span>
            <!-- <span class="info-text">创建人：{{ item.createByName }}</span> -->
          </div>
        </div>

        <div class="review-item-right">
          <Button type="primary" icon="md-apps" @click="enterActivity(item)">进入活动</Button>
          <Dropdown v-show="false" transfer>
            <Button type="primary" ghost style="margin-left: 8px"
              >更多操作<Icon type="md-arrow-dropdown" style="margin-left: 2px"
            /></Button>
            <template #list>
              <DropdownMenu>
                <DropdownItem><Icon type="md-contacts" style="margin-right: 6px" />报名邀请</DropdownItem>
                <DropdownItem><Icon type="md-qr-scanner" style="margin-right: 6px" />导出邀请码</DropdownItem>
                <DropdownItem><Icon type="md-document" style="margin-right: 6px" />复制新建</DropdownItem>
              </DropdownMenu></template
            >
          </Dropdown>
        </div>
      </div>

      <div class="pager">
        <span style="margin-right: 20px"> 共 {{ total }} 条记录 </span>
        <Page :model-value="currentPage" :page-size="pageSize" :total="total" @on-change="changePage"></Page>
      </div>
    </div>
    <no-data v-else>暂无相关评审活动</no-data>
  </div>
</template>

<style lang="scss" scoped>
  .page-review-home {
    padding: 25px;
    padding-top: 10px;
    background-color: #fff;

    .title-bar {
      @include flex(row, space-between, center);
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dfdfdf;

      .title {
        @include flex(row, flex-start, center);
        flex: 1;
        font-size: $font-size-medium-x;

        .tab-item {
          position: relative;
          cursor: pointer;

          &:not(:last-child) {
            margin-right: 30px;
          }
        }

        .tab-item-active {
          &::after {
            position: absolute;
            right: 0;
            bottom: -12px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: $color-primary;
            content: '';
          }
        }
      }

      .operate-btns {
        flex: none;

        .btn-normal-height {
          height: 28px;
          font-size: 14px;
        }

        .btn-success {
          border-color: #13ce66;
          color: #13ce66;
          background-color: #e8faf0;
        }
      }
    }

    .filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 16px;

      .keyword-serach {
        width: 250px;
      }
    }

    .review-list {
      .review-item {
        @include flex(row, space-between, center);
        padding: 24px;
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:not(:last-child) {
          margin-bottom: 24px;
        }

        &:hover {
          border-color: $color-primary;
          transform: scale(1.02);
        }

        .review-item-left {
          .item-title {
            @include flex(row, flex-start, center);
            margin-bottom: 26px;

            .item-name {
              font-weight: 700;
              font-size: $font-size-medium-x;

              &:hover {
                color: $color-primary;
                cursor: pointer;
              }
            }
          }

          .item-info {
            .tag {
              display: inline-block;
              padding: 2px 10px;
              border: 1px solid $color-primary;
              border-radius: 2px;
              font-size: 12px;
              vertical-align: middle;
            }

            .tag-running {
              border-color: $color-primary-light;
              color: $color-primary-light;
              background-color: #f0f9eb;
            }

            .tag-blue {
              border-color: #2fb0f1;
              color: #2fb0f1;
              background-color: #ecf5ff;
            }

            .tag-finished {
              border-color: $color-warning;
              color: $color-warning;
              background-color: #fdf6ec;
            }

            .info-text {
              color: $color-icon;
              font-size: $font-size-small;
            }

            span:not(:first-child) {
              margin-left: 10px;
            }
          }
        }
      }

      .pager {
        @include flex(row, flex-end, center);
        margin-top: 20px;
      }
    }
  }
</style>
