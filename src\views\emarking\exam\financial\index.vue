<script>
  import NoData from '@/components/no_data'

  import {
    apiGetInstitutionFinancialStaffInfo,
    apiSaveExamSchoolFinancialStaffInfo,
    apiDeleteExamSchoolFinancialStaffInfo,
    apiGetUnionExamSchoolFinancialStaffInfos,
    apiDownloadExamSchoolFinancialStaffInfos,
  } from '@/api/user'

  import { deepCopy } from '@/utils/object'
  import { downloadBlob } from '@/utils/download'

  const BlankFinancialInfo = {
    name: null,
    mobile: null,
    email: null,
    invoiceFullName: null,
    taxNo: null,
  }

  export default {
    components: {
      NoData,
    },

    data() {
      return {
        financialInfo: null,
        editFinancialInfo: null,
        editing: false,

        examSchoolsFinancialList: [],
        tableColumns: [
          {
            title: '学校名称',
            key: 'schoolName',
            align: 'center',
          },
          {
            title: '开票全称',
            key: 'invoiceFullName',
            align: 'center',
          },
          {
            title: '开票税号',
            key: 'taxNo',
            align: 'center',
          },
          {
            title: '联系人',
            key: 'name',
            align: 'center',
          },
          {
            title: '手机号',
            key: 'mobile',
            align: 'center',
          },
          {
            title: '邮箱',
            key: 'email',
            align: 'center',
          },
        ],

        loadingList: false,
      }
    },

    computed: {
      exam() {
        return this.$store.getters['emarking/exam']
      },
      examId() {
        return this.exam && this.exam.examId
      },
      examAllSchools() {
        return (this.exam && this.exam.schools) || []
      },
      currentUserInfo() {
        return this.$store.getters['user/info']
      },
      currentUserSchoolId() {
        return (this.currentUserInfo && this.currentUserInfo.schoolId) || ''
      },
      showFinancialList() {
        return !(
          this.currentUserSchoolId &&
          this.examAllSchools.length &&
          this.examAllSchools.some(item => item.schoolId === this.currentUserSchoolId)
        )
      },
    },

    created() {
      if (this.showFinancialList) {
        this.fetchExamSchoolFinancialStaffInfos()
      } else {
        this.fetchFinancialInfo()
      }
    },

    methods: {
      fetchExamSchoolFinancialStaffInfos() {
        if (this.loadingList) {
          return
        }

        this.examSchoolsFinancialList = []
        this.loadingList = true
        this.$TransparentSpin.show()
        return apiGetUnionExamSchoolFinancialStaffInfos(this.examId)
          .then(response => (this.examSchoolsFinancialList = response || []))
          .finally(() => {
            this.$TransparentSpin.hide()
            this.loadingList = false
          })
      },

      handleDownloadSchoolFinancialStaffExcelClick() {
        this.$TransparentSpin.show()
        apiDownloadExamSchoolFinancialStaffInfos(this.examId)
          .then(blob => {
            if (blob) {
              downloadBlob(blob, (this.exam.examName ? this.exam.examName + ' —— ' : '') + '学校财务信息.xlsx')
            } else {
              this.$Message.error({
                duration: 4,
                content: '下载文件失败',
              })
            }
          })
          .finally(() => this.$TransparentSpin.hide())
      },

      fetchFinancialInfo() {
        this.financialInfo = null
        return apiGetInstitutionFinancialStaffInfo().then(response => {
          if (response && Object.keys(response).some(key => response[key])) {
            this.financialInfo = response
          }
        })
      },

      createFinancialInfo() {
        this.editFinancialInfo = deepCopy(BlankFinancialInfo)
        this.editing = true
      },

      handleEditFinancialInfo() {
        this.editFinancialInfo = deepCopy(this.financialInfo || BlankFinancialInfo)
        this.editing = true
      },

      handleDeleteStaffInfoClick() {
        this.$Modal.confirm({
          title: '提示',
          content: '确定要删除当前财务信息？',
          onOk: () => apiDeleteExamSchoolFinancialStaffInfo(this.examId).finally(this.fetchFinancialInfo),
        })
      },

      saveFinancialInfo() {
        const InfoWarnings = this.checkFinancialInfoWarning()
        if (InfoWarnings) {
          this.$Message.warning({
            duration: 4,
            content: InfoWarnings,
          })
          return
        }

        this.editFinancialInfo.examId = this.examId
        apiSaveExamSchoolFinancialStaffInfo(this.editFinancialInfo)
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '成功更新财务信息',
            })
          })
          .finally(() => {
            this.fetchFinancialInfo().finally(() => (this.editing = false))
          })
      },

      checkFinancialInfoWarning() {
        if (!this.editFinancialInfo.name) {
          return '请输入财务联系人姓名'
        } else if (!this.editFinancialInfo.mobile) {
          return '请输入财务联系人手机号码'
        } else if (!this.editFinancialInfo.email) {
          return '请输入财务联系人电子邮箱'
        } else if (!this.editFinancialInfo.invoiceFullName) {
          return '请输入开票全称'
        } else if (!this.editFinancialInfo.taxNo) {
          return '请输入税号'
        }

        const PhoneReg = /^1\d{10}$/
        if (!PhoneReg.test(this.editFinancialInfo.mobile)) {
          return '请输入正确的手机号码'
        }

        const EmailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
        if (!EmailReg.test(this.editFinancialInfo.email)) {
          return '请输入正确的电子邮箱'
        }

        return ''
      },
    },
  }
</script>

<template>
  <div class="container-exam-financial">
    <div class="section-title">财务信息</div>

    <template v-if="showFinancialList">
      <div class="download-tool">
        <TextButton type="primary" icon="md-refresh" @click="fetchExamSchoolFinancialStaffInfos">刷新</TextButton>
        <TextButton type="primary" icon="md-download" @click="handleDownloadSchoolFinancialStaffExcelClick"
          >导出Excel</TextButton
        >
      </div>
      <Table :columns="tableColumns" :data="examSchoolsFinancialList"></Table>
    </template>
    <template v-else>
      <div class="btns">
        <template v-if="editing">
          <TextButton type="primary" @click="saveFinancialInfo">保存</TextButton>
          <TextButton type="warning" @click="editing = false">取消</TextButton>
        </template>
        <template v-else-if="financialInfo">
          <TextButton type="primary" @click="handleEditFinancialInfo">编辑</TextButton>
          <TextButton type="error" @click="handleDeleteStaffInfoClick">删除</TextButton>
        </template>
        <TextButton v-else type="primary" icon="md-add" @click="createFinancialInfo">添加财务信息</TextButton>
      </div>

      <div v-if="financialInfo || editing" style="padding-left: 20px">
        <Form :label-width="100" :label-position="'left'">
          <FormItem label="开票全称：">
            <Input v-if="editing" v-model="editFinancialInfo.invoiceFullName" style="width: 500px" />
            <span v-else>{{ financialInfo.invoiceFullName }}</span>
          </FormItem>
          <FormItem label="税号：">
            <Input v-if="editing" v-model="editFinancialInfo.taxNo" style="width: 500px" clearable />
            <span v-else>{{ financialInfo.taxNo }}</span>
          </FormItem>
          <FormItem label="联系人：">
            <Input v-if="editing" v-model="editFinancialInfo.name" style="width: 500px" clearable />
            <span v-else>{{ financialInfo.name }}</span>
          </FormItem>
          <FormItem label="手机号码：">
            <Input v-if="editing" v-model="editFinancialInfo.mobile" style="width: 500px" clearable />
            <span v-else>{{ financialInfo.mobile }}</span>
          </FormItem>
          <FormItem label="电子邮箱：">
            <Input v-if="editing" v-model="editFinancialInfo.email" style="width: 500px" clearable />
            <span v-else>{{ financialInfo.email }}</span>
          </FormItem>
        </Form>
      </div>
      <NoData v-else :show-image="false"></NoData>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .container-exam-financial {
    padding: 20px;
    background-color: white;

    .section-title {
      font-size: 30px;
    }

    .download-tool {
      margin-bottom: 6px;
      text-align: right;
    }

    .btns {
      margin-bottom: 10px;
      text-align: right;
    }
  }
</style>
