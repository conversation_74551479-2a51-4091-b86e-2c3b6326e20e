<template>
  <div class="main-indicators">
    <div v-for="i in indicators" :key="i.name" class="indicator-item" :class="i.cls">
      <img :src="i.iconSrc" class="icon" />
      <div class="text">
        <div class="name">{{ i.name }}</div>
        <div class="number-unit">
          <span class="number">{{ i.number }}</span
          ><span class="unit">{{ i.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import IconMax from '@/assets/images/supplementary_exercise/icon_max.svg'
  import IconAvg from '@/assets/images/supplementary_exercise/icon_avg.svg'
  import IconMin from '@/assets/images/supplementary_exercise/icon_min.svg'
  import IconAboveAvg from '@/assets/images/supplementary_exercise/icon_above_avg.svg'
  import IconBelowAvg from '@/assets/images/supplementary_exercise/icon_below_avg.svg'

  import { roundNumber } from '@/utils/math'

  export default {
    props: {
      classDetail: Object,
    },
    computed: {
      aboveAvgStuCount() {
        return this.classDetail.studentScoreList.filter(stu => stu.score >= this.classDetail.avgScore).length
      },
      belowAvgStuCount() {
        return this.classDetail.studentScoreList.length - this.aboveAvgStuCount
      },
      indicators() {
        return [
          {
            name: '最高分',
            cls: ['max'],
            iconSrc: IconMax,
            number: this.classDetail.maxScore,
            unit: '分',
          },
          {
            name: '平均分',
            cls: ['avg'],
            iconSrc: IconAvg,
            number: roundNumber(this.classDetail.avgScore, 1),
            unit: '分',
          },
          {
            name: '最低分',
            cls: ['min'],
            iconSrc: IconMin,
            number: this.classDetail.minScore,
            unit: '分',
          },
          {
            name: '平均分以上',
            cls: ['above-avg'],
            iconSrc: IconAboveAvg,
            number: this.aboveAvgStuCount,
            unit: '人',
          },
          {
            name: '平均分以下',
            cls: ['below-avg'],
            iconSrc: IconBelowAvg,
            number: this.belowAvgStuCount,
            unit: '人',
          },
        ]
      },
    },
  }
</script>

<style lang="scss" scoped>
  .main-indicators {
    @include flex(row, space-between, center);
  }

  .indicator-item {
    @include flex(row, space-between, center);
    min-width: 205px;
    height: 100px;
    padding: 12px 24px;
    border-radius: 4px;

    .icon {
      width: 57px;
      height: 61px;
      margin-right: 8px;
    }

    .text {
      line-height: 1;
      text-align: right;

      .name {
        font-size: 16px;
      }

      .number-unit {
        margin-top: 12px;

        .number {
          margin-right: 2px;
          font-size: 36px;
        }
      }
    }

    &.max {
      background-color: rgba(5, 193, 174, 0.1);

      .number-unit {
        color: rgba(5, 193, 174, 1);
      }
    }

    &.avg {
      background-color: rgba(91, 104, 255, 0.1);

      .number-unit {
        color: rgba(91, 104, 255, 1);
      }
    }

    &.min {
      background-color: rgba(249, 229, 234, 1);

      .number-unit {
        color: rgba(239, 43, 88, 1);
      }
    }

    &.above-avg {
      background-color: rgba(223, 237, 247, 1);

      .number-unit {
        color: rgba(27, 139, 227, 1);
      }
    }

    &.below-avg {
      background-color: rgba(251, 237, 217, 1);

      .number-unit {
        color: rgba(255, 164, 39, 1);
      }
    }
  }
</style>
