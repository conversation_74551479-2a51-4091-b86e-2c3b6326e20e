<template>
  <div class="student-card">
    <div class="card-header">
      <div class="title">{{ title }}</div>
      <div v-if="showStatus && status" class="status" :style="{ '--status-color': status.color }">
        {{ status.name }}
      </div>
    </div>
    <div class="stu-info">
      <div class="stu-info-row">
        <span class="info-item">考场：{{ student.roomNo }}</span>
        <span class="info-item">座位：{{ student.seatNum }}</span>
        <span v-if="student.isMarkAbsent" class="status warning">[标记缺考]</span>
        <span v-else-if="student.scanUnits.length == 0" class="status warning">[未扫]</span>
      </div>
      <div class="stu-info-row">
        <span class="info-item">{{ student.admissionNum }}</span>
        <span class="info-item">{{ student.studentName }}</span>
        <span class="info-item">{{ student.schoolName }}</span>
      </div>
    </div>
    <div class="scan-unit-info">
      <Checkbox
        v-for="item in scanUnits"
        :key="item.unitId"
        class="scan-unit-item"
        :class="{ deleted: item.isDel }"
        :model-value="selectedScanUnitIds.includes(item.unitId)"
        @on-change="$event => changeSelectScanUnit(item, $event)"
      >
        <span>{{ item.batchNumberText }} 批次，第 {{ item.paperNoText }} 张，第 {{ item.pageNoText }} 面</span>
        <span v-if="item.isDel" class="delete-sign">（已删除）</span>
        <span v-if="item.isMultiFeedingReScan" class="sign-rescan">重扫</span>
      </Checkbox>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      title: String,
      showStatus: Boolean,
      status: Object,
      student: Object,
      scanUnits: Array,
      selectedScanUnitIds: Array,
    },
    emits: ['change-selected-scan-unit-ids'],
    methods: {
      changeSelectScanUnit(unit, value) {
        let newSelectedScanUnitIds = this.selectedScanUnitIds.slice()
        if (newSelectedScanUnitIds.includes(unit.unitId)) {
          if (!value) {
            newSelectedScanUnitIds = newSelectedScanUnitIds.filter(unitId => unitId != unit.unitId)
          }
        } else {
          if (value) {
            newSelectedScanUnitIds.push(unit.unitId)
          }
        }
        // 至少显示一个扫描单元
        if (newSelectedScanUnitIds.length == 0) {
          return
        }
        this.$emit('change-selected-scan-unit-ids', newSelectedScanUnitIds)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .student-card {
    padding: 16px 12px;
    border: 1px solid $color-border;
    border-radius: 4px;
    overflow: hidden;

    .card-header {
      @include flex(row, space-between, center);

      .title {
        font-weight: 600;
      }

      .status {
        @include flex(row, flex-start, center);

        color: var(--status-color);

        &::before {
          width: 8px;
          height: 8px;
          margin-right: 8px;
          border-radius: 50%;
          background-color: var(--status-color);
          content: '';
        }
      }
    }

    .stu-info {
      margin-top: 16px;
      margin-bottom: 8px;

      .stu-info-row {
        &:not(:first-child) {
          margin-top: 4px;
        }

        .info-item {
          &:not(:first-child) {
            margin-left: 8px;
          }
        }

        .status {
          margin-left: 8px;

          &.warning {
            color: $color-warning;
          }
        }
      }
    }

    .scan-unit-info {
      .scan-unit-item {
        font-size: 12px;
        line-height: 1;

        &.deleted {
          color: $color-icon;
        }

        .sign-rescan {
          display: inline-block;
          margin-left: 4px;
          padding: 2px;
          border: 1px solid $color-success;
          border-radius: 2px;
          color: $color-success;
        }
      }
    }
  }
</style>
