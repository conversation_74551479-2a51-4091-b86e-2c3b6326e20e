<template>
  <div class="container-review-config">
    <div class="box-config-bar">
      <div class="config-item">
        <span>设置模式：</span>
        <Select v-model="currentConfigMode" style="width: 120px">
          <Option v-for="item in configModes" :key="item.name" :value="item.name">{{ item.label }}</Option>
        </Select>
        <Select v-if="showCategorySelect" v-model="currentCategory" style="width: 120px" @on-change="onChangeCategory">
          <Option v-for="item in categories" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
      </div>
      <div class="config-item">
        <span>评审次数：</span>
        <el-input-number v-model="reviewCount" :min="1" :max="15" controls-position="right" style="width: 90px" />
        <span style="margin-left: 4px">评</span>
      </div>
      <div class="config-item">
        <span>计分方式：</span>
        <Select v-model="currentScoreMethod" style="width: 180px">
          <Option v-for="item in scoreMethods" :key="item.name" :value="item.name">{{ item.label }}</Option>
        </Select>
      </div>
      <div class="config-item">
        <span>每个评委最大任务量：</span>
        <el-input-number v-model="judgeMaxTask" :max="2000" controls-position="right" style="width: 120px" />
      </div>
      <div class="config-item">
        <Button type="primary" ghost @click="onSubmit">提交</Button>
      </div>
    </div>
    <div class="box-table">
      <Table :data="activityConfigs" :columns="tableColumns"></Table>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { ElInputNumber } from 'element-plus'
  import 'element-plus/es/components/input-number/style/css'
  import { apiSaveReviewConfig } from '@/api/review/config'

  export default {
    components: {
      'el-input-number': ElInputNumber,
    },
    data() {
      return {
        configModes: [
          {
            label: '按类别设置',
            name: 'category',
          },
          {
            label: '统一设置',
            name: 'unification',
          },
        ],
        currentConfigMode: 'category',
        currentCategory: '',
        reviewCount: 5,
        scoreMethods: [
          {
            label: '取平均分',
            name: 'avg',
          },
          {
            label: '去最高最低后取平均分',
            name: 'avg_drop_high_low',
          },
        ],
        currentScoreMethod: 'avg',
        judgeMaxTask: '',
      }
    },
    computed: {
      ...mapGetters('review', ['categories', 'currentActivity']),
      activityConfigs() {
        let configs = this.$store.getters['review/activityConfigs'] || []
        return configs.map(item => {
          const theScoreMethod = this.scoreMethods.find(s => s.name === item.scoreMethod)

          return {
            ...item,
            scoreMethodName: theScoreMethod?.label || '',
          }
        })
      },
      showCategorySelect() {
        return this.currentConfigMode === 'category'
      },
      tableColumns() {
        let columns = [
          {
            title: '类别',
            key: 'categoryName',
          },
          {
            title: '报名审核通过人数',
            key: 'participantCount',
            align: 'center',
          },
          {
            title: '评审次数',
            key: 'reviewCount',
            align: 'center',
          },
          {
            title: '计分方式',
            key: 'scoreMethodName',
          },
          {
            title: '每个评委最大任务量',
            key: 'judgeMaxTask',
            align: 'center',
          },
          {
            title: '至少需要评委人数',
            align: 'center',
            render: (h, params) => {
              let participantCount = params.row.participantCount
              let reviewCount = params.row.reviewCount
              let judgeMaxTask = params.row.judgeMaxTask
              let count =
                judgeMaxTask && participantCount && reviewCount
                  ? Math.ceil((participantCount * reviewCount) / judgeMaxTask)
                  : '-'

              return h('span', {}, count)
            },
          },
        ]

        return columns
      },
    },
    watch: {
      categories: {
        handler: function (newVal) {
          if (Array.isArray(newVal) && newVal.length) {
            this.currentCategory = newVal[0].id
          }
        },
        immediate: true,
      },
      activityConfigs: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            let first = val[0]
            if (
              val.some(
                item =>
                  item.reviewCount !== first.reviewCount ||
                  item.scoreMethod !== first.scoreMethod ||
                  item.judgeMaxTask !== first.judgeMaxTask
              )
            ) {
              this.currentConfigMode = 'category'
            } else {
              this.currentConfigMode = 'unification'
            }

            this.currentCategory = first.categoryId
            this.reviewCount = first.reviewCount
            this.currentScoreMethod = first.scoreMethod
            this.judgeMaxTask = first.judgeMaxTask
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      onSubmit() {
        const { currentActivity, currentCategory, judgeMaxTask, reviewCount, currentScoreMethod, showCategorySelect } =
          this

        if (!reviewCount) {
          this.$Message.info('请填写评审次数')
          return
        }
        if (!currentScoreMethod) {
          this.$Message.info('请选择计分方式')
          return
        }
        if (!judgeMaxTask) {
          this.$Message.info('请填写每个评委最大任务量')
          return
        }

        apiSaveReviewConfig({
          activityId: currentActivity.id,
          categoryId: showCategorySelect ? currentCategory : undefined,
          scoreMethod: currentScoreMethod,
          judgeMaxTask,
          reviewCount,
        }).then(() => {
          this.fetchReviewConfig()
        })
      },
      fetchReviewConfig() {
        this.$store.dispatch('review/fetchReviewConfig')
      },
      onChangeCategory() {
        const theConfig = this.activityConfigs.find(c => c.categoryId === this.currentCategory)
        if (theConfig) {
          this.reviewCount = theConfig.reviewCount
          this.currentScoreMethod = theConfig.scoreMethod
          this.judgeMaxTask = theConfig.judgeMaxTask
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-review-config {
    .box-config-bar {
      @include flex(row, flex-start, center);
      margin-bottom: 20px;
    }

    .config-item {
      &:not(:last-child) {
        margin-right: 30px;
      }
    }
  }
</style>
