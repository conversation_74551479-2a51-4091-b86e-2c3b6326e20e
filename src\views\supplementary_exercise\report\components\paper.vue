<template>
  <div class="container-coach-report-paper">
    <div class="section-scope">
      <div class="label">讲评范围：</div>
      <div class="box-screen">
        <RadioGroup v-model="currentScreenType">
          <div class="inner-screen">
            <div class="screen-item">
              <Radio label="all">所有题目</Radio>
            </div>
            <div class="screen-item">
              <Radio label="wrongCount">答错人数大于</Radio>
              <div class="box-slider">
                <Slider
                  v-model="selectedWrongCount"
                  :disabled="currentScreenType !== 'wrongCount'"
                  :max="questions.length"
                  show-input
                ></Slider>
              </div>
            </div>
            <div class="screen-item">
              <Radio label="highFrequency">高频错题：</Radio>
              <div class="box-wrong-code">{{ highFrequencyQuestionNames }}</div>
            </div>
          </div>
        </RadioGroup>
      </div>
    </div>
    <div v-if="filterQuestions.length" class="section-comment">
      <div class="panel-left">
        <QuestionCard
          v-for="q in filterQuestions"
          :ref="'question_' + q.questionName"
          :key="q.questionName"
          class="question-item"
          :question="q"
          :show-grade-avg-score="false"
          :show-all-branch="true"
          :score-detail-visible="true"
        ></QuestionCard>
      </div>
      <div class="panel-right">
        <div class="title">
          <span>题目列表</span>
        </div>
        <div class="color-description">
          <span class="color-item">
            <span class="spot spot-easy"></span>
            <span>容易</span>
            <span id="color-item-explain">(1.0~0.7)</span>
          </span>
          <span class="color-item">
            <span class="spot spot-normal"></span>
            <span>一般</span>
            <span id="color-item-explain">(0.7~0.4)</span>
          </span>
          <span class="color-item">
            <span class="spot spot-hard"></span>
            <span>困难</span>
            <span id="color-item-explain">(0.4~0)</span>
          </span>
        </div>
        <div class="list">
          <Table
            :data="filterQuestions"
            :columns="tableColumns"
            :max-height="tableMaxHeight"
            @on-row-click="onRowClick"
          >
            <template #scoreRate="{ row }">
              <div class="box-score-rate">
                <component-progress
                  class="progress-item-body"
                  :rate="Math.min(row.classScoreRate, 100)"
                ></component-progress>
                <span v-if="row.wrongStudentsCount" class="text-wrong-count">{{ row.wrongStudentsCount }} 人答错</span
                ><span v-else class="text-wrong-count"> </span>
              </div>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div v-else-if="!loading" class="no-data">暂无数据~</div>
  </div>
</template>

<script>
  import ComponentProgress from './component_progress.vue'
  import QuestionCard from '@/views/report/exam/components/lecture/question_card.vue'

  import { apiGetPractisePaperComment } from '@/api/emarking/practise'
  import { apiGetBindPaperContent } from '@/api/emarking'

  import { initializePaperQuestions } from '@/helpers/report/paper_review'
  import { scrollInto } from '@/utils/scroll'

  export default {
    components: {
      QuestionCard,
      'component-progress': ComponentProgress,
    },
    props: {
      classDetail: {
        type: Object,
        default: null,
      },
      examInfo: {
        type: Object,
        default: null,
      },
      tabQuestionInitialQuestion: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        // 加载中
        loading: false,
        // 讲评范围
        currentScreenType: 'all',
        selectedWrongCount: 3,
        // 试卷内容
        // paper: null,
        // 题目
        questions: [],
        // 其他页面跳转过来要显示的题目
        initQuestion: null,
        tableMaxHeight: window.innerHeight - 330,
      }
    },
    computed: {
      paper() {
        return this.$store.getters['practise/paperContent']
      },
      avgScoreRate() {
        return this.classDetail.avgScore / this.classDetail.fullScore
      },
      // 高频错题：取题目得分率小于科目平均得分率的前5道题
      highFrequencyQuestions() {
        return this.questions
          .filter(item => item.classAvgScore / item.fullScore < this.avgScoreRate)
          .sort((a, b) => {
            return a.classAvgScore / a.fullScore - b.classAvgScore / b.fullScore
          })
          .slice(0, 5)
          .sort((a, b) => a.questionCode - b.questionCode)
      },
      highFrequencyQuestionNames() {
        if (this.highFrequencyQuestions.length == 0) {
          return '无'
        }
        return this.highFrequencyQuestions
          .map(item => {
            if (item.questionName == item.questionCode) {
              return `第 ${item.questionName} 题`
            } else {
              return item.questionName
            }
          })
          .join('、')
      },
      filterQuestions() {
        const { currentScreenType } = this
        let questions = this.questions.map(item => {
          let wrongStudents = item.students.filter(s => s.score !== item.fullScore)
          return {
            ...item,
            wrongStudentsCount: wrongStudents.length,
          }
        })

        if (currentScreenType === 'all') {
          return questions
        } else if (currentScreenType === 'wrongCount') {
          return questions.filter(item => item.wrongStudentsCount >= this.selectedWrongCount)
        } else {
          return this.highFrequencyQuestions
        }
      },
      tableColumns() {
        return [
          {
            title: '题号',
            key: 'questionName',
            width: 60,
            align: 'center',
          },
          {
            title: '得分率',
            slot: 'scoreRate',
            align: 'center',
          },
        ]
      },
    },
    watch: {
      classDetail() {
        this.fetchData()
      },
    },
    mounted() {
      this.initQuestion = this.tabQuestionInitialQuestion
      this.fetchData()
    },
    activated() {
      this.initQuestion = this.tabQuestionInitialQuestion
      this.$nextTick().then(() => {
        if (!this.loading) {
          this.scrollToInitQuestion()
        }
      })
    },
    methods: {
      onRowClick(row) {
        this.scrollToQuestion(row)
      },
      customColorMethod(percentage) {
        if (percentage > 0 && percentage < 100) {
          return '#37cdbe'
        } else {
          return '#19be6b'
        }
      },
      async fetchData() {
        if (this.loading) {
          return
        }
        try {
          this.loading = true
          this.$TransparentSpin.show()
          // await this.fetchPaperContent()
          let reportQuestions = await this.fetchClassQuestions()
          let questions = initializePaperQuestions(reportQuestions, this.paper)
          questions.forEach(q => {
            q.wrongStudentsCount = q.students.filter(s => s.score < q.fullScore).length
          })
          this.questions = questions
        } catch (ex) {
          this.questions = []
          throw ex
        } finally {
          this.loading = false
          this.$TransparentSpin.hide()
        }
        this.scrollToInitQuestion()
      },
      fetchPaperContent() {
        if (this.paper) {
          return Promise.resolve()
        }
        return apiGetBindPaperContent({
          examId: this.examInfo.examId,
          examSubjectId: this.examInfo.examSubjectId,
        })
          .then(paper => {
            this.paper = paper
          })
          .catch(ex => {
            this.paper = null
            throw ex
          })
      },
      fetchClassQuestions() {
        return apiGetPractisePaperComment({
          examSubjectId: this.examInfo.examSubjectId,
          classId: this.classDetail.classId,
        }).then(data => {
          return data.questions
        })
      },
      scrollToInitQuestion() {
        setTimeout(() => {
          if (!this.initQuestion) {
            return
          }
          this.scrollToQuestion(this.initQuestion)
          this.initQuestion = null
        }, 0)
      },
      scrollToQuestion(q) {
        let el = this.$refs['question_' + q.questionName]
        el = el && el[0] && el[0].$el
        if (!el) {
          return
        }

        scrollInto(el, -60)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-coach-report-paper {
    .section-scope {
      @include flex(row, flex-start, center);
      margin-bottom: 16px;
      padding: 20px;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);

      .label {
        margin-right: 20px;
      }

      .inner-screen {
        @include flex(row, flex-start, center);
      }

      .screen-item {
        @include flex(row, flex-start, center);
        margin-right: 40px;
      }

      .box-slider {
        width: 400px;
      }
    }

    .box-score-rate {
      @include flex(row, flex-start, center);
    }

    .progress-item-body {
      flex-grow: 1;
      max-width: 116px;
    }

    .text-wrong-count {
      min-width: 47px;
      margin-left: 8px;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .section-comment {
    @include flex(row, flex-start, flex-start);
    padding-top: 10px;
  }

  .panel-left {
    flex-grow: 1;
    flex-shrink: 1;

    .question-item {
      margin-bottom: 10px;
    }
  }

  .panel-right {
    position: sticky;
    top: 76px;
    flex-grow: 0;
    flex-shrink: 0;
    width: 300px;
    max-height: 400px;
    max-height: calc(100vh - 100px);
    margin-left: 15px;
    padding: 20px;
    background-color: white;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);

    .title {
      @include flex(row, space-between, center);
      height: 20px;
      font-weight: bold;
      font-size: $font-size-medium-x;
      line-height: 20px;

      .icon-full-screen {
        font-weight: bold;
        font-size: $font-size-large;

        &:hover {
          cursor: pointer;
        }
      }
    }

    .color-description {
      margin-top: 8px;
      margin-bottom: 12px;

      .color-item {
        display: block;
        height: 20px;
        margin-right: 10px;
        line-height: 20px;

        &:not(:last-child) {
          margin-bottom: 4px;
        }

        .spot {
          display: inline-block;
          width: 10px;
          height: 10px;
          vertical-align: middle;
        }

        .spot-easy {
          background-color: $color-success;
        }

        .spot-normal {
          background-color: #2db7f5;
        }

        .spot-hard {
          background-color: $color-error;
        }
      }
    }

    .question-name-item {
      display: inline-block;
      min-width: 30px;
      height: 30px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding-right: 6px;
      padding-left: 6px;
      border: 1px solid $color-primary;
      border-radius: 3px;
      line-height: 28px;
      text-align: center;
      cursor: pointer;
      user-select: none;

      &[data-difficulty='easy'] {
        border: 1px solid $color-success;
        color: $color-success;
      }

      &[data-difficulty='normal'] {
        border: 1px solid #2db7f5;
        color: #2db7f5;
      }

      &[data-difficulty='hard'] {
        border: 1px solid $color-error;
        color: $color-error;
      }
    }
  }

  #color-item-explain {
    color: $color-second-title;
    font-size: $font-size-small;
  }

  .no-data {
    @include flex(row, center, center);
    width: 100%;
    height: 70px;
    color: #999;
    font-size: 14px;
    text-align: center;
    background-color: #fff;
  }
</style>
