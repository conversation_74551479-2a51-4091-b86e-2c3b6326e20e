<template>
  <div class="container-score-result">
    <div class="section-body">
      <div class="box-creation">
        <div class="box-paper-top">
          <span class="number">{{ judgeReviewDetail.score + ' 分' }}</span>
          <span
            >（{{ judgeReviewDetail.realName }}，第{{ judgeReviewDetail.judgeAssignOrder }}份，满分
            {{ fullScore }} 分）</span
          >
        </div>

        <div class="box-creation-name">
          <div class="title">作品名称：</div>
          <div class="creation-name">{{ creationName }}</div>
        </div>
        <div class="box-creation-file">
          <div class="title">作品文件：</div>
          <div class="file">
            <div v-for="item in creationFile" :key="item.fileName" class="file-item">
              <object
                v-if="item.fileType === 'pdf'"
                class="pdf-viewer-object"
                :data="item.filePath"
                type="application/pdf"
                width="100%"
                height="800px"
              >
                <p>您的浏览器不支持查看PDF文件，请<a :href="item.filePath">下载PDF文件</a>查看。</p>
              </object>
              <img v-else-if="item.fileType === 'image'" class="image" :src="item.filePath" />
            </div>
          </div>
        </div>
      </div>

      <div v-if="isMultipleScore" class="container-score-panel">
        <template v-if="scoreItems && scoreItems.length > 1">
          <div v-if="groupScoreItems?.length" class="box-score-items">
            <div v-for="g in groupScoreItems" :key="g.groupName" class="score-items-group">
              <div class="header">{{ g.groupName }}</div>
              <div class="body">
                <div v-for="item in g.list" :key="item.id" class="score-item">
                  <div class="box-item-info">
                    <span class="item-score">({{ item.fullScore }}分)</span>
                    <span class="item-name">{{ item.name }}：</span>
                    <span>{{ item.description }}</span>
                  </div>
                  <div class="box-item-score-input">
                    <div class="item-score-input">
                      <span class="label">给分</span>
                      <InputNumber
                        v-model="item.score"
                        style="width: 150px"
                        :placeholder="'满分' + item.fullScore + '分'"
                        :min="0"
                        :max="item.fullScore"
                        readonly
                      ></InputNumber>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- <template v-else>
          <div class="box-score-items">
            <div class="score-items-group">
              <div class="header">满分：{{ taskDetail.fullScore }} 分</div>
              <div class="body">
                <div class="score-item">
                  <div class="box-item-score-input">
                    <div class="item-score-input">
                      <span class="label">给分</span>
                      <InputNumber
                        v-model="currentSingleScore"
                        :placeholder="'满分' + singleFullScore + '分'"
                        :min="0"
                        :max="singleFullScore"
                        readonly
                        style="width: 150px"
                      ></InputNumber>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template> -->
      </div>
    </div>
  </div>
</template>

<script>
  import { groupArray } from '@/utils/array'

  export default {
    props: {
      taskDetail: Object,
      judgeReviewDetail: Object,
      scoreItems: Array,
      isMultipleScore: Boolean,
      fullScore: Number,
    },
    data() {
      return {
        currentSingleScore: null,
        singleFullScore: null,
        currentComment: '',
        groupScoreItems: [],
      }
    },
    computed: {
      creationName() {
        const { judgeReviewDetail } = this
        if (!judgeReviewDetail || !judgeReviewDetail.formDataList || !judgeReviewDetail.formDataList.length) {
          return ''
        }

        const theFormData = judgeReviewDetail.formDataList.find(item => item.fieldLabel === '作品名称')
        return theFormData && theFormData.fieldValue
      },
      creationFile() {
        const { judgeReviewDetail } = this
        if (!judgeReviewDetail || !judgeReviewDetail.formDataList || !judgeReviewDetail.formDataList.length) {
          return null
        }

        let file = null
        const theFormData = judgeReviewDetail.formDataList.find(item => item.fieldLabel === '作品上传')

        try {
          file = JSON.parse(theFormData.fieldValue)
          if (Array.isArray(file)) {
            file.forEach(item => {
              item.fileType = ['.jpg', 'jpeg', '.png', '.gif'].some(extension => item.filePath.endsWith(extension))
                ? 'image'
                : item.filePath.endsWith('pdf')
                  ? 'pdf'
                  : ''
            })
          }
        } catch (error) {}

        return file
      },
    },
    watch: {
      scoreItems: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            if (val.length > 1) {
              this.groupScoreItems = groupArray(val || [], k => k.groupName).map(g => ({
                groupName: g.key,
                list: g.group.map(s => {
                  const theScoreItem = (val.scoreItems || []).find(item => item.scoreItemId === s.id)

                  return {
                    ...s,
                    score: theScoreItem && theScoreItem.score !== null ? theScoreItem.score : null,
                  }
                }),
              }))
              this.currentComment = val.comment
            }
          }
        },
        immediate: true,
        deep: true,
      },
      judgeReviewDetail: {
        handler: function (val) {
          if (Array.isArray(this.scoreItems) && this.scoreItems.length) {
            if (this.scoreItems.length > 1) {
              this.groupScoreItems = groupArray(this.scoreItems || [], k => k.groupName).map(g => ({
                groupName: g.key,
                list: g.group.map(s => {
                  const theScoreItem = (val.scoreItems || []).find(item => item.scoreItemId === s.id)

                  return {
                    ...s,
                    score: theScoreItem && theScoreItem.score !== null ? theScoreItem.score : null,
                  }
                }),
              }))
              this.currentComment = val.comment
            }
          }
        },
        immediate: true,
        deep: true,
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-score-result {
    width: 100%;
  }

  .section-body {
    @include flex(row, flex-start, flex-start);
    position: relative;
    width: 100%;
    // height: calc(100vh - 60px);
    // overflow: auto;

    .message-box {
      padding-top: 100px;
      font-weight: bold;
      font-size: 30px;
      text-align: center;
    }

    .box-creation {
      position: relative;
      top: 0;
      left: 0;
      flex: 1;
      transform-origin: 0 0;

      .box-creation-name {
        padding: 0 20px 15px;
        background-color: #fff;
      }

      .title {
        display: inline-block;
        font-size: 16px;
      }

      .creation-name {
        display: inline-block;
        font-size: 16px;
      }

      .box-file-item-name {
        margin-bottom: 10px;
      }

      .file-name {
        font-size: 16px;
      }

      .file-item {
        &:not(:last-child) {
          margin-bottom: 20px;
        }

        .image {
          width: 100%;
        }
      }

      .box-creation-file {
        padding: 0 20px;
        background-color: #fff;

        .title {
          margin-bottom: 4px;
        }
      }
    }

    .box-paper-top {
      margin-bottom: 10px;
      padding-left: 20px;
      color: red;
      font-size: $font-size-medium-x;

      .box-action {
        width: 300px;
        white-space: nowrap;
        text-align: right;
      }

      .number {
        font-weight: bold;
        font-size: $font-size-large;
      }
    }

    .container-score-panel {
      position: sticky;
      top: 0;
      display: flex;
      flex-direction: column;
      width: 350px;
      max-height: calc(100vh - 95px);
      background-color: #fff;

      &.score-panel-moving {
        box-shadow: 0 0 10px #aaa;
      }

      .movable-handler {
        padding: 4px 10px;
        color: $color-icon;
        line-height: 24px;
        text-align: right;
        cursor: grab;
      }

      .box-score-items {
        flex: 1;
        box-sizing: border-box;
        padding: 0 15px;
        overflow: auto;
      }

      .box-score-items-has-comment {
        padding-bottom: 0;
      }

      .score-items-group {
        &:not(:last-child) {
          margin-bottom: 15px;
        }

        .header {
          padding: 0 0 10px;
          font-weight: 600;
          font-size: 16px;
        }

        .score-item {
          &:not(:last-child) {
            margin-bottom: 10px;
          }
        }

        .item-info {
          margin-bottom: 10px;
          line-height: 1.5;
        }

        .item-score {
          color: red;
        }

        .item-name {
          font-weight: 600;
        }

        .box-item-info {
          margin-bottom: 10px;
        }
      }

      .item-score-input {
        @include flex(row, flex-start, flex-start);

        &:not(:last-child) {
          margin-bottom: 15px;
        }

        .label {
          margin-right: 12px;
          line-height: 32px;
        }

        .total-score {
          font-weight: 600;
          line-height: 32px;
        }
      }

      .input-comment {
        flex: 1;
      }

      .box-btn-submit {
        @include flex(row, center, center);
        margin-top: 20px;

        .btn-submit {
          width: 100%;
        }
      }

      .fixed-area {
        box-sizing: border-box;
        padding: 15px 15px 20px;
        background-color: #fff;
      }

      .box-total-score {
        @include flex(row, flex-start, center);
        margin-bottom: 10px;
        font-size: 14px;

        .label {
          margin-right: 12px;
        }

        .total-score {
          font-weight: 600;
        }
      }
    }
  }
</style>
