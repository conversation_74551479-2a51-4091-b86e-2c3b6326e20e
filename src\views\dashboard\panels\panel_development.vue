<template>
  <div class="panel-development">
    <div class="section-title">发展分析</div>

    <div class="section-selector">
      <Select
        class="selector-grade selector-scaled"
        :model-value="selectedGradeId"
        size="small"
        @on-change="changeGrade"
      >
        <Option v-for="item in gradeList" :key="item.gradeId" :value="item.gradeId">{{ item.gradeName }}</Option>
      </Select>
      <Select
        v-if="categoryList.length > 0"
        class="selector-category selector-scaled"
        :model-value="selectedCategoryId"
        size="small"
        @on-change="changeCategory"
      >
        <Option v-for="item in categoryList" :key="item.categoryId" :value="item.categoryId">{{
          item.categoryName
        }}</Option>
      </Select>
      <Select
        class="selector-subject selector-scaled"
        :model-value="selectedSubjectId"
        size="small"
        @on-change="changeSubject"
      >
        <Option v-for="item in subjectList" :key="item.subjectId" :value="item.subjectId">{{
          item.subjectName
        }}</Option>
      </Select>
    </div>

    <div v-if="selectedCategorySubjectExams.length == 0" class="empty-tips">暂无数据</div>

    <div v-show="selectedCategorySubjectExams.length > 0" ref="chartTrendRef" class="section-chart-trend"></div>

    <div v-if="compareBaseExam || compareTargetExam" class="section-compare-exam">
      <div class="label">对比考试</div>
      <div class="exam-names">
        <div v-if="compareBaseExam" class="exam-name base">{{ compareBaseExam.examName }}</div>
        <div v-if="compareTargetExam" class="exam-name target">{{ compareTargetExam.examName }}</div>
      </div>
    </div>

    <div v-show="subjectCompareData.length" ref="chartSubjectRef" class="section-chart-subject"></div>

    <div v-show="childrenCompareData.length" ref="chartChildrenRef" class="section-chart-children"></div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onBeforeMount, onMounted, onBeforeUnmount, useTemplateRef, nextTick } from 'vue'

  import { useDashboardStore } from '@/store/dashboard'
  import store from '@/store'

  import { apiGetMySchoolGradeSubjects } from '@/api/user/school'
  import { apiGetDevelopAnalysisEduSchool, apiGetDevelopAnalysisSchool } from '@/api/report/tem'

  import echarts from '@/utils/echarts'
  import { groupArray } from '@/utils/array'
  import { formatDateTime } from '@/utils/date'
  import { roundNumber } from '@/utils/math'
  import { throttle, debounce } from '@/utils/function'
  import SchoolTypeEnum from '@/enum/school/school_type'
  import { UUID_ZERO } from '@/const/string'

  const dashboardStore = useDashboardStore()
  const chartTrendRef = useTemplateRef('chartTrendRef')
  const chartSubjectRef = useTemplateRef('chartSubjectRef')
  const chartChildrenRef = useTemplateRef('chartChildrenRef')

  // 年级科目平铺列表
  const gradeSubjects = ref([])
  // 选中年级Id
  const selectedGradeId = ref(0)
  // 选中类别Id
  const selectedCategoryId = ref(0)
  // 选中科目Id
  const selectedSubjectId = ref(0)
  // 历次考试数据（选定年级，含各类别科目）
  const gradeExams = ref([])
  // 加载中
  const loading = ref(false)
  // 对比的两次考试
  const compareBaseExamId = ref('')
  const compareTargetExamId = ref('')
  // echarts实例
  let chartTrend = null
  let chartSubject = null
  let chartChildren = null
  // 监听图表容器大小变化
  const chartTrendObserver = new ResizeObserver(
    throttle(() => {
      if (chartTrend && chartTrendRef.value?.clientHeight > 0) {
        chartTrend.resize()
      }
    }, 100)
  )
  const chartSubjectObserver = new ResizeObserver(
    throttle(() => {
      if (chartSubject && chartSubjectRef.value?.clientHeight > 0) {
        chartSubject.resize()
      }
    }, 100)
  )
  const chartChildrenObserver = new ResizeObserver(
    throttle(() => {
      if (chartChildren && chartChildrenRef.value?.clientHeight > 0) {
        chartChildren.resize()
      }
    }, 100)
  )

  // 年级列表
  const gradeList = computed(() => {
    return groupArray(gradeSubjects.value, g => g.gradeId)
      .map(g => {
        let subjects = g.group
          .map(s => ({
            subjectId: s.subjectId,
            subjectName: s.subjectName,
          }))
          .sort((a, b) => a.subjectId - b.subjectId)
        subjects.unshift({
          subjectId: 0,
          subjectName: '全科',
        })
        return {
          gradeId: g.key,
          gradeName: g.group[0].gradeName,
          gradeLevel: g.group[0].gradeLevel,
          subjects,
        }
      })
      .sort((a, b) => a.gradeId - b.gradeId)
  })
  // 选中年级
  const selectedGrade = computed(() => {
    return gradeList.value.find(g => g.gradeId == selectedGradeId.value) || null
  })
  // 类别列表
  const categoryList = computed(() => {
    // 仅高中学段分物理类历史类
    if (selectedGrade.value?.gradeLevel != 4) {
      return []
    }
    return [
      {
        categoryId: 0,
        categoryName: '不分类',
      },
      {
        categoryId: 1,
        categoryName: '历史类',
      },
      {
        categoryId: 2,
        categoryName: '物理类',
      },
    ]
  })
  // 科目列表
  const subjectList = computed(() => {
    if (!selectedGrade.value) {
      return []
    }
    let subjects = selectedGrade.value.subjects
    if (categoryList.value.length > 0) {
      // 物理类不显示历史科，历史类不显示物理科
      let selectedCategoryName = categoryList.value.find(c => c.categoryId == selectedCategoryId.value)?.categoryName
      if (selectedCategoryName == '历史类') {
        subjects = subjects.filter(s => s.subjectName != '物理')
      } else if (selectedCategoryName == '物理类') {
        subjects = subjects.filter(s => s.subjectName != '历史')
      }
    }
    return subjects
  })
  // 查询历次考试数据参数
  const queryExamTrendParams = computed(() => {
    // TODO 全体时store中schoolId设为所在机构Id
    let school = dashboardStore.currentSchool
    if (school != null && school.schoolId == 0) {
      let userInfo = store.getters['user/info']
      school = {
        schoolId: userInfo.schoolId,
        schoolName: userInfo.schoolName,
        schoolType: userInfo.schoolType,
      }
    }
    return {
      semesterId: dashboardStore.currentSemester,
      term: dashboardStore.currentTerm,
      gradeId: selectedGradeId.value,
      schoolId: school?.schoolId,
      schoolType: school?.schoolType,
    }
  })
  // 查看学校是否为教育局
  const isEduSchool = computed(() => {
    return queryExamTrendParams.value.schoolType == SchoolTypeEnum.Bureau.id
  })
  // 选定类别科目历次考试数据
  const selectedCategorySubjectExams = computed(() => {
    let exams = []
    gradeExams.value.forEach(exam => {
      let category = exam.subjectCategoryList?.find(c => c.categoryId == selectedCategoryId.value)
      if (!category) {
        return
      }
      let subject = category.subjectList?.find(s => s.subjectId == selectedSubjectId.value)
      if (!subject) {
        return
      }
      exams.push({
        examId: exam.examId,
        examName: exam.examName,
        beginTime: exam.beginTime,
        categoryId: category.categoryId,
        categoryName: category.categoryName,
        subjectId: subject.subjectId,
        subjectName: subject.subjectName,
        school: subject.school,
        children: subject.children,
        subjectList: category.subjectList.map(s => ({
          subjectId: s.subjectId,
          subjectName: s.subjectName,
          value: s.school?.value,
        })),
      })
    })
    return exams
  })
  // 对比基础考试
  const compareBaseExam = computed(() => {
    return selectedCategorySubjectExams.value.find(exam => exam.examId == compareBaseExamId.value)
  })
  // 对比目标考试
  const compareTargetExam = computed(() => {
    return selectedCategorySubjectExams.value.find(exam => exam.examId == compareTargetExamId.value)
  })
  // 趋势图配置
  const chartTrendOptions = computed(() => {
    let exams = selectedCategorySubjectExams.value
    return {
      grid: {
        left: 40,
        right: 16,
        top: 30,
        bottom: 24,
      },
      xAxis: {
        type: 'category',
        data: exams.map(exam => formatDateTime(exam.beginTime, 'YYYY-MM-DD')),
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#FFFFFF',
        },
      },
      yAxis: {
        type: 'value',
        name: '平均分',
        nameTextStyle: {
          color: '#7db7db',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#3C4061',
          },
        },
        axisLabel: {
          color: '#FFFFFF',
        },
      },
      series: [
        {
          data: exams.map(exam => {
            let itemData = {
              // TODO 改为百分比
              value: exam.school.value,
              symbolSize: 8,
              symbol: 'circle',
              extraData: exam,
            }
            let isBase = exam.examId == compareBaseExamId.value
            let isTarget = exam.examId == compareTargetExamId.value
            if (isBase || isTarget) {
              itemData.itemStyle = {
                color: '#ff0000',
              }
              itemData.symbol = 'rect'
            } else {
              itemData.itemStyle = {
                color: '#54a0ff',
              }
            }
            return itemData
          }),
          type: 'line',
        },
      ],
      tooltip: {
        backgroundColor: 'white',
        textStyle: {
          color: '#000',
        },
        formatter: params => {
          const exam = params.data.extraData
          return `<div style="font-weight: bold;margin-bottom:4px">${exam.examName}</div>平均分：${roundNumber(exam.school.value, 2)}`
        },
        confine: true,
      },
    }
  })
  // 学科对比数据
  const subjectCompareData = computed(() => {
    // 仅全科才显示
    if (selectedSubjectId.value != 0) {
      return []
    }
    let baseExamSubjects = (compareBaseExam.value?.subjectList || []).filter(s => s.subjectId != 0)
    let targetExamSubjects = (compareTargetExam.value?.subjectList || []).filter(s => s.subjectId != 0)
    return targetExamSubjects
      .map(s => {
        let target = s.value
        let base = baseExamSubjects.find(x => x.subjectId == s.subjectId)?.value
        let progress = base == undefined ? undefined : target - base
        return {
          subjectId: s.subjectId,
          subjectName: s.subjectName,
          base,
          target,
          progress,
        }
      })
      .sort((a, b) => a.subjectId - b.subjectId)
  })
  // 学科对比图配置
  const chartSubjectOptions = computed(() => {
    return {
      grid: {
        left: 40,
        right: 16,
        top: 30,
        bottom: 24,
      },
      xAxis: {
        type: 'category',
        data: subjectCompareData.value.map(s => {
          if (s.subjectName == '道德与法治') {
            return '道法'
          }
          return s.subjectName
        }),
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#FFFFFF',
        },
      },
      yAxis: {
        type: 'value',
        name: '平均分',
        nameTextStyle: {
          color: '#7db7db',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#3C4061',
          },
        },
        axisLabel: {
          color: '#FFFFFF',
        },
      },
      series: [
        {
          type: 'bar',
          data: subjectCompareData.value.map(s => {
            return {
              value: s.target,
              extraData: s,
            }
          }),
          barWidth: '40%',
          barMaxWidth: 20,
          z: 1,
          itemStyle: { color: '#05d1bc' },
        },
        {
          type: 'bar',
          data: subjectCompareData.value.map(s => ({
            value: s.progress,
            extraData: s,
          })),
          barWidth: '40%',
          barMaxWidth: 20,
          // 重叠并在上层
          z: 2,
          barGap: '-100%',
          itemStyle: {
            color: function (params) {
              return params.value > 0 ? '#27AE60' : '#E74C3C'
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'white',
        textStyle: {
          color: '#000',
        },
        formatter: params => {
          const s = params[0]?.data?.extraData
          if (!s) {
            return ''
          }
          let text = `<div style="margin-bottom: 4px;font-weight: bold;">${s.subjectName}平均分</div>`
          if (s.base != undefined) {
            text += `前次：${roundNumber(s.base, 2)}<br />`
          }
          if (s.target != undefined) {
            text += `后次：${roundNumber(s.target, 2)}<br />`
          }
          if (s.progress != undefined) {
            let progress = roundNumber(s.progress, 2)
            if (progress > 0) {
              text += `提升：${progress}`
            } else if (progress < 0) {
              text += `下降：${progress}`
            } else if (progress == 0) {
              text += `持平`
            }
          }
          return text
        },
      },
    }
  })
  // 下级（学校/班级）对比数据
  const childrenCompareData = computed(() => {
    let baseExamChildren = compareBaseExam.value?.children || []
    let targetExamChildren = compareTargetExam.value?.children || []
    return targetExamChildren.map(child => {
      let target = child.value
      let base = baseExamChildren.find(x => x.id == child.id)?.value
      let progress = base == undefined ? undefined : target - base
      return {
        id: child.id,
        name: child.name,
        base,
        target,
        progress,
      }
    })
  })
  // 下级（学校/班级）对比图配置
  const chartChildrenOptions = computed(() => {
    return {
      grid: {
        left: 92,
        right: 24,
        top: 32,
        bottom: 16,
      },
      xAxis: {
        type: 'value',
        splitLine: { show: true, lineStyle: { color: '#3C4061' } },
        position: 'top',
        axisLabel: {
          color: '#FFFFFF',
        },
      },
      yAxis: {
        type: 'category',
        data: childrenCompareData.value.map(c => c.name),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { color: '#fff', fontSize: 12 },
        inverse: true,
      },
      dataZoom: [
        // 滑动条
        {
          type: 'slider',
          show: true,
          yAxisIndex: 0,
          width: 16,
          right: '2%',
          startValue: 0,
          endValue: 9,
          // 滑动时坐标轴范围不变
          filterMode: 'empty',
          showDetail: false,
        },
        // 内置，支持滚轮缩放
        {
          type: 'inside',
          yAxisIndex: 0,
          startValue: 0,
          endValue: 9,
          filterMode: 'empty',
          zoomOnMouseWheel: false,
          moveOnMouseWheel: true,
          moveOnMouseMove: true,
        },
      ],
      series: [
        {
          type: 'bar',
          data: childrenCompareData.value.map(c => ({
            value: c.target,
            extraData: c,
          })),
          barWidth: '40%',
          barMaxWidth: 20,
          z: 1,
          itemStyle: { color: '#05d1bc' },
        },
        {
          type: 'bar',
          data: childrenCompareData.value.map(c => ({
            value: c.progress,
            extraData: c,
          })),
          barWidth: '40%',
          barMaxWidth: 20,
          // 重叠并在上层
          z: 2,
          barGap: '-100%',
          itemStyle: {
            // barBorderRadius: function (params) {
            //   return params.value > 0 ? [0, 20, 20, 0] : [20, 0, 0, 20]
            // },
            color: function (params) {
              return params.value > 0 ? '#27AE60' : '#E74C3C'
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: 'white',
        textStyle: {
          color: '#000',
        },
        formatter: params => {
          const data = params[0]?.data?.extraData
          if (!data) {
            return ''
          }
          let text = `<div style="margin-bottom: 4px;font-weight: bold;">${data.name}</div>`
          if (data.base != undefined) {
            text += `前次平均分：${roundNumber(data.base, 2)}<br />`
          }
          if (data.target != undefined) {
            text += `后次平均分：${roundNumber(data.target, 2)}<br />`
          }
          if (data.progress != undefined) {
            let progress = roundNumber(data.progress, 2)
            if (progress > 0) {
              text += `平均分提升：${progress}`
            } else if (progress < 0) {
              text += `平均分下降：${progress}`
            } else if (progress == 0) {
              text += `平均分持平`
            }
          }
          return text
        },
      },
    }
  })

  // 学年学期变化时重新加载考试数据
  watch(queryExamTrendParams, debounce(loadExams, 10))
  // 选定类别科目历次考试数据变化时重新设置对比考试
  watch(selectedCategorySubjectExams, setCompareExam)
  // echarts配置变化时重绘
  watch(chartTrendOptions, () => {
    nextTick(() => {
      if (chartTrend) {
        chartTrend.setOption(chartTrendOptions.value)
      }
    })
  })
  watch(chartSubjectOptions, () => {
    nextTick(() => {
      if (chartSubject) {
        chartSubject.setOption(chartSubjectOptions.value)
      }
    })
  })
  watch(chartChildrenOptions, () => {
    nextTick(() => {
      if (chartChildren) {
        chartChildren.setOption(chartChildrenOptions.value)
      }
    })
  })
  // 显示/隐藏加载动画
  watch(loading, () => {
    let charts = [chartTrend, chartSubject, chartChildren]
    nextTick(() => {
      if (loading.value) {
        charts.forEach(chart => {
          chart.showLoading({
            text: '',
            maskColor: 'transparent',
            zlevel: 100,
            spinnerRadius: 20,
            lineWidth: 4,
          })
        })
      } else {
        charts.forEach(chart => {
          chart.hideLoading()
        })
      }
    })
  })

  // 组件创建时加载数据
  onBeforeMount(async () => {
    await loadGradeSubjects()
    changeGrade()
  })
  // 组件挂载时创建echarts实例并监听容器大小变化
  onMounted(() => {
    initChart()
    chartTrendObserver.observe(chartTrendRef.value)
    chartSubjectObserver.observe(chartSubjectRef.value)
    chartChildrenObserver.observe(chartChildrenRef.value)
  })
  // 组件卸载时销毁echarts实例并解除容器大小变化监听
  onBeforeUnmount(() => {
    ;[chartTrend, chartSubject, chartChildren].forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    ;[chartTrendObserver, chartSubjectObserver, chartChildrenObserver].forEach(observer => {
      if (observer) {
        observer.disconnect()
      }
    })
  })

  // 加载年级科目
  async function loadGradeSubjects() {
    gradeSubjects.value = await apiGetMySchoolGradeSubjects()
  }
  // 年级变化
  function changeGrade(value) {
    if (value === undefined) {
      value = selectedGradeId.value
    }
    let grade = gradeList.value.find(g => g.gradeId == value)
    if (!grade && gradeList.value.length > 0) {
      grade = gradeList.value[0]
    }
    selectedGradeId.value = grade ? grade.gradeId : 0
    changeCategory()
  }
  // 类别变化
  function changeCategory(value) {
    if (value === undefined) {
      value = selectedCategoryId.value
    }
    let category = categoryList.value.find(c => c.categoryId == value)
    if (!category && categoryList.value.length > 0) {
      category = categoryList.value[0]
    }
    selectedCategoryId.value = category ? category.categoryId : 0
    changeSubject()
  }
  // 科目变化
  function changeSubject(value) {
    if (value === undefined) {
      value = selectedSubjectId.value
    }
    let subject = subjectList.value.find(s => s.subjectId == value)
    if (!subject && subjectList.value.length > 0) {
      subject = subjectList.value[0]
    }
    selectedSubjectId.value = subject ? subject.subjectId : 0
  }
  // 加载考试数据
  async function loadExams() {
    let params = { ...queryExamTrendParams.value }
    if (!params.semesterId || !params.gradeId || !params.schoolId) {
      gradeExams.value = []
      return
    }

    let res
    loading.value = true
    try {
      if (isEduSchool.value) {
        res = await apiGetDevelopAnalysisEduSchool(params)
      } else {
        res = await apiGetDevelopAnalysisSchool(params)
      }
    } catch (err) {
      res = []
      throw err
    } finally {
      // 请求过程中参数变化则忽略结果
      if (Object.keys(queryExamTrendParams.value).every(key => params[key] == queryExamTrendParams.value[key])) {
        loading.value = false
        res.forEach(exam => {
          exam.beginTime = new Date(exam.beginTime)
          exam.subjectCategoryList?.forEach(category => {
            category.subjectList?.forEach(subject => {
              // 找本机构/学校，下属学校/班级按平均分倒序排
              let children
              let school
              if (isEduSchool.value) {
                children = (subject.schoolList || []).map(x => ({
                  id: x.schoolId,
                  name: x.schoolName,
                  value: x.value,
                }))
                school = children.find(x => x.id == params.schoolId)
                children = children.filter(x => x.id != params.schoolId)
              } else {
                children = (subject.classList || []).map(x => ({
                  id: x.classId,
                  name: x.className,
                  value: x.value,
                }))
                school = children.find(c => c.id == UUID_ZERO)
                children = children.filter(c => c.id != UUID_ZERO)
              }
              children.sort((a, b) => b.value - a.value)
              children.forEach((x, idx) => {
                if (idx > 0 && x.value == children[idx - 1].value) {
                  x.rank = children[idx - 1].rank
                } else {
                  x.rank = idx + 1
                }
              })
              subject.school = school
              subject.children = children
            })
          })
        })
        gradeExams.value = res
      }
    }
  }
  // 设置对比考试
  function setCompareExam() {
    let exams = selectedCategorySubjectExams.value
    // 原对比考试均存在则保留
    if (
      exams.some(exam => exam.examId == compareBaseExamId.value) &&
      exams.some(exam => exam.examId == compareTargetExamId.value)
    ) {
      return
    }
    // 默认最后两次考试对比
    compareTargetExamId.value = exams[exams.length - 1]?.examId || ''
    compareBaseExamId.value = exams[exams.length - 2]?.examId || ''
  }
  // 改变对比考试
  function changeCompareExam(examId) {
    if (!examId) {
      return
    }
    let exams = selectedCategorySubjectExams.value
    let exam = exams.find(item => item.examId == examId)
    if (!exam) {
      return
    }

    let compareExams = [compareBaseExamId.value, compareTargetExamId.value]
      .filter(Boolean)
      .map(examId => exams.find(item => item.examId == examId))
      .filter(Boolean)
    // 点击选中的考试，取消选中
    if (compareExams.includes(exam)) {
      if (compareExams.length > 1) {
        compareExams = compareExams.filter(x => x != exam)
      }
    }
    // 点击未选中的考试，当前选中考试不超过1个则添加，否则保留选中考试和最新考试
    else if (compareExams.length > 2) {
      compareExams.push(exam)
    } else {
      compareExams.sort((a, b) => a.beginTime - b.beginTime)
      compareExams = [exam, compareExams[compareExams.length - 1]]
    }
    compareExams.sort((a, b) => a.beginTime - b.beginTime)
    compareTargetExamId.value = compareExams[compareExams.length - 1]?.examId || ''
    compareBaseExamId.value = compareExams[compareExams.length - 2]?.examId || ''
  }
  // 创建echarts实例
  function initChart() {
    chartTrend = echarts.init(chartTrendRef.value)
    chartTrend.on('click', params => {
      changeCompareExam(params?.data?.extraData?.examId)
    })
    chartSubject = echarts.init(chartSubjectRef.value)
    chartChildren = echarts.init(chartChildrenRef.value)
  }
</script>

<style lang="scss" scoped>
  .panel-development {
    @include flex(column, flex-start, stretch);
    gap: 10px;
    height: 100%;
    padding: 0 10px;
    overflow: hidden;
    color: #b4d5e2;

    :deep(.ivu-select-selection) {
      color: white;
      background-color: transparent;

      .ivu-select-arrow {
        color: #fff;
      }
    }

    :deep(.ivu-select-input) {
      color: #fff;
    }

    :deep(.ivu-select-placeholder) {
      color: #fff;
    }

    .section-title {
      flex-shrink: 0;
      margin-top: 10px;
      color: #fff;
      font-size: 16px;
      text-align: center;
    }

    .section-selector {
      @include flex(row, flex-start, center);
      flex-shrink: 0;
      gap: 8px;

      .selector-grade,
      .selector-category,
      .selector-subject {
        width: 100px;
      }
    }

    .empty-tips {
      @include flex(row, center, center);
      flex: 1 1 300px;
      color: #fff;
    }

    .section-chart-trend {
      flex: 1 0 150px;
      // 使高度可以小于内容高度
      min-height: 0;
    }

    .section-compare-exam {
      @include flex(column, flex-start, flex-start);
      flex-shrink: 0;

      .label {
        position: relative;
        padding-bottom: 4px;
        color: #fff;
        font-size: 14px;

        &::after {
          position: absolute;
          bottom: 2px;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #05d1bc;
          content: '';
        }
      }
    }

    .exam-names {
      width: 100%;
      color: #fff;
      font-size: 13px;
    }

    .section-chart-subject {
      flex: 1 0 150px;
      min-height: 0;
    }

    .section-chart-children {
      flex: 2 0 200px;
      min-height: 0;
    }
  }
</style>
