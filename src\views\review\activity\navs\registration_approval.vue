<script>
  import ComponentRadioGroup from '@/components/radio_group'

  import {
    apiGetStudentRegistrationList,
    apiGetTeacherRegistrationList,
    apiGetStudentRegistrationDetail,
    apiGetTeacherRegistrationDetail,
    apiRegistrationAudit,
    apiGetStudentRegistrationStats,
    apiGetTeacherRegistrationStats,
  } from '@/api/review'

  import { mapGetters } from 'vuex'
  import { debounce } from '@/utils/function'
  import { deepCopy } from '@/utils/object'

  import ActivityUserRoleEnum from '@/enum/review/activity_user_role'

  export default {
    components: {
      's-radio-group': ComponentRadioGroup,
    },

    data() {
      return {
        selectedCategoryId: '',
        categoryListForRadioGroup: [],
        filterStatus: 'audit_pending',
        keyword: '',
        selectedSchoolId: '',
        isFilterSchoolActived: false,

        listTotal: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
        registedRecords: [],
        activeRecordDetail: null,

        auditComment: '',

        currentPage: 1,
      }
    },

    computed: {
      ...mapGetters('review', [
        'isAdmin',
        'isRegisterAuditor',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'enableSchoolAudit',
        'isMultipleSchool',
        'userList',
        'categories',
        'participantIdentity',
        'activityId',
        'schoolList',
      ]),

      currentUserRoleRegistrationAuditorInUserList() {
        const CurrentUserId = this.$store.getters['user/info'].userId
        return this.userList.filter(
          user => user.role === ActivityUserRoleEnum.RegisterAuditor.id && user.userId === CurrentUserId
        )
      },

      statusList() {
        return [
          {
            id: 'audit_pending',
            name: '待审核 ( ' + this.pendingCount + ' )',
          },
          {
            id: 'audit_approved',
            name: '审核通过 ( ' + this.approvedCount + ' )',
          },
          {
            id: 'audit_rejected',
            name: '审核驳回 ( ' + this.rejectedCount + ' )',
          },
        ]
      },

      tableColumns() {
        const CanSwitchSchool = this.userSchoolList && this.userSchoolList.length > 1

        const ColumnSchool = {
          title: '学校',
          key: 'schoolName',
          align: 'center',
        }

        if (CanSwitchSchool) {
          ColumnSchool.filters = this.userSchoolList.map(s => ({
            value: s.id,
            label: s.name,
          }))
          ColumnSchool.filterMultiple = false
          ColumnSchool.filterRemote = () => {}
        }

        const Columns = [ColumnSchool]

        if (this.participantIdentity === 'student') {
          Columns.push({
            title: '班级',
            key: 'className',
            align: 'center',
          })
        }

        Columns.push(
          {
            title: '参赛者',
            key: 'realName',
            align: 'center',
            width: 90,
          },
          {
            title: '作品名称',
            align: 'center',
            render: (h, params) => {
              const TargetFormRecord = (params.row.form || []).find(item => item.fieldLabel === '作品名称')
              return h('span', {}, TargetFormRecord?.fieldValue || '')
            },
          }
        )

        return Columns
      },

      activeRecordIndex() {
        return this.registedRecords.findIndex(item => item.active)
      },

      hasPreviousRecord() {
        return this.activeRecordIndex > 0 || this.currentPage > 1
      },

      hasNextRecord() {
        return (
          (this.activeRecordIndex >= 0 && this.activeRecordIndex < this.registedRecords.length - 1) ||
          this.currentPage * 10 < this.listTotal
        )
      },

      activeRecordCategoryFields() {
        const Fileds = deepCopy(
          (
            (this.activeRecordDetail &&
              this.activeRecordDetail.categoryId &&
              this.categoryListForRadioGroup.find(c => c.id === this.activeRecordDetail.categoryId)) || {
              fields: [],
            }
          ).fields
        ).map(f => {
          const TargetForm = ((this.activeRecordDetail && this.activeRecordDetail.form) || []).find(
            item => item.fieldId === f.id
          )
          if (TargetForm) {
            if (['text', 'textarea', 'radio', 'date'].includes(f.fieldType)) {
              f.fieldValue = TargetForm.fieldValue || ''
            } else if (f.fieldType === 'checkbox') {
              const ParseValue = TargetForm.fieldValue ? JSON.parse(TargetForm.fieldValue) : []
              f.fieldValue = ParseValue.join('、')
            } else if (f.fieldType === 'upload') {
              f.extraObject = f.extraJson ? JSON.parse(f.extraJson) : {}
              const NewAccepts = []
              if (f.extraObject.accepts.some(a => a.includes('image'))) {
                NewAccepts.push('.jpeg', '.jpg', '.png', '.gif')
              }
              if (f.extraObject.accepts.some(a => a.includes('pdf'))) {
                NewAccepts.push('.pdf')
              }
              f.extraObject.accepts = NewAccepts
              if (f.extraObject.maxFileSize) {
                f.extraObject.maxSize = f.extraObject.maxFileSize / 1024 / 1024
              }
              f.fieldValue = (TargetForm.fieldValue ? JSON.parse(TargetForm.fieldValue) : []).map(f => ({
                name: f.fileName || '',
                type: f.fileType || '',
                size: f.fileSize || '',
                path: f.filePath,
                file: f instanceof File ? f : null,
                isUploaded: true,
              }))
            }
          }
          if (!f.fieldValue) {
            f.fieldValue = ''
          }

          return f
        })
        Fileds.sort((a, b) => a.sortOrder - b.sortOrder)
        return Fileds
      },

      userInfo() {
        return this.$store.getters['user/info']
      },

      userSchoolList() {
        const UserSchoolId = this.userInfo.schoolId
        const FieldUserSchoolList = this.schoolList.filter(s => s.id === UserSchoolId)

        if (this.isMultipleSchool && !this.isAdmin && (this.isSchoolAdmin || this.isSchoolPersonInCharge)) {
          if (this.isRegisterAuditor) {
            const UserId = this.userInfo.userId
            const TargetUserRegisterAuditorInfo = this.userList.find(
              info => info.userId === UserId && info.role === 'register_auditor'
            )
            if (TargetUserRegisterAuditorInfo) {
              if (
                TargetUserRegisterAuditorInfo.categoryId &&
                TargetUserRegisterAuditorInfo.categoryId !== this.selectedCategoryId
              ) {
                return FieldUserSchoolList
              } else {
                return this.schoolList
              }
            }
          }

          return FieldUserSchoolList
        }

        return this.schoolList
      },
    },

    watch: {
      filterStatus() {
        this.onPageChange()
      },
    },

    created() {
      this.initCategoryListForRadioGroup()
      this.initSelectedSchoolId()
      this.fetchStatsAndList().finally(() => {
        if (this.registedRecords && this.registedRecords.length) {
          this.changeActiveRecord(this.registedRecords[0])
        }
      })
    },

    methods: {
      initCategoryListForRadioGroup() {
        if (this.categories && this.categories.length) {
          const List = []

          if (this.isAdmin || this.isSchoolAdmin || this.isSchoolPersonInCharge) {
            List.push(...this.categories)
          } else if (this.isRegisterAuditor) {
            List.push(
              ...this.categories.filter(c =>
                this.currentUserRoleRegistrationAuditorInUserList.some(user => user.categoryId === c.id)
              )
            )
          }

          this.categoryListForRadioGroup = List
          this.selectedCategoryId = List.length ? List[0].id : ''
        }
      },
      initSelectedSchoolId() {
        if (this.userSchoolList.length) {
          if (this.userSchoolList.length === 1) {
            this.selectedSchoolId = this.userSchoolList[0].id
          }
        }
      },

      /**
       * fetch
       */
      fetchStatsAndList() {
        return Promise.all([this.fetchRegistrationStats(), this.fetchRegistrationList()])
      },
      fetchRegistrationStats(params = {}) {
        const Request =
          this.participantIdentity === 'student' ? apiGetStudentRegistrationStats : apiGetTeacherRegistrationStats
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.selectedCategoryId,
          schoolId: this.selectedSchoolId || undefined,
        }
        Object.keys(params).forEach(key => (RequestParams[key] = params[key]))

        return Request(RequestParams).then(response => {
          if (response && response.length) {
            const Stat = response[0]
            this.pendingCount = Stat.auditPending
            this.approvedCount = Stat.auditApproved
            this.rejectedCount = Stat.auditRejected
          } else {
            this.pendingCount = 0
            this.approvedCount = 0
            this.rejectedCount = 0
          }
        })
      },
      fetchRegistrationList() {
        if (!this.selectedCategoryId) {
          this.$Message.warning({
            duration: 4,
            content: '类别加载失败，请刷新重试',
          })
          return Promise.resolve()
        }

        const Request =
          this.participantIdentity === 'student' ? apiGetStudentRegistrationList : apiGetTeacherRegistrationList
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.selectedCategoryId,
          status: this.filterStatus,
          currentPage: this.currentPage,
          keyword: this.keyword,
          schoolId: this.selectedSchoolId || undefined,
        }

        return Request(RequestParams)
          .then(response => {
            this.listTotal = (response && response.total && Number(response.total)) || 0
            this.registedRecords = (response && response.records) || []
          })
          .catch(() => {
            this.listTotal = 0
            this.registedRecords = []
          })
      },
      changeActiveRecord(record) {
        this.registedRecords.forEach(item => (item.active = item === record))
        this.fetchActiveRecordDetail()
      },
      fetchActiveRecordDetail() {
        const ActiveRecord = (this.registedRecords && this.registedRecords[this.activeRecordIndex]) || null
        if (!(ActiveRecord && ActiveRecord.categoryId && ActiveRecord.schoolId && ActiveRecord.userId)) {
          this.$Message.warning({
            duration: 4,
            content: '请求所需数据不全，请刷新重试',
          })
          this.activeRecordDetail = null
          return
        }

        const Request =
          this.participantIdentity === 'student' ? apiGetStudentRegistrationDetail : apiGetTeacherRegistrationDetail
        const RequestParams = {
          activityId: this.activityId,
          categoryId: ActiveRecord.categoryId,
          schoolId: ActiveRecord.schoolId,
          userId: ActiveRecord.userId,
        }
        Request(RequestParams).then(response => (this.activeRecordDetail = response || null))
      },

      /**
       * event function
       */
      onPageChange(page = 1, nextActiveIndex = 0) {
        this.currentPage = page
        this.fetchStatsAndList().finally(() => {
          if (this.registedRecords && this.registedRecords.length && nextActiveIndex < this.registedRecords.length) {
            this.changeActiveRecord(this.registedRecords[nextActiveIndex])
          } else {
            this.activeRecordDetail = null
          }
        })
      },
      onSelectedCategoryIdChange(value) {
        if (value && value !== this.selectedCategoryId) {
          this.selectedCategoryId = value
          if (!this.userSchoolList.some(school => school.id === this.selectedSchoolId)) {
            this.selectedSchoolId = ''
            this.initSelectedSchoolId()
          }
          this.onPageChange()
        }
      },
      onKeywordChange: debounce(function (e) {
        this.keyword = (e.target.value || '').trim()
        this.onPageChange()
      }, 600),
      goPreviousRecord() {
        if (this.activeRecordIndex > 0) {
          this.changeActiveRecord(this.registedRecords[this.activeRecordIndex - 1])
        } else if (this.activeRecordIndex === 0 && this.currentPage > 1) {
          this.onPageChange(this.currentPage - 1, 9) // pageSize - 1(10 - 1 = 9)
        }
      },
      goNextRecord() {
        if (this.activeRecordIndex < this.registedRecords.length - 1) {
          this.changeActiveRecord(this.registedRecords[this.activeRecordIndex + 1])
        } else if (
          this.activeRecordIndex === this.registedRecords.length - 1 &&
          this.currentPage < Math.ceil(this.total / 10) // pageSize: 10
        ) {
          this.onPageChange(this.currentPage + 1)
        }
      },
      onTableRowClick(row, rowIndex) {
        this.changeActiveRecord(this.registedRecords[rowIndex])
      },
      commitRegistrationAudit(result) {
        if (result && this.activeRecordDetail) {
          apiRegistrationAudit({
            activityId: this.activeRecordDetail.activityId,
            categoryId: this.activeRecordDetail.categoryId,
            comments: (this.auditComment && this.auditComment.trim()) || '',
            decision: result,
            submissionId: this.activeRecordDetail.lastSubmissionId,
            userId: this.activeRecordDetail.userId,
          }).then(() => {
            this.$Message.success({
              duration: 3,
              content: result === 'approved' ? '已通过' : '已驳回',
            })

            if (this.hasNextRecord) {
              this.onPageChange(this.currentPage, this.activeRecordIndex)
            } else if (this.hasPreviousRecord) {
              if (this.activeRecordIndex) {
                this.onPageChange(this.currentPage, this.activeRecordIndex - 1)
              } else {
                this.onPageChange(this.currentPage - 1, 9)
              }
            } else {
              this.onPageChange()
            }
          })
        }
      },
      handleFilterChange(params) {
        const ParamsResultArray = params._filterChecked.slice()
        if (ParamsResultArray.length) {
          this.selectedSchoolId = ParamsResultArray[0]
        } else {
          this.selectedSchoolId = ''
        }

        this.onPageChange()
      },

      /**
       * style
       */
      getTableRowClassName(row) {
        return row && row.active ? 'row-active' : ''
      },
    },
  }
</script>

<template>
  <div class="container-registration-approval">
    <div class="title-bar">
      <RadioGroup
        :model-value="selectedCategoryId"
        type="button"
        button-style="solid"
        @on-change="onSelectedCategoryIdChange"
      >
        <Radio v-for="c of categoryListForRadioGroup" :key="'c' + c.id" :label="c.id">{{ c.name }}</Radio>
      </RadioGroup>

      <div>
        <Button size="small" type="default" :disabled="!hasPreviousRecord" @click="goPreviousRecord">
          <Icon type="md-arrow-back"></Icon>
          上一份
        </Button>
        <Button size="small" type="default" :disabled="!hasNextRecord" @click="goNextRecord">
          <Icon type="md-arrow-forward"></Icon>
          下一份
        </Button>
      </div>
    </div>

    <div class="action-panel">
      <div class="registed-records-table">
        <div class="filter-bar">
          <s-radio-group v-model="filterStatus" :radioes="statusList"></s-radio-group>
          <Input
            :model-value="keyword"
            :placeholder="participantIdentity === 'student' ? '学生姓名' : '教师姓名 / 手机号码'"
            clearable
            transfer
            suffix="md-search"
            style="width: 200px"
            @on-change="onKeywordChange"
          />
        </div>

        <Table
          :columns="tableColumns"
          :data="registedRecords"
          :row-class-name="getTableRowClassName"
          @on-row-click="onTableRowClick"
          @on-filter-change="handleFilterChange"
        ></Table>

        <Page
          :total="listTotal"
          size="small"
          show-total
          transfer
          style="float: right; margin-top: 14px"
          @on-change="onPageChange"
        />
      </div>

      <div class="registed-record-info">
        <div class="form-detail-panel">
          <div class="form-item form-item-title">
            <div class="title-cell school-name">{{ (activeRecordDetail && activeRecordDetail.schoolName) || '' }}</div>
            <div v-show="participantIdentity === 'student'" class="title-cell">
              {{ (activeRecordDetail && activeRecordDetail.className) || '' }}
            </div>
            <div class="title-cell">{{ (activeRecordDetail && activeRecordDetail.realName) || '' }}</div>
            <div v-show="participantIdentity !== 'student'" class="title-cell">
              {{ (activeRecordDetail && activeRecordDetail.mobile) || '' }}
            </div>
          </div>
          <template v-for="f of activeRecordCategoryFields" :key="f.id">
            <div v-if="f.fieldType === 'upload'" class="form-item form-item-content-column">
              <div class="item-label">{{ f.fieldLabel }}</div>
              <div v-for="fv of f.fieldValue" :key="fv.path" class="item-info">
                <div class="file-name">{{ fv.name }}</div>
                <div class="file-content">
                  <Image v-if="fv.type === 'image'" :src="fv.path" style="width: 100%" />
                  <object v-if="fv.type === 'pdf'" :data="fv.path" type="application/pdf" width="100%" height="1000px">
                    <p>您的浏览器不支持查看PDF文件，请<a :href="fv.path">下载PDF文件</a>查看。</p>
                  </object>
                </div>
              </div>
            </div>
            <div v-else class="form-item form-item-content-row">
              <div class="item-label">{{ f.fieldLabel }}</div>
              <div class="item-info">{{ f.fieldValue }}</div>
            </div>
          </template>
        </div>

        <div class="registration-result">
          <Input
            v-model="auditComment"
            clearable
            transfer
            class="comment"
            :placeholder="
              filterStatus === 'audit_approved'
                ? '驳回原因'
                : filterStatus === 'audit_rejected'
                  ? '审核批注'
                  : '审核批注 / 驳回原因'
            "
          />

          <div class="btns">
            <Button
              type="primary"
              style="width: 90px; letter-spacing: 0.4em"
              :disabled="!activeRecordDetail || filterStatus === 'audit_approved'"
              @click="commitRegistrationAudit('approved')"
              >通过</Button
            >
            <Button
              type="warning"
              style="width: 90px; letter-spacing: 0.4em"
              :disabled="!activeRecordDetail || filterStatus === 'audit_rejected'"
              @click="commitRegistrationAudit('rejected')"
              >驳回</Button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .container-registration-approval {
    padding: 20px;
    background-color: white;

    .title-bar {
      @include flex(row, space-between, center);
      width: 100%;
      margin-bottom: 14px;
      user-select: none;
    }

    .action-panel {
      @include flex(row, space-between, stretch);
      width: 100%;
      min-width: 50px;

      .registed-records-table {
        flex: none;
        width: 540px;

        :deep(.row-active td) {
          background-color: #ebf7ff;
        }

        .filter-bar {
          @include flex(row, space-between, center);
          margin-bottom: 14px;
        }
      }

      .registed-record-info {
        flex: 1;
        margin-left: 20px;

        .form-detail-panel {
          max-height: calc(100vh - 230px);
          padding: 24px 8px 24px 10px;
          border-radius: 6px;
          overflow-y: auto;
          box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

          .form-item {
            font-size: $font-size-medium;
            line-height: 32px;

            &:not(:first-child) {
              margin-top: 6px;
            }
          }

          .form-item-title {
            @include flex(row, flex-start, center);
            font-weight: bold;

            .title-cell {
              &:not(:first-child) {
                margin-left: 1.4em;
              }
            }
          }

          .form-item-content-row {
            @include flex(row, flex-start, center);

            .item-label {
              flex: none;
              width: 5em;
              text-align: right;
            }

            .item-info {
              flex: 1;
              margin-left: 1.4em;
            }
          }

          .form-item-content-column {
            .item-label {
              width: 5em;
              text-align: right;
            }

            .item-info {
              &:not(:first-child) {
                margin-top: 4px;
              }

              .file-name {
                color: $color-small-title;
                font-weight: bold;
                text-align: center;
              }

              .file-content {
                width: 100%;
                padding-left: 1em;
              }
            }
          }
        }

        .registration-result {
          @include flex(row, space-between, center);
          margin-top: 14px;

          .comment {
            flex: 1;
          }

          .btns {
            flex: none;
            margin-left: 2em;
          }
        }
      }
    }
  }
</style>
