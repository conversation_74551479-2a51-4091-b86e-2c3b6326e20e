<template>
  <div class="container-personnel">
    <div class="section-header">
      <RadioGroup
        v-if="showCategoryRadioGroup"
        v-model="selectedCategoryId"
        type="button"
        button-style="solid"
        class="header-category"
      >
        <Radio v-for="c of categoryListForRadioGroup" :key="c.id" :label="c.id">{{ c.name }}</Radio>
      </RadioGroup>
      <div class="header-actions">
        <Button type="primary" icon="md-add" @click="showModalAddUser = true">添加人员</Button>
      </div>
    </div>

    <div class="section-list">
      <Table :columns="tableColumns" :data="users"></Table>
    </div>
    <ModalAddUser v-model="showModalAddUser" @on-add-users="refresh"></ModalAddUser>
  </div>
</template>

<script setup>
  import { ref, computed, onBeforeMount } from 'vue'
  import store from '@/store/index'
  import { Message } from 'view-ui-plus'

  import ModalAddUser from './modal_add_user.vue'
  import TextButton from '@/components/text_button.vue'

  import { deleteActivityUsers } from '@/api/review/activity'

  import ActivityUserRoleEnum from '@/enum/review/activity_user_role'

  const activityId = computed(() => {
    return store.getters['review/activityId']
  })
  const categories = computed(() => {
    return store.getters['review/categories']
  })
  const userList = computed(() => {
    return store.getters['review/userList']
  })
  const isAdmin = computed(() => {
    return store.getters['review/isAdmin']
  })
  const userSchoolId = computed(() => {
    return store.getters['user/info'].schoolId
  })

  // 选中类别
  const selectedCategoryId = ref('')
  // 可选类别
  const categoryListForRadioGroup = computed(() => {
    let list = categories.value.map(c => ({ id: c.id, name: c.name }))
    if (categories.value.length > 1) {
      list.unshift({ id: 'all', name: '全部' })
    }
    return list
  })
  // 是否显示类别选择
  const showCategoryRadioGroup = computed(() => {
    return categoryListForRadioGroup.value.length > 1 && isAdmin.value
  })

  // 人员列表
  const users = computed(() => {
    let users = userList.value || []
    if (selectedCategoryId.value != 'all') {
      users = users.filter(u => !u.categoryId || u.categoryId == selectedCategoryId.value)
    }
    // 学校管理员只可见本校学校负责人
    if (!isAdmin.value) {
      users = users.filter(
        u => u.role == ActivityUserRoleEnum.SchoolPersonInCharge.id && u.schoolId == userSchoolId.value
      )
    }

    let rolesInOrder = ActivityUserRoleEnum.getIds()
    return users.sort((a, b) => {
      if (a.role != b.role) {
        return rolesInOrder.indexOf(a.role) - rolesInOrder.indexOf(b.role)
      }
      if (a.schoolId != b.schoolId) {
        // 学校名称按中文排序
        return (a.schoolName || '').localeCompare(b.schoolName || '')
      }
      // 手机号码排序
      return (a.mobile || '').localeCompare(b.mobile || '')
    })
  })
  const tableColumns = [
    {
      title: '学校',
      key: 'schoolName',
      minWidth: 240,
      sortable: true,
    },
    {
      title: '姓名',
      key: 'userName',
      align: 'center',
      minWidth: 120,
      sortable: true,
    },
    {
      title: '手机号码',
      key: 'mobile',
      align: 'center',
      minWidth: 120,
      sortable: true,
    },
    {
      title: '角色',
      key: 'role',
      align: 'center',
      minWidth: 120,
      sortable: true,
      render: (h, params) => {
        return h('div', ActivityUserRoleEnum.getNameById(params.row.role))
      },
    },
    {
      title: '类别',
      key: 'categoryName',
      align: 'center',
      minWidth: 120,
      sortable: true,
      render: (h, params) => {
        let categoryName
        if (params.row.categoryId) {
          categoryName = params.row.categoryName
        } else if (ActivityUserRoleEnum.RegisterAuditor.id == params.row.role) {
          categoryName = '不限'
        } else {
          categoryName = '--'
        }
        return h('div', categoryName)
      },
    },
    {
      title: '操作',
      width: 100,
      align: 'center',
      render: (h, params) => {
        let btns = []
        if (params.row.role != ActivityUserRoleEnum.Creator.id) {
          btns.push(
            h(
              TextButton,
              {
                type: 'warning',
                onClick: () => {
                  deleteUser(params.row.id)
                },
              },
              () => '删除'
            )
          )
        }
        return h('div', btns)
      },
    },
  ]

  // 添加人员
  const showModalAddUser = ref(false)

  // 删除人员
  async function deleteUser(id) {
    try {
      await deleteActivityUsers({
        activityId: activityId.value,
        ids: [id],
      })
      Message.success('已删除')
    } finally {
      refresh()
    }
  }

  // 刷新
  async function refresh() {
    await store.dispatch('review/refreshActivityDetail')
  }

  // 初始化
  onBeforeMount(() => {
    refresh()
    selectedCategoryId.value = categoryListForRadioGroup.value[0]?.id || ''
  })
</script>

<style lang="scss" scoped>
  .container-personnel {
    padding: 20px;
    background-color: white;
  }

  .section-header {
    @include flex(row, flex-end, center);
    margin-bottom: 16px;

    .header-category {
      flex-grow: 1;
      flex-shrink: 1;
      margin-right: auto;
    }

    .header-actions {
      flex-grow: 0;
      flex-shrink: 0;
      margin-left: 16px;
    }
  }
</style>
