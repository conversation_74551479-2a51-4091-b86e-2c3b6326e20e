<script>
  import ComponentRadioGroup from '@/components/radio_group.vue'

  import { mapGetters } from 'vuex'
  import { deepCopy } from '@/utils/object'
  import { exportZippedSchoolReports } from '@/helpers/report/export_zipped_reports/export_zipped_file.js'

  import SubjectCombinations from '@/enum/report/subject_combinations.js'
  import { REPORTINFOS } from '@/helpers/report/export_zipped_reports/reports_enum'

  export default {
    components: {
      'sure-radio-group': ComponentRadioGroup,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        downloading: false,

        exportBureauReport: true,
        bureauExcelShowRank: false,
        bureauExportSubjectCategories: [0],
        bureauDownloadSubjectCombinationExcels: true,
        bureauSubjectList: [],
        bureauReportList: [],

        exportSchoolsReport: false,
        schoolsExcelShowRank: false,
        schoolsExportSubjectCategories: [0],
        schoolsDownloadSubjectCombinationExcels: true,
        schoolsSubjectList: [],
        schoolsReportList: [],

        showModalExportResult: false,
      }
    },

    computed: {
      ...mapGetters('report', [
        'templateId',
        'currentInstitutionId',
        'currentLevelName',
        'isAdministrator',
        'templateSubjects',
      ]),

      isMultipleSchoolLevel() {
        return this.currentLevelName === 'multipleSchool'
      },

      subjectCategories() {
        return SubjectCombinations.getIdNames()
      },

      isBureauSubjectsCheckboxAllIndeterminate() {
        return Boolean(
          this.bureauSubjectList.length &&
            this.bureauSubjectList.some(item => item.checked) &&
            !this.isBureauSubjectsCheckboxAllChecked
        )
      },
      isBureauSubjectsCheckboxAllChecked() {
        return Boolean(this.bureauSubjectList.length && this.bureauSubjectList.every(item => item.checked))
      },

      isBureauReportsCheckboxAllIndeterminate() {
        return Boolean(
          this.bureauReportList.length &&
            this.bureauReportList.some(item => item.checked) &&
            !this.isBureauReportsCheckboxAllChecked
        )
      },
      isBureauReportsCheckboxAllChecked() {
        return Boolean(this.bureauReportList.length && this.bureauReportList.every(item => item.checked))
      },

      isSchoolsSubjectsCheckboxAllIndeterminate() {
        return Boolean(
          this.schoolsSubjectList.length &&
            this.schoolsSubjectList.some(item => item.checked) &&
            !this.isSchoolsSubjectsCheckboxAllChecked
        )
      },
      isSchoolsSubjectsCheckboxAllChecked() {
        return Boolean(this.schoolsSubjectList.length && this.schoolsSubjectList.every(item => item.checked))
      },

      isSchoolsReportsCheckboxAllIndeterminate() {
        return Boolean(
          this.schoolsReportList.length &&
            this.schoolsReportList.some(item => item.checked) &&
            !this.isSchoolsReportsCheckboxAllChecked
        )
      },
      isSchoolsReportsCheckboxAllChecked() {
        return Boolean(this.schoolsReportList.length && this.schoolsReportList.every(item => item.checked))
      },

      templateSubjectList() {
        return this.templateSubjects || []
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },

      handleModalVisibleChanged(visibility) {
        if (visibility) {
          this.showModalExportResult = false
          this.exportResult = {
            success: 0,
            failed: 0,
            detail: [],
          }

          this.exportBureauReport = true
          this.bureauExcelShowRank = false
          this.bureauExportSubjectCategories = [0]
          this.bureauDownloadSubjectCombinationExcels = true
          this.bureauSubjectList = this.templateSubjectList.map(item => ({
            checked: true,
            subjectId: item.subjectId,
            subjectName: item.subjectName,
            examSubjectId: item.examSubjectId,
          }))
          this.bureauReportList = REPORTINFOS
            ? REPORTINFOS.filter(r => r.mname).map(r => ({
                type: r.type,
                name: r.mname,
                checked: true,
              }))
            : []

          this.exportSchoolsReport = !this.isMultipleSchoolLevel
          this.schoolsExcelShowRank = false
          this.schoolsExportSubjectCategories = [0]
          this.schoolsDownloadSubjectCombinationExcels = true
          this.schoolsSubjectList = deepCopy(this.bureauSubjectList)
          this.schoolsReportList = REPORTINFOS
            ? REPORTINFOS.filter(r => r.name).map(r => ({
                type: r.type,
                name: r.name,
                checked: true,
              }))
            : []
        } else {
          this.closeModal()
        }
      },

      // toggle
      toggleBureauSubjectsCheckAll() {
        if (this.isBureauSubjectsCheckboxAllChecked) {
          this.bureauSubjectList.forEach(item => (item.checked = false))
        } else {
          this.bureauSubjectList.forEach(item => (item.checked = true))
        }
      },
      toggleBureauReportsCheckAll() {
        if (this.isBureauReportsCheckboxAllChecked) {
          this.bureauReportList.forEach(item => (item.checked = false))
        } else {
          this.bureauReportList.forEach(item => (item.checked = true))
        }
      },
      toggleSchoolsSubjectsCheckAll() {
        if (this.isSchoolsSubjectsCheckboxAllChecked) {
          this.schoolsSubjectList.forEach(item => (item.checked = false))
        } else {
          this.schoolsSubjectList.forEach(item => (item.checked = true))
        }
      },
      toggleSchoolsReportsCheckAll() {
        if (this.isSchoolsReportsCheckboxAllChecked) {
          this.schoolsReportList.forEach(item => (item.checked = false))
        } else {
          this.schoolsReportList.forEach(item => (item.checked = true))
        }
      },

      handleModalOK() {
        if (
          !(this.bureauReportList && this.bureauReportList.length) &&
          !(this.schoolsReportList && this.schoolsReportList.length)
        ) {
          this.$Message.warning({
            duration: 4,
            content: '请至少选择一类报表进行导出',
          })
          return
        }

        const Params = {
          bureauReport:
            this.isMultipleSchoolLevel && this.exportBureauReport
              ? {
                  showRank: this.bureauExcelShowRank,
                  subjectCategories: this.bureauExportSubjectCategories,
                  downloadSubjectCombinationExcels: this.bureauDownloadSubjectCombinationExcels,
                  subjectList: this.bureauSubjectList,
                  reportList: this.bureauReportList,
                }
              : null,
          schoolsReport:
            !this.isMultipleSchoolLevel || this.exportSchoolsReport
              ? {
                  showRank: this.schoolsExcelShowRank,
                  subjectCategories: this.schoolsExportSubjectCategories,
                  downloadSubjectCombinationExcels: this.schoolsDownloadSubjectCombinationExcels,
                  subjectList: this.schoolsSubjectList,
                  reportList: this.schoolsReportList,
                }
              : null,
        }

        this.downloading = true
        exportZippedSchoolReports(Params)
          .then(({ success, failed, failedReportList }) => {
            if (!this.showModalExportResult) {
              this.showModalExportResult = true
            }
            this.exportResult.success = success || 0
            this.exportResult.failed = failed || 0
            this.exportResult.detail = `成功：${success || 0};   失败：${failed || 0}`

            if (failedReportList && failedReportList.length) {
              failedReportList.forEach((f, fdx) => {
                this.exportResult.detail += `<br><span style="color: green">${fdx + 1}.</span> ${
                  f.fileName
                } <span style="color: red">${f.explain}</span>`
              })
            }
          })
          .finally(() => (this.downloading = false))
      },
    },
  }
</script>

<template>
  <Modal
    class="modal-export-senior-high-reports-batch"
    title="批量导出报表"
    fullscreen
    :model-value="modelValue"
    :closable="!downloading"
    :mask-closable="false"
    @on-visible-change="handleModalVisibleChanged"
  >
    <div class="setting-panel">
      <template v-if="isMultipleSchoolLevel">
        <div class="bureau-export-setting">
          <div class="level-download-setting">
            <div class="setting-title">导出联考报告</div>
            <i-switch v-model="exportBureauReport" size="small"></i-switch>
          </div>

          <div class="settings-list">
            <div class="setting-item">
              <div class="item-label">选科类别:</div>
              <div class="item-content">
                <sure-radio-group
                  v-model="bureauExportSubjectCategories"
                  :radioes="subjectCategories"
                  :button="true"
                  :multiple="true"
                  :multiple-can-select-none="false"
                  :disabled="!exportBureauReport"
                ></sure-radio-group>
              </div>
            </div>
            <div class="setting-item">
              <div class="item-label">显示排名:</div>
              <div class="item-content">
                <i-switch v-model="bureauExcelShowRank" size="small" :disabled="!exportBureauReport"></i-switch>
              </div>
            </div>
            <div class="setting-item" title="是否下载相关科目组合报表">
              <div class="item-label">科目组合:</div>
              <div class="item-content">
                <i-switch
                  v-model="bureauDownloadSubjectCombinationExcels"
                  size="small"
                  :disabled="!exportBureauReport"
                ></i-switch>
              </div>
            </div>
            <div class="setting-item">
              <div class="item-label">科目:</div>
              <div class="item-content">
                <Checkbox
                  class="check-all"
                  :indeterminate="isBureauSubjectsCheckboxAllIndeterminate"
                  :model-value="isBureauSubjectsCheckboxAllChecked"
                  :disabled="!exportBureauReport"
                  @click.prevent="toggleBureauSubjectsCheckAll"
                  >全选</Checkbox
                >
                <div class="checkbox-items">
                  <Checkbox
                    v-for="s of bureauSubjectList"
                    :key="s.examSubjectId"
                    v-model="s.checked"
                    class="checkbox-item"
                    :disabled="!exportBureauReport"
                    >{{ s.subjectName }}</Checkbox
                  >
                </div>
              </div>
            </div>
            <div class="setting-item">
              <div class="item-label">报表:</div>
              <div class="item-content">
                <Checkbox
                  class="check-all"
                  :indeterminate="isBureauReportsCheckboxAllIndeterminate"
                  :model-value="isBureauReportsCheckboxAllChecked"
                  :disabled="!exportBureauReport"
                  @click.prevent="toggleBureauReportsCheckAll"
                  >全选</Checkbox
                >
                <div class="checkbox-items">
                  <Checkbox
                    v-for="r of bureauReportList"
                    :key="r.type"
                    v-model="r.checked"
                    :disabled="!exportBureauReport"
                    :class="{ 'checkbox-item': r.type !== 'teacher_analyse_by_merged_class' }"
                    >{{ r.name }}</Checkbox
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <Divider style="margin: 16px 0; color: gray"></Divider>
      </template>

      <div class="schools-export-setting">
        <div class="level-download-setting">
          <div class="setting-title">导出学校报告</div>
          <i-switch v-show="isMultipleSchoolLevel" v-model="exportSchoolsReport" size="small"></i-switch>
        </div>

        <div class="settings-list">
          <div class="setting-item">
            <div class="item-label">选科类别:</div>
            <div class="item-content">
              <sure-radio-group
                v-model="schoolsExportSubjectCategories"
                :radioes="subjectCategories"
                :button="true"
                :multiple="true"
                :multiple-can-select-none="false"
                :disabled="!exportSchoolsReport"
              ></sure-radio-group>
            </div>
          </div>
          <div class="setting-item">
            <div class="item-label">显示排名:</div>
            <div class="item-content">
              <i-switch v-model="schoolsExcelShowRank" size="small" :disabled="!exportSchoolsReport"></i-switch>
            </div>
          </div>
          <div class="setting-item" title="是否下载相关科目组合报表">
            <div class="item-label">科目组合:</div>
            <div class="item-content">
              <i-switch
                v-model="schoolsDownloadSubjectCombinationExcels"
                size="small"
                :disabled="!exportSchoolsReport"
              ></i-switch>
            </div>
          </div>
          <div class="setting-item">
            <div class="item-label">科目:</div>
            <div class="item-content">
              <Checkbox
                class="check-all"
                :indeterminate="isSchoolsSubjectsCheckboxAllIndeterminate"
                :model-value="isSchoolsSubjectsCheckboxAllChecked"
                :disabled="!exportSchoolsReport"
                @click.prevent="toggleSchoolsSubjectsCheckAll"
                >全选</Checkbox
              >
              <div class="checkbox-items">
                <Checkbox
                  v-for="s of schoolsSubjectList"
                  :key="s.examSubjectId"
                  v-model="s.checked"
                  class="checkbox-item"
                  :disabled="!exportSchoolsReport"
                  >{{ s.subjectName }}</Checkbox
                >
              </div>
            </div>
          </div>
          <div class="setting-item">
            <div class="item-label">报表:</div>
            <div class="item-content">
              <Checkbox
                class="check-all"
                :indeterminate="isSchoolsReportsCheckboxAllIndeterminate"
                :model-value="isSchoolsReportsCheckboxAllChecked"
                :disabled="!exportSchoolsReport"
                @click.prevent="toggleSchoolsReportsCheckAll"
                >全选</Checkbox
              >
              <div class="checkbox-items">
                <Checkbox
                  v-for="r of schoolsReportList"
                  :key="r.type"
                  v-model="r.checked"
                  class="checkbox-item"
                  :disabled="!exportSchoolsReport"
                  >{{ r.name }}</Checkbox
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showModalExportResult" class="export-result">
      <div class="status">
        <div v-if="!exportResult.failed" class="status-success">
          <Icon type="md-checkmark-circle"></Icon>
        </div>
        <div v-else class="status-fail">
          <Icon type="md-close-circle"></Icon>
        </div>
      </div>
      <div class="detail" v-html="exportResult.detail"></div>
    </div>

    <template #footer>
      <div class="modal-btn">
        <Button v-if="!downloading" type="text" @click="closeModal">取消</Button>
        <Button type="primary" :loading="downloading" @click="handleModalOK">{{
          downloading ? '正在导出' : '导出'
        }}</Button>
      </div>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-export-senior-high-reports-batch {
    min-height: 100px;

    .setting-panel {
      user-select: none;

      .level-download-setting {
        @include flex(row, flex-start, center);

        .setting-title {
          margin-right: 1em;
          font-weight: bold;
          font-size: $font-size-medium-xs;
        }
      }

      .settings-list {
        padding-top: 0.5em;
        padding-left: 1em;

        .setting-item {
          @include flex(row, flex-start, stretch);
          line-height: 2.5;

          .item-label {
            min-width: 5em;
            margin-right: 1em;
            color: $color-small-title;
          }

          .item-content {
            // nont
          }
        }
      }
    }

    .modal-btn {
      margin-top: 10px;
      text-align: right;
    }

    .export-result {
      margin-top: 10px;
      border-top: 1px solid $color-border;

      .status {
        font-size: 40px;
        text-align: center;
      }

      .status-success {
        color: $color-success;
      }

      .status-fail {
        color: $color-error;
      }

      .detail {
        max-height: calc(100vh - 731px);
        padding: 0 20%;
        overflow-y: auto;
        white-space: pre;
      }
    }
  }
</style>
