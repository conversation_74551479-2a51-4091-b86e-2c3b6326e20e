<template>
  <div class="container-home-edit">
    <Tabs v-model="activeTab" :animated="false">
      <TabPane label="banner管理" name="banner">
        <BannerEdit />
      </TabPane>
      <TabPane label="试卷管理" name="paper">
        <CollectionEdit />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import PageCache from '@/utils/page_cache'
  import BannerEdit from './components/banner_edit.vue'
  import CollectionEdit from './components/collection_edit.vue'

  const uniquePageName = 'qlib-home-edit'

  export default {
    components: {
      BannerEdit,
      CollectionEdit,
    },
    data() {
      return {
        activeTab: 'banner',
      }
    },
    created() {
      this.makeDefaultTab()
    },
    beforeUnmount() {
      PageCache.save(uniquePageName, {
        activeTab: this.activeTab,
      })
    },
    methods: {
      makeDefaultTab() {
        let lastActiveTabInfo = PageCache.fetch(uniquePageName)
        if (lastActiveTabInfo && lastActiveTabInfo.activeTab) {
          this.activeTab = lastActiveTabInfo.activeTab
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-home-edit {
    padding: 10px 20px;
    background-color: #fff;
  }
</style>
