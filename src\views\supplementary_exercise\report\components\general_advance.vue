<template>
  <div class="card-general-advance">
    <div class="card-title">学生进退步分析</div>
    <div class="card-content">
      <div class="card-left">
        <div class="title">进步前 5 名</div>
        <Table :columns="tableColumns" :data="progressList"></Table>
      </div>
      <div class="card-right">
        <div class="title">退步前 5 名</div>
        <Table :columns="tableColumns" :data="regressList"></Table>
      </div>
    </div>
  </div>
</template>

<script>
  import { Icon } from 'view-ui-plus'

  export default {
    props: {
      classDetail: Object,
    },
    computed: {
      progressList() {
        let ordered = this.classDetail.studentScoreList
          .filter(stu => stu.rankChange > 0)
          .sort((a, b) => b.rankChange - a.rankChange)
        return this.getRankChangeTop5(ordered)
      },
      regressList() {
        let ordered = this.classDetail.studentScoreList
          .filter(stu => stu.rankChange < 0)
          .sort((a, b) => a.rankChange - b.rankChange)
        return this.getRankChangeTop5(ordered)
      },
      tableColumns() {
        return [
          {
            title: '姓名',
            key: 'studentName',
          },
          {
            title: '成绩',
            key: 'score',
            align: 'center',
          },
          {
            title: '班排名',
            key: 'rank',
            align: 'center',
          },
          {
            title: '升降',
            align: 'center',
            render: (h, params) => {
              let rankChange = params.row.rankChange
              if (rankChange == null) {
                return null
              }
              let iconType = 'ios-arrow-round-up'
              let iconColor = '#3ECBC4'
              if (rankChange < 0) {
                iconType = 'ios-arrow-round-down'
                iconColor = '#FFC43F'
              }
              return h(
                'div',
                {
                  style: {
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  },
                },
                [
                  h(Icon, {
                    type: iconType,
                    size: 20,
                    style: {
                      color: iconColor,
                      fontWeight: 'bold',
                    },
                  }),
                  h('span', Math.abs(rankChange)),
                ]
              )
            },
          },
        ]
      },
    },
    methods: {
      getRankChangeTop5(orderedList) {
        let list = []
        let rankChange5th = 0
        for (let stu of orderedList) {
          if (list.length < 5) {
            list.push(stu)
            if (list.length == 5) {
              rankChange5th = stu.rankChange
            }
          } else if (stu.rankChange == rankChange5th) {
            list.push(stu)
          } else {
            break
          }
        }
        return list
      },
    },
  }
</script>

<style lang="scss" scoped>
  .title {
    margin-bottom: 8px;
    color: #05c1ae;
  }
</style>
