<template>
  <div class="container-banner-edit">
    <div class="box-tool">
      <TextButton type="primary" icon="md-add" @click="onShowAddBanner">添加</TextButton>
      <div class="tip-item">
        <Icon type="md-information-circle" class="tip-icon"></Icon>
        <span class="tip-text">可通过拖拽调整banner顺序</span>
      </div>
    </div>
    <div class="box-banner-list">
      <Table :columns="tableColumns" :data="bannerList" draggable @on-drag-drop="onTableDrag"></Table>
    </div>
    <Modal
      v-model="modalEditBanner"
      class="modal-edit-banner"
      title="编辑"
      :width="700"
      @on-visible-change="handleVisibleChange"
    >
      <div class="content-modal">
        <Form :label-width="80">
          <FormItem label="图片">
            <com-upload
              :accepts="['.jpeg', '.png', '.jpg']"
              :max-size="100"
              :show-file-name="false"
              :file-name="file && file.name"
              @on-selected="onFileSelected"
            ></com-upload>
            <div v-if="currentEditItem.imageUrl" class="box-image">
              <img class="uploaded-image" :src="currentEditItem.imageUrl" alt="" />
            </div>
          </FormItem>
          <FormItem label="跳转链接">
            <Input v-model="currentEditItem.link" placeholder="请输入跳转链接">
              <!-- <template #prepend>
                <Select v-model="protocalType" style="width: 80px">
                  <Option value="http">http://</Option>
                  <Option value="https">https://</Option>
                </Select>
              </template> -->
            </Input>
          </FormItem>
        </Form>
      </div>
      <template #footer>
        <div class="footer">
          <Button type="text" @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleOK">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import comUpload from '@/components/upload.vue'
  import { apiGetFrontPageBanners, apiUploadImage, apiSaveImages } from '@/api/qlib/front_page'
  import { deepCopy } from '@/utils/object'

  export default {
    components: {
      'com-upload': comUpload,
    },
    data() {
      return {
        bannerList: [],
        modalEditBanner: false,
        currentEditItem: {},
        file: null,
        protocalType: 'https',
      }
    },
    computed: {
      currentStageSubject() {
        return this.$store.getters['qlib/currentStageSubject']
      },
      tableColumns() {
        let columns = [
          {
            title: '图片',
            key: 'bannerUrl',
            align: 'center',
            render: (h, params) => {
              return h('img', {
                src: params.row.imageUrl,
                style: {
                  width: '50%',
                  margin: '10px',
                },
              })
            },
          },
          {
            title: '跳转链接',
            key: 'link',
            align: 'center',
          },
          {
            title: '操作',
            key: 'action',
            align: 'center',
            render: (h, params) => {
              let btns = [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => {
                      this.editBannerItem(params.row)
                    },
                  },
                  () => '编辑'
                ),
                h(
                  TextButton,
                  {
                    type: 'warning',
                    onClick: () => {
                      this.deleteBannerItem(params.row)
                    },
                  },
                  () => '删除'
                ),
              ]
              return h('div', {}, btns)
            },
          },
        ]

        return columns
      },
    },
    watch: {
      currentStageSubject() {
        this.getBanners()
      },
    },
    created() {
      this.getBanners()
    },
    methods: {
      getBanners() {
        apiGetFrontPageBanners({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
        }).then(res => {
          this.bannerList = res || []
        })
      },
      onShowAddBanner() {
        this.modalEditBanner = true
        this.currentEditItem = {
          imageUrl: '',
          link: '',
        }
        this.file = null
      },
      handleVisibleChange(visible) {
        if (!visible) {
          this.handleCancel()
        }
      },
      handleCancel() {
        this.modalEditBanner = false
      },
      handleOK() {
        const msg = this.check()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        if (this.currentEditItem.id) {
          const theEditingItemIdx = this.bannerList.findIndex(item => item.id === this.currentEditItem.id)
          if (theEditingItemIdx > -1) {
            this.bannerList.splice(theEditingItemIdx, 1, this.currentEditItem)
          }
        } else {
          this.bannerList.push(this.currentEditItem)
        }
        apiSaveImages({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
          images: this.bannerList,
        }).then(() => {
          this.getBanners()
          this.modalEditBanner = false
        })
      },
      check() {
        if (!this.currentEditItem.imageUrl) {
          return '请上传图片'
        }
        if (!this.currentEditItem.link) {
          return '请输入链接'
        }
        return ''
      },
      onFileSelected(file) {
        this.file = file

        if (file) {
          apiUploadImage({
            stageId: this.currentStageSubject.stageId,
            subjectId: this.currentStageSubject.subjectId,
            file,
          }).then(res => {
            this.currentEditItem.imageUrl = res
          })
        } else {
          this.currentEditItem.imageUrl = ''
        }
      },
      editBannerItem(item) {
        this.currentEditItem = deepCopy(item)
        this.modalEditBanner = true
      },
      deleteBannerItem(item) {
        this.$Modal.confirm({
          title: '删除',
          content: '确定删除该图片吗？',
          onOk: () => {
            const theDelItemIdx = this.bannerList.findIndex(banner => banner.id === item.id)
            if (theDelItemIdx > -1) {
              this.bannerList.splice(theDelItemIdx, 1)
              apiSaveImages({
                stageId: this.currentStageSubject.stageId,
                subjectId: this.currentStageSubject.subjectId,
                images: this.bannerList,
              }).then(() => {
                this.getBanners()
              })
            }
          },
        })
      },
      onTableDrag(sourceIndex, targetIndex) {
        const sourceItem = this.bannerList[sourceIndex]
        const targetItem = this.bannerList[targetIndex]
        this.bannerList[sourceIndex] = targetItem
        this.bannerList[targetIndex] = sourceItem

        apiSaveImages({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
          images: this.bannerList,
        }).then(() => {
          this.getBanners()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-banner-edit {
    .banner-img {
      width: 500px;
    }

    .box-tool {
      @include flex(row, space-between, center);
      margin-bottom: 10px;
    }
  }

  .modal-edit-banner {
    .box-image {
      margin-top: 10px;
    }

    .uploaded-image {
      width: 300px;
    }
  }
</style>
