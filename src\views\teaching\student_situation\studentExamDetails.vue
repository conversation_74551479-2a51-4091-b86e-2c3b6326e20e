<template>
  <div class="subject-exam-wrapper">
    <div class="situation-summary-box">
      <div class="head">
        <h3 class="head-title">{{ getStudentName }}</h3>
        <div class="head-action-box">
          <div class="search-box">
            <Input
              v-model="studentNameForSearch"
              placeholder="输入学生全名进行搜索"
              @on-click="selectStudentByInput"
              @on-enter="selectStudentByInput"
            >
              <template #suffix><Icon class="icon-ios-search" type="ios-search"></Icon></template
            ></Input>
          </div>
        </div>
      </div>
    </div>
    <div class="students-knowledge-score-box">
      <div class="side-panel">
        <template v-if="theStudentList.length">
          <div
            v-for="item in theStudentList"
            :key="item.studentId"
            class="student-item"
            :class="item.studentId == currentStudent ? 'student-item-active' : null"
            @click="selectStudent(item)"
          >
            <span class="student-item-name">{{ item.studentName }}</span>
          </div>
        </template>
        <NoData v-else :show-image="false">暂无学生数据</NoData>
      </div>
      <div class="main-panel">
        <div class="block-box">
          <div class="block-head">
            <h4 class="block-title">成绩趋势</h4>
          </div>
          <div class="block-body">
            <template v-if="trendChartOption">
              <div class="exam-info-wrapper">
                <div class="exam-info-text-box">
                  <h4 class="exam-name">{{ currentExam.templateName }}</h4>
                  <div class="score-box">
                    <span class="score-item">
                      得分：
                      <span class="score-item-text">{{ currentExam.score }}</span>
                    </span>
                    <span class="score-item">
                      班级平均分：
                      <span class="score-item-text">{{ currentExam.clsAvg }}</span>
                    </span>
                    <span class="score-item">
                      班级名次：
                      <span class="score-item-text">{{ currentExam.classRank }}</span>
                    </span>
                  </div>
                </div>
              </div>
              <div id="trend-chart-box" class="trend-chart-box"></div>
            </template>
            <div v-else>
              <NoData :show-image="false">当前暂无数据喔~</NoData>
            </div>
          </div>
        </div>
        <div class="block-box">
          <div class="block-head">
            <h4 class="block-title">知识点掌握情况</h4>
          </div>
          <div class="block-body">
            <div v-if="theStudentKnowledge.length" class="student-knowledge-box">
              <div class="label-box">
                <span class="label-item">
                  <i class="label-item-color label-item-color-student"></i>
                  <span class="label-item-text">学生得分率</span>
                </span>
                <span class="label-item">
                  <i class="label-item-color label-item-color-class"></i>
                  <span class="label-item-text">班级得分率</span>
                </span>
                <span class="label-item">
                  <i class="label-item-color label-item-color-grade"></i>
                  <span class="label-item-text">年级得分率</span>
                </span>
              </div>
              <div class="knowledge-content-box">
                <div v-for="item in theStudentKnowledge" :key="item.knowledgeId" class="knowledge-item">
                  <div class="knowledge-item-name" :title="item.knowledgeName">{{ item.knowledgeName }}</div>
                  <div class="knowledge-item-score">
                    <div class="knowledge-personal-score">
                      <Progress :percent="Number((item.scoreRate * 100).toFixed(2))" stroke-color="#ff9900">
                        <span class="ivu-progress-text-custom">{{
                          Number((item.scoreRate * 100).toFixed(2)) + '%'
                        }}</span>
                      </Progress>
                    </div>
                    <div class="knowledge-class-score">
                      <Progress :percent="Number((item.clsScoreRate * 100).toFixed(2))" stroke-color="#2db7f5" />
                    </div>
                    <div class="knowledge-grade-score">
                      <Progress :percent="Number((item.schScoreRate * 100).toFixed(2))" stroke-color="#19be6b" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <NoData v-else :show-image="false">暂无学生知识点数据</NoData>
          </div>
        </div>
        <div class="block-box">
          <div class="block-head">
            <h4 class="block-title">错题本</h4>
          </div>
          <div class="block-body">
            <topic-list
              :topic-info="theStudentTopicsInfo"
              :topic-list="topicDetailsList"
              :topic-type="topicType"
              :topic-types="topicTypes"
              :show-rate="true"
              :show-basket="showBasket"
              :topic-current-page="topicCurrentPage"
              @on-change-rate="onChangeRate"
              @on-change-type="onChangeQuestionType"
              @on-change-visible="onChangeSolutionVisible"
            />
            <div v-if="topicDetailsList.length" class="page-box">
              <Page
                :page-size="10"
                :model-value="topicCurrentPage"
                :total="theStudentTopicsInfo.page ? theStudentTopicsInfo.page.total : 0"
                size="small"
                show-elevator
                @on-change="changePage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { nextTick } from 'vue'
  import { mapState } from 'vuex'
  import NoData from '@/components/no_data'
  import TopicList from '../components/topic_list'

  import { formatDate } from '@/utils/date'
  import { roundScore } from '@/utils/math'
  import echarts from '@/utils/echarts'
  import ModuleEnum from '@/enum/user/module'
  import {
    apiGetTheStudentSubjectTrend,
    apiGetTheStudentList,
    apiGetTheStudentTopics,
    apiGetTheStudentKnowledge,
  } from '@/api/report'
  import { apiGetQuestionById } from '@/api/qlib'

  export default {
    name: 'StudentExamDetails',
    components: {
      NoData,
      TopicList,
    },
    data() {
      return {
        currentStudent: this.$route.params.studentId || null,
        topicDetailsList: [],
        quesTypeId: null,
        topicType: '0',
        fromRate: 0,
        toRate: 0.6,
        topicPageSize: 10,
        topicCurrentPage: 1,
        studentNameForSearch: '',
        currentExam: {},
        theStudentList: [],
        theStudentExamTrend: [],
        theStudentsKnowledge: [],
        theStudentTopicsInfo: {},
        chartHistoryTrend: null,
      }
    },
    computed: {
      ...mapState('teaching', {
        classInfo: 'classInfo',
        templateIds: 'templateIds',
      }),
      showBasket() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.Question.id)
      },
      scoreRateList() {
        return [
          {
            value: '0',
            label: '0~0.6',
            fromRate: 0,
            toRate: 0.6,
          },
          {
            value: '1',
            label: '0.6~0.7',
            fromRate: 0.6,
            toRate: 0.7,
          },
          {
            value: '2',
            label: '0.7~0.85',
            fromRate: 0.7,
            toRate: 0.85,
          },
          {
            value: '3',
            label: '0.85~1.0',
            fromRate: 0.85,
            toRate: 1.0,
          },
          {
            value: '4',
            label: '0~1.0',
            fromRate: 0,
            toRate: 1.0,
          },
        ]
      },
      theStudentKnowledge() {
        return this.theStudentsKnowledge.knows || []
      },
      topicTypes() {
        let questionTypesObj = this.theStudentTopicsInfo.quesTypeMap
        if (questionTypesObj) {
          questionTypesObj['0'] = '全部题型'
          return questionTypesObj
        } else {
          return {}
        }
      },
      schoolId() {
        return this.$store.getters['user/info'].schoolId
      },
      getStudentName() {
        const theStudent = this.theStudentList.find(s => s.studentId === this.currentStudent)
        return theStudent?.studentName || ''
      },
      trendChartOption() {
        const { theStudentExamTrend, theStudentList } = this
        if (!theStudentExamTrend || !theStudentExamTrend.length) {
          return null
        }

        return {
          tooltip: {
            show: true,
            trigger: 'item',
            axisPointer: {
              type: 'line',
            },
            backgroundColor: 'rgba(245, 245, 245, 0.8)',
            borderWidth: 1,
            borderColor: '#ccc',
            padding: 10,
            textStyle: {
              color: '#000',
            },
            // extraCssText: 'width: 170px',
            formatter: params => {
              const extraData = params.data.extraData
              this.currentExam = extraData
              return `${extraData.templateName}<br />得分： ${extraData.score}<br />班级平均分：${extraData.clsAvg}<br />班级名次：${extraData.classRank}`
            },
            confine: true,
          },
          xAxis: {
            type: 'category',
            show: true,
            boundaryGap: false,
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            data: theStudentExamTrend.map(exam => formatDate(new Date(exam.createTime))),
          },
          yAxis: {
            type: 'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgb(211, 211, 211)',
              },
            },
            axisLabel: {
              show: true,
              interval: 'auto',
              formatter: '{value}',
              color: () => {
                return '#000000'
              },
            },
            max: theStudentList.length,
            interval: 10,
            inverse: true,
          },
          dataZoom: [
            {
              show: theStudentExamTrend.length > 1,
            },
          ],
          grid: {
            left: '5%',
            right: '5%',
            top: '6%',
          },
          series: [
            {
              type: 'line',
              data: theStudentExamTrend.map(exam => {
                return {
                  value: exam.classRank,
                  itemStyle: {
                    color: '#05d1bc',
                  },
                  symbolSize: 8,
                  symbol: 'circle',
                  extraData: exam,
                }
              }),
              areaStyle: {},
              lineStyle: {
                color: '#05d1bc',
              },
              itemStyle: {
                borderColor: '#05d1bc',
              },
              smooth: true,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(5, 209, 188, 0.35)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 255, 255, 0.25)',
                  },
                ],
                global: false,
              },
            },
          ],
        }
      },
      actionData() {
        return {
          classInfo: this.classInfo,
          templateIds: this.templateIds,
        }
      },
    },
    watch: {
      actionData: {
        handler: function () {
          this.isTheOneClass = this.classInfo.length && this.classInfo[1] ? true : false
          this.currentStudent = ''
          this.resetStore()
          this.refreshRequest()
        },
        deep: true,
      },
      theStudentExamTrend: {
        handler: function (val) {
          if (val && val.length) {
            this.currentExam = val[val.length - 1]
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.currentStudent = this.$route.params.studentId
      this.refreshRequest()
    },
    methods: {
      resetStore() {
        this.theStudentList = []
        this.theStudentExamTrend = []
        this.theStudentsKnowledge = []
        this.theStudentTopicsInfo = {}
        this.topicDetailsList = []
      },
      getTheStudentSubjectTrend() {
        const { schoolId, classInfo, templateIds, currentStudent } = this
        if (!classInfo.length) return
        if (!currentStudent) return

        const [gradeId, classId, subjectId] = classInfo

        return apiGetTheStudentSubjectTrend({
          studentId: currentStudent,
          temps: templateIds,
          schoolId,
          gradeId,
          classId,
          subjectId,
        }).then(res => {
          this.theStudentExamTrend = res
            .map(item => ({
              ...item,
              clsAvg: roundScore(item.clsAvg),
            }))
            .sort((a, b) => (a.createTime > b.createTime ? 1 : -1))
          nextTick(() => {
            this.drawChart()
          })
        })
      },
      getTheStudentList() {
        const { schoolId, classInfo, templateIds } = this
        if (!classInfo.length) return

        const [gradeId, classId, subjectId] = classInfo

        return apiGetTheStudentList({
          temps: templateIds,
          schoolId,
          gradeId,
          classId,
          subjectId,
        }).then(res => {
          this.theStudentList = res || []
          return res
        })
      },
      getTheStudentKnowledge() {
        const { schoolId, classInfo, templateIds, currentStudent } = this
        if (!classInfo.length) return
        if (!currentStudent) return

        const [gradeId, classId, subjectId] = classInfo

        return apiGetTheStudentKnowledge({
          temps: templateIds,
          studentId: currentStudent,
          schoolId,
          gradeId,
          classId,
          subjectId,
        }).then(res => {
          this.theStudentsKnowledge = res || []
        })
      },
      async getTheStudentTopics() {
        const {
          schoolId,
          classInfo,
          topicPageSize,
          topicCurrentPage,
          fromRate,
          toRate,
          templateIds,
          quesTypeId,
          currentStudent,
        } = this
        if (!classInfo.length) return
        if (!currentStudent) return

        const [gradeId, classId, subjectId] = classInfo

        const data = await apiGetTheStudentTopics({
          studentId: currentStudent,
          temps: templateIds,
          current: topicCurrentPage,
          size: topicPageSize,
          schoolId,
          gradeId,
          classId,
          subjectId,
          fromRate,
          toRate,
          quesTypeId: quesTypeId == 0 ? null : quesTypeId,
        })

        const { page } = data
        this.theStudentTopicsInfo = data

        if (page.records && page.records.length) {
          page.records.forEach(item => {
            if (!data.quesTypeMap[this.topicType]) {
              this.topicType = '0'
            }
            if (item.questionId && item.questionId != '0') {
              apiGetQuestionById(item.questionId).then(res => {
                let noExplaintion = false
                res.branches = res.branches.map(item => {
                  noExplaintion = !item.explanation ? true : false
                  return item
                })

                if (noExplaintion) {
                  res.noExplaintion = true
                }
                res.scoreRate = item.scoreRate
                res.isTheOneClass = true
                res.isPersonalScoreRate = true
                this.topicDetailsList = this.topicDetailsList.concat(res)
              })
            }
          })
        }
      },
      drawChart() {
        if (this.trendChartOption) {
          if (this.chartHistoryTrend) {
            this.chartHistoryTrend.dispose()
          }
          this.chartHistoryTrend = echarts.init(document.getElementById('trend-chart-box'))
          this.chartHistoryTrend.setOption(this.trendChartOption)
        }
      },
      async refreshRequest() {
        const { classInfo, currentStudent } = this
        if (!classInfo.length) return

        if (this.templateIds.length) {
          let studentList = await this.getTheStudentList()
          if (!currentStudent) {
            if (studentList && studentList.length) {
              let studentId = studentList[0].studentId
              this.currentStudent = studentId
            }
          }

          this.getTheStudentSubjectTrend()
          this.getTheStudentKnowledge()
          this.getTheStudentTopics()
          return
        }
      },
      selectStudent(item) {
        if (item.studentId == this.currentStudent) return
        this.currentStudent = item.studentId
        this.topicDetailsList = []
        // this.refreshRequest()
        this.getTheStudentSubjectTrend()
        this.getTheStudentKnowledge()
        this.getTheStudentTopics()
      },
      selectStudentByInput() {
        let targetStudent = this.theStudentList.find(x => x.studentName === this.studentNameForSearch.trim())
        if (targetStudent) {
          this.selectStudent(targetStudent)
          this.$Message.success({
            duration: 2,
            content: '已匹配学生',
          })
          this.studentNameForSearch = ''
        } else {
          this.$Message.warning({
            duration: 4,
            content: '无匹配学生',
          })
        }
      },
      onChangeRate(value) {
        this.fromRate = this.scoreRateList[value].fromRate
        this.toRate = this.scoreRateList[value].toRate
        this.topicCurrentPage = 1

        this.changePage(1)
      },
      onChangeQuestionType(value) {
        this.quesTypeId = value
        this.topicType = value
        this.topicCurrentPage = 1

        this.changePage(1)
      },
      onChangeSolutionVisible(value) {
        const theItem = this.topicDetailsList.find(item => item.id === value)

        if (theItem) {
          theItem.isChecking = !theItem.isChecking
        }
      },
      changePage(page) {
        this.topicCurrentPage = page
        this.topicDetailsList = []
        this.getTheStudentTopics()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .subject-exam-wrapper {
    margin-top: 10px;

    .page-box {
      margin-top: 30px;
      text-align: right;
    }

    .no-data {
      .no-data-text {
        color: #999;
        font-size: 16px;
        text-align: center;
      }
    }

    .situation-summary-box {
      margin-bottom: 15px;
      padding: 20px;
      background-color: #fff;

      .head {
        @include flex(row, space-between, center);
        // margin-bottom: 20px;

        .head-title {
          font-size: 25px;
        }

        .head-action-box {
          @include flex(row, center, center);

          .search-box {
            width: 200px;
            margin-right: 10px;
          }

          .select-box {
            margin-right: 10px;
          }

          .cascader-box {
            width: 250px;
            margin-right: 10px;
          }
        }
      }
    }

    .students-knowledge-score-box {
      @include flex(row, flex-start, flex-start);

      .side-panel {
        position: sticky;
        top: 60px;
        width: 200px;
        min-height: 300px;
        max-height: 500px;
        max-height: calc(100vh - 125px);
        margin-right: 20px;
        overflow: auto;
        background-color: #fff;
        cursor: pointer;

        .student-item {
          @include flex(row, flex-start, center);
          padding: 0 20px;
          cursor: pointer;

          .student-item-name {
            display: inline-block;
            width: 100%;
            padding: 20px 0;
            border-bottom: 1px solid #e7e7e7;
            font-size: 14px;
            text-align: center;
          }
        }

        .student-item-active {
          background-color: rgba(5, 193, 174, 0.2);
        }
      }

      .main-panel {
        flex: 1;

        .block-box {
          margin-bottom: 15px;
          padding: 20px 15px;
          background-color: #fff;

          .block-head {
            @include flex(row, flex-start, center);
            position: relative;
            margin-bottom: 20px;
            padding-left: 20px;

            &::before {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              width: 4px;
              height: 24px;
              margin: auto;
              background-color: $color-primary;
              content: '';
            }

            .block-title {
              margin-right: 10px;
              font-size: 18px;
            }
          }

          .exam-info-wrapper {
            @include flex(row, space-between, center);
            padding: 0 70px;

            .exam-info-text-box {
              max-width: 600px;

              .exam-name {
                margin-bottom: 10px;
                font-size: 16px;
              }

              .score-box {
                .score-item {
                  margin-right: 30px;

                  &:last-child {
                    margin-right: 0;
                  }

                  .score-item-text {
                    font-weight: bold;
                  }
                }
              }
            }

            .btn-box {
              padding-right: 30px;

              .btn-checkOriginPaper {
                font-weight: bold;
                font-size: 16px;
                cursor: pointer;

                .icon-arrow {
                  color: #999;
                }

                &:hover {
                  color: $color-primary;
                }
              }
            }
          }

          .trend-chart-box {
            height: 500px;
          }

          .student-knowledge-box {
            .label-box {
              @include flex(row, flex-end, center);
              margin-bottom: 10px;

              .label-item {
                margin-left: 10px;
              }

              .label-item-color {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 3px;
              }

              .label-item-color-student {
                background-color: #f90;
              }

              .label-item-color-class {
                background-color: #2db7f5;
              }

              .label-item-color-grade {
                background-color: #19be6b;
              }
            }

            .knowledge-content-box {
              @include flex(row, space-between, center);
              flex-wrap: wrap;

              .knowledge-item {
                width: 48%;
                margin-bottom: 20px;

                .knowledge-item-name {
                  margin-bottom: 10px;
                  padding-right: 40px;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .ivu-progress-text-custom {
                  color: #808695;
                }
              }
            }
          }
        }
      }
    }
  }

  .icon-ios-search:hover {
    cursor: pointer;
  }
</style>
