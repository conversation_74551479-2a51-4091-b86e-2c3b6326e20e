<script>
  import LayoutHeader from '@/components/layout_header'
  import ModalChangeActivityStatus from '../components/modal_change_activity_status.vue'

  import { apiDeleteActivityProject } from '@/api/review'

  import iView from '@/iview'
  import Store from '@/store/index'
  import PageCache from '@/utils/page_cache'
  import { backOrDefault } from '@/utils/router'
  import { mapGetters } from 'vuex'
  import ReviewProjectStatus from '@/enum/review/review_project_status'

  const UniquePageName = 'review-activity'

  export default {
    components: {
      'layout-header': LayoutHeader,
      'modal-change-activity-status': ModalChangeActivityStatus,
    },

    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      Store.commit('review/clear')
      Store.dispatch('review/fetchActivityDetail', to.params.activityId)
        .then(() => next())
        .catch(() => iView.LoadingBar.error())
        .finally(() => iView.LoadingBar.finish())
    },

    data() {
      return {
        isModalChangeActivityStatusShowed: false,
      }
    },

    computed: {
      ...mapGetters('review', [
        'currentActivity',
        'activityName',
        'activityId',
        'isCreator',
        'isAdmin',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'isRegisterAuditor',
        'enableSchoolAudit',
        'needAudit',
      ]),

      activityStatusName() {
        return this.currentActivity?.statusName || ''
      },

      navs() {
        let list = [
          {
            name: 'review_detail',
            label: '项目信息',
            visible: true,
          },
          {
            name: 'review_personnel_management',
            label: '人员管理',
            visible: this.isAdmin || this.isSchoolAdmin,
          },
          {
            name: 'review_registration_management',
            label: '报名管理',
            visible: this.isAdmin || this.isSchoolAdmin || this.isSchoolPersonInCharge,
          },
          {
            name: 'review_registration_approval',
            label: '报名审核',
            visible:
              this.needAudit &&
              (this.isAdmin ||
                this.isRegisterAuditor ||
                ((this.isSchoolAdmin || this.isSchoolPersonInCharge) && this.enableSchoolAudit)),
          },
          {
            name: 'review_setting',
            label: '评审设置',
            visible: this.isAdmin,
          },
          {
            name: 'review_judge',
            label: '评委管理',
            visible: this.isAdmin,
          },
          {
            name: 'review_monitor',
            label: '评审监控',
            visible: this.isAdmin,
            disabled: !this.reviewMonitorVisible,
          },
          {
            name: 'review_award',
            label: '评审结果',
            visible: this.isAdmin,
          },
        ]

        return list.filter(x => x.visible)
      },

      activeNavName() {
        const matched = this.$route.matched
        return this.navs.find(nav => nav.visible && !nav.disabled && matched.some(m => m.name === nav.name))?.name || ''
      },

      reviewMonitorVisible() {
        return this.currentActivity?.status >= ReviewProjectStatus.UnderReview.id
      },
    },

    created() {
      if (this.activeNavName) {
        return
      }
      let initNav
      if (this.$route.name === 'review_activity') {
        const PageCacheActivityNavName = PageCache.fetch(UniquePageName)
        initNav =
          this.navs.find(nav => nav.visible && !nav.disabled && nav.name === PageCacheActivityNavName) || this.navs[0]
      } else {
        initNav = this.navs[0]
      }
      this.changeNav(initNav)
    },

    methods: {
      routeBack() {
        backOrDefault(this.$router, { name: 'review' })
      },
      changeNav(nav) {
        if (nav && nav.visible && !nav.disabled && nav.name !== this.activeNavName) {
          this.$router.replace({
            name: nav.name,
          })
          PageCache.save(UniquePageName, nav.name)
        }
      },
      delteActivityProject() {
        if (!this.activityId) {
          return
        }
        this.$Modal.confirm({
          title: '删除评审活动项目',
          content: '您确定要删除本评审活动项目吗？删除后不可恢复',
          onOk: () =>
            apiDeleteActivityProject(this.activityId).then(() => {
              this.$Message.success({
                duration: 3,
                content: '已删除',
              })
              this.routeBack()
            }),
        })
      },
    },
  }
</script>

<template>
  <div class="container-activity">
    <layout-header class="activity-header-bar" sticky :top="0" background>
      <div class="info-panel">
        <TextButton type="primary" icon="ios-arrow-back" title="评审活动列表" @click="routeBack"></TextButton>
        <span style="margin: 0 10px">{{ activityName }}</span>
        <span class="description">{{ '报名截止时间：' + (currentActivity?.registerEndTime || '').slice(0, 10) }}</span>
        <span v-show="activityStatusName" class="status-tag">{{ activityStatusName }}</span>

        <!-- <span class="description">{{ '创建人：' + (currentActivity?.createByName || '') }}</span> -->
      </div>
      <div class="t-btns">
        <TextButton v-if="isAdmin" type="primary" @click="isModalChangeActivityStatusShowed = true"
          >活动状态设置</TextButton
        >
        <TextButton v-if="isCreator" type="error" @click="delteActivityProject">删除项目</TextButton>
      </div>
    </layout-header>

    <div class="nav-menu-bar">
      <div
        v-for="nav of navs"
        :key="nav.name"
        :class="{ 'active-nav': nav.name === activeNavName, 'disabled-nav': nav.disabled }"
        class="nav-item"
        @click="changeNav(nav)"
      >
        {{ nav.label }}
      </div>
    </div>

    <router-view class="nav-content"></router-view>

    <modal-change-activity-status v-model="isModalChangeActivityStatusShowed"></modal-change-activity-status>
  </div>
</template>

<style lang="scss" scoped>
  .container-activity {
    position: relative;

    .activity-header-bar {
      @include flex(row, space-between, center);
      z-index: 2;

      .info-panel {
        z-index: 100;
        height: 50px;
        font-size: $font-size-large;
        line-height: 50px;

        .status-tag {
          height: 25px;
          margin-left: 10px;
          padding: 4px 10px 4px 10px;
          border: 1px solid $color-primary;
          border-color: $color-primary-light;
          border-radius: 2px;
          color: $color-primary-light;
          font-size: 12px;
          background-color: #f0f9eb;
          user-select: none;
        }

        .description {
          display: inline-block;
          margin-left: 10px;
          color: $color-icon;
          font-size: $font-size-small;
        }
      }
    }

    .nav-menu-bar {
      @include flex(row, space-between, center);
      margin: 10px 0;
      font-size: $font-size-medium-x;
      background-color: #ffffff;
      user-select: none;

      .nav-item {
        flex: 1;
        height: 46px;
        line-height: 46px;
        text-align: center;

        &:hover {
          cursor: pointer;
        }
      }

      .active-nav {
        color: $color-primary;
        font-weight: 600;
      }

      .disabled-nav {
        color: $color-disabled;
      }
    }
  }
</style>
