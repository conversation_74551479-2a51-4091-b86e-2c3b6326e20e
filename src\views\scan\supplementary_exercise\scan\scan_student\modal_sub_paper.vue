<template>
  <Modal
    :model-value="modelValue"
    :mask-closable="false"
    fullscreen
    footer-hide
    :closable="false"
    class="modal-scan-sub-paper"
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <span class="modal-header-title">查看子图</span>
        <template v-if="currentStudent">
          <div class="stu-info">
            <span class="stu-info-item">学校：{{ currentStudent.schoolName }}</span>
            <span class="stu-info-item">姓名：{{ currentStudent.studentName }}</span>
            <span class="stu-info-item">考号：{{ currentStudent.admissionNum }}</span>
            <span class="stu-info-item">考场：{{ currentStudent.roomNo }}</span>
            <span class="stu-info-item">座号：{{ currentStudent.seatNum }}</span>
            <span class="stu-info-item">
              <span v-if="currentStudent.isMarkAbsent" class="status-warning">[标记缺考]</span>
              <span v-else-if="currentStudent.isEmpty" class="status-warning">[未扫]</span>
              <span v-if="currentStudent.isScanAbsent" class="status-warning">[扫描缺考]</span>
              <span v-if="currentStudent.isDuplicate" class="status-error">[多页]</span>
              <span v-if="currentStudent.isMissing" class="status-error">[缺页]</span>
            </span>
          </div>
          <div class="nav">
            <Button
              type="text"
              icon="ios-arrow-back"
              :disabled="!hasPrevious"
              title="上一人"
              @click="goPrevious"
            ></Button>
            <Button type="text" icon="ios-arrow-forward" :disabled="!hasNext" title="下一人" @click="goNext"></Button>
          </div>
        </template>
        <span class="switch">
          <span class="label">只看已扫</span>
          <i-switch :model-value="skipNotScan" @on-change="changeSkipNotScan"></i-switch>
        </span>
        <Button size="small" type="primary" class="btn-refresh" @click="refresh">刷新</Button>
        <span class="modal-header-icon" @click="closeModal"><Icon type="ios-close" /></span>
      </div>
    </template>
    <div class="modal-content">
      <div class="panel panel-left">
        <div class="panel-header">
          <span class="label">原图</span>
          <Select
            v-model="showImagePageIndexList"
            multiple
            clearable
            :max-tag-count="2"
            placeholder="不限页面"
            class="select"
            @on-change="changeSelectedPages"
          >
            <Option v-for="p in templatePages" :key="p.pageIndex" :value="p.pageIndex">{{ p.pageName }}</Option>
          </Select>
        </div>
        <div class="panel-body">
          <div v-for="p in currentStudentShowImagePages" :key="p.id" class="page relative">
            <span class="page-name label">{{ p.pageName }}</span>
            <img :src="p.imgUrl" />
          </div>
        </div>
      </div>
      <div class="panel panel-right">
        <div class="panel-header">
          <span class="label">子图</span>
          <Select
            v-model="showImageBlockIdList"
            multiple
            clearable
            :max-tag-count="2"
            placeholder="不限题块"
            class="select"
          >
            <Option v-for="block in blockDefine" :key="block.blockId" :value="block.blockId">{{
              block.blockName
            }}</Option>
          </Select>
        </div>
        <div class="panel-body">
          <div v-for="block in currentStudentShowSubPapers" :key="block.blockId" class="sub-paper relative">
            <span class="block-name label"
              >{{ block.blockName
              }}<span v-if="block.subPapers.length == 0">{{ block.notCheck ? '（未选做）' : '（无子图）' }}</span></span
            >
            <img v-for="s in block.subPapers" :key="s.index" :src="s.imgUrl" />
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
  import { apiGetStudentSubPapers } from '@/api/scan/scanStu'

  import { mapGetters } from 'vuex'
  import { groupArray } from '@/utils/array'
  import { replaceBlockImageUrlPixelTypeKeyword } from '@/helpers/emarking/block_image'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      roomStudents: Array,
      initStudentId: String,
    },
    emits: ['update:modelValue', 'refresh'],
    data() {
      return {
        // 当前学生Id
        currentStudentId: '',
        // 跳过未扫
        skipNotScan: true,
        // 当前学生子图
        currentStudentSubPapers: [],
        // 显示原图的页面序号列表
        showImagePageIndexList: [],
        // 显示子图的题块Id列表
        showImageBlockIdList: [],
        // 加载中
        loading: false,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'blockDefine', 'templatePageCount']),
      /**
       * 特殊演示学校
       */
      isIgradeShowcaseSchool() {
        return this.$store.getters['user/isIgradeShowcaseSchool']
      },
      // 模板页面
      templatePages() {
        let pages = []
        for (let i = 1; i <= this.templatePageCount; i++) {
          pages.push({
            pageIndex: i,
            pageName: `第 ${i} 面`,
          })
        }
        return pages
      },
      // 浏览学生列表
      studentList() {
        if (!this.skipNotScan) {
          return this.roomStudents
        } else {
          return this.roomStudents.filter(stu => !stu.isMarkAbsent && !stu.isEmpty)
        }
      },
      // 当前学生
      currentStudent() {
        return this.studentList.find(s => s.studentId == this.currentStudentId)
      },
      // 当前学生显示的原图
      currentStudentShowImagePages() {
        if (!this.currentStudent || this.currentStudent.isEmpty || this.currentStudent.isMarkAbsent) {
          return []
        }
        let pages = []
        this.currentStudent.scanUnits.forEach(unit => {
          let pageNos = unit.pageNos.sort()
          unit.adjustImgUrls.forEach((imgUrl, idx) => {
            let pageIndex = pageNos[idx]
            if (
              this.showImagePageIndexList &&
              this.showImagePageIndexList.length > 0 &&
              !this.showImagePageIndexList.includes(pageIndex)
            ) {
              return
            }
            pages.push({
              unitId: unit.unitId,
              pageIndex,
              pageName: `第 ${pageIndex} 面`,
              imgUrl,
            })
          })
        })
        // 先按页面顺序排，再按扫描单元排
        let result = []
        pages.sort((a, b) => b.pageIndex - a.pageIndex)
        groupArray(pages, x => x.pageIndex)
          .sort((a, b) => a.key - b.key)
          .forEach(({ group }) => {
            if (group.length > 1) {
              group.sort((a, b) => {
                return a.unitId < b.unitId ? -1 : 1
              })
            }
            group.forEach((p, idx) => {
              result.push({
                id: `${p.pageIndex}-${idx + 1}`,
                ...p,
              })
            })
          })
        return result
      },
      // 当前学生显示的子图
      currentStudentShowSubPapers() {
        if (!this.currentStudent || this.currentStudent.isEmpty || this.currentStudent.isMarkAbsent) {
          return []
        }
        let result = []
        this.blockDefine.forEach(block => {
          if (
            this.showImageBlockIdList &&
            this.showImageBlockIdList.length > 0 &&
            !this.showImageBlockIdList.includes(block.blockId)
          ) {
            return
          }
          let subPapers = this.currentStudentSubPapers.filter(s => s.subjectiveId == block.blockId)
          // 剔除未选做的
          let notCheck = false
          if (subPapers.length > 0) {
            subPapers = subPapers.filter(s => s.check != false)
            if (subPapers.length == 0) {
              notCheck = true
            }
          }
          // 多个扫描单元按扫描单元排，子图跨页的按页面顺序排
          subPapers.sort((a, b) => {
            if (a.unitId == b.unitId) {
              return a.pageNo - b.pageNo
            } else {
              return a.unitId < b.unitId ? -1 : 1
            }
          })
          subPapers.forEach((s, idx) => {
            s.index = idx + 1
            if (this.isIgradeShowcaseSchool) {
              const TargetBlockDefine = (this.blockDefine || []).find(define => define.blockId === s.subjectiveId)
              s.imgUrl = replaceBlockImageUrlPixelTypeKeyword(
                s.imgUrl,
                TargetBlockDefine && TargetBlockDefine.imgPixelType
              )
            }
          })
          result.push({
            ...block,
            subPapers,
            notCheck,
          })
        })
        return result
      },
      // 当前学生序号
      currentStudentIndex() {
        return this.studentList.findIndex(s => s.studentId == this.currentStudentId)
      },
      // 是否有下一份
      hasNext() {
        return this.currentStudentIndex >= 0 && this.currentStudentIndex < this.studentList.length - 1
      },
      // 是否有上一份
      hasPrevious() {
        return this.currentStudentIndex > 0
      },
    },
    watch: {
      roomStudents() {
        if (this.modelValue) {
          this.handleRoomStudentsRefreshed()
        }
      },
    },
    methods: {
      /**
       * 初始化
       */
      init() {
        this.currentStudentId = ''
        this.currentStudentSubPapers = []
        this.showImagePageIndexList = []
        this.showImageBlockIdList = []
        this.loading = false
        this.setDefaultStudentId(this.initStudentId)
      },
      /**
       * 设置默认学生
       */
      setDefaultStudentId(studentId) {
        if (this.studentList.some(stu => stu.studentId == studentId)) {
          this.changeCurrentStudentId(studentId)
        } else if (this.studentList.length > 0) {
          this.changeCurrentStudentId(this.studentList[0].studentId)
        } else {
          this.changeCurrentStudentId('')
        }
      },
      /**
       * 设置当前学生
       */
      changeCurrentStudentId(studentId) {
        if (!studentId) {
          this.currentStudentId = ''
          this.currentStudentSubPapers = []
          return
        }
        this.loading = true
        apiGetStudentSubPapers({ examSubjectId: this.examSubjectId, studentId })
          .then(subPapers => {
            this.currentStudentId = studentId
            this.currentStudentSubPapers = subPapers
          })
          .catch(err => {
            this.currentStudentId = studentId
            this.currentStudentSubPapers = []
            throw err
          })
          .finally(() => {
            this.loading = false
          })
      },
      /**
       * 切换跳过未扫
       */
      changeSkipNotScan(value) {
        if (this.loading) {
          return
        }
        this.skipNotScan = value
        this.setDefaultStudentId(this.currentStudentId)
      },
      /**
       * 上一人下一人导航
       */
      goPrevious() {
        this.go(-1)
      },
      goNext() {
        this.go(1)
      },
      go(delta) {
        let index = this.currentStudentIndex + delta
        let stu = this.studentList[index]
        if (stu) {
          this.changeCurrentStudentId(stu.studentId)
        }
      },
      handleKeydown(e) {
        if (e.key == 'ArrowLeft') {
          this.goPrevious()
        } else if (e.key == 'ArrowRight') {
          this.goNext()
        }
      },
      /**
       * 选择原图页面子图联动
       */
      changeSelectedPages() {
        let blockIdSet = new Set()
        this.showImagePageIndexList.forEach(pageIndex => {
          this.$store.getters['scan/pageBlockIds'](pageIndex).forEach(blockId => {
            blockIdSet.add(blockId)
          })
        })
        this.showImageBlockIdList = Array.from(blockIdSet)
      },
      /**
       * 关闭弹窗
       */
      handleVisibleChange(visibility) {
        if (visibility) {
          this.init()
          document.body.addEventListener('keydown', this.handleKeydown)
        } else {
          document.body.removeEventListener('keydown', this.handleKeydown)
          this.closeModal()
        }
      },
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      /**
       * 刷新
       */
      refresh() {
        this.$emit('refresh')
      },
      handleRoomStudentsRefreshed() {
        this.setDefaultStudentId(this.currentStudentId)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    @include flex(row, flex-start, center);
    height: 24px;
    margin-right: -8px;

    .modal-header-title {
      margin-right: auto;
      color: $color-title;
      font-size: $font-size-medium-x;
    }

    .stu-info-item {
      margin-left: 15px;

      .status-warning {
        color: $color-warning;
      }

      .status-error {
        color: $color-error;
      }
    }

    .nav {
      margin-right: 30px;
      margin-left: 30px;
    }

    .switch {
      margin-right: 30px;
    }

    .btn-refresh {
      margin-right: 30px;
    }

    .modal-header-icon {
      color: #999;
      font-size: 31px;
      transition: color ease 0.2s;
    }

    .modal-header-icon:hover {
      color: #444;
      cursor: pointer;
    }
  }

  .modal-content {
    @include flex(row, flex-start, stretch);
    width: 100%;
    height: 100%;

    .panel {
      @include flex(column, flex-start, stretch);
      flex-grow: 0;
      flex-shrink: 0;
      width: 50%;

      &.panel-left {
        border-right: 1px solid $color-border;
      }

      .panel-header {
        @include flex(row, flex-start, center);
        flex-grow: 0;
        flex-shrink: 0;
        padding: 10px 16px;

        .label {
          margin-right: 20px;
          color: $color-primary;
        }

        .select {
          width: 300px;
          margin-right: auto;
        }
      }

      .panel-body {
        overflow: auto;

        .relative {
          position: relative;
          width: 100%;

          &.sub-paper {
            min-height: 50px;
          }

          .label {
            position: absolute;
            top: 10px;
            left: 10px;
            color: red;
            font-weight: bold;
            font-size: $font-size-large;
            line-height: 1;
            background-color: rgba(255, 255, 255, 0.7);
          }

          img {
            width: 100%;
          }
        }
      }
    }
  }
</style>

<style lang="scss">
  .modal-scan-sub-paper {
    .ivu-modal-body {
      padding: 0;
    }
  }
</style>
