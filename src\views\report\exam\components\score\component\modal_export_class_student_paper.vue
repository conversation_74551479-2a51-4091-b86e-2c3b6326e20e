<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    :mask-closable="false"
    :closable="!exporting"
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <div class="actions">
      <div class="options">
        <Checkbox v-model="showScore" :disabled="!showStudentPaperBlockScore">显示得分</Checkbox>
      </div>
      <Button type="primary" :loading="exporting" @click="handleBtnExportClick">{{
        exporting ? '正在导出' : '开始导出'
      }}</Button>
    </div>
    <div class="progress" v-html="progressText"></div>
  </Modal>
</template>

<script>
  import { apiGetStudentScore, apiGetExamPaperPicAndMarkSituation } from '@/api/report'

  import { mapGetters } from 'vuex'
  import JSZip from 'jszip'
  import StudentPaperDrawHelper from '@/helpers/report/student_paper_draw_helper'
  import { downloadBlob } from '@/utils/download'
  import { loadImageBlob } from '@/utils/promise'

  export default {
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        exporting: false,
        students: [],
        studentPaperDrawHelper: null,
        showScore: false,
        progressText: '',
      }
    },
    computed: {
      ...mapGetters('report', [
        'examId',
        'examName',
        'isMultipleSchool',
        'isAdministrator',
        'currentExamSubjectId',
        'templateSubjects',
        'templateId',
        'currentSchoolId',
        'currentClassId',
        'currentExamSubjectName',
        'examSchools',
        'currentSchoolName',
        'currentClassName',
        'currentParams',
        'currentReport',
        'currentLevelName',
      ]),
      modalTitle() {
        let title = '导出班级考生原卷'
        if (this.currentClassName && this.currentExamSubjectName) {
          title += `（${this.currentClassName}${this.currentExamSubjectName}）`
        }
        return title
      },
      showStudentPaperBlockScore() {
        return (
          this.isAdministrator ||
          (this.currentReport &&
            this.currentReport.rolePositions.some(role => role.detail && role.detail.showStudentPaperBlockScore))
        )
      },
    },
    methods: {
      resetData() {
        this.exporting = false
        this.students = []
        this.studentPaperDrawHelper = null
        this.progressText = ''
      },
      async handleBtnExportClick() {
        try {
          this.resetData()
          await this.doExport()
        } catch (err) {
          let message = '导出失败'
          if (typeof err === 'string') {
            message += `：${err}`
          }
          this.$Message.error({
            content: message,
          })
        } finally {
          this.resetData()
        }
      },
      async doExport() {
        this.exporting = true

        // 1. 获取本班所有考生
        if (
          !this.currentSchoolId ||
          !this.currentClassId ||
          !this.currentExamSubjectId ||
          this.currentLevelName != 'class'
        ) {
          this.$Message.info({
            content: '参数错误！请关闭本窗口再重新打开',
          })
          return
        }
        await this.getStudents()
        if (this.students.length == 0) {
          this.$Message.info({
            content: '无考生',
          })
          return
        }

        // 2. 创建canvas及zip对象
        let canvas = document.createElement('canvas')
        let zip = new JSZip()

        // 3. 分别导出每个学生
        for (let i = 0; i < this.students.length; i++) {
          let stu = this.students[i]
          this.progressText = `${stu.studentName}（${i + 1}/${this.students.length}）`

          let studentImages = await this.getStudentPaperImages(canvas, stu)
          studentImages.forEach(img => {
            try {
              zip.file(img.fileName, img.blob)
            } catch {
              throw `添加图片${img.fileName}到压缩文件失败`
            }
          })
        }

        // 4. 打包
        this.progressText = '正在打包'
        let zipBlob = null
        try {
          zipBlob = await zip.generateAsync({ type: 'blob' })
        } catch {
          throw '生成压缩文件失败'
        }

        // 5. 下载
        downloadBlob(zipBlob, `${this.currentClassName}${this.currentExamSubjectName}考生原卷.zip`)
      },
      getStudents() {
        return apiGetStudentScore({
          examId: this.examId,
          templateId: this.templateId,
          examSubjectId: this.currentExamSubjectId,
          schoolId: this.currentSchoolId,
          classId: this.currentClassId,
          currentPage: 1,
          pageSize: 1000,
          keyword: '',
          reportName: 'cls_stuScore',
        }).then(data => {
          this.students = data.records.map(item => ({
            studentId: item.id,
            studentName: item.name,
            admissionNum: item.admissionNum,
          }))
        })
      },
      async getStudentPaperImages(canvas, stu) {
        // 获取学生原卷
        let paperData = await apiGetExamPaperPicAndMarkSituation({
          studentId: stu.studentId,
          examSubjectId: this.currentExamSubjectId,
          templateId: this.templateId,
          reportName: 'cls_stuScore',
        })

        let studentImages = []
        // 不显示分数，则无需画图
        if (!this.showScore || !this.showStudentPaperBlockScore) {
          for (let pageIndex = 0; pageIndex < paperData.paperUrls.length; pageIndex++) {
            try {
              let blob = await this.getImageBlob(paperData.paperUrls[pageIndex])
              studentImages.push({
                fileName: `${stu.admissionNum}-${stu.studentName}-${pageIndex + 1}.png`,
                blob,
              })
            } catch {
              throw `加载图片失败：${stu.studentName}`
            }
          }
          return studentImages
        }

        // 显示分数，则分别绘制每张图
        if (!this.studentPaperDrawHelper) {
          this.studentPaperDrawHelper = new StudentPaperDrawHelper(paperData.templateStr)
        }
        for (let pageIndex = 0; pageIndex < paperData.paperUrls.length; pageIndex++) {
          try {
            await this.studentPaperDrawHelper.drawPaperCanvas(
              canvas,
              paperData.paperUrls[pageIndex],
              pageIndex,
              paperData
            )
          } catch (err) {
            if (typeof err == 'string') {
              throw `${stu.studentName}：${err}`
            } else {
              throw `${stu.studentName}：绘制答卷图失败`
            }
          }

          let blob = null
          try {
            blob = await this.exportCanvasImagePromise(canvas)
          } catch {
            throw `${stu.studentName}：导出为图像失败`
          }

          studentImages.push({
            fileName: `${stu.admissionNum}-${stu.studentName}-${pageIndex + 1}.jpg`,
            blob,
          })
        }
        return studentImages
      },
      exportCanvasImagePromise(canvas) {
        return new Promise(resolve => {
          canvas.toBlob(
            blob => {
              resolve(blob)
            },
            'image/jpeg',
            0.72
          )
        })
      },
      getImageBlob(url) {
        return loadImageBlob(url)
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.resetData()
          this.$emit('update:modelValue', false)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .actions {
    @include flex(row, space-between, center);
  }

  .progress {
    margin-top: 10px;
    text-align: right;
  }
</style>
