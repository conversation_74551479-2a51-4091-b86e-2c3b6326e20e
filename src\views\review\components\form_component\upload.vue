<template>
  <el-upload class="upload-custom" multiple :limit="options?.maxFileCount?.value" :before-upload="beforeUpload">
    <el-button type="default">点击上传文件</el-button>
    <!-- <template #tip>
      <div class="el-upload__tip">文件最大1M，且最多只能上传2个</div>
    </template> -->
  </el-upload>
</template>

<script>
  import { ElUpload, ElButton } from 'element-plus'
  import 'element-plus/es/components/upload/style/css'
  import 'element-plus/es/components/button/style/css'

  export default {
    components: {
      'el-upload': ElUpload,
      'el-button': ElButton,
    },
    props: {
      options: {
        type: Object,
        default: () => ({
          accept: {
            value: '',
          },
          maxFileSize: {
            value: '',
          },
          maxFileCount: {
            value: 1,
          },
        }),
      },
    },
    methods: {
      beforeUpload() {
        return false
      },
    },
  }
</script>
