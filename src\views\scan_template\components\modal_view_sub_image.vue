<template>
  <Modal
    :model-value="modelValue"
    title="查看子图"
    :width="1000"
    :styles="{ top: '50px' }"
    class="modal-view-sub-image"
    footer-hide
    @on-visible-change="handleVisibilityChange"
  >
    <div class="main">
      <div class="nav">
        <div
          v-for="b in blocks"
          :key="b.blockId"
          class="nav-item"
          :class="{ current: b.blockId == currentBlockId }"
          @click="goToBlock(b)"
        >
          <span class="title">{{ b.blockName }}</span>
          <span v-if="!b.src" class="warning">暂无子图</span>
        </div>
      </div>
      <div ref="imagePane" class="list" @scroll="handleScroll">
        <div v-for="b in blocks" :key="b.blockId" class="block-item" :data-block-id="b.blockId">
          <template v-if="b.src">
            <div class="block-name">{{ b.blockName }}</div>
            <img class="block-image" :src="b.src" />
          </template>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
  import { throttle } from '@/utils/function'

  export default {
    props: {
      modelValue: Boolean,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        blocks: [],
        currentBlockId: '',
      }
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init()
        } else {
          this.clear()
        }
      },
    },
    beforeUnmount() {
      this.clear()
    },
    methods: {
      handleVisibilityChange(value) {
        this.$emit('update:modelValue', value)
      },
      async init() {
        this.blocks = []
        this.currentBlockId = ''

        let blocks = await this.$store.dispatch('scanTemplate/exportBlockSubImages')
        blocks.forEach(block => {
          if (block.blob) {
            block.src = URL.createObjectURL(block.blob)
          }
        })
        this.blocks = blocks
        let currentBlock = this.blocks.find(x => x.src)
        if (currentBlock) {
          this.currentBlockId = currentBlock.blockId
        }
      },
      clear() {
        setTimeout(() => {
          this.blocks.forEach(block => {
            if (block.src) {
              URL.revokeObjectURL(block.src)
            }
          })
          this.blocks = []
          this.currentBlockId = ''
        }, 500)
      },
      goToBlock(block) {
        let imagePane = this.$refs.imagePane
        let el = imagePane.querySelector(`.block-item[data-block-id="${block.blockId}"]`)
        if (el) {
          imagePane.scrollTop = el.offsetTop
        }
      },
      handleScroll: throttle(function () {
        let imagePane = this.$refs.imagePane
        let els = imagePane.querySelectorAll('.block-item')

        let top = imagePane.scrollTop + 100
        let currentEl = null
        for (let el of els) {
          if (el.childElementCount == 0) {
            continue
          }
          if (el.offsetTop < top && el.offsetTop + el.offsetHeight >= top) {
            currentEl = el
            break
          }
        }
        this.currentBlockId = (currentEl && currentEl.dataset.blockId) || ''
      }, 100),
    },
  }
</script>

<style lang="scss" scoped>
  .main {
    @include flex(row, flex-start, flex-start);
    height: calc(100vh - 150px);
    margin: -16px;
  }

  .nav {
    flex-grow: 0;
    flex-shrink: 0;
    width: 200px;
    height: 100%;
    border-right: 1px solid $color-border;
    overflow-y: auto;

    .nav-item {
      @include flex(row, space-between, center);
      padding: 16px;
      border-bottom: 1px solid $color-border;
      line-height: 1.2;
      cursor: pointer;

      &.current {
        color: $color-primary;
      }

      .warning {
        flex-grow: 0;
        flex-shrink: 0;
        margin-left: 8px;
        color: $color-warning;
        font-size: $font-size-small;
      }
    }
  }

  .list {
    position: relative;
    flex-grow: 1;
    height: 100%;
    padding-right: 16px;
    padding-left: 16px;
    overflow-y: auto;
    background-color: $color-background;

    .block-item {
      padding-bottom: 16px;

      .block-name {
        padding-top: 8px;
        padding-bottom: 8px;
        font-weight: bold;
        font-size: $font-size-medium-x;
        line-height: 1.2;
      }

      .block-image {
        width: 100%;
      }
    }
  }
</style>
