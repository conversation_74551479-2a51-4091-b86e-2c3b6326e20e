<template>
  <Modal
    class="modal-view-sheet"
    :model-value="modelValue"
    :title="title"
    :width="800"
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <img v-for="url in imageUrls" :key="url" :src="url" @click="viewImageInNewTab(url)" />
  </Modal>
</template>

<script>
  export default {
    props: {
      modelValue: Boolean,
      sheet: Object,
    },
    emits: ['update:modelValue'],
    computed: {
      title() {
        return (this.sheet && this.sheet.name) || ''
      },
      imageUrls() {
        return ((this.sheet && this.sheet.imageUrls) || '').split(',').filter(Boolean)
      },
    },
    methods: {
      handleVisibleChange(visible) {
        if (!visible) {
          this.$emit('update:modelValue', false)
        }
      },
      viewImageInNewTab(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  img {
    width: 100%;
    cursor: zoom-in;
  }
</style>
