<script>
  import FilterBar from '@/views/emarking/home/<USER>/filter_bar.vue'

  import { apiDownloadMultiTemplateSchoolCompareExcel, apiGetReportExams } from '@/api/report'

  import { formatDate, formatTime } from '@/utils/date'
  import { downloadBlob } from '@/utils/download'

  export default {
    components: {
      'filter-bar': FilterBar,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        templateTableColumns: [
          {
            title: '考试名称',
            key: 'examName',
          },
          {
            title: '模板名称',
            key: 'templateName',
          },
          {
            title: '考试日期',
            key: 'startDateText',
            width: 100,
          },
          {
            type: 'selection',
            width: 40,
          },
        ],
        templates: [],
        selectedTemplates: [],

        tableMaxHeight: window.innerHeight - 200,

        isDownloading: false,

        // request params
        requestParams: {
          currentPage: 1,
          pageSize: 60,
          semesterId: '',
          term: '',
          gradeId: '',
          examType: '',
          examScope: '',
          beginTime: '',
          endTime: '',
          keyword: '',
        },
      }
    },

    computed: {
      currentUserId() {
        return this.$store.getters['user/info'].userId
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChanged(visibility) {
        if (visibility) {
          if (this.selectedTemplates.length) {
            this.selectedTemplates.sort((a, b) => b.time - a.time)
          }
          this.fetchExams()
        } else {
          this.closeModal()
        }
      },

      changeParams(data) {
        Object.keys(data).forEach(key => {
          if (key in this.requestParams) {
            this.requestParams[key] = data[key]
          }
        })
        this.fetchExams()
      },

      fetchExams() {
        return apiGetReportExams(this.requestParams)
          .then(response => {
            const ExamTemplates = []
            ;((response && response.exams) || []).forEach(exam => {
              const IsExamAdministrator = (exam.administrators || []).some(
                administrator => administrator.userId === this.currentUserId
              )
              ;(exam.templates || []).forEach(template =>
                ExamTemplates.push({
                  examId: exam.examId,
                  examName: exam.examName,
                  templateId: template.templateId,
                  templateName: template.templateName,
                  time: (exam && exam.startDate && exam.startDate.getTime()) || 0,
                  startDateText: exam && exam.startDate ? formatDate(exam.startDate) : '',
                  _checked: this.selectedTemplates.some(
                    st => st.examId === exam.examId && st.templateId === template.templateId
                  ),
                  _disabled: !IsExamAdministrator,
                })
              )
            })
            this.templates = ExamTemplates
          })
          .catch(() => (this.templates = []))
      },

      handleTableDataSelectionChanged(selection = []) {
        const PageSelectedTemplates = (this.templates || []).filter(x =>
          (this.selectedTemplates || []).some(y => y.examId === x.examId && y.templateId === x.templateId)
        )
        const MinusTemplates = PageSelectedTemplates.filter(
          x => !selection.some(y => y.examId === x.examId && y.templateId === x.templateId)
        )
        const AddTemplates = selection.filter(
          x => !this.selectedTemplates.some(y => y.examId === x.examId && y.templateId === x.templateId)
        )

        // minus
        this.selectedTemplates = this.selectedTemplates.filter(
          x => !MinusTemplates.some(y => y.examId === x.examId && y.templateId === x.templateId)
        )
        // add
        this.selectedTemplates.push(...AddTemplates)
      },

      resetSelectedTemplates() {
        this.selectedTemplates = []
        this.templates.forEach(item => {
          item._checked = true
          item._checked = false
        })
      },

      deleteSeletedTemplate(template) {
        this.selectedTemplates = this.selectedTemplates.filter(
          item => item.examId !== template.examId || item.templateId !== template.templateId
        )
        this.templates.forEach(item => {
          const TemplateChecked = this.selectedTemplates.some(
            st => st.examId === item.examId && st.templateId === item.templateId
          )
          item._checked = !TemplateChecked
          item._checked = TemplateChecked
        })
      },

      exportExcel() {
        this.$TransparentSpin.show()
        this.isDownloading = true
        apiDownloadMultiTemplateSchoolCompareExcel(this.selectedTemplates.map(st => st.templateId))
          .then(responseBlob => {
            const Current = new Date()
            const FileName =
              '多次考试【学校对比】合并导出(' + formatDate(Current) + ' ' + formatTime(Current) + ').xlsx'
            downloadBlob(responseBlob, FileName)
          })
          .finally(() => {
            this.isDownloading = false
            this.$TransparentSpin.hide()
          })
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    title="多次考试【学校对比】合并导出"
    fullscreen
    @on-visible-change="handleModalVisibleChanged"
  >
    <div class="modal-export-multi-template-school-compare-panel">
      <div class="user-exam-template-list">
        <filter-bar
          style="margin-bottom: 16px"
          search-placeholder="考试名称"
          :fields="['term', 'grade', 'examType', 'examScope', 'time']"
          :params="requestParams"
          @change="changeParams"
        ></filter-bar>
        <Table
          border
          :columns="templateTableColumns"
          :data="templates"
          :max-height="tableMaxHeight"
          @on-selection-change="handleTableDataSelectionChanged"
        ></Table>
      </div>

      <div class="select-template-list-panel">
        <div class="panel-title-bar">
          <div class="panel-title">
            已选择模板<span class="selected-template-count">（ {{ selectedTemplates.length }} 个 ）</span>
          </div>
          <TextButton type="error" @click="resetSelectedTemplates">清空</TextButton>
        </div>
        <div class="select-template-list">
          <div v-for="template of selectedTemplates" :key="template.templateId" class="template-info-card">
            <div class="template-info">
              <div class="template-name">{{ template.templateName }}</div>
              <div class="other-info">
                <div class="info-item">考试名称：{{ template.examName }}</div>
                <div class="info-item">考试日期：{{ template.startDateText }}</div>
              </div>
            </div>
            <div class="delete-icon" @click="deleteSeletedTemplate(template)"><Icon type="md-close-circle" /></div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button
        type="primary"
        :disabled="!selectedTemplates.length || isDownloading"
        :loading="isDownloading"
        @click="exportExcel"
        >导出</Button
      >
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-export-multi-template-school-compare-panel {
    @include flex(row, flex-start, stretch);
    height: 100%;

    .user-exam-template-list {
      flex: 5;
    }

    .select-template-list-panel {
      flex: 3;
      height: 100%;
      margin-left: 20px;
      padding-left: 20px;
      border-left: 1px dashed gray;

      .panel-title-bar {
        @include flex(row, space-between, center);

        .panel-title {
          color: $color-primary-dark;
          font-size: $font-size-medium-x;

          .selected-template-count {
            font-size: $font-size-medium;
          }
        }
      }

      .select-template-list {
        height: calc(100vh - 180px);
        margin-top: 10px;
        overflow-y: auto;

        .template-info-card {
          @include flex(row, space-between, center);
          height: 120px;
          margin-right: 6px;
          padding: 10px 1em;
          border: 1px solid $color-border;
          border-radius: 4px;
          box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          &:not(:first-child) {
            margin-top: 10px;
          }

          .template-info {
            .template-name {
              height: 30px;
              font-weight: bold;
              font-size: $font-size-medium-xs;
            }

            .other-info {
              font-size: $font-size-medium-s;
            }
          }

          .delete-icon {
            color: $color-error;
            font-size: $font-size-large-x;

            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
  }
</style>
