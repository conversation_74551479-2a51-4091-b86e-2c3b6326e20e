<template>
  <div class="container-score-config">
    <div class="box-config-bar">
      <div class="config-item">
        <span>评分方式：</span>
        <Select v-model="currentScoreMode" style="width: 120px" @on-change="onChangeScoreMode">
          <Option v-for="item in scoreModes" :key="item.name" :value="item.name">{{ item.label }}</Option>
        </Select>
      </div>
      <div class="config-item">
        <span>满分：</span>
        <el-input-number v-model="fullScore" :min="1" :max="1000" controls-position="right" style="width: 90px" />
        <span style="margin-left: 4px">分</span>
      </div>
      <div class="config-item">
        <Button type="primary" ghost @click="onSubmit">提交</Button>
      </div>
    </div>
    <template v-if="isMultipleScore">
      <div class="box-score-multiple">
        <div class="box-left">
          <div class="header">多项评分设置</div>
          <div class="setting-overview">
            <span>总分：</span>
            <span style="font-weight: 600">{{ computedFullScore }}</span>
            <span>分</span>
            <span
              >，共 <span style="font-weight: 600">{{ scoreItems.length }}</span> 项评分细则</span
            >
          </div>
        </div>
        <div class="box-right">
          <Button @click="onShowTemplate">从模板导入</Button>
          <Button :disabled="!scoreItems.length" @click="onSaveToTemplate">保存为模板</Button>
        </div>
      </div>
      <div v-if="scoreItems.length" class="box-score-item">
        <div v-for="(item, idx) in scoreItems" :key="idx" class="score-item">
          <div class="score-item-header">
            <div class="header-left">
              <el-input
                v-model="item.name"
                class="score-item-name"
                placeholder="请输入评分项名称"
                style="width: 250px; height: 40px; font-weight: 600; font-size: 18px"
              />
            </div>
            <div class="header-right">
              <span class="score-item-score">{{ item.score }} 分</span>
            </div>
          </div>
          <div class="score-item-body">
            <div class="box-score-item-rule">
              <div v-for="(r, rIdx) in item.rules" :key="rIdx" class="score-rule-item">
                <div class="rule-item-info">
                  <span class="rule-item-score">({{ r.score }}分)</span>
                  <span class="rule-item-name">{{ r.name }}：</span>
                  <span>{{ r.description }}</span>
                </div>
                <div class="rule-item-action">
                  <div class="btn-edit" @click="onScoreRuleItemEdit(idx, rIdx)">
                    <Icon type="ios-create-outline" size="18" />
                  </div>
                  <div class="btn-delete" @click="onScoreRuleItemDelete(idx, rIdx)">
                    <Icon type="ios-trash-outline" size="18" />
                  </div>
                </div>
              </div>
            </div>
            <div class="box-btn-add-rule-item">
              <Button type="primary" ghost icon="md-add" @click="onScoreRuleAdd(item)">添加评分细则</Button>
              <TextButton type="warning" icon="ios-trash-outline" @click="onScoreItemDelete(idx)"
                >删除评分项</TextButton
              >
            </div>
          </div>
        </div>
      </div>
      <div class="box-btn-add-score-item">
        <Button type="primary" ghost icon="md-add" @click="onScoreItemAdd">添加评分项</Button>
      </div>
    </template>

    <Modal v-model="modalRuleAddVisible" title="评分细则设置" :width="600">
      <div class="container-modal-content">
        <el-form ref="scoreItemForm" :model="dynamicRuleItemOptions" :rules="formDynamicRules" label-width="90px">
          <el-form-item label="细则名称" prop="name" required>
            <el-input
              v-model="dynamicRuleItemOptions.name"
              placeholder="请输入细则名称"
              :maxlength="50"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="分值" prop="score" required>
            <el-input-number
              v-model="dynamicRuleItemOptions.score"
              :min="1"
              :max="100"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="细则说明" prop="description" required>
            <el-input
              v-model="dynamicRuleItemOptions.description"
              type="textarea"
              placeholder="请输入细则说明"
              :autosize="{ minRows: 3 }"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="container-modal-footer">
          <Button @click="onModalCancel">取消</Button>
          <Button type="primary" @click="onModalConfirm">确定</Button>
        </div>
      </template>
    </Modal>

    <Modal v-model="modalTemplateAddVisible" title="保存为模板" :width="500">
      <div class="container-modal-content">
        <div style="margin-bottom: 10px">请输入模板名称：</div>
        <Input v-model="templateName" placeholder="模板名称" />
      </div>
      <template #footer>
        <div class="container-modal-footer">
          <Button @click="onModalTemplateAddCancel">取消</Button>
          <Button type="primary" @click="onModalTemplateAddConfirm">确定</Button>
        </div>
      </template>
    </Modal>

    <Modal
      v-model="modalTemplateVisible"
      title="导入评分细则模板"
      :width="600"
      footer-hide
      @on-visible-change="onModalTemplateVisibleChange"
    >
      <div class="container-modal-content">
        <div class="box-action">
          <div class="search-bar">
            <Input v-model="searchKeyword" placeholder="请输入关键词搜索" clearable @on-clear="onClear" />
          </div>
          <Button type="primary" style="margin: 0 10px" @click="onSearch">搜索</Button>
          <Button type="primary" ghost @click="onTemplateAction">{{
            isTemplateEditing ? '取消管理' : '管理模板'
          }}</Button>
        </div>
        <div v-if="filteredTemplateList.length" class="box-list">
          <div v-for="item in filteredTemplateList" :key="item.id" class="list-item" @click="onImportTemplate(item)">
            <div class="list-item-name">{{ item.name }}</div>
            <div class="list-item-action">
              <div v-if="isTemplateEditing" class="btn-template-del" @click="onTemplateDel($event, item)">
                <Icon type="ios-trash-outline" size="18" />
              </div>
            </div>
          </div>
        </div>
        <NoData v-else />
      </div>
    </Modal>
  </div>
</template>

<script>
  import { ElInputNumber, ElInput, ElForm, ElFormItem } from 'element-plus'
  import 'element-plus/es/components/input-number/style/css'
  import 'element-plus/es/components/input/style/css'
  import 'element-plus/es/components/form/style/css'
  import 'element-plus/es/components/form-item/style/css'
  import NoData from '@/components/no_data'
  import { deepCopy } from '@/utils/object'
  import { groupArray } from '@/utils/array'
  import {
    apiSaveScoreMultipleConfig,
    apiGetScoreConfig,
    apiSaveScoreSingleConfig,
    apiSaveScoreItemTemplate,
    apiGetScoreConfigTemplate,
    apiDelelteScoreTemplate,
  } from '@/api/review/config'

  export default {
    components: {
      'el-input-number': ElInputNumber,
      'el-input': ElInput,
      'el-form': ElForm,
      'el-form-item': ElFormItem,
      NoData,
    },
    props: {
      currentCategory: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        scoreModes: [
          {
            label: '单项评分',
            name: 'single',
          },
          {
            label: '多项评分',
            name: 'multiple',
          },
        ],
        currentScoreMode: 'single',
        fullScore: 100,
        scoreItems: [],
        modalRuleAddVisible: false,
        currentScoreItem: null,
        dynamicRuleItemOptions: {
          name: '',
          score: 10,
          description: '',
        },
        formDynamicRules: {
          name: [{ required: true, message: '请输入细则名称', trigger: 'blur' }],
          score: [{ required: true, message: '请填写细则分值', trigger: 'blur' }],
          description: [{ required: true, message: '请输入细则说明', trigger: 'blur' }],
        },
        isScoreItemEditing: false,
        currentRuleIdx: 0,
        modalTemplateAddVisible: false,
        templateName: '',
        modalTemplateVisible: false,
        templateList: [],
        searchKeyword: '',
        filteredTemplateList: [],
        isTemplateEditing: false,
      }
    },
    computed: {
      currentActivity() {
        return this.$store.getters['review/currentActivity']
      },
      activityConfigs() {
        let configs = this.$store.getters['review/activityConfigs'] || []
        return configs
      },
      currentCategoryConfigs() {
        const theConfigs = this.activityConfigs.find(c => c.categoryId === this.currentCategory)
        return theConfigs
      },
      isMultipleScore() {
        return this.currentScoreMode === 'multiple'
      },
      computedFullScore() {
        return this.scoreItems.reduce((prev, cur) => {
          return cur.score + prev
        }, 0)
      },
      categories() {
        return this.$store.getters['review/categories'] || []
      },
    },
    watch: {
      currentCategoryConfigs: {
        handler: function (val) {
          this.fullScore = (val && val.fullScore) || 100
          this.currentScoreMode = (val && val.scoreMode) || 'single'
          if (this.currentScoreMode === 'multiple') {
            this.getScoreItems()
          }
        },
        immediate: true,
        deep: true,
      },
      computedFullScore: {
        handler: function (val) {
          this.fullScore = val
        },
        immediate: true,
      },
      modalRuleAddVisible(val) {
        if (val) {
          this.$refs.scoreItemForm.resetFields()
        }
      },
    },
    methods: {
      fetchReviewConfig() {
        this.$store.dispatch('review/fetchReviewConfig')
      },
      getScoreItems() {
        apiGetScoreConfig({
          activityId: this.currentActivity.id,
          categoryId: this.currentCategory,
        }).then(res => {
          let groups = groupArray(res || [], k => k.groupName)

          this.scoreItems = groups.map(g => {
            let score = g.group.reduce((prev, cur) => {
              cur.score = cur.fullScore
              return prev + cur.score
            }, 0)

            return {
              name: g.key,
              score,
              rules: g.group,
            }
          })
        })
      },
      onChangeScoreMode() {
        if (this.isMultipleScore) {
          this.getScoreItems()
        }
      },
      onScoreItemAdd() {
        this.scoreItems.push({
          name: '',
          score: 0,
          rules: [],
        })
      },
      onScoreRuleAdd(item) {
        this.currentScoreItem = item
        this.modalRuleAddVisible = true
      },
      onScoreItemDelete(idx) {
        this.scoreItems.splice(idx, 1)
      },
      onScoreRuleItemEdit(scoreIdx, ruleIdx) {
        this.currentScoreItem = this.scoreItems[scoreIdx]
        this.dynamicRuleItemOptions = deepCopy(this.currentScoreItem.rules[ruleIdx])
        this.currentRuleIdx = ruleIdx
        this.isScoreItemEditing = true
        this.modalRuleAddVisible = true
      },
      onScoreRuleItemDelete(scoreIdx, ruleIdx) {
        let scoreItem = this.scoreItems[scoreIdx]
        scoreItem.rules.splice(ruleIdx, 1)
        scoreItem.score = scoreItem.rules.reduce((prev, cur) => {
          return cur.score + prev
        }, 0)
      },
      onModalCancel() {
        this.modalRuleAddVisible = false
        this.isScoreItemEditing = false
      },
      onModalConfirm() {
        const { dynamicRuleItemOptions } = this

        if (!dynamicRuleItemOptions.name) {
          this.$Message.info('请输入评分细则名称')
          return
        }
        if (!dynamicRuleItemOptions.score) {
          this.$Message.info('请输入评分细则分值')
          return
        }
        if (!dynamicRuleItemOptions.description) {
          this.$Message.info('请输入评分细则说明')
          return
        }

        if (!this.isScoreItemEditing) {
          this.currentScoreItem.rules.push({
            ...dynamicRuleItemOptions,
          })
        } else {
          this.currentScoreItem.rules[this.currentRuleIdx] = deepCopy(dynamicRuleItemOptions)
        }

        this.currentScoreItem.score = this.currentScoreItem.rules.reduce((prev, cur) => {
          return cur.score + prev
        }, 0)

        this.modalRuleAddVisible = false
        this.isScoreItemEditing = false
      },
      check() {
        const { scoreItems, isMultipleScore, fullScore, computedFullScore } = this
        if (isMultipleScore) {
          if (scoreItems.some(item => !item.name)) {
            return '评分项名称不能为空'
          }
          if (fullScore !== computedFullScore) {
            return '细则评分的总分和满分不一致，请调整'
          }
          if (!fullScore) {
            return '请填写满分'
          }

          const theItem = scoreItems.find(item => !item.rules.length)
          if (theItem) {
            return `${theItem.name} 还没有添加评分细则`
          }
        }
      },
      onSubmit() {
        const { isMultipleScore, currentActivity, currentCategory, scoreItems } = this
        let scoreItemList = []

        let msg = this.check()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        scoreItems.forEach(item => {
          item.rules.forEach((r, rIdx) => {
            scoreItemList.push({
              ...r,
              groupName: item.name,
              fullScore: r.score,
              sortOrder: rIdx + 1,
            })
          })
        })

        if (isMultipleScore) {
          apiSaveScoreMultipleConfig({
            activityId: currentActivity.id,
            categoryId: currentCategory,
            scoreItemList,
          }).then(() => {
            this.$Message.success('已提交')
            this.fetchReviewConfig()
          })
        } else {
          apiSaveScoreSingleConfig({
            activityId: currentActivity.id,
            categoryId: currentCategory,
            fullScore: this.fullScore,
          }).then(() => {
            this.$Message.success('已提交')
            this.fetchReviewConfig()
          })
        }
      },
      onSaveToTemplate() {
        let msg = this.check()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        this.modalTemplateAddVisible = true
      },
      onModalTemplateAddCancel() {
        this.modalTemplateAddVisible = false
      },
      onModalTemplateAddConfirm() {
        if (!this.templateName) {
          this.$Message.info('请输入模板名称')
          return
        }

        const { scoreItems } = this
        let scoreItemList = []

        let msg = this.check()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        scoreItems.forEach(item => {
          item.rules.forEach((r, rIdx) => {
            scoreItemList.push({
              ...r,
              groupName: item.name,
              fullScore: r.score,
              sortOrder: rIdx + 1,
            })
          })
        })

        apiSaveScoreItemTemplate({
          name: this.templateName,
          scoreItemList,
        }).then(() => {
          this.$Message.success('已保存')
          this.modalTemplateAddVisible = false
        })
      },
      onShowTemplate() {
        this.getTemplateList()
        this.modalTemplateVisible = true
      },
      onModalTemplateCancel() {
        this.modalTemplateVisible = false
        this.isTemplateEditing = false
      },
      onModalTemplateConfirm() {
        this.modalTemplateVisible = false
        this.isTemplateEditing = false
      },
      onModalTemplateVisibleChange() {
        this.isTemplateEditing = false
      },
      getTemplateList() {
        apiGetScoreConfigTemplate().then(res => {
          this.templateList = res || []
          this.filteredTemplateList = deepCopy(this.templateList)
        })
      },
      onClear() {
        this.filteredTemplateList = deepCopy(this.templateList)
      },
      onSearch() {
        this.filteredTemplateList = this.templateList.filter(item => item.name.includes(this.searchKeyword))
      },
      onTemplateAction() {
        this.isTemplateEditing = !this.isTemplateEditing
      },
      onTemplateDel(event, item) {
        event.stopPropagation()
        apiDelelteScoreTemplate({
          id: item.id,
        }).then(() => {
          this.$Message.success('已删除')
          this.getTemplateList()
        })
      },
      onImportTemplate(item) {
        let groups = groupArray(item.scoreItemList || [], k => k.groupName)

        this.scoreItems = groups.map(g => {
          let score = g.group.reduce((prev, cur) => {
            cur.score = cur.fullScore
            return prev + cur.score
          }, 0)

          return {
            name: g.key,
            score,
            rules: g.group,
          }
        })
        this.modalTemplateVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-score-config {
    .box-config-bar {
      @include flex(row, flex-start, center);
      margin-bottom: 20px;
    }

    .config-item {
      &:not(:last-child) {
        margin-right: 30px;
      }
    }

    .box-score-multiple {
      @include flex(row, space-between, center);

      .header {
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .box-score-item {
      margin-top: 15px;
      padding: 15px 0;

      :deep(.el-input__wrapper) {
        box-shadow: none;
      }

      .score-item {
        &:not(:last-child) {
          margin-bottom: 30px;
        }
      }

      .score-item-header {
        @include flex(row, space-between, center);
        padding-bottom: 4px;
        border-bottom: 1px solid #dfdfdf;
      }

      .score-item-body {
        padding-left: 20px;
      }

      .score-rule-item {
        padding-top: 15px;
        border-bottom: 1px solid #dfdfdf;

        &:hover {
          background-color: #f9f9f9;
        }
      }

      .rule-item-info {
        margin-bottom: 10px;
        line-height: 1.5;
      }

      .rule-item-action {
        @include flex(row, flex-end, center);
        padding-bottom: 6px;

        .btn-edit {
          margin-right: 10px;
          cursor: pointer;

          &:hover {
            color: #37cdbe;
          }
        }

        .btn-delete {
          cursor: pointer;

          &:hover {
            color: red;
          }
        }
      }

      .rule-item-score {
        color: red;
      }

      .rule-item-name {
        font-weight: 600;
      }

      .box-btn-add-rule-item {
        @include flex(row, space-between, center);
        margin-top: 20px;
      }

      .score-item-name {
        font-weight: 600;
        font-size: 18px;
      }

      .score-item-score {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .box-btn-add-score-item {
      @include flex(row, center, center);
      margin-top: 30px;
    }
  }

  .container-modal-footer {
    @include flex(row, flex-end, center);
  }

  .box-list {
    margin-bottom: 40px;

    .list-item {
      @include flex(row, space-between, center);
      padding: 10px 0;
      border-bottom: 1px solid #dfdfdf;
      cursor: pointer;

      &:hover {
        background-color: #f9f9f9;
      }
    }

    .list-item-name {
      margin-right: 15px;
    }
  }

  .box-action {
    @include flex(row, flex-start, center);
    margin-bottom: 15px;

    .search-bar {
      flex: 1;
    }
  }
</style>
