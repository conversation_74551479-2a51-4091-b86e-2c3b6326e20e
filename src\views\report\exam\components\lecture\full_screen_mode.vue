<template>
  <div class="component-full-screen-lecture">
    <component-question-card
      v-if="activeQuestion"
      ref="card"
      class="question-card"
      :question="activeQuestion"
      :show-grade-avg-score="showGradeAvgScore"
      :show-all-branch="showAllBranch"
      :score-detail-visible="scoreDetailVisible"
    ></component-question-card>

    <div v-show="hasPreviousQuestion" class="icon-affix-previous" title="上一题" @click="goToPreviousQuestion">
      <Icon type="md-arrow-round-back" />
    </div>
    <div v-show="hasNextQuestion" class="icon-affix-next" title="下一题" @click="goToNextQuestion">
      <Icon type="md-arrow-round-forward" />
    </div>
    <div class="icon-affix-menu" title="题目列表" @click="drawerSelectQuestionShowed = true">
      <Icon type="ios-list-box-outline" />
    </div>
    <div class="icon-affix-exit" title="退出全屏" @click="exitFullChrome">
      <Icon type="ios-contract" />
    </div>

    <Drawer v-model="drawerSelectQuestionShowed" title="题目列表" width="230">
      <div class="drawer-panel">
        <QuickSelectorQuestion :questions="questions" @on-change="goToTargetQuestion" />
      </div>
    </Drawer>
  </div>
</template>

<script>
  import ComponentQuestionCard from './question_card.vue'
  import QuickSelectorQuestion from './quick_selector_question.vue'

  export default {
    components: {
      'component-question-card': ComponentQuestionCard,
      QuickSelectorQuestion,
    },
    props: {
      questions: {
        type: Array,
        default: () => [],
      },
      showGradeAvgScore: {
        type: Boolean,
        default: true,
      },
      showAllBranch: {
        type: Boolean,
        default: true,
      },
      scoreDetailVisible: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['on-change-mode'],
    data() {
      return {
        activeQuestion: null,
        drawerSelectQuestionShowed: false,
        htmlQuestionPanel: null,
      }
    },

    computed: {
      activeQuestionIndex() {
        if (!this.questions || !this.questions.length) {
          return -1
        } else {
          return this.questions.findIndex(
            x =>
              x.originalQuestionId === (this.activeQuestion.originalQuestionId || 0) ||
              x.questionName == (this.activeQuestion.questionName || 0)
          )
        }
      },

      hasPreviousQuestion() {
        return this.activeQuestionIndex > 0
      },

      hasNextQuestion() {
        return this.activeQuestionIndex > -1 && this.activeQuestionIndex < this.questions.length - 1
      },
    },

    created() {
      this.initQuesitons()
      this.createEventListener()
    },

    mounted() {
      if (this.$refs.card) {
        this.htmlQuestionPanel = this.$refs.card.$refs.questionPanel
      }
    },

    beforeUnmount() {
      document.removeEventListener('keydown', this.handleFullScoreKeyboardOperation)
    },

    methods: {
      initQuesitons() {
        if (!this.questions || !this.questions.length) {
          this.exitFullChrome()
          return
        }
        this.activeQuestion = this.questions[0]
      },

      createEventListener() {
        document.addEventListener('keydown', this.handleFullScoreKeyboardOperation)
      },

      handleFullScoreKeyboardOperation(event) {
        if (event.code === 'Escape') {
          this.exitFullChrome()
        } else if (event.code === 'ArrowRight') {
          this.goToNextQuestion()
        } else if (event.code === 'ArrowLeft') {
          this.goToPreviousQuestion()
        }
      },

      goToTargetQuestion(targetQuestion) {
        this.activeQuestion = targetQuestion
        this.htmlQuestionPanel.scrollTo(0, 0)
        this.drawerSelectQuestionShowed = false
      },

      goToPreviousQuestion() {
        if (this.activeQuestionIndex < 1) {
          return
        }
        this.activeQuestion = this.questions[this.activeQuestionIndex - 1]
        this.htmlQuestionPanel.scrollTo(0, 0)
      },

      goToNextQuestion() {
        if (this.activeQuestionIndex > this.questions.length - 2) {
          return
        }
        this.activeQuestion = this.questions[this.activeQuestionIndex + 1]
        this.htmlQuestionPanel.scrollTo(0, 0)
      },

      exitFullChrome() {
        this.$emit('on-change-mode', 'normal')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .component-full-screen-lecture {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10001;
    display: inline-block;
    width: 100%;
    background-color: white;

    .question-card {
      :deep(.question-panel) {
        height: calc(100vh - 58px);
        overflow-y: auto;
      }
    }

    .icon-affix-previous {
      top: 56px;
      right: 100px;
    }

    .icon-affix-next {
      top: 56px;
      right: 40px;
    }

    .icon-affix-menu {
      right: 50px;
      bottom: 80px;
    }

    .icon-affix-exit {
      right: 50px;
      bottom: 30px;
    }

    .icon-affix-previous,
    .icon-affix-next,
    .icon-affix-menu,
    .icon-affix-exit {
      position: fixed;
      z-index: 4;
      display: block;
      width: 48px;
      height: 40px;
      color: white;
      font-size: $font-size-large-x;
      line-height: 1.8;
      text-align: center;
      background-color: $color-content;
      cursor: pointer;
      opacity: 0.4;

      &:hover {
        opacity: 1;
      }
    }
  }

  :deep(.ivu-drawer-body) {
    padding: 16px 10px 16px 20px;
  }
</style>
