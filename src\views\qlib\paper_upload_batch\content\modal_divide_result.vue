<template>
  <Modal
    :model-value="modelValue"
    :closable="true"
    :mask-closable="false"
    title="划题结果"
    :width="800"
    :footer-hide="true"
    @on-cancel="handleCancel"
  >
    <div class="modal-inner">
      <Table
        class="table-divide-result"
        border
        :columns="tableDivideResultColumns"
        :data="tableDivideResultData"
        no-data-text="无题目"
        no-filtered-data-text="无题目"
      >
        <template #type="{ row }">
          <Select
            :model-value="row.questionType.id"
            class="selector-topic-questiontype"
            filterable
            clearable
            transfer
            @on-change="changeQuestionType(row, $event)"
          >
            <Option v-for="item in questionTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </template>
        <template #codes="{ row }">
          <div class="topic-codes">
            <span
              v-for="code in row.questionCodes"
              :key="code"
              class="code"
              :class="{ active: code == previewQuestion.code }"
              @click="changePreviewQuestion(code)"
              >{{ code }}</span
            >
          </div>
        </template>
      </Table>
      <div class="actions">
        <div class="nav">
          <span class="current"
            >第<span class="code">{{ previewQuestion.code }}</span
            >题</span
          >
          <Button class="btn-pre" type="text" @click="navPreviewQuestion(true)">上一题</Button>
          <Button class="btn-next" type="text" @click="navPreviewQuestion(false)">下一题</Button>
        </div>
        <div class="confirm">
          <Button class="btn-result-cancel" type="text" @click="handleCancel">取消</Button>
          <Button class="btn-result-ok" type="primary" @click="handleOK">确定</Button>
        </div>
      </div>
      <div
        v-if="previewQuestion.content"
        ref="previewQuestionContentRef"
        class="preview-question-content"
        v-html="previewQuestion.content"
      ></div>
    </div>
  </Modal>
</template>

<script setup>
  import { ref, computed, watch, useTemplateRef, markRaw } from 'vue'
  import iView from '@/iview'
  import store from '@/store'
  import { QDOM } from '@/helpers/qlib/qdom'
  import { deepCopy } from '@/utils/object'
  import { groupArrayAdjacent, unGroupArray } from '@/utils/array'
  import MathJaxUtil from '@/utils/mathjax'
  import { sleep } from '@/utils/promise'

  const props = defineProps({
    modelValue: Boolean,
    selection: Object,
    paperInfo: Object,
  })
  const emits = defineEmits(['update:modelValue', 'add'])

  const previewQuestionContentRef = useTemplateRef('previewQuestionContentRef')

  // 划题结果
  const divideResult = ref([])
  // 预览题目
  const previewQuestion = ref({
    code: 0,
    content: '',
  })
  // 解析题目内容中
  const extractingQuestions = ref(false)

  // 题型
  const questionTypes = computed(() => {
    let stageId = props.paperInfo?.stage.id
    let subjectId = props.paperInfo?.subject.id
    if (stageId && subjectId) {
      return store.getters['qlib/questionTypesByStageSubject'](stageId, subjectId)
    } else {
      return []
    }
  })

  // 表格列
  const tableDivideResultColumns = [
    {
      title: '题型',
      slot: 'type',
      align: 'center',
      width: 160,
    },
    {
      title: '大题',
      key: 'topicName',
      align: 'center',
      minWidth: 160,
      maxWidth: 250,
    },
    {
      title: '数量',
      key: 'questionCount',
      align: 'center',
      width: 60,
    },
    {
      title: '题号',
      slot: 'codes',
      align: 'center',
      minWidth: 200,
      maxWidth: 400,
    },
  ]

  // 表格数据
  const tableDivideResultData = computed(() => {
    return divideResult.value.map(t => ({
      key: t.key,
      topicName: t.topicName,
      questionCount: t.questionCount,
      questionType: t.questionType,
      questionCodes: t.questions.map(q => q.code),
    }))
  })

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        startDividing()
      }
    }
  )

  async function startDividing() {
    divideResult.value = []
    previewQuestion.value = {
      code: 0,
      content: '',
    }

    try {
      let extractQuestions = QDOM.clone(props.selection).extractTopicQuestions()
      extractQuestions.forEach(q => {
        q.content = markRaw(q.content)
      })
      divideResult.value = groupArrayAdjacent(extractQuestions, q => q.topic).map((item, idx) => {
        let questionType = questionTypes.value.find(t => t.name === item.group[0].topic) || {
          id: -1 * idx,
          name: '',
        }
        return {
          key: idx,
          questionType: deepCopy(questionType),
          topicName: item.group[0].topic,
          questions: item.group,
          questionCount: item.group.length,
        }
      })

      if (divideResult.value.length > 0) {
        changePreviewQuestion(divideResult.value[0].questions[0].code)
      }
    } catch (err) {
      iView.Message.error({
        content: '提取题号失败',
        duration: 5,
        closable: true,
      })
      divideResult.value = []
      previewQuestion.value = {
        code: 0,
        content: '',
      }
      throw err
    }
  }

  async function changePreviewQuestion(code) {
    let topic = divideResult.value.find(t => t.questions.some(q => q.code === code))
    if (!topic) {
      return
    }

    let question = topic.questions.find(q => q.code === code)
    previewQuestion.value = {
      code,
      content: question.content.innerHTML,
    }

    await sleep(0)
    MathJaxUtil.render(previewQuestionContentRef.value)
  }

  function navPreviewQuestion(isPrevious) {
    let questions = unGroupArray(divideResult.value, t => t.questions)
    let activePreviewQuestionIndex = questions.findIndex(q => q.code === previewQuestion.value.code)
    if (isPrevious) {
      if (activePreviewQuestionIndex > 0) {
        changePreviewQuestion(questions[activePreviewQuestionIndex - 1].code)
      }
    } else {
      if (activePreviewQuestionIndex >= 0 && activePreviewQuestionIndex < questions.length - 1) {
        changePreviewQuestion(questions[activePreviewQuestionIndex + 1].code)
      }
    }
  }

  function changeQuestionType(row, questionTypeId) {
    let topic = divideResult.value.find(t => t.key == row.key)
    let questionType = questionTypes.value.find(t => t.id === questionTypeId) || {}
    topic.questionType = {
      id: questionType.id,
      name: questionType.name,
    }
  }

  function handleCancel() {
    emits('update:modelValue', false)
  }

  async function handleOK() {
    // TODO 保证划出的题与试卷中的题顺序一致
    if (divideResult.value.length == 0) {
      iView.Message.warning('无题目')
      return
    }
    if (divideResult.value.some(t => t.questionType.id == null || t.questionType.id <= 0)) {
      iView.Message.warning('未选择题型')
      return
    }
    let newQuestions = await extractQuestions()
    emits('add', {
      newQuestions,
    })
  }

  async function extractQuestions() {
    extractingQuestions.value = true
    let newQuestions = []
    try {
      let stage = {
        id: props.paperInfo.stage.id,
        name: props.paperInfo.stage.name,
      }
      let subject = {
        id: props.paperInfo.subject.id,
        name: props.paperInfo.subject.name,
      }

      for (let topic of divideResult.value) {
        let questionType = questionTypes.value.find(x => x.id == topic.questionType.id) || {}
        for (let q of topic.questions) {
          let newQuestion = await q.content.extractQuestion({
            stage,
            subject,
            questionType,
            code: q.code,
          })
          newQuestions.push(newQuestion)
        }
      }
      return newQuestions
    } catch (err) {
      iView.Message.error({
        content: '解析题目内容失败',
        duration: 5,
        closable: true,
      })
      handleCancel()
      throw err
    } finally {
      extractingQuestions.value = false
    }
  }

  async function divideAndExtractQuestions() {
    try {
      await startDividing()
      if (divideResult.value.some(t => t.questionType.id == null || t.questionType.id <= 0)) {
        return []
      }
      return await extractQuestions()
    } catch {
      return []
    }
  }

  defineExpose({ divideAndExtractQuestions })
</script>

<style lang="scss" scoped>
  .modal-inner {
    .table-divide-result {
      width: 100%;
      border-collapse: collapse;

      td,
      th {
        padding: 10px;
        text-align: center;
      }

      th {
        background-color: $color-background;
      }

      .topic-codes {
        @include flex(row, center, center);
        flex-wrap: wrap;
        padding: 8px 0;
        column-gap: 8px;

        .code {
          display: inline-block;
          min-width: 24px;
          height: 24px;
          border: 1px solid transparent;
          border-radius: 4px;
          line-height: 24px;
          text-align: center;
          cursor: pointer;
          user-select: none;

          &.active {
            border-color: $color-primary;
            color: $color-primary;
          }
        }
      }
    }

    .actions {
      @include flex(row, space-between, center);
      padding: 16px 0;

      .nav {
        @include flex(row, flex-start, center);

        .current {
          margin-right: 32px;

          .code {
            margin-right: 4px;
            margin-left: 4px;
            color: $color-primary;
            font-weight: bold;
          }
        }
      }

      .confirm {
        @include flex(row, flex-end, center);
        column-gap: 8px;
      }
    }

    .preview-question-content {
      max-height: 300px;
      overflow-y: auto;

      :deep(table) {
        border-collapse: collapse;

        td,
        th {
          border: 1px solid black;
        }
      }
    }
  }
</style>
