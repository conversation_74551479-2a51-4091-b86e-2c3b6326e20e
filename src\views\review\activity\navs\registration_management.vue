<script>
  import RadioGroup from '@/components/radio_group'
  import TextButton from '@/components/text_button'
  import ModalRegister from '../../components/modal_register.vue'
  import ModalCheck from '../../components/modal_check.vue'

  import {
    apiGetStudentRegistrationStats,
    apiGetTeacherRegistrationStats,
    apiGetStudentRegistrationList,
    apiGetTeacherRegistrationList,
    apiSubmitRegistration,
    apiWithdrawRegistration,
    apiDeleteRegistrationRecord,
  } from '@/api/review'

  import { mapGetters } from 'vuex'
  import { debounce } from '@/utils/function'

  export default {
    components: {
      's-radio-group': RadioGroup,
      'modal-register': ModalRegister,
      'modal-check': ModalCheck,
    },

    data() {
      return {
        selectedCategoryId: 'all',
        hasSubmittedSchools: [],
        registedCount: 0,
        submittedCount: 0,
        pendingCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
        selectedSchoolId: 'all',
        keyword: '',
        filterStatus: 'all',
        statusList: [
          {
            id: 'all',
            name: '全部',
          },
          {
            id: 'unsubmitted',
            name: '未提交',
          },
          {
            id: 'audit_pending',
            name: '待审核',
          },
          {
            id: 'audit_approved',
            name: '审核通过',
          },
          {
            id: 'audit_rejected',
            name: '审核驳回',
          },
        ],
        registrationList: [],
        listTotal: 0,
        currentPage: 1,
        pageSize: 10,
        modalRegisterVisible: false,
        modalRegisterIsCreateMode: true,
        modalRegisterUser: null,
        modalCheckVisible: false,
      }
    },

    computed: {
      ...mapGetters('review', [
        'currentActivity',
        'activityId',
        'categories',
        'schoolList',
        'participantIdentity',
        'isAdmin',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'isMultipleSchool',
      ]),

      currentUserSchoolId() {
        return this.$store.getters['user/info'].schoolId
      },

      tableColumns() {
        const _Columns = [
          {
            title: '学校',
            key: 'schoolName',
            align: 'center',
          },
        ]

        if (this.participantIdentity === 'student') {
          _Columns.push({
            title: '班级',
            key: 'className',
            align: 'center',
          })
        }

        _Columns.push(
          {
            title: '参赛者',
            key: 'realName',
            align: 'center',
          },
          {
            title: '参评类别',
            // key: 'categoryName',
            align: 'center',
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.categoryName ||
                  (this.categories.find(c => c.id === params.row.categoryId) || { name: '' }).name
              ),
          },
          {
            title: '作品名称',
            align: 'center',
            render: (h, params) => {
              const TargetFormRecord = (params.row.form || []).find(item => item.fieldLabel === '作品名称')
              return h('span', {}, TargetFormRecord?.fieldValue || '')
            },
          },
          {
            title: '状态',
            align: 'center',
            render: (h, params) => {
              const TargetStatus = this.statusList.find(s => s.id === params.row.status)
              return h('span', {}, TargetStatus?.name || '')
            },
          },
          {
            title: '上传人',
            key: 'registerByRealName',
            align: 'center',
          },
          {
            title: '上传时间',
            align: 'center',
            width: 100,
            render: (h, params) =>
              h('span', {}, [
                h('div', {}, (params.row.registerTime || '').slice(0, 10)),
                h(
                  'div',
                  {
                    style: {
                      'margin-top': '2px',
                    },
                  },
                  (params.row.registerTime || '').slice(10)
                ),
              ]),
          },
          {
            title: '操作',
            align: 'center',
            width: 172,
            render: (h, params) => {
              const TextButtonEdit = h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => {
                    this.modalRegisterUser = params.row
                    this.modalRegisterIsCreateMode = false
                    this.modalRegisterVisible = true
                  },
                },
                () => '编辑'
              )
              const TextButtonWithdraw = h(
                TextButton,
                {
                  type: 'warning',
                  onClick: () =>
                    apiWithdrawRegistration({
                      activityId: this.activityId,
                      categoryId: params.row.categoryId,
                      schoolId: params.row.schoolId,
                      userId: params.row.userId,
                    })
                      .then(() => {
                        this.$Message.success({
                          duration: 3,
                          content: '撤回成功',
                        })
                      })
                      .finally(this.fetchRegistrationList),
                },
                () => '撤回'
              )
              const TextButtonCheck = h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => {
                    this.modalRegisterUser = params.row
                    this.modalCheckVisible = true
                  },
                },
                () => '查看'
              )
              const TextButtonDelete = h(
                TextButton,
                {
                  type: 'error',
                  onClick: () => {
                    const TargetFormRecord = (params.row.form || []).find(item => item.fieldLabel === '作品名称')
                    this.$Modal.confirm({
                      title: `删除报名记录（${params.row.realName} 作品${TargetFormRecord ? ': ' + TargetFormRecord.fieldValue : ''}）`,
                      content: '您确定要删除该报名记录吗，此操作不可恢复！',
                      width: 560,
                      onOk: () =>
                        apiDeleteRegistrationRecord({
                          activityId: this.activityId,
                          categoryId: params.row.categoryId,
                          schoolId: params.row.schoolId,
                          userId: params.row.userId,
                        })
                          .then(() => {
                            this.$Message.success({
                              duration: 3,
                              content: '删除报名记录成功',
                            })
                          })
                          .finally(() => {
                            if (this.registrationList.length === 1 && !params.index) {
                              const TargetPage = Math.max(this.currentPage - 1, 1)
                              this.onPageChange(TargetPage)
                            } else {
                              this.fetchRegistrationList()
                            }
                          }),
                    })
                  },
                },
                () => '删除'
              )
              const TextButtonSubmit = h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () =>
                    apiSubmitRegistration({
                      activityId: this.activityId,
                      categoryId: params.row.categoryId,
                      schoolId: params.row.schoolId,
                      userId: params.row.userId,
                    })
                      .then(() => {
                        this.$Message.success({
                          duration: 3,
                          content: '提交成功',
                        })
                      })
                      .finally(this.fetchRegistrationList),
                },
                () => '提交'
              )

              const TextButtons = []

              if (params.row.status === 'unsubmitted') {
                TextButtons.push(TextButtonCheck, TextButtonEdit, TextButtonSubmit, TextButtonDelete)
              } else if (params.row.status === 'audit_pending') {
                TextButtons.push(TextButtonCheck, TextButtonWithdraw)
              } else if (params.row.status === 'audit_approved') {
                TextButtons.push(TextButtonCheck)
              } else if (params.row.status === 'audit_rejected') {
                TextButtons.push(TextButtonCheck, TextButtonEdit, TextButtonSubmit, TextButtonDelete)
              }

              return h('div', {}, TextButtons)
            },
          }
        )

        return _Columns
      },

      categoryListForRadioGroup() {
        const List = [
          {
            id: 'all',
            name: '全部',
          },
        ]
        if (this.categories && this.categories.length) {
          List.push(
            ...this.categories.map(c => ({
              id: c.id,
              name: c.name,
            }))
          )
        }
        return List
      },

      canViewSchoolStatistic() {
        return this.isMultipleSchool && this.isAdmin
      },

      schoolListForSelect() {
        const List = this.schoolList.map(s => {
          const TargetHasSubmittedRecord = (this.hasSubmittedSchools || []).find(hss => hss.schoolId === s.id)
          return {
            schoolId: s.id,
            schoolName: s.name,
            total: TargetHasSubmittedRecord?.total || 0,
            unSubmittedCount: TargetHasSubmittedRecord?.unsubmitted || 0,
            submittedCount: TargetHasSubmittedRecord
              ? Math.max((TargetHasSubmittedRecord.total || 0) - (TargetHasSubmittedRecord.unsubmitted || 0), 0)
              : 0,
          }
        })

        List.sort((a, b) => b.total - a.total)
        List.unshift({
          schoolId: 'all',
          schoolName: '全部',
          total: List.reduce((acc, cur) => acc + cur.total, 0),
          unSubmittedCount: List.reduce((acc, cur) => acc + cur.unSubmittedCount, 0),
          submittedCount: List.reduce((acc, cur) => acc + cur.submittedCount, 0),
        })

        return List
      },
    },

    watch: {
      filterStatus() {
        this.onPageChange()
      },
    },

    created() {
      this.getStats()
    },

    methods: {
      onSelectedCategoryIdChanged(value) {
        if (value && value !== this.selectedCategoryId) {
          this.selectedCategoryId = value
          this.getStats()
        }
      },
      onSelectedSchoolChanged(value) {
        if (value !== this.selectedSchoolId) {
          this.selectedSchoolId = value
          this.onPageChange()
        }
      },
      onCreateRegistrationRecord() {
        this.modalRegisterIsCreateMode = true
        this.modalRegisterUser = null
        this.modalRegisterVisible = true
      },
      onKeywordChanged: debounce(function (e) {
        this.keyword = (e.target.value || '').trim()
        this.onPageChange()
      }, 600),
      onPageChange(page = 1) {
        this.currentPage = page
        return this.fetchRegistrationList()
      },
      onPageSizeChange(size = 10) {
        this.pageSize = size
        this.onPageChange()
      },

      fetchRegistrationStats(params = {}) {
        const Request =
          this.participantIdentity === 'student' ? apiGetStudentRegistrationStats : apiGetTeacherRegistrationStats
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.selectedCategoryId === 'all' ? undefined : this.selectedCategoryId,
        }
        Object.keys(params).forEach(key => (RequestParams[key] = params[key]))

        return Request(RequestParams)
      },

      async getStats() {
        const RequestParams = this.canViewSchoolStatistic
          ? undefined
          : {
              schoolId: this.currentUserSchoolId,
            }
        const ProjectStats = await this.fetchRegistrationStats(RequestParams)
        if (ProjectStats && ProjectStats.length) {
          const Stat = ProjectStats[0]
          this.registedCount = Stat.total
          this.submittedCount = Stat.total - Stat.unsubmitted
          this.pendingCount = Stat.auditPending
          this.approvedCount = Stat.auditApproved
          this.rejectedCount = Stat.auditRejected
        } else {
          this.registedCount = 0
          this.submittedCount = 0
          this.pendingCount = 0
          this.approvedCount = 0
          this.rejectedCount = 0
        }

        if (this.canViewSchoolStatistic) {
          this.hasSubmittedSchools =
            (await this.fetchRegistrationStats({
              groupBySchool: true,
            })) || []
        }

        this.onPageChange()
      },

      fetchRegistrationList() {
        const Request =
          this.participantIdentity === 'student' ? apiGetStudentRegistrationList : apiGetTeacherRegistrationList
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.selectedCategoryId === 'all' ? undefined : this.selectedCategoryId,
          schoolId: this.canViewSchoolStatistic
            ? this.selectedSchoolId === 'all'
              ? undefined
              : this.selectedSchoolId
            : this.currentUserSchoolId,
          status: this.filterStatus === 'all' ? undefined : this.filterStatus,
          keyword: this.keyword,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
        }

        return Request(RequestParams)
          .then(response => {
            this.listTotal = (response && response.total && Number(response.total)) || 0
            this.registrationList = (response && response.records) || []
          })
          .catch(() => {
            this.listTotal = 0
            this.registrationList = []
          })
      },
    },
  }
</script>

<template>
  <div class="container-registration-management">
    <div v-show="categoryListForRadioGroup.length > 2" class="category-select-bar">
      <RadioGroup
        :model-value="selectedCategoryId"
        type="button"
        button-style="solid"
        @on-change="onSelectedCategoryIdChanged"
      >
        <Radio v-for="c of categoryListForRadioGroup" :key="'c' + c.id" :label="c.id">{{ c.name }}</Radio>
      </RadioGroup>
    </div>

    <div class="statistic-card-bar">
      <div v-if="canViewSchoolStatistic" class="statistic-card">
        <div class="card-text">
          <div class="name">提交学校数</div>
          <div class="value">{{ (hasSubmittedSchools && hasSubmittedSchools.length) || 0 }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_green.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">报名人数</div>
          <div class="value">{{ registedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_purple.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">提交人数</div>
          <div class="value">{{ submittedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_box_yellow.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">待审核</div>
          <div class="value">{{ pendingCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_green.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">审核通过</div>
          <div class="value">{{ approvedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_human_purple.svg" />
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-text">
          <div class="name">审核驳回</div>
          <div class="value">{{ rejectedCount }}</div>
        </div>
        <div class="card-icon">
          <img src="@/assets/images/review/statistic_card_icon_box_yellow.svg" />
        </div>
      </div>
    </div>

    <div class="statistic-detail">
      <div v-show="canViewSchoolStatistic" class="statistic-school-selector">
        <div class="school-item row-title">
          <div class="school-name">学校</div>
          <div class="school-submitted-count">报名人数</div>
        </div>
        <div class="overflow-panel">
          <div
            v-for="s of schoolListForSelect"
            :key="s.schoolId"
            :class="{ 'active-row-content': s.schoolId === selectedSchoolId }"
            class="school-item row-content"
            @click="onSelectedSchoolChanged(s.schoolId)"
          >
            <div class="school-name">{{ s.schoolName }}</div>
            <div class="school-submitted-count">{{ s.total }}</div>
          </div>
        </div>
      </div>
      <div class="statistic-table-panel">
        <div class="operate-bar">
          <div class="filter-bar">
            <s-radio-group v-model="filterStatus" :radioes="statusList"></s-radio-group>
            <Input
              :model-value="keyword"
              :placeholder="participantIdentity === 'student' ? '学生姓名' : '教师姓名 / 手机号码'"
              style="width: 250px; margin-left: 10px"
              clearable
              transfer
              suffix="md-search"
              @on-change="onKeywordChanged"
            />
          </div>
          <Button class="btn-create" type="primary" icon="md-add" ghost @click="onCreateRegistrationRecord"
            >新增报名</Button
          >
        </div>

        <Table :columns="tableColumns" :data="registrationList"></Table>

        <div class="pager">
          <Page
            :total="listTotal"
            :model-value="currentPage"
            :page-size="pageSize"
            :page-size-opts="[10, 20, 50]"
            show-total
            show-elevator
            show-sizer
            tansfer
            @on-change="onPageChange"
            @on-page-size-change="onPageSizeChange"
          ></Page>
        </div>
      </div>
    </div>

    <modal-register
      v-model="modalRegisterVisible"
      :active-category-id="selectedCategoryId"
      :is-create="modalRegisterIsCreateMode"
      :user="modalRegisterUser"
      @refresh="getStats"
    ></modal-register>

    <modal-check v-model="modalCheckVisible" :user="modalRegisterUser"></modal-check>
  </div>
</template>

<style lang="scss" scoped>
  .container-registration-management {
    padding: 20px;
    background-color: #ffffff;

    .category-select-bar {
      margin-bottom: 14px;
      user-select: none;
    }

    .statistic-card-bar {
      @include flex(row, space-between, center);

      .statistic-card {
        @include flex(row, space-between, center);
        flex: 1;
        max-width: 260px;
        height: 108px;
        padding: 16px 18px;
        border-radius: 6px;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

        &:not(:first-child) {
          margin-left: 20px;
        }

        .card-text {
          @include flex(column, space-between, stretch);
          flex: 1;
          height: 100%;

          .name {
            flex: 1;
            font-size: $font-size-medium-x;
            user-select: none;
          }

          .value {
            flex: none;
            font-weight: 700;
            font-size: 28px;
          }
        }

        .card-icon {
          flex: none;
          user-select: none;
        }
      }
    }

    .statistic-detail {
      @include flex(row, space-between, stretch);
      min-height: 50px;
      margin-top: 14px;

      .statistic-school-selector {
        flex: none;
        width: 360px;
        height: fit-content;
        margin-right: 20px;
        padding: 20px;
        border-radius: 6px;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

        .overflow-panel {
          max-height: calc(100vh - 420px);
          overflow-y: auto;
        }

        .school-item {
          @include flex(row, flex-start, center);
          min-height: 50px;
          padding: 15px 0;
          user-select: none;

          .school-name {
            flex: 3;
            text-align: center;
          }

          .school-submitted-count {
            flex: 2;
            text-align: center;
          }
        }

        .row-title {
          font-weight: 700;
          font-size: $font-size-medium-xs;
          background-color: $color-background-light;
        }

        .row-content:hover {
          color: #61affe;
          background-color: #ebf3fb;
          cursor: pointer;
        }

        .active-row-content {
          color: $color-primary;
          background-color: #e8f6f0;
        }
      }

      .statistic-table-panel {
        flex: 1;
        height: fit-content;
        padding: 20px;
        border-radius: 6px;
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

        .operate-bar {
          @include flex(row, space-between, center);
          margin-bottom: 10px;

          .filter-bar {
            @include flex(row, flex-start, center);
            flex: 1;
          }

          .btn-create {
            flex: none;
            height: 28px;
            border-color: #13ce66;
            color: #13ce66;
            font-size: 14px;
            background-color: #e8faf0;
          }
        }

        .pager {
          float: right;
          margin-top: 14px;
        }
      }
    }
  }
</style>
