<template>
  <div class="card-general-topics">
    <div class="card-title">题组得分分析</div>
    <div class="card-content">
      <div ref="chart" class="card-left"></div>
      <div class="card-right">
        <Table :columns="tableColumns" :data="topics"></Table>
      </div>
    </div>
  </div>
</template>

<script>
  import echarts from '@/utils/echarts'
  import { roundNumber } from '@/utils/math'

  export default {
    props: {
      classDetail: Object,
    },
    data() {
      return {
        chartInstance: null,
      }
    },
    computed: {
      topics() {
        return this.classDetail.topicScores
          .map(t => {
            let scoreRatePercent = roundNumber(t.scoreRate * 100, 1)
            let top5Names = t.students.map(stu => stu.studentName)
            return {
              ...t,
              scoreRatePercent,
              top5Names,
            }
          })
          .sort((a, b) => a.scoreRate - b.scoreRate)
      },
      tableColumns() {
        return [
          {
            title: '题型',
            key: 'topicName',
            minWidth: 150,
          },
          {
            title: '得分率',
            align: 'center',
            width: 100,
            render: (h, params) => h('span', `${params.row.scoreRatePercent}%`),
          },
          {
            title: '错误前 5 名',
            align: 'center',
            minWidth: 300,
            render: (h, params) => {
              return h(
                'span',
                params.row.top5Names.map((name, idx) =>
                  h(
                    'span',
                    {
                      style: {
                        display: 'inline-block',
                        minWidth: '3em',
                        marginLeft: idx > 0 ? '1em' : 0,
                      },
                    },
                    name
                  )
                )
              )
            },
          },
        ]
      },
      chartOption() {
        let topics = this.topics.slice().reverse()
        return {
          tooltip: {
            trigger: 'item',
          },
          grid: {
            left: 100,
            top: 24,
            bottom: 24,
          },
          yAxis: {
            type: 'category',
            data: topics.map(t => t.topicName),
            axisTick: {
              show: false,
            },
          },
          xAxis: {
            type: 'value',
            label: '得分率',
          },
          series: [
            {
              type: 'bar',
              data: topics.map(t => ({
                name: t.topicName,
                value: t.scoreRatePercent,
                itemStyle: {
                  color: '#3ECBC4',
                },
              })),
              barWidth: 16,
            },
          ],
        }
      },
    },
    watch: {
      chartOption() {
        this.drawChart()
      },
    },
    mounted() {
      if (this.$refs.chart) {
        this.chartInstance = echarts.init(this.$refs.chart)
        this.drawChart()
      }
    },
    beforeUnmount() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
    },
    methods: {
      drawChart() {
        if (this.chartInstance) {
          this.chartInstance.setOption(this.chartOption, true)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .card-left {
    align-self: stretch;
    min-height: 300px;
  }
</style>
