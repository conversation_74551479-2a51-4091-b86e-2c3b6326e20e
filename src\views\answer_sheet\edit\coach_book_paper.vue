<template>
  <SheetEdit
    :answer-sheet="answerSheet"
    :source-type="sourceType"
    :source-id="paperId"
    @save-sheet="saveSheet"
    @download-sheet="downloadSheet"
    @page-back="pageBack"
  ></SheetEdit>
</template>

<script>
  import SheetEdit from './edit'

  import {
    apiGetAnswerSheetIdByPaperId,
    apiGetAnswerSheetNewId,
    apiGetAnswerSheetDetail,
    apiGetAnswerSheetInfo,
    apiGetAnswerSheetPDFUrl,
    apiSaveAnswerSheet,
  } from '@/api/emarking/answer_sheet'
  import { apiGetPaperContent } from '@/api/qlib/paper'
  import { apiGetCoachBookInfo, apiGetCoachBookPapers } from '@/api/qlib/coach_book'

  import { mapGetters, mapMutations } from 'vuex'
  import {
    parseQuestionsAndBlocks,
    isSameAnswerSheetPaperStructure,
    updateQuestionNameScoreAnswerBlockName,
    getScoreChangedSubjectiveQuestionsTableHtml,
  } from '@/helpers/answer_sheet/util'
  import PaperStructure from '@/helpers/answer_sheet/paper_structure'
  import { downloadUrl } from '@/utils/download'
  import { numberToChinese } from '@/utils/number'

  import SourceTypeEnum from '@/enum/answer_sheet/source_type'
  import ModeEnum from '@/enum/answer_sheet/mode'
  import { SheetVersion } from '@/const/answer_sheet'

  export default {
    components: {
      SheetEdit,
    },
    data() {
      return {
        paper: null,
        sheetId: '',
        answerSheet: null,
        // 教辅
        coachBookInfo: null,
        coachBookPaperInfo: null,
      }
    },
    computed: {
      ...mapGetters('answerSheet', ['sheetName']),
      paperId() {
        return this.$route.params.paperId
      },
      coachBookId() {
        return this.$route.params.coachBookId
      },
      isNewAnswerSheet() {
        return !this.sheetId
      },
      sourceType() {
        return SourceTypeEnum.Paper.id
      },
    },
    created() {
      this.resetSheet()
      this.init()
    },
    methods: {
      ...mapMutations('answerSheet', [
        'resetSheet',
        'createFromPaper',
        'setCoachBookInfo',
        'initCoachBookStyle',
        'extractCoachBookInfo',
        'changePageFooterContent',
        'displayQuestionContent',
        'generatePages',
      ]),
      async init() {
        this.showLoadingSpin()
        try {
          await Promise.all([this.loadPaper(), this.loadCoachBook()])
          if (this.isNewAnswerSheet) {
            this.createNewSheet()
          } else {
            await this.loadSheet()
            await this.checkAndImportSheet()
          }
        } finally {
          this.$Spin.hide()
        }
      },
      showLoadingSpin() {
        this.$Spin.show({
          render: h =>
            h(
              'div',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '正在加载答题卡，请稍候'
            ),
        })
      },

      /**
       * 创建新卡
       */
      createNewSheet() {
        return apiGetAnswerSheetNewId().then(newId => {
          this.generateSheetAndPage(newId)
        })
      },

      /**
       * 加载数据
       */
      loadCoachBook() {
        return Promise.all([apiGetCoachBookInfo(this.coachBookId), apiGetCoachBookPapers(this.coachBookId)]).then(
          ([coachBookInfo, papers]) => {
            let stageSubjects = this.$store.getters['qlib/stageSubjects']()
            let stage = stageSubjects.find(s => s.id == coachBookInfo.stageId)
            if (stage) {
              let subject = stage.subjects.find(s => s.id == coachBookInfo.subjectId)
              coachBookInfo.stageName = (stage && stage.name) || ''
              coachBookInfo.subjectName = (subject && subject.name) || ''
            }
            let grades = this.$store.getters['qlib/gradesByStage'](coachBookInfo.stageId)
            let grade = grades?.find(g => g.id == coachBookInfo.gradeId)
            coachBookInfo.gradeName = grade?.name || ''

            let termName = ''
            if (coachBookInfo.term == 1) {
              // 毕业年级科目不分上下册
              if (
                coachBookInfo.gradeId == 9 ||
                (coachBookInfo.gradeId == 8 && ['地理', '生物'].includes(coachBookInfo.subjectName))
              ) {
                termName = ''
              } else {
                termName = '上册'
              }
            } else if (coachBookInfo.term == 2) {
              termName = '下册'
            }
            coachBookInfo.termName = termName

            this.coachBookInfo = coachBookInfo
            papers.forEach((p, idx) => {
              p.sortCode = idx + 1
              p.sortCodeText = numberToChinese(p.sortCode)
            })
            this.coachBookPaperInfo = papers.find(x => x.id == this.paperId)
          }
        )
      },
      loadPaper() {
        return Promise.all([apiGetPaperContent(this.paperId), apiGetAnswerSheetIdByPaperId(this.paperId)]).then(
          ([paper, sheetId]) => {
            this.paper = paper
            this.sheetId = sheetId || ''
          }
        )
      },
      loadSheet() {
        return apiGetAnswerSheetDetail(this.sheetId).then(res => {
          this.answerSheet = res
        })
      },

      /**
       * 导入与生成
       */
      async checkAndImportSheet() {
        // 检查答题卡
        let sheet
        try {
          sheet = JSON.parse(this.answerSheet.sheetJson)
        } catch {
          this.handleSheetError('答题卡结构损坏', '抱歉！由于未知原因，答题卡结构已被损坏')
          return
        }

        if (sheet.version !== SheetVersion) {
          // 上一个版本可选导入，更旧版本不可导入
          if (sheet.version + 1 == SheetVersion) {
            let back = await this.handlePreviousVersion()
            if (back) {
              this.pageBack()
              return
            }
          } else {
            this.handleSheetError('答题卡版本不一致', '本答题卡不是由当前版本的答题卡制作工具制作的，无法直接编辑。')
            return
          }
        }

        let {
          objectiveQuestions: paperObjectives,
          subjectiveQuestions: paperSubjectives,
          blocks: paperBlocks,
        } = PaperStructure.extractFromPaper(this.paper)
        let { objectiveQuestions, subjectiveQuestions, blocks } = parseQuestionsAndBlocks({
          objectivesJson: this.answerSheet.objectivesJson,
          blocksJson: this.answerSheet.blocksJson,
          sheet,
        })
        // 比较试卷题目是否改变
        let isSamePaperStructure = isSameAnswerSheetPaperStructure(
          paperObjectives,
          paperSubjectives,
          objectiveQuestions,
          subjectiveQuestions
        )
        if (!isSamePaperStructure) {
          this.handleSheetError('试卷结构已改变', '当前试卷题目结构与答题卡不一致，无法直接编辑。')
        } else {
          // 先阅后扫提示分数改变
          if (sheet.mode == ModeEnum.PostScan.id) {
            let html = getScoreChangedSubjectiveQuestionsTableHtml(paperSubjectives, subjectiveQuestions)
            if (html) {
              this.$Modal.warning({
                title: '请注意',
                content: '以下题目分数发生改变，打分区域已重新生成：<br><br>' + html,
              })
            }
          }

          // 更新题目分数答案
          updateQuestionNameScoreAnswerBlockName({
            objectiveQuestions,
            subjectiveQuestions,
            blocks,
            objectiveDefines: paperObjectives,
            subjectiveDefines: paperSubjectives,
            blockDefines: paperBlocks,
            updateOriginalQuestionId: true,
          })

          try {
            await this.$store.dispatch('answerSheet/importSheet', {
              sheet,
              objectiveQuestions,
              subjectiveQuestions,
              blocks,
              paper: this.paper,
              enableEditQuestion: false,
              from: this.sourceType,
            })
          } catch (err) {
            this.$Message.error({
              content: '导入答题卡失败',
            })
            throw err
          }
          this.setCoachBookInfo(this.coachBookInfo)
          this.extractCoachBookInfo()
          this.initCoachBookStyle()
          // this.setDefaultPageFooterContent()
          this.generatePages()
        }
      },
      async generateSheetAndPage(newId) {
        let isIgradeCoachBookPrimaryStage = this.coachBookInfo?.partner == '同级生' && this.coachBookInfo?.stageId == 1
        try {
          this.createFromPaper({
            newId,
            paper: this.paper,
            title: isIgradeCoachBookPrimaryStage ? this.paper.paperInfo.name : null,
          })
        } catch (err) {
          // 延后弹出，避免上一个对话框关闭时将此对话框一起关闭了
          setTimeout(() => {
            this.$Modal.error({
              title: '生成答题卡失败',
              content: err,
            })
          }, 1000)
          return
        }
        this.setCoachBookInfo(this.coachBookInfo)
        this.initCoachBookStyle()
        // 同级生教辅答题卡统一加页脚
        this.setDefaultPageFooterContent()
        // 小学答题卡显示题目内容
        if (isIgradeCoachBookPrimaryStage) {
          await this.$store.dispatch('answerSheet/preProcessPaper')
          this.displayQuestionContent()
        }
        this.generatePages()
      },
      // 同级生教辅答题卡统一加页脚
      setDefaultPageFooterContent() {
        if (this.coachBookInfo?.partner != '同级生') {
          return
        }
        let pressName = ['语文', '道德与法治', '道法', '历史'].includes(this.coachBookInfo.subjectName)
          ? ''
          : this.coachBookInfo.pressName
        let subjectName = this.coachBookInfo.subjectName
        let gradeName = this.coachBookInfo.gradeName
        let termName = this.coachBookInfo.termName
        let paperSortCodeText = this.coachBookPaperInfo.sortCodeText
        let footerContent = `${pressName}${subjectName} ${gradeName}${termName}（${paperSortCodeText}）    第 {{page}} 页，共 {{total}} 页`
        this.changePageFooterContent(footerContent)
      },
      handleSheetError(title, content) {
        this.$Modal.confirm({
          title: `${title}`,
          content: `${content}
          <br><br>您可以：
          <br>1. 退出编辑，或：
          <br>2. 重新生成答题卡，<strong>原答题卡将被覆盖</strong>`,
          okText: '退出编辑',
          cancelText: '重新生成',
          onOk: () => {
            this.pageBack()
          },
          onCancel: () => {
            this.generateSheetAndPage(this.sheetId)
          },
        })
      },
      handlePreviousVersion() {
        this.$Spin.hide()
        return new Promise(resolve => {
          this.$Modal.confirm({
            title: '答题卡版本不一致',
            width: 430,
            content: `本答题卡不是由当前版本的答题卡制作工具制作的。
            <br><br>您可以：
            <br>1. 退出编辑，或：
            <br>2. 继续编辑答题卡，<strong>原答题卡版式将发生变化</strong>，需要您检查调整`,
            okText: '退出编辑',
            cancelText: '继续编辑',
            onOk: () => {
              resolve(true)
            },
            onCancel: () => {
              this.showLoadingSpin()
              resolve(false)
            },
          })
        })
      },

      /**
       * 按钮事件
       */
      async saveSheet(data) {
        data.coachBookId = this.$route.params.coachBookId
        data.sheetType = 3
        try {
          this.sheetId = await apiSaveAnswerSheet(data)
          this.$Message.success({
            content: '保存答题卡成功',
          })
          if (!this.answerSheet) {
            // 重新加载答题卡
            await this.loadSheet()
          } else {
            this.answerSheet.sheetJson = data.sheetJson
            this.answerSheet.objectivesJson = data.objectivesJson
            this.answerSheet.blocksJson = data.blocksJson
          }
        } finally {
          this.$Spin.hide()
        }
      },
      async downloadSheet() {
        this.$Spin.show({
          render: h =>
            h(
              'span',
              {
                style: {
                  fontSize: '24px',
                },
              },
              '下载中，请稍候'
            ),
        })
        let sheetInfo = await apiGetAnswerSheetInfo(this.sheetId)
        let pdfUrl = await apiGetAnswerSheetPDFUrl(this.sheetId)
        let fileName = `${sheetInfo.name}.pdf`
        if (sheetInfo && pdfUrl) {
          downloadUrl(pdfUrl, fileName, () => {
            this.$Spin.hide()
          })
        } else {
          this.$Spin.hide()
        }
      },
      pageBack() {
        this.$router.go(-1)
      },
    },
  }
</script>
