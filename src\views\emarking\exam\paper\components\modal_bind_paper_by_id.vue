<template>
  <Modal
    :model-value="modelValue"
    title="按试卷ID进行绑定"
    @on-ok="handleModalBindPaperOK"
    @on-visible-change="handleVisibilityChange"
  >
    <Input v-model="paperId" clearable placeholder="试卷ID" />
    <!-- <div v-if="existsObjectiveQuestion" style="margin-top: 16px">
      更新客观题答案<i-switch v-model="updateAnswer" style="margin-left: 8px"></i-switch>
    </div> -->
  </Modal>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { Message } from 'view-ui-plus'

  import { apiGetPaperInfo } from '@/api/qlib/paper'

  const props = defineProps({
    modelValue: Boolean,
    existsObjectiveQuestion: Boolean,
  })

  const emit = defineEmits(['update:modelValue', 'bind'])

  const paperId = ref('')
  const updateAnswer = ref(false)

  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        paperId.value = ''
        updateAnswer.value = false
      }
    }
  )

  async function handleModalBindPaperOK() {
    if (!paperId.value) {
      Message.warning({
        content: '请输入要绑定的试卷ID',
      })
      return
    }
    let paperInfo = await apiGetPaperInfo(paperId.value)
    if (paperInfo == null) {
      Message.warning({
        content: '找不到试卷',
      })
      return
    }
    emit('bind', { paperInfo, updateAnswer: updateAnswer.value })
  }

  function handleVisibilityChange(visibility) {
    if (!visibility) {
      emit('update:modelValue', false)
    }
  }
</script>
