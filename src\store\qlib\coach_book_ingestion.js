import { defineStore } from 'pinia'
import { ref } from 'vue'

import { apiGetCoachBookIngestionInfo } from '@/api/qlib/coach_book_ingestion'

export const useCoachBookIngestionStore = defineStore('coach-book-ingestion', () => {
  // 教辅信息
  const coachBookInfo = ref(null)
  // 录入项目信息
  const ingestionInfo = ref(null)
  // 当前步骤
  const currentStep = ref('upload')

  function setCoachBookInfo(info) {
    coachBookInfo.value = info
  }

  function setIngestionInfo(info) {
    ingestionInfo.value = info
  }

  function reset() {
    coachBookInfo.value = null
    ingestionInfo.value = null
  }

  async function refreshIngestionInfo() {
    let ingestionInfo = null
    if (coachBookInfo.value) {
      ingestionInfo = await apiGetCoachBookIngestionInfo(coachBookInfo.value.id)
    }
    setIngestionInfo(ingestionInfo)
  }

  return {
    coachBookInfo,
    ingestionInfo,
    currentStep,
    setCoachBookInfo,
    setIngestionInfo,
    reset,
    refreshIngestionInfo,
  }
})
