<template>
  <Modal :model-value="modelValue" class="modal-add-school" title="添加学校" @on-visible-change="handleVisibleChange">
    <Form :label-width="80">
      <FormItem label="选择学校">
        <Select
          v-model="schoolId"
          placeholder="输入学校名称搜索"
          clearable
          filterable
          @on-query-change="handleQueryChange"
        >
          <Option v-for="s in filterSchools" :key="s.id" :value="s.id">{{ s.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="考试编码">
        <Input v-model="examSchoolCode" type="text" :maxlength="examSchoolCodeMaxLength" clearable></Input>
      </FormItem>
    </Form>
    <template #footer>
      <div>
        <Button type="text" :disabled="adding" @click="handleCancel">取消</Button>
        <Button type="primary" :loading="adding" @click="handleModalAddSchoolOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiGetSchools } from '@/api/emarking'

  import { debounce } from '@/utils/function'

  export default {
    props: {
      modelValue: Boolean,
      examSchoolCodeMaxLength: Number,
      examSchoolCodes: Array,
    },
    emits: ['update:modelValue', 'add'],
    data() {
      return {
        allSchools: [],
        query: '',
        schoolId: '',
        examSchoolCode: '',
        adding: false,
      }
    },
    computed: {
      exam() {
        return this.$store.getters['emarking/exam']
      },
      examSchoolIdSet() {
        return new Set(this.exam.schools.map(s => s.schoolId))
      },
      filterSchools() {
        if (!this.query) {
          return []
        }
        return this.allSchools.filter(s => s.name.includes(this.query)).slice(0, 100)
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init()
        }
      },
    },
    methods: {
      init() {
        this.allSchools = []
        this.query = ''
        this.schoolId = ''
        this.examSchoolCode = ''
        this.adding = false
        this.getAllSchools()
      },
      getAllSchools() {
        apiGetSchools().then(resSchools => {
          this.allSchools = resSchools.filter(x => !this.examSchoolIdSet.has(x.id))
        })
      },
      handleQueryChange: debounce(function (query) {
        this.query = query || ''
      }, 200),
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      handleModalAddSchoolOK() {
        this.examSchoolCode = (this.examSchoolCode || '').trim()

        let message = this.check()
        if (message) {
          this.$Message.info({
            content: message,
          })
          return
        }
        this.$emit('add', {
          schoolId: this.schoolId,
          examSchoolCode: this.examSchoolCode,
        })
        this.adding = true
      },
      check() {
        if (!this.schoolId) {
          return '请选择学校'
        }
        if (this.examSchoolIdSet.has(this.schoolId)) {
          return '考试已添加该学校'
        }
        // 如果存在考试编码，则检查编码长度与其他学校一致
        if (!this.examSchoolCode) {
          return
        }
        let hasCodeSchool = this.examSchoolCodes.find(x => x.examSchoolCode)
        if (!hasCodeSchool) {
          return
        }
        let schoolCodeLength = String(hasCodeSchool.examSchoolCode).length
        let pattern = new RegExp(`^\\d{${schoolCodeLength}}$`)
        if (!pattern.test(this.examSchoolCode)) {
          return `当前考试编码应为${schoolCodeLength}位数字`
        }
        // 不能与其他学校相同
        let sameCodeSchool = this.examSchoolCodes.find(x => x.examSchoolCode == this.examSchoolCode)
        if (sameCodeSchool) {
          return `考试编码不能与【${sameCodeSchool.schoolName}】重复`
        }
      },
    },
  }
</script>
