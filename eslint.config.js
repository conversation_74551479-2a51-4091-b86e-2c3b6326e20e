const globals = require('globals')
const js = require('@eslint/js')
const vuePlugin = require('eslint-plugin-vue')
const importPlugin = require('eslint-plugin-import')
const prettierConfig = require('eslint-config-prettier')

module.exports = [
  js.configs.recommended,
  importPlugin.flatConfigs.recommended,
  ...vuePlugin.configs['flat/recommended'],
  {
    files: ['**/*.js', '**/*.cjs', '**/*.mjs', '**/*.vue'],
    ignores: ['build/', 'dist/', 'src/assets/'],
    languageOptions: {
      ecmaVersion: 'latest',
      globals: {
        ...globals.node,
        WxLogin: 'readonly',
      },
    },
    rules: {
      'no-console': 'error',
      'no-promise-executor-return': 'error',
      'no-template-curly-in-string': 'error',
      'no-unreachable-loop': 'error',
      'array-callback-return': 'error',
      'no-loop-func': 'error',
      'vue/require-default-prop': 'off',
      'vue/no-v-html': 'off',
      'vue/multi-word-component-names': 'off',
    },
    settings: {
      'import/resolver': {
        alias: {
          map: [['@', './src']],
          extensions: ['.js', '.mjs', '.cjs', '.vue'],
        },
      },
    },
  },
  prettierConfig,
]
