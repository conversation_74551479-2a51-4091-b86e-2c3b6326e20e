const path = require('path')
const rspack = require('@rspack/core')
const { VueLoaderPlugin } = require('vue-loader')

module.exports = function getConfig(type) {
  if (!['dev', 'build', 'dist'].includes(type)) {
    throw '获取webpack配置函数getConfig参数type值错误'
  }

  // --------------------------------------------------------------------------------
  let mode = type === 'dist' ? 'production' : 'development'

  // --------------------------------------------------------------------------------
  let entry = {
    main: path.join(__dirname, './src/main.js'),
  }

  // --------------------------------------------------------------------------------
  let output = {
    filename: type == 'dev' ? 'scripts/[name].js' : 'scripts/[name]-[contenthash].js',
    path: path.join(__dirname, type === 'dist' ? './dist/' : './build/'),
    publicPath: '/',
    clean: true,
    devtoolModuleFilenameTemplate: info => {
      return `webpack://${info.namespace}/${info.resourcePath}${info.loaders ? '-' + info.hash : ''}`
    },
  }

  // --------------------------------------------------------------------------------
  let resolve = {
    extensions: ['.js', '.vue', '.mjs'],
    alias: {
      '@': path.join(__dirname, './src'),
    },
  }

  // --------------------------------------------------------------------------------
  let cssRuleUse = [
    {
      loader: type === 'dist' ? rspack.CssExtractRspackPlugin.loader : 'style-loader',
    },
    {
      loader: 'css-loader',
    },
  ]
  let module = {
    rules: [
      // html2canvas不支持text-underline-offset，暂时修改源码
      {
        test: /html2canvas\.js$/,
        loader: 'string-replace-loader',
        options: {
          search: '_this\\.ctx\\.fillRect\\(text\\.bounds\\.left, Math\\.round\\(text\\.bounds\\.top \\+ baseline\\)',
          replace(match) {
            let replacement = '_this.ctx.fillRect(text.bounds.left, Math.round(text.bounds.top + baseline + 2)'
            console.log(`替换源码(html2canvas.js): \n${match}\n->\n${replacement}`)
            return replacement
          },
          flags: 'g',
        },
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          compilerOptions: {
            whitespace: 'preserve',
          },
        },
      },
      {
        test: /\.css$/,
        use: cssRuleUse,
      },
      {
        test: /\.less$/,
        use: [
          ...cssRuleUse,
          {
            loader: 'less-loader',
            options: {
              lessOptions: {
                javascriptEnabled: true,
                math: 'always',
              },
            },
          },
        ],
      },
      {
        test: /\.scss$/,
        use: [
          ...cssRuleUse,
          {
            loader: 'sass-loader',
            options: {
              sassOptions: {
                fiber: false,
              },
              additionalData: `
                @use "@/assets/styles/variables.scss" as *;
                @use "@/assets/styles/mixins.scss" as *;
              `,
            },
          },
        ],
      },
      {
        test: /\.(gif|jpg|png|svg)$/,
        type: 'asset',
        generator: {
          filename: 'images/[name]-[contenthash][ext]',
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf)$/,
        type: 'asset',
        generator: {
          filename: 'fonts/[name]-[contenthash][ext]',
        },
      },
    ],
  }
  if (type === 'dist') {
    module.rules.push({
      test: /\.m?js$/,
      exclude: [
        /node_modules/,
        // 如需编译node_modules中的代码，以下目录应跳过
        //   /node_modules[\\\/]@babel[\\\/]runtime-corejs/,
        //   /node_modules[\\\/]core-js/,
        //   /node_modules[\\\/]webpack[\\\/]buildin/,
      ],
      use: [
        {
          loader: 'babel-loader',
        },
      ],
    })
  }

  // --------------------------------------------------------------------------------
  let plugins = [
    new VueLoaderPlugin(),
    new rspack.DefinePlugin({
      __VUE_OPTIONS_API__: 'true',
      __VUE_PROD_DEVTOOLS__: 'false',
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
    }),
    new rspack.HtmlRspackPlugin({
      filename: './index.html',
      template: './src/templates/index.html',
    }),
    new rspack.CopyRspackPlugin({
      patterns: [
        { from: './src/assets/scripts/ckeditor', to: './scripts/ckeditor' },
        { from: './src/assets/images/icon.png', to: './images/icon.png' },
        { from: './src/assets/styles/login_wechat_custom.css', to: './styles/login_wechat_custom.css' },
      ],
    }),
  ]
  if (type === 'dist') {
    plugins.push(
      new rspack.CssExtractRspackPlugin({
        filename: 'styles/[name]-[contenthash].css',
        ignoreOrder: true,
      })
    )
  }

  // --------------------------------------------------------------------------------
  let optimization = {
    runtimeChunk: 'single',
    splitChunks: {
      chunks: 'all',
    },
  }
  if (type == 'dev') {
    optimization = undefined
  }

  // --------------------------------------------------------------------------------
  let devServer = {
    port: '8086',
    open: true,
    hot: true,
    historyApiFallback: true,
    client: {
      overlay: {
        runtimeErrors: false,
      },
    },
    proxy: [
      {
        context: ['/api2'],
        target: 'http://localhost:8300',
        secure: false,
        changeOrigin: true,
        pathRewrite: { '^/api2': '' },
      },
      {
        context: ['/lib'],
        target: 'https://mark.igrade.cn',
        secure: false,
        changeOrigin: true,
      },
      {
        context: ['/lib/ckeditor4/plugins/image3'],
        target: 'http://localhost:8086',
        secure: false,
        changeOrigin: false,
        pathRewrite: { '^/lib/ckeditor4/plugins/image3': '/scripts/ckeditor/plugins/image3' },
      },
    ],
  }

  // --------------------------------------------------------------------------------
  let devtool = type == 'dist' ? false : 'cheap-module-source-map'

  // --------------------------------------------------------------------------------
  let performance = {
    assetFilter: assetFilename => !assetFilename.endsWith('.gif'),
    maxEntrypointSize: 1024 * 1024 * 3,
    maxAssetSize: 1024 * 1024 * 1.5,
  }

  // --------------------------------------------------------------------------------
  return {
    mode,
    entry,
    output,
    resolve,
    module,
    plugins,
    optimization,
    devServer,
    devtool,
    performance,
  }
}
