<script>
  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          // nothing.
        } else {
          this.onCloseModal()
        }
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" @on-visible-change="handleModalVisibleChange">
    <div class="modal-project-subject-questions-conflict-handler">
      <!-- <Form  -->
    </div>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-project-subject-questions-conflict-handler {
    background-color: white;
  }
</style>
