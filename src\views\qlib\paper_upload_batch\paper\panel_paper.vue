<template>
  <div ref="containerRef" class="container-paper-upload-batch-paper">
    <div v-if="topics.length == 0" class="empty">暂无试卷题目</div>
    <template v-else>
      <div class="header">
        <div class="switcher">
          <span class="switcher-label">查看解析</span>
          <i-switch v-model="showAllAnswers" size="small" @on-change="toggleShowAllAnswers"></i-switch>
        </div>
        <template v-if="saving">正在保存中...</template>
        <template v-else-if="!questionsSaved">
          <Poptip confirm transfer title="确定完成本试卷划题？" @on-ok="finish">
            <Button class="btn-finish" type="primary" size="small">完成</Button>
          </Poptip>
        </template>
      </div>
      <div class="main">
        <div v-for="t in topics" :key="t.code" class="p-topic">
          <div class="p-topic-head">
            <span>{{ t.content }}</span>
            <template v-if="!questionsSaved">
              <Button class="btn-set-topic" type="primary" size="small" @click="handleEditTopic(t)">设置</Button>
              <Poptip confirm transfer :title="`确定删除【${t.title}】？`" @on-ok="removeTopic(t)">
                <Button class="btn-delete" type="warning" size="small">删除</Button>
              </Poptip>
            </template>
          </div>
          <div class="p-topic-questions">
            <QuestionCard
              v-for="q in t.questions"
              ref="questionCardsRef"
              :key="q.id"
              class="question-item"
              :question="q"
              :show-chapter="false"
              :show-btn-expand-branch="enableExpandBranch"
              :enable-change="!questionsSaved"
              @remove="removeQuestion(q)"
              @edit="editQuestion(q)"
              @toggle-expand-branch="toggleExpandBranch(q)"
            >
            </QuestionCard>
          </div>
        </div>
      </div>
    </template>

    <ModalEditTopic
      :show="showModalEditTopic"
      :topic="topicInModalEdit"
      @cancel="showModalEditTopic = false"
      @change="changeTopicInfo"
    ></ModalEditTopic>

    <ModalEditQuestion
      :show="showModalEditQuestion"
      :question="questionInModalEdit"
      @cancel="showModalEditQuestion = false"
      @change="changeQuestion"
    ></ModalEditQuestion>
  </div>
</template>

<script setup>
  import { ref, computed, useTemplateRef, onMounted } from 'vue'
  import { useUploadBatchStore } from '@/store/qlib/upload_batch'

  import QuestionCard from './question_card.vue'
  import ModalEditTopic from './modal_edit_topic.vue'
  import ModalEditQuestion from './modal_edit_question.vue'

  import { apiAddDividePaperQuestions } from '@/api/qlib/paper_upload'
  import StepStatusEnum from '@/enum/qlib/upload_batch/step_status'

  const uploadBatchStore = useUploadBatchStore()
  const containerRef = useTemplateRef('containerRef')
  const questionCardsRef = useTemplateRef('questionCardsRef')

  // 显示所有答案
  const showAllAnswers = ref(true)
  // 大题设置
  const showModalEditTopic = ref(false)
  const topicInModalEdit = ref(null)
  // 题目编辑
  const showModalEditQuestion = ref(false)
  const questionInModalEdit = ref(null)
  // 页面宽度
  const containerWidth = ref(0)
  const containerResizeObserver = new ResizeObserver(() => {
    if (containerRef.value) {
      containerWidth.value = containerRef.value.clientWidth
    }
  })

  // 试卷
  const paper = computed(() => {
    return uploadBatchStore.currentFile?.paper
  })
  // 所有大题
  const topics = computed(() => {
    return paper.value?.paperStructure.topics || []
  })
  const enableExpandBranch = computed(() => {
    let subjectName = paper.value?.paperInfo?.subject?.name
    return ['语文', '英语', '地理'].includes(subjectName)
  })
  // 是否已保存
  const questionsSaved = computed(() => {
    return uploadBatchStore.currentFile?.saveQuestionsStatus == StepStatusEnum.Succeeded.id
  })
  // 是否正在保存
  const saving = computed(() => {
    return uploadBatchStore.currentFile?.saveQuestionsStatus == StepStatusEnum.Processing.id
  })

  onMounted(() => {
    containerResizeObserver.observe(containerRef.value)
  })

  // 切换显示答案解析
  function toggleShowAllAnswers(show) {
    if (questionCardsRef.value) {
      questionCardsRef.value.forEach(card => {
        card.setCollapsed(!show)
      })
    }
  }
  // 保存题目
  async function finish() {
    if (questionsSaved.value) {
      return
    }
    if (!paper.value) {
      return
    }
    let { paperInfo, paperStructure, questionList } = paper.value
    if (questionList.length == 0) {
      return
    }

    uploadBatchStore.addCurrentFileMessage(`保存题目中...`)
    uploadBatchStore.setCurrentFileSavingQuestions()
    try {
      await apiAddDividePaperQuestions({
        paperId: paperInfo.id,
        paperStructure,
        questions: questionList,
      })
      uploadBatchStore.setCurrentFileSaveQuestionsSucceeded()
      uploadBatchStore.addCurrentFileMessage(`保存题目成功`)
      uploadBatchStore.nextFile()
    } catch (err) {
      let message = err.message || err.msg
      uploadBatchStore.setCurrentFileSaveQuestionsFailed(message)
      uploadBatchStore.addCurrentFileMessage(`保存题目失败: ${message}`, 'error')
      throw err
    }
  }

  // 编辑大题
  function handleEditTopic(topic) {
    topicInModalEdit.value = topic
    showModalEditTopic.value = true
  }
  function changeTopicInfo(topic) {
    paper.value?.changeTopicInfo(topic)
    showModalEditTopic.value = false
  }
  // 删除大题
  function removeTopic(topic) {
    let questionIds = topic.questions.map(q => q.id)
    paper.value?.remove(questionIds)
  }

  // 合并/拆分小题
  function toggleExpandBranch(question) {
    if (question.code == 0) {
      paper.value?.collapseQuestionBranches(question)
    } else {
      paper.value?.expandQuestionBranches(question)
    }
  }
  // 编辑题目
  function editQuestion(question) {
    questionInModalEdit.value = question
    showModalEditQuestion.value = true
  }
  function changeQuestion(question) {
    paper.value?.replaceById(question)
    showModalEditQuestion.value = false
    questionInModalEdit.value = null
  }
  // 删除题目
  function removeQuestion(q) {
    paper.value?.remove([q.id])
  }
</script>

<style lang="scss" scoped>
  .container-paper-upload-batch-paper {
    @include flex(column, flex-start, stretch);
    position: relative;

    .empty {
      @include flex(column, center, center);
      width: 100%;
      height: 100%;
      color: $color-icon;
      font-size: $font-size-large;
    }

    .header {
      @include flex(row, space-between, center);
      flex-shrink: 0;
      gap: 16px;
      height: 40px;
      padding: 0 16px;
      border-bottom: 1px solid $color-border;
    }

    .main {
      flex-grow: 1;
      flex-shrink: 1;
      padding: 16px;
      overflow: auto;
    }
  }
</style>
