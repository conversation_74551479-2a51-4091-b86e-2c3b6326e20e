<template>
  <div class="scan-btn" :class="cls">
    <Button
      class="btn-start"
      type="primary"
      shape="circle"
      size="large"
      :disabled="isScanDisabled"
      @click="startScan"
      >{{ btnScanText }}</Button
    >
    <div v-if="existsMyScanEx" class="exception-tip error">存在定位或考号异常答卷，请您及时处理后再继续扫描</div>
    <div v-if="batchStatusUploadingOrCreatingExam" class="creating-exam-tip">正在识别教辅作业章节</div>
  </div>
</template>

<script>
  import ScannerStatusEnum from '@/enum/emarking/scanner_status'

  export default {
    props: {
      scannerStatusId: String,
      currentBatchStatus: String,
      stats: Object,
      inline: Boolean,
    },
    emits: ['start-scan'],
    computed: {
      hasExam() {
        return this.$store.getters['scan/hasExam']
      },
      cls() {
        return {
          inline: this.inline,
          block: !this.inline,
        }
      },
      existsMyScanEx() {
        return this.stats.existsMyScanEx
      },
      batchStatusUploadingOrCreatingExam() {
        return ['uploading', 'creatingExam'].includes(this.currentBatchStatus)
      },
      isScanDisabled() {
        return (
          this.scannerStatusId != ScannerStatusEnum.Ready.id ||
          this.existsMyScanEx ||
          this.batchStatusUploadingOrCreatingExam
        )
      },
      btnScanText() {
        if (this.scannerStatusId == ScannerStatusEnum.Scanning.id) {
          return '扫描中'
        } else {
          return '开始扫描'
        }
      },
    },
    created() {
      if (!this.inline) {
        document.addEventListener('keydown', this.handleKeydown)
      }
    },
    beforeUnmount() {
      document.removeEventListener('keydown', this.handleKeydown)
    },
    methods: {
      startScan() {
        if (this.isScanDisabled) {
          return
        }
        this.$emit('start-scan')
      },
      handleKeydown(event) {
        if (event.key == 'Enter') {
          this.startScan()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-btn {
    position: relative;

    .exception-tip {
      position: absolute;
      top: 50px;
      color: $color-icon;
      white-space: nowrap;

      &.error {
        color: $color-error;
      }
    }

    .creating-exam-tip {
      position: absolute;
      top: 50px;
      color: $color-icon;
      white-space: nowrap;
    }

    &.inline {
      margin-left: auto;

      .exception-tip,
      .creating-exam-tip {
        right: 0;
        text-align: right;
      }
    }

    &.block {
      margin-top: 30px;
      text-align: center;

      .btn-start {
        width: 200px;
      }

      .exception-tip,
      .creating-exam-tip {
        left: 0;
        width: 100%;
        text-align: center;
      }
    }
  }
</style>
