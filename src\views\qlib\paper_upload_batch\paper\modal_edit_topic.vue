<template>
  <Drawer
    :transfer="false"
    inner
    :model-value="show"
    :title="modalTitle"
    width="100%"
    :mask="false"
    @on-visible-change="handleVisibleChange"
  >
    <div class="drawer-inner">
      <div class="drawer-body">
        <Form v-if="cloneTopic" class="section-info" :label-width="70" label-position="left">
          <FormItem label="大题名称">
            <Input v-model="cloneTopic.name" maxlength="20"></Input>
          </FormItem>
          <FormItem label="大题说明">
            <Input v-model="cloneTopic.description" maxlength="100"></Input>
          </FormItem>
        </Form>

        <div v-if="cloneTopic" class="section-score">
          <div class="node-topic">
            <span class="topic-info">本大题共 {{ cloneTopic.vQuestions.length }} 题，共 {{ cloneTopic.score }} 分</span>
            <span v-if="cloneTopic.showSetEachQuestionScore" class="topic-action">
              <span>每题</span>
              <Input
                class="input-score"
                size="small"
                type="number"
                :model-value="cloneTopic.eachQuestionScore"
                @on-change="setEachQuestionScore"
              ></Input>
              <span>分</span>
            </span>
          </div>

          <div class="question-list">
            <div v-for="q in cloneTopic.vQuestions" :key="q.code" class="question">
              <div class="node">
                <span class="question-info">
                  <span class="question-code">{{ q.code }}题</span>
                  <span class="question-score">
                    <Input
                      class="input-score"
                      size="small"
                      type="number"
                      :model-value="q.score"
                      :disabled="q.branches.length > 1"
                      @on-change="setQuestionScore(q, $event)"
                    ></Input>
                  </span>
                </span>
                <span v-if="q.showSetEachBranchScore" class="question-action">
                  <span>每小题</span>
                  <Input
                    class="input-score"
                    size="small"
                    type="number"
                    :model-value="q.eachBranchScore"
                    @on-change="setEachBranchScore(q, $event)"
                  ></Input>
                  <span>分</span>
                </span>
              </div>

              <div v-if="q.branches.length > 1" class="branch-list">
                <div v-for="b in q.branches" :key="b.code" class="branch node">
                  <span class="branch-info">
                    <span class="branch-code">小题{{ b.code }}</span>
                    <span class="branch-score">
                      <Input
                        class="input-score"
                        size="small"
                        type="number"
                        :model-value="b.score"
                        @on-change="setBranchScore(b, $event)"
                      ></Input>
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="drawer-footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK">确定</Button>
      </div>
    </div>
  </Drawer>
</template>

<script>
  export default {
    props: {
      show: Boolean,
      topic: Object,
    },
    emits: ['change', 'cancel'],
    data() {
      return {
        cloneTopic: null,
      }
    },
    computed: {
      modalTitle() {
        if (!this.cloneTopic) {
          return ''
        }

        return `第${this.cloneTopic.codeInChinese}大题`
      },
    },
    watch: {
      show() {
        if (this.show) {
          this.cloneTopic = {
            code: this.topic.code,
            codeInChinese: this.topic.codeInChinese,
            name: this.topic.name,
            description: this.topic.description,
            score: this.topic.score,
            eachQuestionScore: null,
            showSetEachQuestionScore:
              this.topic.vQuestions.length > 1 && this.topic.vQuestions.every(q => q.branches.length === 1),
            vQuestions: this.topic.vQuestions.map(q => {
              return {
                questionId: q.questionId,
                code: q.code,
                score: q.score,
                eachBranchScore: null,
                showSetEachBranchScore: q.branches.length > 1,
                branches: q.branches.map(b => ({
                  id: b.id,
                  code: b.code,
                  score: b.score,
                })),
              }
            }),
          }

          this.updateTopicScore()
        }
      },
    },
    methods: {
      setEachQuestionScore(e) {
        let score = this.getScore(e)
        this.cloneTopic.vQuestions.forEach(q => {
          q.branches.forEach(b => {
            b.score = score
          })
        })

        this.updateTopicScore()
      },
      setQuestionScore(q, e) {
        let score = this.getScore(e)
        q.branches.forEach(b => {
          b.score = score
        })

        this.updateTopicScore()
      },
      setEachBranchScore(q, e) {
        let score = this.getScore(e)
        q.branches.forEach(b => {
          b.score = score
        })

        this.updateTopicScore()
      },
      setBranchScore(b, e) {
        b.score = this.getScore(e)

        this.updateTopicScore()
      },
      getScore(e) {
        let score = parseFloat((e && e.target && e.target.value) || 0)
        if (!(score >= 0)) {
          score = 0
        }
        return score
      },
      updateTopicScore() {
        let topic = this.cloneTopic,
          questions = this.cloneTopic.vQuestions

        questions.forEach(q => {
          q.score = q.branches.reduce((a, c) => a + c.score, 0)
          if (q.branches.every(b => b.score === q.branches[0].score)) {
            q.eachBranchScore = q.branches[0].score
          } else {
            q.eachBranchScore = null
          }
        })

        if (questions.every(q => q.score === questions[0].score)) {
          topic.eachQuestionScore = questions[0].score
        } else {
          topic.eachQuestionScore = null
        }

        topic.score = questions.reduce((a, c) => a + c.score, 0)
      },
      handleOK() {
        if (!this.cloneTopic.name) {
          this.$Message.info({
            content: '大题名称不能为空',
          })
          return
        }

        let scores = this.cloneTopic.vQuestions.map(q => ({
          id: q.questionId,
          branches: q.branches.map(b => ({
            id: b.id,
            score: b.score || 0,
          })),
        }))

        this.$emit('change', {
          code: this.cloneTopic.code,
          name: this.cloneTopic.name,
          description: this.cloneTopic.description,
          scores,
        })
      },
      handleCancel() {
        this.$emit('cancel')
      },
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .drawer-inner {
    @include flex(column, space-between, stretch);
    height: 100%;

    .drawer-body {
      flex-grow: 1;
      overflow: auto;
    }

    .drawer-footer {
      @include flex(row, flex-end, center);
      flex-shrink: 0;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid $color-border;
      column-gap: 8px;
    }
  }

  .section-score {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px dashed $color-divider;
  }

  .input-score {
    width: 60px;
  }

  .node-topic {
    @include flex(row, space-between, center);
    margin-bottom: 10px;
  }

  .node {
    @include flex(row, space-between, center);
    padding: 5px;

    &:hover {
      border-radius: 2px;
      background-color: #ebf7ff;
    }
  }

  .question-list {
    padding-left: 20px;
  }

  .branch-list {
    padding-left: 20px;
    color: $color-icon;
  }

  .question-code {
    display: inline-block;
    min-width: 2em;
    margin-right: 0.5em;
  }

  .branch-code {
    display: inline-block;
    min-width: 3em;
    margin-right: 0.5em;
  }
</style>
