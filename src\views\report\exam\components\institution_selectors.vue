<script>
  import { mapGetters } from 'vuex'

  export default {
    props: {
      showInstitutionSelector: {
        type: Boolean,
        default: false,
      },
      showSchoolSelector: {
        type: Boolean,
        default: false,
      },
      showClassSelector: {
        type: Boolean,
        default: false,
      },
    },

    computed: {
      ...mapGetters('report', [
        'currentInstitutionId',
        'underLevelInstitutions',
        'currentSchoolId',
        'visibleExamSchools',
        'currentClassId',
        'classes',
      ]),
    },

    methods: {
      changeInstitution(institutionId) {
        this.$store.dispatch('report/changeInstitution', institutionId || this.currentInstitutionId)
      },

      changeSchool(schoolId) {
        this.$store.dispatch('report/changeSchool', schoolId || this.currentSchoolId)
      },

      changeClass(classId) {
        this.$store.dispatch('report/changeClass', classId || this.currentClassId)
      },
    },
  }
</script>

<template>
  <div class="institution-selectors">
    <Select
      v-show="showInstitutionSelector"
      :model-value="currentInstitutionId"
      class="selector-institution"
      clearable
      filterable
      @on-change="changeInstitution"
    >
      <Option v-for="ins of underLevelInstitutions" :key="'ins-' + ins.id" :value="ins.id">{{ ins.name }}</Option>
    </Select>

    <Select
      v-show="showSchoolSelector"
      :model-value="currentSchoolId"
      class="selector-school"
      clearable
      filterable
      placeholder="选择学校"
      @on-change="changeSchool"
    >
      <Option v-for="school of visibleExamSchools" :key="'school-' + school.schoolId" :value="school.schoolId">{{
        school.schoolName
      }}</Option>
    </Select>

    <Select
      v-show="showClassSelector"
      :model-value="currentClassId"
      class="selector-class"
      clearable
      filterable
      placeholder="选择班级"
      @on-change="changeClass"
    >
      <Option v-for="cls of classes" :key="'cls-' + cls.classId" :value="cls.classId">{{ cls.className }}</Option>
    </Select>
  </div>
</template>

<style lang="scss" scoped>
  .institution-selectors {
    .selector-institution {
      flex-grow: 0;
      width: 200px;
      margin-left: 20px;
    }

    .selector-school {
      flex-grow: 0;
      width: 240px;
      margin-left: 20px;
    }

    .selector-class {
      flex-grow: 0;
      width: 180px;
      margin-left: 10px;
    }
  }
</style>
