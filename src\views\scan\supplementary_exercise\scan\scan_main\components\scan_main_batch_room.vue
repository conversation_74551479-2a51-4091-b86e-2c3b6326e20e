<template>
  <div class="batch-room">
    <div class="title-action">
      <div class="title">扫描批次</div>
      <div v-if="hasPermissionScan" class="action">
        <template v-if="action">
          <TextButton v-if="isActionDeleteBatch" type="error" @click="handleDelete">删除选中批次</TextButton>
          <TextButton v-if="isActionDeleteBatchRoom" type="error" @click="handleDelete">删除选中批次考场</TextButton>
          <TextButton type="default" @click="handleCancelAction">取消</TextButton>
        </template>
        <Dropdown v-else placement="bottom-end" @on-click="handleDropdownClick">
          <span class="action-label">操作<Icon type="ios-arrow-down"></Icon></span>
          <template #list>
            <DropdownMenu>
              <DropdownItem v-for="item in actionDropdownItems" :key="item.id" :name="item.id">{{
                item.name
              }}</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
      </div>
    </div>
    <Table
      class="table-batch-room"
      :columns="tableBatchRoomColumns"
      :data="tableBatchRoomData"
      :row-class-name="rowClassName"
      :max-height="tableMaxHeight"
      :span-method="handleSpan"
      @on-row-click="handleRowClick"
      @on-select="handleSelect"
      @on-select-cancel="handleSelectCancel"
      @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleSelectAllCancel"
    ></Table>
    <div class="page">
      <span class="total">共 {{ batchCount }} 个批次</span>
      <Page size="small" :total="batchCount" :model-value="currentPage" :page-size="pageSize" @on-change="changePage" />
    </div>
  </div>
</template>

<script>
  import { Tooltip } from 'view-ui-plus'

  import { apiDeleteScanBatch } from '@/api/scan/scan_batch'

  import { mapGetters } from 'vuex'
  import { showDeleteResult } from '@/helpers/scan/delete_result'
  import { uniq, groupArray } from '@/utils/array'
  import { formatDateTime } from '@/utils/date'
  import BatchStatusEnum from '@/enum/scan/batch_status'

  export default {
    props: {
      batchRooms: Array,
      batchCount: Number,
      roomNo: Number,
      currentPage: Number,
      pageSize: Number,
      top: Number,
    },
    emits: ['change-page', 'change-room'],
    data() {
      return {
        titleHeight: 37,
        pageHeight: 30,
        action: '',
        selectedBatchRooms: [],
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'isInSchoolExam', 'viewMode', 'hasPermissionScan']),
      existsNotScanInAllBatchRooms() {
        return this.batchRooms.some(batch => batch.existsNotScanInAllBatchRooms)
      },
      actionDropdownItems() {
        return [
          {
            id: 'deleteBatch',
            name: '删除批次',
          },
          {
            id: 'deleteBatchRoom',
            name: '删除批次考场',
          },
        ]
      },
      isActionDeleteBatch() {
        return this.action == 'deleteBatch'
      },
      isActionDeleteBatchRoom() {
        return this.action == 'deleteBatchRoom'
      },
      tableMaxHeight() {
        return window.innerHeight - this.top - this.titleHeight - this.pageHeight
      },
      tableBatchRoomColumns() {
        let columns = [
          {
            title: '批次',
            key: 'batchNo',
            minWidth: 60,
            maxWidth: 85,
          },
          {
            title: '考场',
            key: 'roomNo',
            minWidth: 60,
            align: 'center',
          },
          {
            title: '考生',
            key: 'total',
            align: 'center',
            width: 50,
            render: (h, params) =>
              h(
                'span',
                {
                  class: {
                    clickable: Boolean(params.row.roomNo),
                  },
                  onClick: e => {
                    e.stopPropagation()
                    if (params.row.roomNo) {
                      this.$emit('change-room', {
                        roomNo: params.row.roomNo,
                        status: 'all',
                      })
                    }
                  },
                },
                params.row.total
              ),
          },
          {
            title: '已扫',
            key: 'scanned',
            align: 'center',
            width: 50,
            render: (h, params) =>
              h(
                'span',
                {
                  class: {
                    clickable: Boolean(params.row.roomNo),
                  },
                  onClick: e => {
                    e.stopPropagation()
                    if (params.row.roomNo) {
                      this.$emit('change-room', {
                        roomNo: params.row.roomNo,
                        status: 'scanned',
                      })
                    }
                  },
                },
                params.row.scanned
              ),
          },
          {
            title: '标记缺考',
            key: 'markAbsent',
            align: 'center',
            width: 50,
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                '标记\n缺考'
              ),
            render: (h, params) =>
              h(
                'span',
                {
                  class: {
                    clickable: Boolean(params.row.roomNo),
                  },
                  onClick: e => {
                    e.stopPropagation()
                    if (params.row.roomNo) {
                      this.$emit('change-room', {
                        roomNo: params.row.roomNo,
                        status: 'markAbsent',
                      })
                    }
                  },
                },
                params.row.markAbsent
              ),
          },
          {
            title: '未扫',
            key: 'unScan',
            align: 'center',
            width: 50,
            renderHeader: h => {
              if (!this.existsNotScanInAllBatchRooms) {
                return h('span', '未扫')
              } else {
                return h(
                  Tooltip,
                  {
                    placement: 'top',
                    transfer: true,
                    content: '存在考场未扫描完整，请检查',
                  },
                  () =>
                    h(
                      'span',
                      {
                        class: {
                          'title-warning-un-scan': true,
                        },
                      },
                      '未扫'
                    )
                )
              }
            },
            render: (h, params) =>
              h(
                'span',
                {
                  class: {
                    clickable: Boolean(params.row.roomNo),
                    'warning-un-scan': params.row.unScan > 0 && params.row.batchStatus == BatchStatusEnum.Normal.id,
                  },
                  onClick: e => {
                    e.stopPropagation()
                    if (params.row.roomNo) {
                      this.$emit('change-room', {
                        roomNo: params.row.roomNo,
                        status: 'unScan',
                      })
                    }
                  },
                },
                params.row.unScan
              ),
          },
          {
            title: '扫描员',
            align: 'center',
            key: 'scanRealName',
            width: 55,
          },
          {
            title: '扫描时间',
            width: 70,
            align: 'center',
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                '扫描\n时间'
              ),
            render: (h, params) =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                params.row.scanTimeStr
              ),
          },
        ]

        if (this.action) {
          columns.unshift({
            type: 'selection',
            width: 30,
          })
        }

        return columns
      },
      tableBatchRoomData() {
        let rooms = []
        this.batchRooms.forEach(batch => {
          let batchInfo = {
            batchId: batch.batchId,
            batchNo: `${batch.scanClient}-${batch.batchNo}`,
            batchStatus: batch.status,
            scanRealName: batch.scanRealName,
            scanTimeStr: batch.scanTime ? formatDateTime(new Date(batch.scanTime), 'MM-DD\nHH:mm:ss') : '',
          }
          if (batch.rooms.length == 0) {
            rooms.push(batchInfo)
          } else {
            batch.rooms.forEach(room => {
              rooms.push({
                ...batchInfo,
                roomNo: room.roomNo,
                unScan: room.stats.total - room.stats.scanned - room.stats.markAbsent,
                ...room.stats,
              })
            })
          }
        })
        if (this.isActionDeleteBatch) {
          rooms.forEach(room => {
            room._checked = this.selectedBatchRooms.some(batchRoom => batchRoom.batchId == room.batchId)
          })
        } else if (this.isActionDeleteBatchRoom) {
          rooms.forEach(room => {
            room._checked = this.selectedBatchRooms.some(
              batchRoom => batchRoom.batchId == room.batchId && batchRoom.roomNo == room.roomNo
            )
          })
        }
        return rooms
      },
    },
    watch: {
      // 刷新时保留已选
      batchRooms() {
        this.selectedBatchRooms = this.selectedBatchRooms.filter(selectedBatchRoom => {
          if (this.isActionDeleteBatch) {
            return (
              selectedBatchRoom.roomNo == null &&
              this.batchRooms.some(batch => selectedBatchRoom.batchId == batch.batchId)
            )
          } else if (this.isActionDeleteBatchRoom) {
            let batch = this.batchRooms.find(batch => selectedBatchRoom.batchId == batch.batchId)
            if (!batch) {
              return false
            }
            if (batch.rooms.length == 0) {
              return selectedBatchRoom.roomNo == null
            } else {
              return batch.rooms.some(room => selectedBatchRoom.roomNo == room.roomNo)
            }
          } else {
            return false
          }
        })
      },
    },
    methods: {
      handleSpan({ row, rowIndex, column }) {
        if ((column.type == 'selection' && this.isActionDeleteBatch) || column.title == '批次') {
          let aboveRow = this.tableBatchRoomData[rowIndex - 1]
          if (aboveRow && aboveRow.batchId == row.batchId) {
            return [0, 0]
          }
          let rowSpan = 1
          let belowRow
          while ((belowRow = this.tableBatchRoomData[rowIndex + 1]) != null) {
            if (belowRow.batchId == row.batchId) {
              rowIndex++
              rowSpan++
            } else {
              break
            }
          }
          return [rowSpan, 1]
        }
      },
      handleDropdownClick(id) {
        this.action = id
      },
      handleCancelAction() {
        this.action = ''
        this.selectedBatchRooms = []
      },
      handleDelete() {
        if (!this.isActionDeleteBatch && !this.isActionDeleteBatchRoom) {
          return
        }
        let typeText = this.isActionDeleteBatchRoom ? '批次考场' : '批次'
        if (this.selectedBatchRooms.length == 0) {
          this.$Message.info({
            content: `请先选择要删除的${typeText}`,
          })
          return
        }
        let selectedBatchRooms = this.selectedBatchRooms.slice()
        let modalContent = `您将删除选中的以下${selectedBatchRooms.length}个${typeText}：<br>`
        modalContent += selectedBatchRooms
          .map(batchRoom => {
            let row = this.tableBatchRoomData.find(x => x.batchId == batchRoom.batchId)
            if (!row) {
              return `<strong style="color:red">批次不存在</strong>`
            }
            if (this.isActionDeleteBatchRoom) {
              return row.batchNo + (batchRoom.roomNo ? `：${batchRoom.roomNo}` : '')
            } else {
              return row.batchNo
            }
          })
          .map(str => `<span style="display:inline-block">${str}</span>`)
          .join('、')
        this.$Modal.confirm({
          title: `删除${typeText}`,
          content: modalContent,
          loading: true,
          onOk: () => {
            // 数据不断自动刷新，须检查数据选中批次考场是否发生变化
            let selectChanged = this.selectedBatchRooms.length != selectedBatchRooms.length
            if (!selectChanged) {
              selectChanged = this.selectedBatchRooms.some(batchRoom => {
                return !selectedBatchRooms.some(x => x.batchId == batchRoom.batchId && x.roomNo == batchRoom.roomNo)
              })
            }
            if (selectChanged) {
              this.$Message.info({
                content: '数据已发生变化，请重新选择',
              })
              this.$Modal.remove()
              return
            }

            let data = groupArray(selectedBatchRooms, x => x.batchId).map(g => {
              return {
                batchId: g.group[0].batchId,
                roomNos: g.group.map(x => x.roomNo).filter(Boolean),
              }
            })
            apiDeleteScanBatch({
              examSubjectId: this.$store.getters['scan/examSubjectId'],
              batches: data,
            })
              .then(res => {
                showDeleteResult(res, count => `已删除${count}份答卷`, {
                  examSubjectId: this.examSubjectId,
                  isInSchoolExam: this.isInSchoolExam,
                })
              })
              .finally(() => {
                this.$Modal.remove()
                this.changePage(this.currentPage)
              })
          },
        })
      },
      rowClassName(row) {
        let classes = []
        if (row.roomNo == this.roomNo) {
          classes.push('active')
        }
        return classes.join(' ')
      },
      handleSelect(rows) {
        if (this.isActionDeleteBatchRoom) {
          this.selectedBatchRooms = rows.map(row => {
            return {
              batchId: row.batchId,
              roomNo: row.roomNo || null,
            }
          })
        } else if (this.isActionDeleteBatch) {
          this.selectedBatchRooms = uniq(rows.map(row => row.batchId)).map(batchId => {
            return {
              batchId,
              roomNo: null,
            }
          })
        } else {
          this.selectedBatchRooms = []
        }
      },
      handleSelectCancel(rows) {
        this.handleSelect(rows)
      },
      handleSelectAll() {
        this.handleSelect(this.tableBatchRoomData)
      },
      handleSelectAllCancel() {
        this.selectedBatchRooms = []
      },
      handleRowClick(row) {
        if (row.roomNo) {
          this.$emit('change-room', {
            roomNo: row.roomNo,
            status: 'all',
          })
        }
      },
      changePage(currentPage) {
        this.selectedBatchRooms = []
        this.$emit('change-page', currentPage)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .batch-room {
    padding-left: 8px;
    border-left: 1px solid $color-border;
    /** 避免表格切换显示滚动条引起外部宽度变化 */
    overflow-x: hidden;
  }

  .title-action {
    @include flex(row, space-between, center);

    .title {
      margin-top: 8px;
      margin-bottom: 8px;
      font-weight: bold;
      line-height: 1.5;
    }

    .action {
      .action-label {
        cursor: pointer;
      }
    }
  }

  .table-batch-room {
    line-height: 1.2;

    :deep(.title-warning-un-scan) {
      color: $color-warning;
    }

    :deep(.ivu-table-row.active td) {
      background-color: $color-iview-table-active-row;
    }

    :deep(.clickable) {
      cursor: pointer;
    }

    :deep(.warning-un-scan) {
      display: inline-block;
      border-bottom: 2px solid $color-warning;
      color: $color-warning;
      font-weight: bold;
    }

    :deep(.ivu-table-cell) {
      padding-right: 4px;
      padding-left: 4px;
    }
  }

  .page {
    @include flex(row, flex-end, center);
    flex-wrap: wrap;
    margin-top: 8px;

    .total {
      margin-right: 10px;
      color: $color-icon;
    }
  }
</style>
