<script>
  import ObjectiveQuestion from '@/helpers/emarking/objective_question'
  import { numberToChinese } from '@/utils/number'
  import { randomId } from '@/utils/string'

  import { default as BranchTypeEnum, ObjectiveBranchTypeEnum } from '@/enum/qlib/branch_type'
  import { Max_Question_Code, Max_Option_Count, Min_Option_Count } from '@/const/emarking'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue', 'add-objectives'],

    data() {
      return {
        newObjectives: [],
      }
    },

    computed: {
      topics() {
        const TopicList = []

        for (let i = 1; i <= 15; i++) {
          TopicList.push({
            topicCode: i,
            topicCodeInChinese: numberToChinese(i),
          })
        }

        return TopicList
      },
      branchTypes() {
        return ObjectiveBranchTypeEnum.getEntries()
      },
      trueOrFalseBranchTypeId() {
        return BranchTypeEnum.TrueOrFalse.id
      },
      optionCountList() {
        const Counts = []

        for (let i = Min_Option_Count; i <= Max_Option_Count; i++) {
          Counts.push(i)
        }

        return Counts
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.newObjectives = []
          this.addObjective()
        } else {
          this.onCloseModal()
        }
      },

      changeTopicCode(item, code) {
        item.topicCode = code
        const TopicName = (this.topics.find(t => t.topicCode === item.topicCode) || { topicCodeInChinese: '' })
          .topicCodeInChinese
        item.questions.forEach(q => {
          q.topicCode = code
          q.topicName = TopicName
        })
      },
      changeCodeFrom(item, event) {
        item.codeFrom = event.target.value
        this.generateQuestions(item)
      },
      changeCodeTo(item, event) {
        item.codeTo = event.target.value
        this.generateQuestions(item)
      },
      changeBranchTypeId(item, id) {
        item.branchTypeId = id
        item.questions.forEach(q => (q.branchTypeId = id))
        if (item.branchTypeId === ObjectiveBranchTypeEnum.TrueOrFalse.id) {
          this.changeOptionCount(item, 2)
        }
      },
      changeOptionCount(item, count) {
        item.optionCount = count
        item.questions.forEach(q => (q.optionCount = count))
      },
      changeScore(item, event) {
        item.score = event.target.value
        const NumebrScore = Number(item.score)
        item.questions.forEach(q => (q.fullScore = NumebrScore))
      },

      addObjective() {
        const LastObjective = this.newObjectives[this.newObjectives.length - 1]
        this.newObjectives.push({
          itemId: randomId(),
          topicCode: (LastObjective && LastObjective.topicCode + 1) || 1,
          branchTypeId: (LastObjective && LastObjective.branchTypeId) || this.branchTypes[0].id,
          codeFrom: (LastObjective && LastObjective.codeTo && Number(LastObjective.codeTo) + 1) || '',
          codeTo: '',
          optionCount: (LastObjective && LastObjective.optionCount) || 4,
          score: (LastObjective && LastObjective.score) || '',
          questions: [],
        })
      },
      generateQuestions(item) {
        const CodeFrom = Number(item.codeFrom)
        const CodeTo = Number(item.codeTo)

        if (
          !(Number.isInteger(CodeFrom) && CodeFrom >= 1 && Number.isInteger(CodeTo) && CodeTo >= 1) ||
          CodeFrom > CodeTo ||
          CodeFrom > Max_Question_Code ||
          CodeTo > Max_Question_Code
        ) {
          item.questions = []
          return
        }

        const TempQuestions = []

        for (let i = CodeFrom; i <= CodeTo; i++) {
          let TargetQuestion = item.questions.find(x => x.questionCode === i)
          if (TargetQuestion) {
            TempQuestions.push(TargetQuestion)
          } else {
            TargetQuestion = new ObjectiveQuestion()
            TempQuestions.push(TargetQuestion)
            TargetQuestion.questionId = randomId()
            TargetQuestion.questionCode = i
            TargetQuestion.questionName = TargetQuestion.defaultQuestionName
            TargetQuestion.branchName = TargetQuestion.defaultBranchName
            TargetQuestion.topicCode = item.topicCode
            TargetQuestion.topicName = (
              this.topics.find(t => t.topicCode === item.topicCode) || { topicCodeInChinese: '' }
            ).topicCodeInChinese
            TargetQuestion.branchTypeId = item.branchTypeId
            TargetQuestion.optionCount = item.optionCount
            TargetQuestion.fullScore = Number(item.score) || 0
          }
        }

        item.questions = TempQuestions
      },
      deleteObjective(pdx) {
        this.newObjectives.splice(pdx, 1)
      },

      handleAddObjectives() {
        const TransferedNewObjectives = []

        this.newObjectives.forEach(obj => {
          ;(obj.questions || []).forEach(q => {
            TransferedNewObjectives.push({
              answer: '',
              answerScoreList: '',
              fullScore: q.fullScore,
              isAdditional: q.isAdditional,
              optionCount: q.optionCount,
              questionCode: q.questionCode,
              questionName: q.questionName,
              questionType: q.branchTypeId,
              topicCode: q.topicCode,
              topicName: q.topicName,
            })
          })
        })
        // console.log('transfered new objectives: ', this.newObjectives, TransferedNewObjectives)

        this.$emit('add-objectives', TransferedNewObjectives)
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" title="新增客观题" width="760" @on-visible-change="handleModalVisibleChange">
    <div class="modal-paper-add-objectives">
      <div v-for="(item, index) of newObjectives" :key="item.tempId" class="add-objectives-components">
        <span class="span-questions-serial-row-content">
          <span>第</span>
          <Select
            :model-value="item.topicCode"
            class="selector selector-topic-code"
            style="width: 70px"
            size="small"
            @click="changeTopicCode(item, $event)"
          >
            <Option v-for="t of topics" :key="t.topicCode" :value="t.topicCode">{{ t.topicCodeInChinese }}</Option>
          </Select>
          <span>大题，第</span>
          <Input
            :model-value="item.codeFrom"
            class="input"
            size="small"
            type="number"
            @on-change="changeCodeFrom(item, $event)"
          />
          <span>至</span>
          <Input
            :model-value="item.codeTo"
            class="input"
            size="small"
            type="number"
            @on-change="changeCodeTo(item, $event)"
          />
          <span>题，</span>
          <Select
            :model-value="item.branchTypeId"
            class="selector"
            style="width: 80px"
            size="small"
            @on-change="changeBranchTypeId(item, $event)"
          >
            <Option v-for="t of branchTypes" :key="t.id" :value="t.id">{{ t.name }}</Option>
          </Select>
          <span>，每题</span>
          <Select
            :model-value="item.optionCount"
            :disabled="item.branchTypeId === trueOrFalseBranchTypeId"
            class="selector"
            style="width: 50px"
            size="small"
            @on-change="changeOptionCount(item, $event)"
          >
            <Option v-for="op of optionCountList" :key="op" :value="op">{{ op }}</Option>
          </Select>
          <span>个选项，</span>
          <Input
            :model-value="item.score"
            style="width: 70px"
            size="small"
            type="number"
            @on-change="changeScore(item, $event)"
          />
          <span>分</span>
        </span>

        <span class="span-questions-serial-row-action">
          <Icon
            v-if="index + 1 === newObjectives.length"
            class="icon-btn btn-add-item"
            type="md-add"
            @click="addObjective"
          ></Icon>
          <Icon
            v-if="newObjectives.length > 1"
            class="icon-btn btn-remove-item"
            type="md-close"
            @click="deleteObjective(index)"
          ></Icon>
        </span>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary" @click="handleAddObjectives">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-paper-add-objectives {
    .add-objectives-components {
      @include flex(row, space-between, center);
      line-height: 40px;

      .span-questions-serial-row-content {
        .selector {
          display: inline-block;
        }

        .input {
          width: 50px;
        }
      }

      .span-questions-serial-row-action {
        min-width: 45px;
        text-align: right;

        .icon-btn {
          font-size: $font-size-large;
          cursor: pointer;
        }

        .btn-add-item {
          color: $color-success;
        }

        .btn-remove-item {
          color: $color-warning;
        }
      }
    }
  }
</style>
