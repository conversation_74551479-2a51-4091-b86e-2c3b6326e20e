<script>
  import Uploader from './uploader.vue'

  import { apiGetStudentRegistrationDetail, apiGetTeacherRegistrationDetail } from '@/api/review'

  import { mapGetters } from 'vuex'
  import { deepCopy } from '@/utils/object'

  export default {
    components: {
      'component-uploader': Uploader,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      user: {
        type: Object,
        default: () => null,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        userDetail: null,
      }
    },

    computed: {
      ...mapGetters('review', ['activityId', 'categories', 'participantIdentity']),

      isStudentPaticipant() {
        return this.participantIdentity === 'student'
      },

      categoryListForRadioGroup() {
        return this.categories.filter(c => c.id !== 'all')
      },

      selectedCategoryFields() {
        const Fileds = deepCopy(
          (
            (this.userDetail?.categoryId &&
              this.categoryListForRadioGroup.find(c => c.id === this.userDetail.categoryId)) || {
              fields: [],
            }
          ).fields
        ).map(f => {
          const TargetForm = (this.userDetail?.form || []).find(item => item.fieldId === f.id)
          if (TargetForm) {
            if (['text', 'textarea', 'radio', 'date'].includes(f.fieldType)) {
              f.fieldValue = TargetForm.fieldValue || ''
            } else if (f.fieldType === 'checkbox') {
              const ParseValue = TargetForm.fieldValue ? JSON.parse(TargetForm.fieldValue) : []
              f.fieldValue = ParseValue.join('、')
            } else if (f.fieldType === 'upload') {
              f.extraObject = f.extraJson ? JSON.parse(f.extraJson) : {}
              const NewAccepts = []
              if (f.extraObject.accepts.some(a => a.includes('image'))) {
                NewAccepts.push('.jpeg', '.jpg', '.png', '.gif')
              }
              if (f.extraObject.accepts.some(a => a.includes('pdf'))) {
                NewAccepts.push('.pdf')
              }
              f.extraObject.accepts = NewAccepts
              if (f.extraObject.maxFileSize) {
                f.extraObject.maxSize = f.extraObject.maxFileSize / 1024 / 1024
              }
              f.fieldValue = (TargetForm.fieldValue ? JSON.parse(TargetForm.fieldValue) : []).map(f => ({
                name: f.fileName || '',
                type: f.fileType || '',
                size: f.fileSize || '',
                path: f.filePath,
                file: f instanceof File ? f : null,
                isUploaded: true,
              }))
            }
          }
          if (!f.fieldValue) {
            f.fieldValue = ''
          }

          return f
        })
        Fileds.sort((a, b) => a.sortOrder - b.sortOrder)
        return Fileds
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.fetchUserDetail()
        } else {
          this.closeModal()
        }
      },

      fetchUserDetail() {
        if (!(this.user && this.user.categoryId && this.user.schoolId && this.user.userId)) {
          this.$Message.warning({
            duration: 4,
            content: '请求所需数据不全，请刷新重试',
          })
          this.staticFormValidate.userId = this.user.userId
          return
        }

        const Request = this.isStudentPaticipant ? apiGetStudentRegistrationDetail : apiGetTeacherRegistrationDetail
        const RequestParams = {
          activityId: this.activityId,
          categoryId: this.user.categoryId,
          schoolId: this.user.schoolId,
          userId: this.user.userId,
        }
        Request(RequestParams).then(response => (this.userDetail = response || null))
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    footer-hide
    title="查看报名信息"
    width="1000"
    @on-visible-change="handleModalVisibleChange"
  >
    <div class="modal-panel">
      <Form :label-width="200" label-position="right">
        <FormItem label="评审类别">{{ userDetail?.categoryName || '' }}</FormItem>
        <FormItem label="参赛学校">{{ userDetail?.schoolName || '' }}</FormItem>
        <FormItem label="参赛者">{{ userDetail?.realName || '' }}</FormItem>
        <FormItem v-for="f of selectedCategoryFields" :key="f.id" :label="f.fieldLabel">
          <component-uploader
            v-if="f.fieldType === 'upload'"
            :accepts="f.extraObject.accepts"
            :uploaded-files="f.fieldValue"
            :max-file-count="f.extraObject.maxFileCount"
            :max-size="f.extraObject.maxSize"
            :is-mode-check="true"
            tag-max-width="580px"
            style="width: 600px"
          ></component-uploader>
          <span v-else>{{ f.fieldValue }}</span>
        </FormItem>
      </Form>
    </div>
  </Modal>
</template>
