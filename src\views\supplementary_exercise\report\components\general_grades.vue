<template>
  <div class="card-general-grades">
    <div class="card-title">成绩等级</div>
    <div class="card-content">
      <div class="card-left">
        <div class="selector-tab">
          <RadioGroup v-model="activeTab" type="button" size="small">
            <Radio label="default">默认分段</Radio>
            <Radio label="custom">自定义分段</Radio>
          </RadioGroup>
          <div v-if="activeTab == 'custom'" class="custom-interval">
            <InputNumber
              v-model="customIntervalScoreText"
              :precision="0"
              :min="0"
              :max="fullScore"
              size="small"
              @on-change="changeCustomIntervalScore"
            ></InputNumber>
            <span class="label">分 / 段</span>
          </div>
        </div>
        <Table class="table-intervals" :columns="tableColumns" :data="intervals" :max-height="tableMaxHeight"></Table>
      </div>
      <div ref="chart" class="card-right" :class="{ hidden: intervals.length == 0 }"></div>
    </div>
  </div>
</template>

<script>
  import { isPositiveInteger } from '@/utils/number'
  import { roundNumber } from '@/utils/math'
  import echarts from '@/utils/echarts'
  import { debounce } from '@/utils/function'

  export default {
    props: {
      classDetail: Object,
    },
    data() {
      return {
        tableMaxHeight: window.innerHeight - 500,
        activeTab: 'default',
        customIntervalScoreText: null,
        customIntervalScore: null,
        chartInstance: null,
      }
    },
    computed: {
      fullScore() {
        return this.classDetail.fullScore || 0
      },
      defaultIntervals() {
        let scoreWeak = Math.round(this.fullScore * 0.6)
        let scoreGood = Math.round(this.fullScore * 0.85)
        return [
          {
            name: '满分',
            start: this.fullScore,
            startIncluded: true,
            end: this.fullScore,
            endIncluded: true,
          },
          {
            name: '优秀',
            start: scoreGood,
            startIncluded: true,
            end: this.fullScore,
            endIncluded: false,
          },
          {
            name: '良好',
            start: scoreWeak,
            startIncluded: true,
            end: scoreGood,
            endIncluded: false,
          },
          {
            name: '薄弱',
            start: 0,
            startIncluded: true,
            end: scoreWeak,
            endIncluded: false,
          },
        ]
      },
      customIntervals() {
        let list = []
        let valid = isPositiveInteger(this.customIntervalScore) && this.customIntervalScore < this.fullScore
        if (!valid) {
          return list
        }
        let start = 0
        let end
        while ((end = start + this.customIntervalScore) <= this.fullScore) {
          list.unshift({
            name: '',
            start,
            startIncluded: true,
            end,
            endIncluded: false,
          })
          start = end
        }
        if (end < this.fullScore) {
          list.unshift({
            name: '',
            start: end,
            startIncluded: true,
            end: this.fullScore,
            endIncluded: false,
          })
        }
        list.unshift({
          name: '',
          start: this.fullScore,
          startIncluded: true,
          end: this.fullScore,
          endIncluded: true,
        })
        return list
      },
      intervals() {
        let activeIntervals = this.activeTab == 'custom' ? this.customIntervals : this.defaultIntervals
        let studentScoreList = this.classDetail.studentScoreList
        return activeIntervals.map(interval => {
          // 段名称
          let intervalText = ''
          if (interval.start == this.fullScore) {
            intervalText = `${this.fullScore}分`
          } else {
            intervalText += interval.startIncluded ? '[' : '('
            intervalText += `${interval.start} - ${interval.end}`
            intervalText += interval.endIncluded ? ']' : ')'
            intervalText += '分'
          }
          let intervalNameAndText = interval.name
          if (interval.name) {
            intervalNameAndText += ' '
          }
          intervalNameAndText += intervalText

          // 段内学生统计
          let students = studentScoreList.filter(
            stu =>
              (stu.score > interval.start && stu.score < interval.end) ||
              (interval.startIncluded && stu.score == interval.start) ||
              (interval.endIncluded && stu.score == interval.end)
          )
          students.sort((a, b) => b.score - a.score)
          let studentCount = students.length
          let maxScore = students.length > 0 ? students[0].score : ''
          let minScore = students.length > 0 ? students[students.length - 1].score : ''
          let studentNames = students.map(stu => stu.studentName).join('，')
          let percent =
            studentScoreList.length == 0 ? 0 : roundNumber((students.length / studentScoreList.length) * 100, 1)

          return {
            ...interval,
            intervalText,
            intervalNameAndText,
            studentCount,
            maxScore,
            minScore,
            studentNames,
            percent,
          }
        })
      },
      tableColumns() {
        return [
          {
            title: '分段',
            key: 'intervalNameAndText',
            maxWidth: 180,
          },
          {
            title: '人数',
            key: 'studentCount',
            width: 100,
            align: 'center',
          },
          {
            title: '最高分',
            key: 'maxScore',
            width: 100,
            align: 'center',
          },
          {
            title: '最低分',
            key: 'minScore',
            width: 100,
            align: 'center',
          },
          {
            title: '学生',
            key: 'studentNames',
            tooltip: true,
            align: 'center',
          },
        ]
      },
      chartOption() {
        let intervals = this.intervals.slice().reverse()
        return {
          tooltip: {
            trigger: 'item',
          },
          grid: {
            top: 24,
            bottom: 124,
            right: 24,
          },
          xAxis: {
            type: 'category',
            data: intervals.map(interval => interval.name || interval.intervalText),
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: 0,
              width: 580 / intervals.length,
              overflow: 'break',
            },
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              type: 'bar',
              data: intervals.map(item => ({
                name: item.name || item.intervalText,
                value: item.studentCount,
                itemStyle: {
                  color: '#05C1AE',
                },
              })),
              barWidth: 16,
            },
          ],
        }
      },
      minAcceptableCustomInterval() {
        return this.fullScore ? Math.min(10, this.fullScore / 10) : 10
      },
    },
    watch: {
      chartOption() {
        this.drawChart()
      },
    },
    mounted() {
      if (this.$refs.chart) {
        this.chartInstance = echarts.init(this.$refs.chart)
        this.drawChart()
      }
    },
    beforeUnmount() {
      if (this.chartInstance) {
        this.chartInstance.dispose()
      }
    },
    methods: {
      changeCustomIntervalScore: debounce(function () {
        this.customIntervalScore = Math.max(this.customIntervalScoreText, this.minAcceptableCustomInterval)
        this.customIntervalScoreText = this.customIntervalScore
      }, 500),
      drawChart() {
        if (this.chartInstance) {
          this.chartInstance.setOption(this.chartOption, true)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .card-left {
    .selector-tab {
      @include flex(row, flex-start, center);
      margin-bottom: 8px;

      .custom-interval {
        margin-left: 16px;
      }
    }
  }

  .card-right {
    height: 380px;

    &.hidden {
      visibility: hidden;
    }
  }
</style>
