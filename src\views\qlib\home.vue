<template>
  <div class="page-qlib-home">
    <div class="section-header">
      <div class="container-left">
        <div class="subject-list">
          <div v-for="stage in stageSubjects" :key="stage.id" class="stage">
            <div class="stage-title">
              {{ stage.name }}
            </div>
            <div class="stage-subjects">
              <span
                v-for="subject in stage.subjects"
                :key="subject.id"
                class="subject-item"
                :class="{
                  'subject-item-current':
                    stage.id == currentStageSubject.stageId && subject.id == currentStageSubject.subjectId,
                }"
                @click="
                  onSubjectClick({
                    stageId: stage.id,
                    stageName: stage.name,
                    subjectId: subject.id,
                    subjectName: subject.name,
                  })
                "
              >
                {{ subject.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="container-middle">
        <div class="box-banner">
          <el-carousel height="250px">
            <el-carousel-item v-for="item in bannerList" :key="item.id">
              <div class="carousel-item">
                <a :href="item.link" :target="isTargetBlank(item.link)">
                  <img class="carousel-item-image" :src="item.imageUrl" :alt="item.name" />
                </a>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="box-search-bar">
          <div class="box-radio">
            <RadioGroup v-model="currentSearchType" type="button" button-style="solid" @on-change="changeSearchType">
              <Radio v-for="s of searchTypes" :key="s.key" :label="s.key">{{ s.name }}</Radio>
            </RadioGroup>
          </div>
          <div class="box-search">
            <Input
              v-model="keyword"
              search
              clearable
              enter-button="搜索"
              placeholder="请输入关键词"
              @on-search="onSearch"
            />
          </div>
        </div>
      </div>
      <div class="container-right">
        <div class="box-stat-item">
          <div class="inner-stat-item">
            <div class="box-item-icon"></div>
            <div class="box-item-content">
              <div class="item-name">组卷</div>
              <div class="item-stat" @click="onToMyPaperMake">
                <span>组卷份数：</span>
                <span class="count">{{ paperMakeCount }}</span>
              </div>
            </div>
          </div>
          <div class="inner-item-btn">
            <Button type="primary" @click="onToPaperMake">去组卷</Button>
          </div>
        </div>
      </div>
    </div>
    <div class="section-body">
      <div class="outer-list">
        <div class="block-list" v-for="c in collectionList" :key="c.collectionName">
          <div class="block-header">
            <span class="block-title">{{ c.collectionName }}</span>
            <TextButton type="default" class="block-more" @click="onToMore(c)">更多</TextButton>
          </div>
          <div class="block-body">
            <div class="list-item" v-for="paper in c.list" :Key="paper.id" @click="onToPaper(paper)">
              <span class="list-item-name">{{ paper.paperName }}</span>
              <span class="list-item-count">{{ paper.browseCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { ElCarousel, ElCarouselItem } from 'element-plus'
  import 'element-plus/es/components/carousel/style/css'
  import 'element-plus/es/components/carousel-item/style/css'
  import { mapMutations } from 'vuex'
  import { groupArray } from '@/utils/array'
  import { apiGetFrontPageBanners, apiGetFrontPagePaperList } from '@/api/qlib/front_page'
  import { apiGetMakeCount } from '@/api/qlib/paper'

  export default {
    components: {
      'el-carousel': ElCarousel,
      'el-carousel-item': ElCarouselItem,
    },
    data() {
      return {
        currentSearchType: 'question',
        searchTypes: [
          {
            name: '试题',
            key: 'question',
          },
          {
            name: '试卷',
            key: 'paper',
          },
        ],
        bannerList: [],
        collectionList: [],
        paperMakeCount: 0,
        keyword: '',
      }
    },
    computed: {
      currentStageSubject() {
        return this.$store.getters['qlib/currentStageSubject']
      },
      stageSubjects() {
        return this.$store.getters['qlib/stageSubjects']()
      },
      isQuestionLibraryAdministrator() {
        return this.$store.getters['user/isQuestionLibraryAdministrator']
      },
    },
    created() {
      this.getBanners()
      this.getPaperList()
      this.getMakeCount()
    },
    methods: {
      ...mapMutations({
        updateCurrentStageSubject: 'qlib/updateCurrentStageSubject',
      }),
      isTargetBlank(url) {
        return url.includes('http') || url.includes('https') ? '_blank' : '_self'
      },
      getBanners() {
        apiGetFrontPageBanners({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
        }).then(res => {
          this.bannerList = res || []
        })
      },
      getPaperList() {
        apiGetFrontPagePaperList({
          stageId: this.currentStageSubject.stageId,
          subjectId: this.currentStageSubject.subjectId,
        }).then(res => {
          const result = groupArray(res || [], item => item.collectionName).map(g => ({
            collectionName: g.key,
            list: g.group,
          }))

          this.collectionList = result
        })
      },
      getMakeCount() {
        apiGetMakeCount().then(res => {
          this.paperMakeCount = res || 0
        })
      },
      onSubjectClick(nextStageSubject) {
        if (this.isQuestionLibraryAdministrator) {
          this.updateCurrentStageSubject(nextStageSubject)
        } else {
          this.$store
            .dispatch('qlib/getUserCurrentStageSubjectEditorRolePermissions', nextStageSubject)
            .finally(() => this.updateCurrentStageSubject(nextStageSubject))
        }

        this.getBanners()
        this.getPaperList()
      },
      changeSearchType(val) {},
      onToMyPaperMake() {
        this.$router.push({
          name: 'qlib-records',
        })
      },
      onToPaperMake() {
        this.$router.push({
          name: 'qlib-byknowledge',
        })
      },
      onToPaper(paper) {
        this.$router.push({
          name: 'qlib-examPaperView',
          params: {
            paperid: paper.paperId,
          },
        })
      },
      onToMore(collection) {
        this.$router.push({
          path: '/qlib/bypaper',
          query: {
            paperTypeName: collection.collectionName,
          },
        })
      },
      onSearch() {
        if (this.currentSearchType === 'question') {
          this.$router.push({
            name: 'qlib-byknowledge',
            params: {
              keyword: this.keyword,
            },
          })
        } else {
          this.$router.push({
            name: 'qlib-bypaper',
            params: {
              keyword: this.keyword,
            },
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-qlib-home {
    .section-header {
      @include flex(row, flex-start, stretch);
      width: 100%;
      padding: 10px 0;
    }

    .container-left {
      box-sizing: border-box;
      width: 300px;
      padding: 10px 0;
      background-color: #fff;
    }

    .subject-list {
      box-sizing: border-box;
      width: 300px;
      padding: 0 15px;
      line-height: 2.5;
      background-color: white;
      // box-shadow: 0 0 10px $color-shadow;

      .stage-title {
        color: $color-info;
      }

      .stage-subjects {
        @include flex(row, flex-start, center);
        flex-wrap: wrap;
        margin-left: 10px;

        .subject-item {
          margin: 3px;
          padding: 2px 5px;
          border-radius: 3px;
          font-size: $font-size-medium;
          line-height: 1.5;
          cursor: pointer;

          &:hover {
            color: $color-primary;
          }
        }

        .subject-item.subject-item-current {
          color: white;
          background-color: $color-info;
        }
      }
    }

    .container-middle {
      flex: 1;
      margin-left: 10px;
      background-color: #fff;
    }

    .box-banner {
      width: 100%;
      height: 250px;
      margin-bottom: 10px;

      .carousel-item {
        cursor: pointer;

        .carousel-item-image {
          width: 100%;
          height: 250px;
          object-fit: cover;
        }
      }
    }

    .box-search-bar {
      @include flex(row, flex-start, center);
      padding: 0 10px 10px;

      .box-search {
        flex: 1;
        margin-left: 15px;
      }
    }

    .container-right {
      @include flex(row, center, center);
      width: 250px;
      margin-left: 10px;
      padding: 15px;
      background-color: #fff;

      .box-stat-item {
        width: 100%;
        padding: 15px;
        // background-color: $color-primary;
      }

      .inner-stat-item {
        @include flex(row, flex-start, center);
      }

      .inner-item-btn {
        @include flex(row, flex-end, center);
        margin-top: 10px;
      }

      .box-item-icon {
        width: 50px;
        height: 50px;
        margin-right: 10px;
        border: 1px solid #dfdfdf;
      }

      .item-name {
        font-size: 16px;
      }

      .item-stat {
        margin-top: 6px;
        color: #999;
        font-size: 13px;
        cursor: pointer;

        .count {
          color: $color-primary;
          font-size: 18px;
          text-decoration: underline;
        }
      }
    }

    .section-body {
      background-color: #fff;
    }

    .outer-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;

      .block-list {
        padding: 15px;
      }

      .block-header {
        @include flex(row, space-between, center);
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dfdfdf;
      }

      .block-title {
        font-size: 20px;
      }

      .list-item {
        @include flex(row, space-between, center);
        cursor: pointer;

        &:not(:last-child) {
          margin-bottom: 12px;
        }

        .list-item-name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &:hover {
          .list-item-name {
            color: $color-primary;
          }
        }
      }

      .list-item-count {
        color: #999;
      }
    }
  }
</style>
