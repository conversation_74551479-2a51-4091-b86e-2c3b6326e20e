<template>
  <div class="container-paper-upload-batch-upload">
    <template v-if="uploadBatchStore.files.length == 0">
      <SelectFile class="select-file" @selected="handleFileSelected"></SelectFile>
    </template>
    <template v-else>
      <div class="files-info">
        <span>共 {{ uploadBatchStore.files.length }} 个 Word 文件</span>
        <TextButton type="primary" @click="showModalFileList = true">点击查看</TextButton>
      </div>
      <Button v-if="uploadBatchStore.unStarted" type="primary" long @click="start">开始执行</Button>
      <div v-else-if="uploadBatchStore.finished" class="status-finished">
        <div class="status">执行完毕</div>
        <div class="summary">
          <span class="succeeded-count"
            >成功划题<span class="number">{{ uploadBatchStore.succeededFileCount }}</span
            >份试卷</span
          >
          <span v-if="uploadBatchStore.files.length > uploadBatchStore.succeededFileCount" class="skiped-count"
            >，尚有<span class="number">{{ uploadBatchStore.files.length - uploadBatchStore.succeededFileCount }}</span
            >份试卷未完成</span
          >
          <div class="show-detail">
            <Button type="primary" size="small" @click="showModalFileList = true">查看详情</Button>
          </div>
        </div>
      </div>
      <CurrentFile v-else-if="uploadBatchStore.currentFile" class="current-file"></CurrentFile>
    </template>

    <ModalFileList v-model="showModalFileList"></ModalFileList>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useUploadBatchStore } from '@/store/qlib/upload_batch'

  import SelectFile from './select_file.vue'
  import ModalFileList from './modal_file_list.vue'
  import CurrentFile from './current_file.vue'

  const uploadBatchStore = useUploadBatchStore()

  const showModalFileList = ref(false)

  function handleFileSelected(files) {
    uploadBatchStore.changeFiles(files)
  }

  function start() {
    uploadBatchStore.changeCurrentFileIndex(0)
  }
</script>

<style lang="scss" scoped>
  .container-paper-upload-batch-upload {
    @include flex(column, flex-start, stretch);
    position: relative;
    gap: 16px;
    height: 100%;
    padding: 16px;
    overflow: hidden;

    .select-file {
      height: 32px;
    }

    .files-info {
      flex-shrink: 0;
    }

    .status-finished {
      text-align: center;

      .status {
        padding-top: 16px;
        padding-bottom: 8px;
        font-size: $font-size-large;
      }

      .number {
        margin-right: 4px;
        margin-left: 4px;
      }

      .succeeded-count .number {
        color: $color-success;
      }

      .skiped-count .number {
        color: $color-warning;
      }

      .show-detail {
        margin-top: 8px;
        text-align: center;
      }
    }

    .current-file {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: hidden;
    }
  }
</style>
