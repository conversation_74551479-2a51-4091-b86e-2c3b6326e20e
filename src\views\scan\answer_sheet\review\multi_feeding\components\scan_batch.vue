<template>
  <div class="section-scan-batch">
    <div class="header">
      <span class="header-title">扫描批次</span>
    </div>
    <div class="body">
      <Tabs v-model="activeTab" @on-click="onTabClick">
        <TabPane v-for="tab in tabList" :key="tab.value" :label="tab.label" :name="tab.value"></TabPane>
      </Tabs>
      <div class="box-table">
        <Table :columns="scanBatchTableColumns" :data="batches"></Table>
        <div v-if="batchTotal > 0" class="box-page">
          <span class="summary">共 {{ batchTotal }} 个批次</span>
          <Page :model-value="pageNum" :page-size="pageSize" :total="batchTotal" @on-change="changeCurrentPage"></Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { I<PERSON>, <PERSON><PERSON><PERSON>, But<PERSON> } from 'view-ui-plus'
  import { apiGetBatchPage, apiRetryCheckBatch } from '@/api/scan/multi_feeding'
  import { formatDateTime } from '@/utils/date'
  import MultiFeedingBatchCheckStatus from '@/enum/scan/multi_feeding_batch_check_status'
  import MultiFeedingCheckStatus from '@/enum/scan/multi_feeding_check_status'

  export default {
    props: {
      statData: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['show-batch-detail', 'on-refresh'],
    data() {
      return {
        pageNum: 1,
        pageSize: 10,
        batchTotal: 0,
        batches: [],
        activeTab: 'batch-exists-check-result',
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'viewMode']),
      scanBatchTableColumns() {
        let columns = [
          {
            title: '批次信息',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    marginTop: '10px',
                    marginBottom: '10px',
                  },
                },
                [
                  h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          className: 'td-text-main',
                          style: {
                            lineHeight: 1,
                            verticalAlign: 'middle',
                          },
                        },
                        `${params.row.scanClient}-${params.row.batchNo}`
                      ),
                      h(
                        Tooltip,
                        {
                          placement: 'top',
                          transfer: true,
                        },
                        {
                          default: () =>
                            h(Icon, {
                              type: 'ios-alert-outline',
                              size: '14',
                              style: {
                                marginLeft: '4px',
                                verticalAlign: 'middle',
                              },
                            }),
                          content: () =>
                            h(
                              'div',
                              {
                                style: {
                                  whiteSpace: 'normal',
                                },
                              },
                              [
                                h(
                                  'div',
                                  {
                                    style: {
                                      marginBottom: '6px',
                                    },
                                  },
                                  '批次详情'
                                ),
                                h(
                                  'div',
                                  {
                                    style: {
                                      fontSize: '13px',
                                    },
                                  },
                                  `扫描时间：${formatDateTime(new Date(params.row.scanTime), 'YYYY-MM-DD\nHH:mm:ss')}`
                                ),
                                h(
                                  'div',
                                  {
                                    style: {
                                      fontSize: '13px',
                                    },
                                  },
                                  `扫描仪：${params.row.deviceName}`
                                ),
                              ]
                            ),
                        }
                      ),
                    ]
                  ),
                  h(
                    'div',
                    {
                      className: 'td-text-sub',
                      style: {
                        marginTop: '8px',
                      },
                    },
                    params.row.scanStationName
                  ),
                ]
              )
            },
          },
          {
            title: '扫描员',
            key: 'scanRealName',
            align: 'center',
            render: (h, params) => {
              return h(
                'span',
                {
                  className: 'td-text-main',
                },
                params.row.scanRealName
              )
            },
          },
          {
            title: '已扫张数',
            key: 'scanPaperCount',
            align: 'center',
            render: (h, params) => {
              return h(
                'span',
                {
                  className: 'td-text-main',
                },
                params.row.scanPaperCount
              )
            },
          },
          {
            title: '重张检查状态',
            align: 'center',
            render: (h, params) => {
              let statusName = MultiFeedingBatchCheckStatus.getNameById(params.row.multiFeedingCheckStatus)
              let className = ''
              if (params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Suspended.id) {
                className = 'tag-suspended'
              } else if (params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Running.id) {
                className = 'tag-running'
              } else if (params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Completed.id) {
                className = 'tag-completed'
              } else if (params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Failed.id) {
                className = 'tag-failed'
              }

              let dynamicComponents = [
                h(
                  'span',
                  {
                    className: `tag-check-status ${className}`,
                  },
                  statusName
                ),
              ]

              if (
                [MultiFeedingBatchCheckStatus.Suspended.id, MultiFeedingBatchCheckStatus.Failed.id].includes(
                  params.row.multiFeedingCheckStatus
                )
              ) {
                dynamicComponents.push(
                  h(
                    Tooltip,
                    {
                      placement: 'top',
                      transfer: true,
                    },
                    {
                      default: () =>
                        h(Icon, {
                          type: 'ios-help-circle-outline',
                          size: '14',
                          style: {
                            marginLeft: '4px',
                            verticalAlign: 'middle',
                          },
                        }),
                      content: () =>
                        h(
                          'div',
                          {
                            style: {
                              whiteSpace: 'normal',
                            },
                          },
                          [
                            h(
                              'div',
                              {
                                style: {
                                  marginBottom: '6px',
                                  textAlign: 'center',
                                },
                              },
                              '失败原因'
                            ),
                            h(
                              'div',
                              {
                                style: {
                                  fontSize: '13px',
                                },
                              },
                              params.row.multiFeedingCheckFailReason
                            ),
                          ]
                        ),
                    }
                  )
                )
              }

              return h('div', {}, dynamicComponents)
            },
          },
          {
            title: '疑似重张数',
            key: 'total',
            align: 'center',
            render: (h, params) => {
              return h(
                'span',
                {
                  className: 'td-text-main',
                },
                [MultiFeedingBatchCheckStatus.Running.id, MultiFeedingBatchCheckStatus.Failed.id].includes(
                  params.row.multiFeedingCheckStatus
                )
                  ? '-'
                  : params.row.total
              )
            },
          },
          {
            title: '确认重张数',
            align: 'center',
            render: (h, params) => {
              return h(
                'span',
                {
                  className: 'td-text-main',
                },
                [MultiFeedingBatchCheckStatus.Running.id, MultiFeedingBatchCheckStatus.Failed.id].includes(
                  params.row.multiFeedingCheckStatus
                )
                  ? '-'
                  : params.row.multiFeeding
              )
            },
          },
          {
            title: '处理进度',
            render: (h, params) => {
              let dynamicComponents = []
              let { init, reScanCheckResultInit, notReScanned } = params.row

              if (init > 0) {
                dynamicComponents.push(
                  h(
                    'div',
                    {
                      style: {
                        paddingTop: '4px',
                        paddingBottom: '4px',
                        color: MultiFeedingCheckStatus.Init.color,
                      },
                    },
                    `${init}个待人工确认`
                  )
                )
              }

              if (notReScanned > 0) {
                dynamicComponents.push(
                  h(
                    'div',
                    {
                      style: {
                        paddingTop: '4px',
                        paddingBottom: '4px',
                        color: MultiFeedingCheckStatus.NotReScanned.color,
                      },
                    },
                    `${notReScanned}个待重扫`
                  )
                )
              }

              if (reScanCheckResultInit > 0) {
                dynamicComponents.push(
                  h(
                    'div',
                    {
                      style: {
                        paddingTop: '4px',
                        paddingBottom: '4px',
                        color: MultiFeedingCheckStatus.ReScanCheckInit.color,
                      },
                    },
                    `${reScanCheckResultInit}个待重扫复核`
                  )
                )
              }

              return h(
                'div',
                {
                  className: 'td-text-main',
                },
                !dynamicComponents.length ||
                  [MultiFeedingBatchCheckStatus.Running.id, MultiFeedingBatchCheckStatus.Failed.id].includes(
                    params.row.multiFeedingCheckStatus
                  )
                  ? '-'
                  : dynamicComponents
              )
            },
          },
          {
            title: '操作',
            align: 'center',
            render: (h, params) => {
              if (
                params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Completed.id &&
                (params.row.total > 0 || params.row.multiFeeding > 0)
              ) {
                return h('div', {}, [
                  h(
                    Button,
                    {
                      size: 'small',
                      style: {
                        fontSize: '13px',
                      },
                      onClick: () => {
                        this.showBatchDetail(params.row)
                      },
                    },
                    () => '查看详情'
                  ),
                ])
              } else if (params.row.multiFeedingCheckStatus === MultiFeedingBatchCheckStatus.Failed.id) {
                return h('div', {}, [
                  h(
                    Button,
                    {
                      type: 'warning',
                      size: 'small',
                      style: {
                        fontSize: '13px',
                      },
                      onClick: () => {
                        this.retryCheckBatch(params.row)
                      },
                    },
                    () => '重试检查'
                  ),
                ])
              } else {
                return h(
                  'span',
                  {
                    style: {
                      fontWeight: 600,
                    },
                  },
                  '-'
                )
              }
            },
          },
        ]

        if (this.activeTab === 'batch-running') {
          columns.splice(4, 4)
        } else if (this.activeTab === 'batch-failed') {
          columns.splice(4, 3)
        }

        return columns
      },
      batchExistCheckResultCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.batchExistsCheckResult
      },
      batchRunningCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.batchRunning + this.statData.batchSuspended + this.statData.batchInit
      },
      batchFailedCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.batchFailed
      },
      batchTotalCount() {
        if (!this.statData || !Object.keys(this.statData).length) return 0
        return this.statData.batchTotal
      },
      tabList() {
        return [
          {
            value: 'batch-exists-check-result',
            name: '有问题批次',
            label: h => {
              return h('div', [h('span', '有问题批次'), h('span', {}, `（${this.batchExistCheckResultCount}）`)])
            },
          },
          {
            value: 'batch-running',
            name: '处理中',
            label: h => {
              return h('div', [h('span', '处理中'), h('span', {}, `（${this.batchRunningCount}）`)])
            },
          },
          {
            value: 'batch-failed',
            name: '检查失败',
            label: h => {
              return h('div', [h('span', '检查失败'), h('span', {}, `（${this.batchFailedCount}）`)])
            },
          },
          {
            value: 'batch-all',
            name: '所有批次',
            label: h => {
              return h('div', [h('span', '所有批次'), h('span', {}, `（${this.batchTotalCount}）`)])
            },
          },
        ]
      },
    },
    watch: {
      viewMode: {
        handler: function (newVal) {
          if (newVal) {
            this.fetchBatchPage()
          }
        },
        deep: true,
      },
    },
    created() {
      this.fetchBatchPage()
    },
    methods: {
      onRefresh() {
        this.fetchBatchPage()
      },
      fetchBatchPage() {
        const { pageNum, pageSize, examSubjectId, viewMode, activeTab } = this
        let status = undefined
        if (activeTab === 'batch-running') {
          status = `${MultiFeedingBatchCheckStatus.Init.id},${MultiFeedingBatchCheckStatus.Running.id},${MultiFeedingBatchCheckStatus.Suspended.id}`
        } else if (activeTab === 'batch-failed') {
          status = MultiFeedingBatchCheckStatus.Failed.id
        } else if (activeTab === 'batch-exists-check-result') {
          status = MultiFeedingBatchCheckStatus.Completed.id
        }

        return apiGetBatchPage({
          page: pageNum,
          size: pageSize,
          scanStationId: viewMode.scanStationId,
          existsCheckResult: activeTab === 'batch-exists-check-result' ? true : undefined,
          examSubjectId,
          status,
        }).then(res => {
          if ((!res.records || !res.records.length) && this.pageNum !== 1) {
            // 当前不在第一页且无数据，则重置到第一页
            this.pageNum = 1
            this.fetchBatchPage()
            return
          }
          this.batches = res.records
          this.batchTotal = Number(res.total)
        })
      },
      changeCurrentPage(pageNum) {
        this.pageNum = pageNum
        this.fetchBatchPage()
      },
      changeStatus() {
        this.pageNum = 1
        this.batchTotal = 0
        this.fetchBatchPage()
      },
      onTabClick() {
        this.pageNum = 1
        this.fetchBatchPage()
      },
      showBatchDetail(item) {
        this.$emit('show-batch-detail', item.batchId)
      },
      retryCheckBatch(item) {
        const { examSubjectId } = this

        this.$Modal.confirm({
          title: '重试检查',
          content: `确定重试检查 ${item.scanClient}-${item.batchNo}批次吗？`,
          loading: true,
          onOk: () => {
            apiRetryCheckBatch({
              batchId: item.batchId,
              examSubjectId,
            }).then(() => {
              this.$Modal.remove()
              this.$Message.success('检查成功')
              this.$emit('on-refresh')
            })
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-scan-batch {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e5e9f2;
    border-radius: 16px;
    background-color: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 33, 111, 0.05);

    :deep(.ivu-tabs-bar) {
      margin-bottom: 10px !important;
    }

    :deep(.td-text-main) {
      font-weight: 600;
      font-size: 14px;
    }

    :deep(.td-text-sub) {
      color: $color-icon;
      font-size: $font-size-medium-s;
    }

    :deep(.tag-check-status) {
      display: inline-block;
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      background-color: #eee;
    }

    :deep(.tag-suspended) {
      color: #e88c03;
      background-color: #fbf1e3;
    }

    :deep(.tag-completed) {
      color: #28a745;
      background-color: #e7f7ef;
    }

    :deep(.tag-failed) {
      color: #dc3545;
      background-color: #fdecea;
    }

    :deep(.tag-running) {
      color: #007bff;
      background-color: #e6f2ff;
    }
  }
</style>
