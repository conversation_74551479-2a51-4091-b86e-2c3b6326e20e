<template>
  <Movable v-model:position="position" class="container-score-panel" on-move-class="score-panel-moving">
    <div class="movable-handler">按住拖动</div>
    <template v-if="isMultipleScore">
      <div v-if="groupScoreItems?.length" class="box-score-items">
        <div v-for="g in groupScoreItems" :key="g.groupName" class="score-items-group">
          <div class="header">{{ g.groupName }}</div>
          <div class="body">
            <div v-for="item in g.list" :key="item.id" class="score-item">
              <div class="box-item-info">
                <span class="item-score">({{ item.fullScore }}分)</span>
                <span class="item-name">{{ item.name }}：</span>
                <span>{{ item.description }}</span>
              </div>
              <div class="box-item-score-input">
                <div class="item-score-input">
                  <span class="label">给分</span>
                  <InputNumber
                    v-model="item.score"
                    style="width: 150px"
                    :placeholder="'满分' + item.fullScore + '分'"
                    :min="0"
                    :max="item.fullScore"
                  ></InputNumber>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="box-score-items">
        <div class="score-items-group">
          <!-- <div class="header">满分：{{ taskDetail.fullScore }} 分</div> -->
          <div class="body">
            <div class="score-item">
              <div class="box-item-score-input">
                <div class="item-score-input">
                  <span class="label">给分</span>
                  <InputNumber
                    v-model="currentSingleScore"
                    :placeholder="'满分' + taskDetail.fullScore + '分'"
                    :min="0"
                    :max="taskDetail.fullScore"
                    style="width: 150px"
                  ></InputNumber>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="fixed-area">
      <div v-if="isMultipleScore" class="box-total-score">
        <div class="label">总分</div>
        <div class="total-score">{{ totalScore }}</div>
      </div>
      <div class="item-score-input">
        <div class="label">点评</div>
        <Input v-model="currentComment" class="input-comment" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" />
      </div>
      <div class="box-btn-submit">
        <Button type="primary" class="btn-submit" @click="onSubmitScore">提交分数</Button>
      </div>
    </div>
  </Movable>
</template>

<script>
  import Movable from '@/views/emarking/marking/components/movable.vue'
  import { groupArray } from '@/utils/array'

  export default {
    components: {
      Movable,
    },
    props: {
      taskDetail: {
        type: Object,
        default: () => {},
      },
      judgeReviewDetail: {
        type: Object,
        default: () => {},
      },
      scoreItems: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['on-submit'],
    data() {
      return {
        currentSingleScore: null,
        currentComment: '',
        groupScoreItems: [],
        position: {
          right: 50,
          top: 60,
        },
      }
    },
    computed: {
      hidden() {
        return !this.taskDetail || !Object.keys(this.taskDetail).length
      },
      isMultipleScore() {
        return !this.hidden && this.taskDetail.scoreMode === 'multiple'
      },
      isSubmitted() {
        return this.judgeReviewDetail && this.judgeReviewDetail.status === 'submitted'
      },
      totalScore() {
        return this.groupScoreItems.reduce((prev, cur) => {
          return (
            prev +
            cur.list.reduce((p, c) => {
              return p + c.score
            }, 0)
          )
        }, 0)
      },
    },
    watch: {
      scoreItems: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            this.groupScoreItems = groupArray(val, k => k.groupName).map(g => ({
              groupName: g.key,
              list: g.group.map(s => ({
                ...s,
                score: null,
              })),
            }))
          }
        },
        immediate: true,
        deep: true,
      },
      judgeReviewDetail: {
        handler: function (val) {
          if (!val) return
          if (val.status === 'submitted') {
            if (this.isMultipleScore) {
              this.groupScoreItems = groupArray(this.scoreItems || [], k => k.groupName).map(g => ({
                groupName: g.key,
                list: g.group.map(s => {
                  const theScoreItem = (val.scoreItems || []).find(item => item.scoreItemId === s.id)

                  return {
                    ...s,
                    score: theScoreItem && theScoreItem.score !== null ? theScoreItem.score : null,
                  }
                }),
              }))
            } else {
              this.currentSingleScore = (val.scoreItems && val.scoreItems.length && val.scoreItems[0].score) || null
            }
            this.currentComment = val.comment
          } else if (val.status === 'assigned') {
            this.groupScoreItems = groupArray(this.scoreItems || [], k => k.groupName).map(g => ({
              groupName: g.key,
              list: g.group.map(s => ({
                ...s,
                score: null,
              })),
            }))
            this.currentComment = null
            this.currentSingleScore = null
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      onSubmitScore() {
        const { groupScoreItems, isMultipleScore, currentSingleScore, currentComment } = this
        let scores = []

        if (isMultipleScore) {
          let hasNoScore = groupScoreItems.some(g => {
            return g.list.some(item => item.score == null)
          })

          if (hasNoScore) {
            this.$Message.info('请给每个评分项给分后提交')
            return
          }

          scores = groupScoreItems.reduce((prev, cur) => {
            let result = cur.list.map(item => ({
              score: item.score,
              scoreItemId: item.id,
            }))
            return prev.concat(result)
          }, [])
        } else {
          if (currentSingleScore == null) {
            this.$Message.info('请给分后提交')
            return
          }

          scores = [
            {
              score: currentSingleScore,
            },
          ]
        }

        this.$emit('on-submit', {
          comment: currentComment,
          scores,
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-score-panel {
    position: fixed;
    top: 60px;
    right: 50px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    width: 400px;
    max-height: calc(100vh - 70px);
    background-color: #fff;

    &.score-panel-moving {
      box-shadow: 0 0 10px #aaa;
    }

    .movable-handler {
      padding: 4px 10px;
      color: $color-icon;
      line-height: 24px;
      text-align: right;
      cursor: grab;
    }

    .box-score-items {
      flex: 1;
      box-sizing: border-box;
      padding: 0 15px;
      overflow: auto;
    }

    .box-score-items-has-comment {
      padding-bottom: 0;
    }

    .score-items-group {
      &:not(:last-child) {
        margin-bottom: 15px;
      }

      .header {
        padding: 0 0 10px;
        font-weight: 600;
        font-size: 16px;
      }

      .score-item {
        &:not(:last-child) {
          margin-bottom: 10px;
        }
      }

      .item-info {
        margin-bottom: 10px;
        line-height: 1.5;
      }

      .item-score {
        color: red;
      }

      .item-name {
        font-weight: 600;
      }

      .box-item-info {
        margin-bottom: 10px;
      }
    }

    .item-score-input {
      @include flex(row, flex-start, flex-start);

      &:not(:last-child) {
        margin-bottom: 15px;
      }

      .label {
        margin-right: 12px;
        line-height: 32px;
      }

      .total-score {
        font-weight: 600;
        line-height: 32px;
      }
    }

    .input-comment {
      flex: 1;
    }

    .box-btn-submit {
      @include flex(row, center, center);
      margin-top: 20px;

      .btn-submit {
        width: 100%;
      }
    }

    .fixed-area {
      box-sizing: border-box;
      padding: 15px 15px 20px;
      background-color: #fff;
    }

    .box-total-score {
      @include flex(row, flex-start, center);
      margin-bottom: 10px;
      font-size: 14px;

      .label {
        margin-right: 12px;
      }

      .total-score {
        font-weight: 600;
      }
    }
  }
</style>
