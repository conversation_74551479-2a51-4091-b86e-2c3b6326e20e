<script>
  import { apiGetExamSchoolCodes } from '@/api/emarking/exam'
  import {
    apiAddNotStatSchools,
    apiDeleteNotStatSchools,
    apiSetReportTemplateNonCountingSchools,
    apiRemoveReportTemplateNonContingStudents,
  } from '@/api/report'

  import TextButton from '@/components/text_button.vue'

  export default {
    props: {
      examId: {
        type: String,
        default: '',
      },
      templateId: {
        type: String,
        default: '',
      },
      isNewTemplate: {
        type: Boolean,
        default: false,
      },
      examSchools: {
        type: Array,
        default: () => [],
      },
      templateNotStatSchools: {
        type: Array,
        default: () => [],
      },
    },

    emits: ['save-temp-not-stat-schools', 'fetch-not-stat-school-list'],

    data() {
      return {
        CheckboxGroupMaxHeight: window.innerHeight - 400 + 'px',

        tableColumns: [
          {
            title: '',
            type: 'selection',
            width: 40,
          },
          {
            title: '学校名称',
            key: 'name',
          },
          {
            title: '学校代码',
            key: 'code',
          },
          {
            title: '删除',
            width: 300,
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'error',
                  onClick: () => this.handleDeleteNotStatSchool(params.row),
                },
                () => '删除'
              ),
          },
        ],

        modalAddNotStatSchoolShowed: false,
        modalSelectedNotStatSchoolIds: [],
        searchText: '',
        selectedDeleteNotStatSchoolList: [],

        examSchoolCodes: [],
      }
    },

    computed: {
      notStatSchoolList() {
        let list = []

        if (this.templateNotStatSchools && this.templateNotStatSchools.length) {
          list = this.templateNotStatSchools.map(school => ({
            id: school.schoolId,
            name: school.schoolName,
            code: school.examSchoolCode,
          }))
          list.sort((a, b) => Number(a.code) - Number(b.code))
        }

        return list
      },
      examSchoolList() {
        let list = (this.examSchools || []).map(school => {
          let targetSchool = this.examSchoolCodes.find(s => s.schoolId === school.schoolId)
          return {
            examSchoolCode: (targetSchool && targetSchool.examSchoolCode) || '',
            schoolId: school.schoolId,
            schoolName: school.schoolName,
          }
        })
        list.sort((a, b) => Number(a.examSchoolCode) - Number(b.examSchoolCode))

        return list
      },
      fieldExamSchoolList() {
        return this.examSchoolList.filter(
          school =>
            (school.schoolName.includes(this.searchText) || school.examSchoolCode.includes(this.searchText)) &&
            !this.notStatSchoolList.some(noSchool => noSchool.id === school.schoolId)
        )
      },
      checkboxAllChecked() {
        return (
          this.fieldExamSchoolList.length > 0 &&
          this.fieldExamSchoolList.every(school => this.modalSelectedNotStatSchoolIds.includes(school.schoolId))
        )
      },
      checkboxAllIndeterminate() {
        return (
          this.fieldExamSchoolList.length > 0 &&
          !this.checkboxAllChecked &&
          this.fieldExamSchoolList.some(school => this.modalSelectedNotStatSchoolIds.includes(school.schoolId))
        )
      },
    },

    created() {
      this.fetchExamSchoolList()
    },

    methods: {
      fetchExamSchoolList() {
        return apiGetExamSchoolCodes(this.examId)
          .then(response => (this.examSchoolCodes = response || []))
          .catch(() => (this.examSchoolCodes = []))
      },

      fetchNotStatSchoolList() {
        this.$emit('fetch-not-stat-school-list')
      },

      showModalAddNotStatSchool() {
        this.modalSelectedNotStatSchoolIds = []
        this.modalAddNotStatSchoolShowed = true
      },

      handleCheckboxSchoolClick(school) {
        if (this.modalSelectedNotStatSchoolIds.some(id => id === school.schoolId)) {
          this.modalSelectedNotStatSchoolIds = this.modalSelectedNotStatSchoolIds.filter(id => id !== school.schoolId)
        } else {
          this.modalSelectedNotStatSchoolIds.push(school.schoolId)
        }
      },

      handleCheckboxAllChange(value) {
        if (value) {
          this.fieldExamSchoolList.forEach(school => {
            if (!this.modalSelectedNotStatSchoolIds.includes(school.schoolId)) {
              this.modalSelectedNotStatSchoolIds.push(school.schoolId)
            }
          })
        } else {
          let fieldExamSchoolIds = this.fieldExamSchoolList.map(school => school.schoolId)
          this.modalSelectedNotStatSchoolIds = this.modalSelectedNotStatSchoolIds.filter(
            id => !fieldExamSchoolIds.includes(id)
          )
        }
      },

      handleConfirmAddNotStatSchool() {
        if (this.modalSelectedNotStatSchoolIds.length == 0) {
          this.$Message.warning({
            duration: 4,
            content: '请至少选择一所学校',
          })
          return
        }

        const NotStatAllSchool = this.examSchoolList.every(
          school =>
            this.modalSelectedNotStatSchoolIds.includes(school.schoolId) ||
            this.notStatSchoolList.some(s => s.id == school.schoolId)
        )
        if (NotStatAllSchool) {
          this.$Message.warning({
            duration: 4,
            content: '不能所有学校均剔除',
          })
          return
        }

        if (this.isNewTemplate) {
          let newNotStatSchoolList = [...this.notStatSchoolList]
          this.modalSelectedNotStatSchoolIds.forEach(id => {
            if (newNotStatSchoolList.some(x => x.id == id)) {
              return
            }
            let targetSchool = this.examSchoolList.find(school => school.schoolId === id)
            if (targetSchool) {
              newNotStatSchoolList.push({
                id: targetSchool.schoolId,
                name: targetSchool.schoolName,
                code: targetSchool.examSchoolCode,
              })
            }
          })
          newNotStatSchoolList.sort((a, b) => Number(a.code) - Number(b.code))
          this.$emit('save-temp-not-stat-schools', newNotStatSchoolList)
          this.modalAddNotStatSchoolShowed = false
        } else {
          this.commitNotStatSchoolsSetting(this.templateId, this.modalSelectedNotStatSchoolIds)
        }
      },

      commitNotStatSchoolsSetting(templateId, schoolIds) {
        apiSetReportTemplateNonCountingSchools({
          templateId: templateId,
          schoolIds: schoolIds,
        })
          .then(() => {
            this.$Message.success({
              duration: 4,
              content: '添加剔除学校成功',
            })
            this.modalAddNotStatSchoolShowed = false
          })
          .finally(() => this.fetchNotStatSchoolList())
      },

      handleDeleteNotStatSchool(school) {
        this.$Modal.confirm({
          title: '删除剔除学校',
          content: `您确定要将 ${school.name} 从剔除学校列表中删除吗？`,
          onOk: () => {
            apiRemoveReportTemplateNonContingStudents({
              templateId: this.templateId,
              schoolIds: [school.id],
            })
              .then(() => {
                this.$Message.success({
                  duration: 4,
                  content: '删除剔除学校成功',
                })
              })
              .finally(() => this.fetchNotStatSchoolList())
          },
        })
      },

      handleClickDeleteSelectedNotStatSchools() {
        this.$Modal.confirm({
          title: '删除剔除学校',
          content: `您确定要将已选择的 ${this.selectedDeleteNotStatSchoolList.length}所学校 从剔除学校列表中删除吗？`,
          onOk: () => {
            apiRemoveReportTemplateNonContingStudents({
              templateId: this.templateId,
              schoolIds: this.selectedDeleteNotStatSchoolList.map(school => school.id),
            })
              .then(() => {
                this.$Message.success({
                  duration: 4,
                  content: '删除剔除学校成功',
                })
                this.selectedDeleteNotStatSchoolList = []
              })
              .finally(() => this.fetchNotStatSchoolList())
          },
        })
      },

      handleTableNotStatSchoolSelectStatusChange(value) {
        this.selectedDeleteNotStatSchoolList = value
      },
    },
  }
</script>

<template>
  <div class="container-report-config-parameter-not-stat-schools">
    <div class="section-action">
      <div class="school-count">
        共剔除 <span class="color-primary">{{ notStatSchoolList.length }}</span> 所学校
      </div>

      <div>
        <TextButton
          v-show="selectedDeleteNotStatSchoolList && selectedDeleteNotStatSchoolList.length"
          type="error"
          icon="md-trash"
          @click="handleClickDeleteSelectedNotStatSchools"
          >删除已选剔除学校</TextButton
        >
        <TextButton type="primary" icon="md-add" @click="showModalAddNotStatSchool">添加剔除学校</TextButton>
      </div>
    </div>

    <Table
      class="section-table"
      :columns="tableColumns"
      :data="notStatSchoolList"
      @on-selection-change="handleTableNotStatSchoolSelectStatusChange"
    ></Table>

    <Modal v-model="modalAddNotStatSchoolShowed" class="modal-add" title="添加剔除学校">
      <Form :label-width="80">
        <FormItem label="搜索框">
          <Input v-model="searchText" clearable maxlength="20" placeholder="输入学校名称（或学校代码）"></Input>
        </FormItem>
        <FormItem label="学校列表">
          <template #label>
            <div>
              <span>学校列表</span><br /><span>全选</span
              ><Checkbox
                class="checkbox-all"
                :model-value="checkboxAllChecked"
                :indeterminate="checkboxAllIndeterminate"
                :disabled="fieldExamSchoolList.length == 0"
                @on-change="handleCheckboxAllChange"
              ></Checkbox>
            </div>
          </template>
          <div
            class="checkbox-wrapper"
            :style="{ maxHeight: CheckboxGroupMaxHeight, overflow: 'auto', paddingLeft: '10px' }"
          >
            <div v-for="school of fieldExamSchoolList" :key="school.schoolId" class="school-item">
              <Checkbox
                class="checkbox-school"
                :model-value="modalSelectedNotStatSchoolIds.some(id => id === school.schoolId)"
                @on-change="handleCheckboxSchoolClick(school)"
                >【{{ school.examSchoolCode }}】{{ school.schoolName }}</Checkbox
              >
            </div>
          </div>
        </FormItem>
      </Form>

      <template #footer>
        <div class="footer">
          <div v-if="modalSelectedNotStatSchoolIds.length > 0" class="summary">
            已选<span class="number">{{ modalSelectedNotStatSchoolIds.length }}</span
            >所学校
          </div>
          <Button type="text" @click="modalAddNotStatSchoolShowed = false">取消</Button>
          <Button type="primary" @click="handleConfirmAddNotStatSchool">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<style lang="scss" scoped>
  .container-report-config-parameter-not-stat-schools {
    .section-action {
      @include flex(row, space-between, center);
      height: 32px;
      margin-bottom: 4px;

      .school-count {
        .color-primary {
          color: $color-primary;
        }
      }
    }
  }

  .modal-add {
    .checkbox-all {
      margin-top: 10px;
      margin-right: 0;
      margin-left: 5px;
    }

    .footer {
      @include flex(row, flex-end, center);

      .summary {
        margin-right: auto;

        .number {
          margin-right: 5px;
          margin-left: 5px;
          color: $color-primary;
        }
      }
    }
  }
</style>
