<template>
  <el-radio-group v-model="activeRadio">
    <el-radio-button v-for="item in options.options?.value" :label="item">{{ item }}</el-radio-button>
  </el-radio-group>
</template>

<script>
  import { ElRadioGroup, ElRadio, ElRadioButton } from 'element-plus'
  import 'element-plus/es/components/radio-group/style/css'
  import 'element-plus/es/components/radio/style/css'
  import 'element-plus/es/components/radio-button/style/css'

  export default {
    components: {
      'el-radio-group': ElRadioGroup,
      'el-radio': ElRadio,
      'el-radio-button': ElRadioButton,
    },
    props: {
      options: {
        type: Object,
        default: () => ({
          options: {
            value: [],
          },
        }),
      },
    },
    data() {
      return {
        activeRadio: '',
      }
    },
  }
</script>
