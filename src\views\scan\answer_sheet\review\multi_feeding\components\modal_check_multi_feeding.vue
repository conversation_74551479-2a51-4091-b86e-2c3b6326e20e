<template>
  <Modal
    class="modal-check-multi-feeding"
    :model-value="modelValue"
    :mask-closable="false"
    fullscreen
    footer-hide
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <div class="header-left">{{ title }}</div>
        <div class="header-right">
          <div v-if="checkResultDetail">{{ index == null ? '-' : index + 1 }} / {{ total }}</div>
          <div class="box-quick-nav">
            <TextButton :disabled="isPrevDisabled" @click="goPrev">上一份</TextButton>
            <TextButton :disabled="isNextDisabled" @click="goNext">下一份</TextButton>
          </div>
        </div>
      </div>
    </template>
    <div class="container-modal-content">
      <div class="container-left">
        <ImageContainer
          :images="displayImages"
          :show-loading-spin="!isAllDisplayImagesLoaded"
          @image-loaded="handleImageLoaded"
        ></ImageContainer>
      </div>
      <div class="container-right">
        <div class="section-config">
          <div class="config-back-page">
            <i-switch v-model="showOnlyBackPage"></i-switch><span class="label">仅显示背面图片</span>
          </div>
          <div v-if="existsChangeStatusButton" class="config-change-status">
            <i-switch v-model="showChangeStatusButton"></i-switch><span class="label">修改状态</span>
          </div>
        </div>
        <div class="section-info">
          <template v-if="checkResultDetail">
            <StudentCard
              :key="checkResultDetail.scanUnit.unitId"
              class="student-card"
              title="当前学生"
              :check-result-detail="checkResultDetail"
              show-status
              :status="status"
              :student="checkResultDetail.student"
              :scan-units="thisStudentDisplayScanUnits"
              :selected-scan-unit-ids="selectedScanUnitIds"
              @change-selected-scan-unit-ids="changeSelectedScanUnitIds"
            ></StudentCard>

            <StudentCard
              v-for="(stu, idx) in checkResultDetail.skipStudents"
              :key="stu.studentId"
              class="student-card"
              :title="'跳过学生' + (checkResultDetail.skipStudents.length > 1 ? idx + 1 : '')"
              :check-result-detail="checkResultDetail"
              :show-status="false"
              :student="stu"
              :scan-units="stu.scanUnits"
              :selected-scan-unit-ids="selectedScanUnitIds"
              @change-selected-scan-unit-ids="changeSelectedScanUnitIds"
            ></StudentCard>

            <StudentCard
              :key="checkResultDetail.nextScanUnit.unitId"
              class="student-card"
              title="下个学生"
              :check-result-detail="checkResultDetail"
              :show-status="false"
              :student="checkResultDetail.nextStudent"
              :scan-units="nextStudentDisplayScanUnits"
              :selected-scan-unit-ids="selectedScanUnitIds"
              @change-selected-scan-unit-ids="changeSelectedScanUnitIds"
            ></StudentCard>
          </template>
          <div v-else class="empty">暂无数据</div>
        </div>
        <div v-if="displayButtons.length > 0" class="section-action">
          <Button
            v-for="btn in displayButtons"
            :key="btn.id"
            class="btn"
            :type="btn.type"
            :disabled="!checkResultDetail || !isAllDisplayImagesLoaded"
            @click="btn.handler"
            >{{ btn.text }}</Button
          >
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
  import ImageContainer from './image_container.vue'
  import StudentCard from './student_card.vue'

  import {
    apiGetCheckResultDetail,
    apiGetNextCheckResultDetail,
    apiGetPreviousCheckResultDetail,
    apiChangeCheckResult,
    apiChangeReScanCheckResult,
  } from '@/api/scan/multi_feeding'

  import ScanUnitRecognizeStatusEnum from '@/enum/scan/scan_unit_recognize_status'
  import MultiFeedingManualCheckResultEnum from '@/enum/scan/multi_feeding_manual_check_result'
  import MultiFeedingReScanCheckResultEnum from '@/enum/scan/multi_feeding_rescan_check_result'
  import { default as StatusEnum, getStatus } from '@/enum/scan/multi_feeding_check_status'

  export default {
    components: {
      ImageContainer,
      StudentCard,
    },
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '重张检查结果',
      },
      requestParams: Object,
      initId: String,
    },
    emits: ['update:modelValue', 'change-index', 'on-refresh'],
    data() {
      return {
        // 总数
        total: 0,
        // 序号
        index: 0,
        // 重张详情
        checkResultDetail: null,
        // 已勾选的扫描单元
        selectedScanUnitIds: [],
        // 只显示背面图片
        showOnlyBackPage: true,
        // 显示修改状态按钮
        showChangeStatusButton: false,
        // 已加载成功的图片Urls
        loadedImageUrlSet: new Set(),
      }
    },
    computed: {
      /**
       * 当前学生扫描单元
       */
      thisStudentDisplayScanUnits() {
        return this.getStudentScanUnitsInDisplayOrder(this.checkResultDetail?.student, this.checkResultDetail?.scanUnit)
      },
      /**
       * 下个学生扫描单元
       */
      nextStudentDisplayScanUnits() {
        return this.getStudentScanUnitsInDisplayOrder(
          this.checkResultDetail?.nextStudent,
          this.checkResultDetail?.nextScanUnit
        )
      },
      /**
       * 显示的图片
       */
      displayImages() {
        if (!this.checkResultDetail || this.selectedScanUnitIds.length == 0) {
          return []
        }

        // 所有扫描单元
        let scanUnits = [...this.thisStudentDisplayScanUnits]
        this.checkResultDetail?.skipStudents?.forEach(stu => {
          scanUnits.push(...stu.scanUnits)
        })
        scanUnits.push(...this.nextStudentDisplayScanUnits)

        // 剔除未勾选的
        scanUnits = scanUnits.filter(unit => this.selectedScanUnitIds.includes(unit.unitId))

        let result = []
        scanUnits.forEach(scanUnit => {
          let imageUrls = []
          // 不区分扫描单元是否有多张纸
          // 定位成功，用校正后图像
          if (
            [
              ScanUnitRecognizeStatusEnum.MatchSuccess.id,
              ScanUnitRecognizeStatusEnum.SubjectException.id,
              ScanUnitRecognizeStatusEnum.CornerException.id,
              ScanUnitRecognizeStatusEnum.AdmissionNumException.id,
              ScanUnitRecognizeStatusEnum.RecognizeSuccess.id,
            ].includes(scanUnit.recogStatus)
          ) {
            imageUrls.push(...scanUnit.adjustImgUrls)
          }
          // 定位失败，用原始图像
          else {
            scanUnit.papers.forEach(paper => {
              // 纸张原图不考虑是否是实际页码顺序
              imageUrls.push(...paper.imgUrls)
            })
          }

          imageUrls.forEach((url, idx) => {
            if (this.showOnlyBackPage && idx % 2 == 0) {
              return
            }
            result.push({
              batchNumberText: scanUnit.batchNumberText,
              paperNoText: scanUnit.paperNoText,
              studentName: scanUnit.studentName,
              isMultiFeedingReScan: scanUnit.isMultiFeedingReScan,
              url,
            })
          })
        })
        return result
      },
      /**
       * 是否所有显示的图片都加载完成
       */
      isAllDisplayImagesLoaded() {
        return this.displayImages.every(x => this.loadedImageUrlSet.has(x.url))
      },
      /**
       * 是否禁用上一份按钮
       */
      isPrevDisabled() {
        return this.index == null || this.index <= 0 || !this.isAllDisplayImagesLoaded
      },
      /**
       * 是否禁用下一份按钮
       */
      isNextDisabled() {
        return this.index == null || this.index >= this.total - 1 || !this.isAllDisplayImagesLoaded
      },
      /**
       * 状态
       */
      status() {
        if (!this.checkResultDetail) {
          return null
        }
        return getStatus(this.checkResultDetail)
      },
      /**
       * 操作按钮
       */
      actionButtons() {
        if (!this.status) {
          return []
        }
        if (this.status.id == StatusEnum.Init.id) {
          return [
            {
              id: 'init-confirm-normal',
              type: 'primary',
              text: '确认正常',
              handler: this.changeCheckResultNormal,
            },
            {
              id: 'init-confirm-multi-feeding',
              type: 'warning',
              text: '确认重张',
              handler: this.changeCheckResultMultiFeeding,
            },
            {
              id: 'init-skip',
              type: 'default',
              text: '暂不确认',
              handler: this.goNext,
            },
          ]
        } else if (this.status.id == StatusEnum.CheckNormal.id) {
          return [
            {
              id: 'normal-change-multi-feeding',
              type: 'warning',
              text: '转为确认重张',
              changeStatus: true,
              handler: this.changeCheckResultMultiFeeding,
            },
            {
              id: 'normal-change-init',
              type: 'default',
              text: '转为暂不确认',
              changeStatus: true,
              handler: this.changeCheckResultInit,
            },
          ]
        } else if (this.status.id == StatusEnum.NotReScanned.id) {
          return [
            {
              id: 'not-rescanned-change-normal',
              type: 'primary',
              text: '转为确认正常',
              changeStatus: true,
              handler: this.changeCheckResultNormal,
            },
            {
              id: 'not-rescanned-change-init',
              type: 'default',
              text: '转为暂不确认',
              changeStatus: true,
              handler: this.changeCheckResultInit,
            },
          ]
        } else if (this.status.id == StatusEnum.ReScanCheckInit.id) {
          return [
            {
              id: 'rescanned-confirm-yes',
              type: 'primary',
              text: '复核通过',
              handler: this.changeReScanCheckResultYes,
            },
            {
              id: 'rescanned-confirm-no',
              type: 'warning',
              text: '复核不通过',
              handler: this.changeReScanCheckResultNo,
            },
          ]
        } else if (this.status.id == StatusEnum.ReScanCheckYes.id) {
          return [
            {
              id: 'rescanned-change-no',
              type: 'warning',
              text: '转为复核不通过',
              changeStatus: true,
              handler: this.changeReScanCheckResultNo,
            },
          ]
        } else if (this.status.id == StatusEnum.ReScanCheckNo.id) {
          return [
            {
              id: 'rescanned-change-yes',
              type: 'primary',
              text: '转为复核通过',
              changeStatus: true,
              handler: this.changeReScanCheckResultYes,
            },
          ]
        } else {
          return []
        }
      },
      /**
       * 显示按钮
       */
      displayButtons() {
        if (this.showChangeStatusButton) {
          return this.actionButtons
        }
        return this.actionButtons.filter(btn => !btn.changeStatus)
      },
      /**
       * 存在修改状态按钮
       */
      existsChangeStatusButton() {
        return this.actionButtons.some(btn => btn.changeStatus)
      },
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.init()
        }
      },
      displayImages() {
        this.loadedImageUrlSet = new Set()
      },
    },
    methods: {
      /**
       * 初始化
       */
      async init() {
        this.total = 0
        this.index = 0
        this.checkResultDetail = null
        this.selectedScanUnitIds = []
        this.loadedImageUrlSet = new Set()
        this.showChangeStatusButton = false

        let res = await apiGetCheckResultDetail({ ...this.requestParams, id: this.initId })
        this.total = res.total
        this.index = res.index
        this.checkResultDetail = res.record

        if (this.checkResultDetail != null) {
          this.selectScanUnits()
        } else {
          this.$Message.warning('检查结果状态已发生变化')
          this.closeModal()
        }
      },
      /**
       * 设置默认勾选的扫描单元
       */
      selectScanUnits() {
        if (!this.checkResultDetail) {
          this.selectedScanUnitIds = []
          return
        }
        let unitIds = []
        let { scanUnit, student, skipStudents, nextScanUnit, reScanned, checkResultUpdateTime } = this.checkResultDetail
        // 已重扫，默认显示重扫后的新扫描单元
        if (reScanned) {
          unitIds.push(scanUnit.unitId)
          let confirmMultiFeedingTime = new Date(checkResultUpdateTime)
          student.scanUnits?.forEach(unit => {
            // 标记否重扫扫描单元
            if (
              unit.pageNoText == scanUnit.pageNoText &&
              unit.unitId != scanUnit.unitId &&
              new Date(unit.insertTime) > confirmMultiFeedingTime
            ) {
              unit.isMultiFeedingReScan = true
              unitIds.push(unit.unitId)
            }
          })
        }
        // 尚未重扫，默认显示可能与当前扫描单元内容相同的，方便对比
        else {
          // 如果扫描单元已被删除且存在新扫描的，则显示新扫描的
          let newScanUnit = null
          if (scanUnit.isDel) {
            newScanUnit = student.scanUnits?.find(unit => {
              return (
                unit.pageNoText == scanUnit.pageNoText &&
                unit.unitId != scanUnit.unitId &&
                new Date(unit.insertTime) > new Date(scanUnit.insertTime)
              )
            })
          }
          if (newScanUnit) {
            unitIds.push(newScanUnit.unitId)
          } else {
            unitIds.push(scanUnit.unitId)
          }

          // 存在跳过学生，加入与当前扫描单元页码相同的
          if (skipStudents.length > 0) {
            skipStudents.forEach(stu => {
              stu.scanUnits?.forEach(unit => {
                if (unit.pageNoText == scanUnit.pageNoText) {
                  unitIds.push(unit.unitId)
                }
              })
            })
          }
          // 不存在跳过学生，则加入下一张对应扫描单元
          else {
            unitIds.push(nextScanUnit.unitId)
          }
        }
        this.selectedScanUnitIds = unitIds
      },

      /**
       * 获取按展示顺序的学生扫描单元，当前纸张和下一张在前
       */
      getStudentScanUnitsInDisplayOrder(stu, firstScanUnit) {
        let list = []
        if (firstScanUnit) {
          list.push(firstScanUnit)
        }
        stu?.scanUnits?.forEach(unit => {
          if (unit.unitId != firstScanUnit?.unitId) {
            list.push(unit)
          }
        })
        return list
      },

      /**
       * 勾选扫描单元
       */
      changeSelectedScanUnitIds(unitIds) {
        this.selectedScanUnitIds = unitIds
      },

      /**
       * 图片加载
       */
      handleImageLoaded(url) {
        this.loadedImageUrlSet.add(url)
      },

      /**
       * 确认正常
       */
      async changeCheckResultNormal() {
        await this.changeCheckResult(MultiFeedingManualCheckResultEnum.Normal.id)
      },
      /**
       * 确认重张
       */
      async changeCheckResultMultiFeeding() {
        await this.changeCheckResult(MultiFeedingManualCheckResultEnum.MultiFeeding.id)
      },
      /**
       * 转为待确认
       */
      async changeCheckResultInit() {
        await this.changeCheckResult(MultiFeedingManualCheckResultEnum.Init.id)
      },
      /**
       * 修改人工确认结果
       */
      async changeCheckResult(value) {
        if (!this.checkResultDetail) {
          return
        }
        if (this.displayImages.length == 0) {
          this.$Message.warning('未查看图像')
          return
        }
        if (!this.isAllDisplayImagesLoaded) {
          this.$Message.warning('请等待图像加载完成')
          return
        }

        if (this.status.id == StatusEnum.Init.id) {
          await this.submitCheckResult(value)
        } else {
          this.$Modal.confirm({
            title: '请注意',
            content: `是否将人工确认状态改为【${MultiFeedingManualCheckResultEnum.getNameById(value)}】？`,
            onOk: () => {
              this.submitCheckResult(value)
            },
          })
        }
      },
      async submitCheckResult(value) {
        await apiChangeCheckResult({
          examSubjectId: this.requestParams.examSubjectId,
          id: this.checkResultDetail.id,
          checkResult: value,
        })
        if (value == MultiFeedingManualCheckResultEnum.Normal.id) {
          this.$Message.success('已确认正常')
        } else if (value == MultiFeedingManualCheckResultEnum.MultiFeeding.id) {
          this.$Message.warning('已确认重张')
        } else if (value == MultiFeedingManualCheckResultEnum.Init.id) {
          this.$Message.info('已转为待人工确认')
        }
        await this.goNext()
      },

      /**
       * 重扫复核通过
       */
      async changeReScanCheckResultYes() {
        await this.changeReScanCheckResult(MultiFeedingReScanCheckResultEnum.Yes.id)
      },
      /**
       * 重扫复核不通过
       */
      async changeReScanCheckResultNo() {
        await this.changeReScanCheckResult(MultiFeedingReScanCheckResultEnum.No.id)
      },
      /**
       * 修改重扫复核结果
       */
      async changeReScanCheckResult(value) {
        if (!this.checkResultDetail) {
          return
        }
        if (this.displayImages.length == 0) {
          this.$Message.warning('未查看图像')
          return
        }
        if (!this.isAllDisplayImagesLoaded) {
          this.$Message.warning('请等待图像加载完成')
          return
        }

        if (this.status.id == StatusEnum.ReScanCheckInit.id) {
          await this.submitReScanCheckResult(value)
        } else {
          this.$Modal.confirm({
            title: '请注意',
            content: `是否将重扫复核状态改为【${MultiFeedingReScanCheckResultEnum.getNameById(value)}】？`,
            onOk: () => {
              this.submitReScanCheckResult(value)
            },
          })
        }
      },
      async submitReScanCheckResult(value) {
        await apiChangeReScanCheckResult({
          examSubjectId: this.requestParams.examSubjectId,
          id: this.checkResultDetail.id,
          reScanCheckResult: value,
        })
        if (value == MultiFeedingReScanCheckResultEnum.Yes.id) {
          this.$Message.success('重扫复核通过')
        } else if (value == MultiFeedingReScanCheckResultEnum.No.id) {
          this.$Message.warning('重扫复核不通过')
        } else if (value == MultiFeedingReScanCheckResultEnum.Init.id) {
          this.$Message.info('已转为待重扫复核')
        }
        await this.goNext()
      },

      /**
       * 上一份
       */
      async goPrev() {
        let id = this.checkResultDetail?.id || Number.MAX_SAFE_INTEGER.toString()
        let res = await apiGetPreviousCheckResultDetail({ ...this.requestParams, id })
        this.total = res.total
        this.index = res.index
        this.checkResultDetail = res.record

        if (this.checkResultDetail != null) {
          this.selectScanUnits()
        } else {
          this.$Modal.info({
            title: '无上一份',
            content: '点击【确定】关闭弹窗',
            onOk: this.closeModal,
          })
        }
      },

      /**
       * 下一份
       */
      async goNext() {
        let id = this.checkResultDetail?.id || 0
        let res = await apiGetNextCheckResultDetail({ ...this.requestParams, id })
        this.total = res.total
        this.index = res.index
        this.checkResultDetail = res.record

        if (this.checkResultDetail != null) {
          this.selectScanUnits()
        } else {
          if (res.total > 0) {
            this.$Modal.confirm({
              title: '暂无下一份',
              content: '是否从第一份继续？',
              onOk: this.goNext,
              onCancel: this.closeModal,
            })
          } else {
            this.$Modal.info({
              title: '暂无下一份',
              content: '点击【确定】关闭弹窗',
              onOk: this.closeModal,
            })
          }
        }
      },

      /**
       * 关闭弹窗
       */
      handleVisibleChange(visibility) {
        if (!visibility) {
          this.closeModal()
          this.$emit('on-refresh')
        }
      },
      closeModal() {
        this.$emit('update:modelValue', false)
        this.$emit('change-index', this.index)
      },
    },
  }
</script>

<style lang="scss">
  .modal-check-multi-feeding {
    .ivu-modal-body {
      top: 52px;
      padding: 0;
    }

    .modal-header {
      @include flex(row, space-between, center);
      padding: 4px 60px 4px 0;

      .header-right {
        @include flex(row, flex-start, center);
      }

      .box-quick-nav {
        margin-left: 20px;
      }
    }

    .container-modal-content {
      @include flex(row, flex-start, flex-start);
      height: 100%;

      .container-left {
        flex-grow: 1;
        flex-shrink: 1;
        height: 100%;
      }

      .container-right {
        @include flex(column, flex-start, stretch);
        flex-grow: 0;
        flex-shrink: 0;
        width: 400px;
        height: 100%;
        padding: 16px;
        border-left: 1px solid $color-border;

        .section-config {
          @include flex(row, space-between, center);
          flex-grow: 0;
          flex-shrink: 0;
          margin-bottom: 16px;

          .config-back-page,
          .config-change-status {
            @include flex(row, flex-start, center);

            .label {
              margin-left: 4px;
            }
          }
        }

        .section-info {
          flex-grow: 1;
          flex-shrink: 1;
          overflow: auto;

          .student-card:not(:first-child) {
            margin-top: 24px;
          }

          .empty {
            @include flex(row, center, center);
            height: 100%;
            color: $color-icon;
          }
        }

        .section-action {
          @include flex(row, space-between, center);
          flex-grow: 0;
          flex-shrink: 0;
          margin-top: 16px;
        }
      }
    }
  }
</style>
