<template>
  <div class="panel-stat">
    <div class="header">
      <span class="title">考试统计</span>
      <div class="tabs">
        <span
          v-for="tab in tabList"
          :key="tab.value"
          :class="['tab-item', tab.value === activeTab ? 'active' : '']"
          @click="handleTabClick(tab.value)"
          >{{ tab.name }}</span
        >
      </div>
    </div>
    <div v-if="activeTab === 'month'" class="section-chart-line">
      <div v-if="statData.length" id="chart-line" class="chart-line"></div>
      <div v-else class="no-data">暂无数据</div>
    </div>
    <div v-else class="section-chart-pie">
      <div v-if="statData.length" id="chart-pie" class="chart-pie"></div>
      <div v-else class="no-data">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import echarts from '@/utils/echarts'
  import { useDashboardStore } from '@/store/dashboard'
  import { apiGetMonthExamStat } from '@/api/emarking/exam'

  const dashboardStore = useDashboardStore()
  const tabList = ref([
    {
      value: 'month',
      name: '按月',
    },
    {
      value: 'total',
      name: '总计',
    },
  ])
  const activeTab = ref('month')
  const statData = ref([])
  let chartLine = null
  let chartPie = null

  const currentSemester = computed(() => {
    return dashboardStore.currentSemester
  })
  const currentTerm = computed(() => {
    return dashboardStore.currentTerm
  })
  const currentSchool = computed(() => {
    return dashboardStore.currentSchool
  })
  const lineOptions = computed(() => {
    if (!statData.value.length) return null
    return {
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        backgroundColor: 'rgba(245, 245, 245, 0.8)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#000',
        },
        confine: true,
      },
      legend: {
        data: [
          {
            name: '全部考试',
            textStyle: {
              color: '#fff',
            },
            itemStyle: {
              color: 'yellow',
            },
          },
          {
            name: '校内考试',
            textStyle: {
              color: '#fff',
            },
            itemStyle: {
              color: '#379ee9',
            },
          },
          {
            name: '区域联考',
            textStyle: {
              color: '#fff',
            },
            itemStyle: {
              color: '#05d1bc',
            },
          },
          {
            name: '数智教辅',
            textStyle: {
              color: '#fff',
            },
            itemStyle: {
              color: '#d76f1c',
            },
          },
        ],
      },
      xAxis: {
        type: 'category',
        show: true,
        boundaryGap: false,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgb(211, 211, 211)',
          },
        },
        axisLabel: {
          color: () => {
            return '#fff'
          },
        },
        data: statData.value.map(item => item.month),
      },
      yAxis: {
        type: 'value',
        name: '次数',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: 'rgb(211, 211, 211)',
          },
        },
        axisLabel: {
          show: true,
          interval: 'auto',
          formatter: '{value}',
          color: () => {
            return '#fff'
          },
        },
        nameTextStyle: {
          color: '#7db7db',
        },
        minInterval: 1,
      },
      grid: {
        left: '10%',
        right: '5%',
        top: '28%',
        bottom: '10%',
      },
      series: [
        {
          type: 'line',
          name: '全部考试',
          data: statData.value.map(item => {
            return {
              value: item.coachExamCount + item.schoolExamCount + item.unionExamCount,
              itemStyle: {
                color: 'yellow',
              },
              symbolSize: 8,
              symbol: 'circle',
              extraData: item,
            }
          }),
          areaStyle: {},
          lineStyle: {
            color: 'yellow',
          },
          itemStyle: {
            borderColor: 'yellow',
          },
          smooth: true,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(5, 209, 188, 0.35)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.25)',
              },
            ],
            global: false,
          },
        },
        {
          type: 'line',
          name: '校内考试',
          data: statData.value.map(item => {
            return {
              value: item.schoolExamCount,
              itemStyle: {
                color: '#379ee9',
              },
              symbolSize: 8,
              symbol: 'circle',
              extraData: item,
            }
          }),
          areaStyle: {},
          lineStyle: {
            color: '#379ee9',
          },
          itemStyle: {
            borderColor: '#379ee9',
          },
          smooth: true,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(5, 209, 188, 0.35)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.25)',
              },
            ],
            global: false,
          },
        },
        {
          type: 'line',
          name: '区域联考',
          data: statData.value.map(item => {
            return {
              value: item.unionExamCount,
              itemStyle: {
                color: '#05d1bc',
              },
              symbolSize: 8,
              symbol: 'circle',
              extraData: item,
            }
          }),
          areaStyle: {},
          lineStyle: {
            color: '#05d1bc',
          },
          itemStyle: {
            borderColor: '#05d1bc',
          },
          smooth: true,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(5, 209, 188, 0.35)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.25)',
              },
            ],
            global: false,
          },
        },
        {
          type: 'line',
          name: '数智教辅',
          data: statData.value.map(item => {
            return {
              value: item.coachExamCount,
              itemStyle: {
                color: '#d76f1c',
              },
              symbolSize: 8,
              symbol: 'circle',
              extraData: item,
            }
          }),
          areaStyle: {},
          lineStyle: {
            color: '#d76f1c',
          },
          itemStyle: {
            borderColor: '#d76f1c',
          },
          smooth: true,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(5, 209, 188, 0.35)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.25)',
              },
            ],
            global: false,
          },
        },
      ],
    }
  })
  const pieOptions = computed(() => {
    if (!statData.value.length) return null
    const schoolExamCount = statData.value.reduce((acc, cur) => acc + cur.schoolExamCount, 0)
    const unionExamCount = statData.value.reduce((acc, cur) => acc + cur.unionExamCount, 0)
    const coachExamCount = statData.value.reduce((acc, cur) => acc + cur.coachExamCount, 0)

    return {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        top: '2%',
        left: 'center',
        textStyle: {
          color: '#fff',
        },
      },
      series: [
        {
          type: 'pie',
          top: '20%',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderWidth: 2,
          },
          label: {
            show: true,
            color: '#fff',
            formatter: '{b}: {c}次',
          },
          emphasis: {
            // label: {
            //   show: true,
            //   fontSize: 20,
            //   color: '#fff',
            //   fontWeight: 'bold',
            // },
          },
          labelLine: {
            show: true,
          },
          data: [
            {
              value: schoolExamCount,
              name: '校内考试',
              itemStyle: {
                color: '#379ee9',
              },
            },
            {
              value: unionExamCount,
              name: '区域联考',
              itemStyle: {
                color: '#05d1bc',
              },
            },
            {
              value: coachExamCount,
              name: '数智教辅',
              itemStyle: {
                color: '#d76f1c',
              },
            },
          ],
        },
      ],
    }
  })

  const handleTabClick = val => {
    activeTab.value = val
    nextTick(() => {
      if (val === 'month') {
        drawLine()
      } else {
        drawPie()
      }
    })
  }

  const drawLine = () => {
    if (lineOptions.value) {
      if (chartLine) {
        chartLine.dispose()
      }
      chartLine = echarts.init(document.getElementById('chart-line'))
      chartLine.setOption(lineOptions.value)
    }
  }

  const drawPie = () => {
    if (pieOptions.value) {
      if (chartPie) {
        chartPie.dispose()
      }
      chartPie = echarts.init(document.getElementById('chart-pie'))
      chartPie.setOption(pieOptions.value)
    }
  }

  const fetchMonthExamStat = () => {
    apiGetMonthExamStat({
      semesterId: currentSemester.value,
      term: currentTerm.value,
      childSchoolId: currentSchool.value.schoolId !== '0' ? currentSchool.value.schoolId : undefined,
    }).then(res => {
      statData.value = res
      dashboardStore.monthExamStat = res
      nextTick(() => {
        if (statData.value.length) {
          if (activeTab.value === 'month') {
            drawLine()
          } else {
            drawPie()
          }
        }
      })
    })
  }

  watch([currentSemester, currentTerm, currentSchool], () => {
    if (currentSemester.value && currentSchool.value) {
      fetchMonthExamStat()
    }
  })
</script>

<style lang="scss" scoped>
  .panel-stat {
    .header {
      @include flex(row, flex-start, center);
      margin-bottom: 10px;
      padding-top: 10px;

      .title {
        width: 90px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        background-color: rgba(49, 72, 128, 0.8); // #314880
      }

      .tab-item {
        display: inline-block;
        min-width: 50px;
        height: 26px;
        margin-left: 6px;
        padding: 0 10px;
        border: 1px solid #999;
        color: #fff;
        font-size: 13px;
        line-height: 26px;
        text-align: center;
        cursor: pointer;

        &.active {
          border-color: rgb(148 171 226 / 80%);
          background-color: #0069ea;
        }
      }
    }

    .chart-line {
      height: 180px;
    }

    .chart-pie {
      height: 180px;
    }

    .no-data {
      @include flex(row, center, center);
      height: 180px;
      color: #fff;
    }

    .section-chart-pie {
      // margin-top: 20px;
    }
  }
</style>
