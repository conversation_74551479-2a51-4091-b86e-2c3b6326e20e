<template>
  <Drawer title="评审历史" width="470" :model-value="modelValue" @on-visible-change="handleVisibleChange">
    <div class="drawer-mark-history-inner">
      <Form v-if="showFilter" class="filted-area">
        <FormItem label="分数：">
          <InputNumber
            v-model="minScore"
            style="width: 80px"
            size="small"
            clearable
            placeholder="最低分"
            @on-change="debouncedEmitMinScoreChange"
          ></InputNumber>
          分
          <span style="display: inline-block; margin: 0 1em 0 1em">~</span>
          <InputNumber
            v-model="maxScore"
            size="small"
            clearable
            style="width: 80px"
            placeholder="最高分"
            @on-change="debouncedEmitMaxScoreChange"
          ></InputNumber>
          分
        </FormItem>
        <FormItem label="时间：">
          <DatePicker
            :model-value="dateRange"
            type="datetimerange"
            size="small"
            format="yyyy-MM-dd HH:mm"
            style="width: 300px"
            clearable
            @on-change="changeDateRange"
            @on-open-change="handleDatePickerOpenChange"
            @on-clear="handleDatePickerClear"
          ></DatePicker>
        </FormItem>
      </Form>

      <Table
        class="table-mark-history"
        :data="records"
        :columns="tableColumns"
        :row-class-name="rowClassName"
        @on-row-click="handleRowClick"
      ></Table>

      <Page
        class="pager"
        size="small"
        :total="historyTotal"
        :model-value="currentPage"
        :page-size="pageSize"
        @on-change="changePage"
      />
    </div>
  </Drawer>
</template>

<script>
  import { formatMonthDayTime } from '@/utils/date'
  import { debounce } from '@/utils/function'

  export default {
    props: {
      modelValue: Boolean,
      historyTotal: Number,
      currentPage: Number,
      pageSize: Number,
      records: {
        type: Array,
        default: () => [],
      },
      historyFilted: {
        type: Object,
        default: () => ({
          minScore: null,
          maxScore: null,
          date: ['', ''],
        }),
      },
      showFilter: {
        type: Boolean,
        default: true,
      },
      currentAssignOrder: Number,
    },
    emits: ['go', 'update:modelValue', 'change-page', 'change-history-filted'],
    data() {
      return {
        minScore: null,
        maxScore: null,
        dateRange: ['', ''],
        debouncedEmitMinScoreChange: () => {},
        debouncedEmitMaxScoreChange: () => {},
        debouncedEmitDateRangeChange: () => {},
        tableColumns: [
          {
            title: '序号',
            render: (h, params) => {
              return h('span', {}, params.row.judgeAssignOrder)
            },
          },
          {
            title: '评分',
            align: 'center',
            render: (h, params) => {
              return h('span', {}, params.row.score)
            },
          },
          {
            title: '时间',
            align: 'right',
            render: (h, params) => {
              return h('span', {}, formatMonthDayTime(new Date(params.row.submitTime)))
            },
          },
        ],
      }
    },
    watch: {
      modelValue() {
        if (this.modelValue) {
          this.minScore = this.historyFilted.minScore
          this.maxScore = this.historyFilted.maxScore
          this.dateRange = this.historyFilted.date.slice()
        }
      },
    },
    created() {
      this.debouncedEmitMinScoreChange = debounce(this.emitMinScoreChange, 500)
      this.debouncedEmitMaxScoreChange = debounce(this.emitMaxScoreChange, 500)
      this.debouncedEmitDateRangeChange = debounce(this.emitDateRangeChange, 100)
    },
    methods: {
      rowClassName(row) {
        if (row.judgeAssignOrder == this.currentAssignOrder) {
          return 'row-current-paper'
        } else {
          return ''
        }
      },
      handleRowClick(rowData) {
        this.$emit('go', rowData.judgeAssignOrder)
      },

      handleVisibleChange(visibility) {
        this.$emit('update:modelValue', visibility)
      },

      changePage(page) {
        this.$emit('change-page', page)
      },

      emitMinScoreChange() {
        this.$emit('change-history-filted', {
          key: 'minScore',
          value: this.minScore,
        })
      },

      emitMaxScoreChange() {
        this.$emit('change-history-filted', {
          key: 'maxScore',
          value: this.maxScore,
        })
      },

      changeDateRange(dateRange) {
        this.dateRange = dateRange
      },

      handleDatePickerOpenChange(open) {
        if (!open) {
          this.debouncedEmitDateRangeChange()
        }
      },

      handleDatePickerClear() {
        // 选择窗口打开时，点清空会触发clear和open-change两个事件
        // 加节流防止连续多次发送时间改变事件
        this.debouncedEmitDateRangeChange()
      },

      emitDateRangeChange() {
        this.$emit('change-history-filted', {
          key: 'date',
          value: this.dateRange.slice(),
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .filted-area {
    margin-bottom: 20px;

    :deep(.ivu-form-item) {
      margin-bottom: 10px;
    }
  }

  .table-mark-history {
    margin-bottom: 20px;

    :deep(.ivu-table-row) {
      cursor: pointer;

      &.row-current-paper td {
        background-color: $color-iview-table-active-row;
      }
    }
  }

  .pager {
    text-align: right;
  }
</style>
