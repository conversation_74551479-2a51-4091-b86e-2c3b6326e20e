<script>
  import TextButton from '@/components/text_button'

  import { apiGetChildOrgAndSchool } from '@/api/user'
  import { apiGetMergeableExamList } from '@/api/emarking'

  import { deepCopy } from '@/utils/object'

  import ExamScope from '@/enum/emarking/exam_scope'
  import ExamTypeEnum from '@/enum/emarking/exam_type'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      exams: {
        type: Array,
        default: () => [],
      },
      selectedTerm: {
        type: Object,
        default: () => null,
      },
      selectedGrade: {
        type: Object,
        default: () => null,
      },
    },

    emits: ['update:modelValue', 'update-exams'],

    data() {
      return {
        selectedExams: [],
        activeTab: 'selectedList',

        baseColumns: [
          {
            title: '考试项目名称',
            key: 'examName',
          },
          {
            title: '所属机构 / 学校',
            key: 'schoolName',
            width: 300,
          },
          {
            title: '联考 / 校内考试',
            align: 'center',
            width: 200,
            render: (h, params) => {
              const TargetExamScopeObject = this.examScopes.find(s => s.id === params.row.examScope)
              return h('span', {}, (TargetExamScopeObject && TargetExamScopeObject.name) || '-')
            },
          },
          {
            title: '考试类型',
            align: 'center',
            width: 200,
            render: (h, params) => {
              const TargetExamTypeObject = this.examTypes.find(t => t.id === params.row.examType)
              return h('span', {}, (TargetExamTypeObject && TargetExamTypeObject.name) || '-')
            },
          },
        ],
        selectedListColumns: [],
        selectableExamColumns: [],
        responseExams: [],
        selectableResponseExamCheckboxSelect: [],

        selectedSchoolId: '',
      }
    },

    computed: {
      examScopes() {
        return ExamScope.getIdNames().slice(0, 2) // 去掉统考选项
      },
      examTypes() {
        return ExamTypeEnum.getIdNames()
      },
      childOrgAndSchools() {
        return this.$store.getters['report/childOrgAndSchools']
      },
      userInfo() {
        return this.$store.getters['user/info']
      },
      currentSchoolId() {
        return (this.userInfo && this.userInfo.schoolId) || ''
      },
      currentSchoolName() {
        return (this.userInfo && this.userInfo.schoolName) || ''
      },
      tiledChildInstitutions() {
        const Result = []

        /*
        // 整个机构树太多，渲染导致太卡，暂只显示下级机构/学校
        const _FillResult = (node, level) => {
          Result.push({
            id: node.schoolId,
            name: node.schoolName,
            isOrg: node.schoolType === 1,
            level: level,
          })

          if (node.schools && node.schools.length) {
            node.schools.forEach(x => _FillResult(x, level + 1))
          }
        }

        this.childOrgAndSchools.forEach(x => _FillResult(x, 1))
        */

        this.childOrgAndSchools.forEach(x =>
          Result.push({
            id: x.schoolId,
            name: x.schoolName,
            isOrg: x.schoolType === 1,
            level: 1,
          })
        )
        Result.unshift({
          id: this.currentSchoolId,
          name: this.currentSchoolName,
          isOrg: this.$store.getters['user/isBureauInstitution'],
          level: 0,
        })

        return Result
      },
      selectionResponseExams() {
        return this.responseExams.map(e => ({
          ...e,
          _disabled: this.selectedExams.some(x => x.examId === e.examId),
        }))
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      async onVisibleChange(visible) {
        if (visible) {
          this.selectedListColumns = deepCopy(this.baseColumns)
          this.selectedListColumns.push({
            title: '操作',
            align: 'center',
            width: 120,
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'warning',
                  onClick: () => this.handleRemoveSelectedExam(params.row),
                },
                () => '移除'
              ),
          })
          this.selectableExamColumns = deepCopy(this.baseColumns)
          this.selectableExamColumns.unshift({
            type: 'selection',
            width: 40,
          })
          this.selectableExamColumns.push({
            title: '操作',
            align: 'center',
            width: 180,
            render: (h, params) =>
              h('div', {}, [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    disabled: params.row._disabled,
                    onClick: () => this.handleAddSelectedExam(params.row),
                  },
                  () => '添加'
                ),
                h(
                  TextButton,
                  {
                    type: 'warning',
                    disabled: !params.row._disabled,
                    onClick: () => this.handleRemoveSelectedExam(params.row),
                  },
                  () => '移除'
                ),
              ]),
          })

          if (!this.childOrgAndSchools.length) {
            await this.fetchChildOrgAndSchool()
          }

          if (!this.selectedSchoolId) {
            this.selectedSchoolId =
              (this.tiledChildInstitutions && this.tiledChildInstitutions[0] && this.tiledChildInstitutions[0].id) || ''
          }

          this.selectedExams = [...this.exams]

          this.fetchMergeableExamList()
        } else {
          this.onCloseModal()
        }
      },
      onSchoolChange(value = '') {
        this.selectedSchoolId = value
        this.fetchMergeableExamList()
      },
      handleAddSelectedExam(exam) {
        this.selectedExams.push(exam)
      },
      handleSelectableResponseExamCheckboxSelect(rows = []) {
        this.selectableResponseExamCheckboxSelect = rows
      },
      handleAddSelectedExamBatch() {
        this.selectedExams.push(...this.selectableResponseExamCheckboxSelect)
        this.selectableResponseExamCheckboxSelect = []
      },
      handleRemoveSelectedExam(exam) {
        this.selectedExams = this.selectedExams.filter(x => x.examId !== exam.examId)
      },

      fetchChildOrgAndSchool() {
        const UserInfo = this.$store.getters['user/info']
        return apiGetChildOrgAndSchool({
          eduSchoolId: UserInfo.schoolId,
        }).then(response => {
          this.$store.commit('report/setChildOrgAndSchools', response)
        })
      },
      fetchMergeableExamList() {
        if (
          !this.selectedSchoolId ||
          !this.selectedTerm ||
          !this.selectedTerm.semesterId ||
          !this.selectedTerm.term ||
          !this.selectedGrade ||
          !this.selectedGrade.id
        ) {
          return Promise.resolve()
        }

        this.selectableResponseExamCheckboxSelect = []
        return apiGetMergeableExamList({
          schoolId: this.selectedSchoolId,
          semesterId: this.selectedTerm.semesterId,
          term: this.selectedTerm.term,
          gradeId: this.selectedGrade.id,
        })
          .then(response => (this.responseExams = response || []))
          .catch(() => (this.responseExams = []))
      },

      updateSelectedExams() {
        if (this.selectedExams.length < 2) {
          this.$Message.warning({
            duartion: 3,
            content: '请至少选择2个考试项目',
          })
          return
        }

        this.$emit('update-exams', this.selectedExams)
        this.onCloseModal()
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" width="1500" @on-visible-change="onVisibleChange">
    <div class="modal-select-exams-panel">
      <Tabs v-model="activeTab" :animated="false">
        <TabPane name="selectedList" label="考试项目">
          <Table :columns="selectedListColumns" :data="selectedExams"></Table>
        </TabPane>
        <TabPane name="selectExam" label="选择项目">
          <div class="filter-bar">
            <div class="filter-items">
              <div class="filter-item">
                <span class="item-label">学期：</span>
                <span class="item-value">{{ selectedTerm.termName }}</span>
              </div>

              <div class="filter-item">
                <span class="item-label">年级：</span>
                <span class="item-value">{{ (selectedGrade && selectedGrade.name) || '' }}</span>
              </div>

              <div class="filter-item">
                <span class="item-label">机构 / 学校：</span>
                <Select
                  :model-value="selectedSchoolId"
                  transfer
                  filterable
                  style="width: 260px"
                  @on-change="onSchoolChange"
                >
                  <Option v-for="s of tiledChildInstitutions" :key="s.id" :value="s.id">{{ s.name }}</Option>
                </Select>
              </div>
            </div>

            <TextButton
              v-show="selectableResponseExamCheckboxSelect.length"
              type="primary"
              @click="handleAddSelectedExamBatch"
              >批量添加选中项目</TextButton
            >
          </div>

          <Table
            :columns="selectableExamColumns"
            :data="selectionResponseExams"
            @on-selection-change="handleSelectableResponseExamCheckboxSelect"
          ></Table>
        </TabPane>
      </Tabs>
    </div>

    <template #footer>
      <div class="modal-select-exams-footer">
        <div class="modal-selected-exam-count">
          已选 <span class="color-primary">{{ selectedExams.length }}</span> 个考试项目
        </div>
        <div>
          <Button type="text" @click="onCloseModal">取消</Button>
          <Button type="primary" @click="updateSelectedExams">确定</Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-select-exams-panel {
    min-height: 100px;

    .filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 10px;

      .filter-items {
        @include flex(row, flex-start, center);

        .filter-item {
          @include flex(row, flex-start, center);

          .item-label {
            margin-left: 10px;
            white-space: nowrap;
            user-select: none;
          }

          .item-value {
            color: $color-second-title;
          }

          &:not(:last-child) {
            margin-right: 12px;
          }
        }
      }
    }
  }

  .modal-select-exams-footer {
    @include flex(row, space-between, center);

    .modal-selected-exam-count {
      .color-primary {
        color: $color-primary;
      }
    }
  }
</style>
