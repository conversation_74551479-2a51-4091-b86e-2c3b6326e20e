<template>
  <div class="image-container">
    <div class="main-content">
      <div class="image-pane">
        <div v-if="imagesCopy.length > 0" class="image-wrapper" :style="paperImageWrapperStyle">
          <div v-for="item in imagesCopy" :key="item.url" class="image-item" :style="pageStyle">
            <div class="description">
              {{ item.batchNumberText }} 批次，第 {{ item.paperNoText }} 张
              <Divider class="divider" type="vertical"></Divider>
              {{ item.studentName }}
              <template v-if="item.isMultiFeedingReScan">
                <Divider class="divider" type="vertical"></Divider>
                <span class="sign-rescan">重扫</span>
              </template>
            </div>
            <img
              class="page-image"
              :src="item.url"
              @load="handleImageLoaded(item.url)"
              @error="handleImageLoaded(item.url)"
            />
          </div>
        </div>
        <div v-else-if="emptyText" class="empty">{{ emptyText }}</div>
        <div v-if="showLoadingSpin" class="loading-image-spin">
          <Spin size="large"></Spin>
        </div>
      </div>
    </div>
    <div class="sider">
      <div v-if="images.length > 0" class="toolbar">
        <div class="btn" title="放大图片" @click="zoomImage('in')">
          <img src="@/assets/images/common/zoom_in.svg" style="width: 16px" />
        </div>
        <div class="btn" title="缩小图片" @click="zoomImage('out')">
          <img src="@/assets/images/common/zoom_out.svg" style="width: 16px" />
        </div>
        <div class="btn" title="还原" @click="resetImage">
          <Icon class="btn-icon-reset" type="md-refresh" :size="18"></Icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { roundNumber } from '@/utils/math'
  import { loadImage } from '@/utils/promise'

  export default {
    props: {
      images: {
        type: Array,
        default: () => [],
      },
      showLoadingSpin: Boolean,
      emptyText: {
        type: String,
        default: '暂无数据',
      },
    },
    emits: ['image-loaded'],
    data() {
      return {
        imagesCopy: [],
        scaleIndex: 0,
        validScaleValues: [],
      }
    },
    computed: {
      paperImageScale() {
        return this.validScaleValues[this.scaleIndex]
      },
      paperImageWrapperStyle() {
        if (this.paperImageScale <= 1) {
          return null
        } else {
          return {
            width: `${this.paperImageScale * 100}%`,
          }
        }
      },
      pageStyle() {
        if (this.paperImageScale <= 1) {
          return {
            width: `${this.paperImageScale * 100}%`,
          }
        } else {
          return {
            width: '100%',
          }
        }
      },
    },
    watch: {
      /**
       * 图片改变时，先加载图片，再显示出来，避免页面抖动、闪烁
       */
      images() {
        let urls = this.images.map(x => x.url)
        Promise.allSettled(urls.map(url => loadImage(url, false))).then(() => {
          if (this.images.length == urls.length && this.images.every(x => urls.includes(x.url))) {
            this.imagesCopy = this.images.slice()
          }
        })
      },
      imagesCopy() {
        this.$nextTick(() => {
          this.checkImageLoaded()
        })
      },
    },
    created() {
      this.addValidScaleValues()
      this.setDefaultScaleIndex()
    },
    methods: {
      addValidScaleValues() {
        let values = [0.25, 0.333, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
        let v = 1
        while (v < 5) {
          v += 0.1
          values.push(roundNumber(v, 4))
        }
        this.validScaleValues = values
      },
      setDefaultScaleIndex() {
        // 默认并排显示两张图
        this.scaleIndex = this.validScaleValues.indexOf(0.5)
      },
      zoomImage(option) {
        let nextScaleIndex = option === 'in' ? this.scaleIndex + 1 : this.scaleIndex - 1
        if (nextScaleIndex >= this.validScaleValues.length) {
          nextScaleIndex = this.validScaleValues.length - 1
        }
        if (nextScaleIndex < 0) {
          nextScaleIndex = 0
        }
        this.scaleIndex = nextScaleIndex
      },
      resetImage() {
        this.setDefaultScaleIndex()
      },
      /**
       * 检查图片是否已加载
       */
      checkImageLoaded() {
        this.$el.querySelectorAll('.page-image').forEach(img => {
          if (img.complete) {
            this.handleImageLoaded(img.src)
          }
        })
      },
      handleImageLoaded(url) {
        this.$emit('image-loaded', url)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-container {
    @include flex(row, flex-start, flex-start);
    width: 100%;
    height: 100%;
  }

  .main-content {
    flex-grow: 1;
    height: 100%;
    overflow: hidden;
  }

  .image-pane {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  .image-wrapper {
    @include flex(row, flex-start, flex-start);
    flex-wrap: wrap;

    .image-item {
      position: relative;
      overflow: hidden;

      .description {
        position: absolute;
        top: 10px;
        left: 10px;
        padding: 10px;
        color: $color-error;
        font-weight: bold;
        font-size: 16px;
        line-height: 1;
        background-color: rgba(255, 255, 255, 0.8);

        .divider {
          margin-right: 10px;
          margin-left: 10px;
        }

        .sign-rescan {
          color: $color-success;
        }
      }

      .page-image {
        // 若为行内元素，则图片包含框高度比图片高度大
        display: block;
        width: 100%;
      }
    }
  }

  .empty {
    @include flex(row, center, center);
    height: 100%;
    color: $color-icon;
  }

  .loading-image-spin {
    @include flex(row, center, center);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .sider {
    flex-grow: 0;
    flex-shrink: 0;
    width: 30px;
    height: 100%;
    padding-top: 16px;
    overflow: auto;
  }

  .toolbar {
    flex-grow: 0;
    flex-shrink: 0;
    width: 30px;

    :deep(.btn) {
      margin-bottom: 10px;
      text-align: center;
      cursor: pointer;
      user-select: none;

      .btn-icon-reset {
        margin-right: 2px;
        transform: scaleX(-1);
      }

      &.disabled {
        filter: grayscale(1) brightness(2.2);
      }
    }
  }
</style>
