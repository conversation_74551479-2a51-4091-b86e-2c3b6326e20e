<template>
  <div class="page-review-create">
    <div v-if="isAdmin" class="section-steps-bar">
      <Steps :current="currentStep">
        <Step
          v-for="(item, idx) in steps"
          :key="idx"
          :title="item.title"
          :content="item.content"
          :status="currentStep !== idx ? 'wait' : 'process'"
          @click="onStepClick(idx)"
        ></Step>
      </Steps>
    </div>
    <div class="section-content">
      <template v-if="currentStep === 0">
        <FormBaseInfo
          v-if="isAdmin"
          :form-options="formOptions"
          is-detail
          @on-next="onNext"
          @on-category-add="onCategoryAdd"
          @on-category-edit="onCategoryEdit"
          @on-category-change="onCategoryChange"
          @on-category-del="onCategoryDel"
          @on-refresh="onRefresh"
        />
        <base-info v-else :form-options="formOptions"></base-info>
      </template>
      <template v-else-if="currentStep === 1">
        <FormSchoolPicker
          :activity-detail="formOptions"
          :school-tree="schoolTree"
          :checked-data="currentActivity.schoolList"
          is-detail
          @on-next="onNext"
          @on-prev="onPrev"
          @on-refresh="onRefresh"
        />
      </template>
      <template v-else-if="currentStep === 2">
        <FormCustom
          :activity-detail="formOptions"
          :categories="currentActivity.categories"
          is-detail
          @on-prev="onPrev"
          @on-submit="onSubmit"
          @on-refresh="onRefresh"
        />
      </template>
    </div>
  </div>
</template>

<script>
  import FormBaseInfo from '../components/form_base_info.vue'
  import FormSchoolPicker from '../components/form_school_picker.vue'
  import FormCustom from '../components/form_custom.vue'
  import BaseInfo from '../components/base_info_no_edit.vue'

  import { apiGetChildOrgAndSchool } from '@/api/user/school'
  import { apiCreateActivity } from '@/api/review/activity'

  import PageCache from '@/utils/page_cache'
  import { formatDateTime } from '@/utils/date'
  import { deepCopy } from '@/utils/object'

  const baseInfoComponentName = 'forn_base_info'
  const schoolPickerComponentName = 'form_school_picker'
  const formCustomComponentName = 'form_custom'

  const staticFormItemOptions = [
    {
      fieldLabel: '作品名称',
      fieldType: 'text',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 1,
      description: '',
      extraJson: '{"placeholder": "请输入作品名称", "minLength": 1, "maxLength": 100}',
    },
    {
      fieldLabel: '作品上传',
      fieldType: 'upload',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 2,
      description: '',
      isDefault: true,
      extraJson: '{"accepts": ["image","pdf"], "maxFileSize": 5242880, "maxFileCount": 1}',
    },
  ]

  export default {
    components: {
      FormBaseInfo,
      FormSchoolPicker,
      FormCustom,
      'base-info': BaseInfo,
    },
    data() {
      return {
        steps: [
          {
            title: '基本信息',
            content: '活动名称、时间、类别',
          },
          {
            title: '选择参赛学校',
            content: '参赛学校',
          },
          {
            title: '提交内容设置',
            content: '设置报名表单、文件上传等',
          },
        ],
        currentStep: 0,
        formOptions: {
          name: '',
          description: '',
          participantIdentity: 'student',
          registrantIdentity: 'teacher',
          registerStartTime: '',
          registerEndTime: '',
          submitEndTime: '',
          categories: [],
          allowMultipleCategories: false,
          isPublic: false,
          needAudit: false,
        },
        schoolTree: [],
      }
    },
    computed: {
      schoolId() {
        return this.$store.state.user.schoolId || ''
      },
      currentActivity() {
        return this.$store.getters['review/currentActivity']
      },
      activityId() {
        return this.$store.getters['review/activityId']
      },
      isAdmin() {
        return this.$store.getters['review/isAdmin']
      },
    },
    watch: {
      currentActivity() {
        this.updateFormOptions()
      },
    },
    created() {
      this.getSchoolTree()
      this.updateFormOptions()
    },
    methods: {
      onNext() {
        this.currentStep++
      },
      onPrev() {
        this.currentStep--
      },
      onStepClick(idx) {
        this.currentStep = idx
      },
      onRefresh() {
        this.getReviewDetail()
      },
      getReviewDetail() {
        // 目前路由修改后没有 activityId store更新后页面title也会更新
        // const { activityId } = this.$route?.params

        // apiGetReviewActivityDetail({
        //   activityId,
        // }).then(res => {
        //   this.formOptions = {
        //     ...res,
        //     categories: res.categoryList,
        //   }
        // })
        this.$store.dispatch('review/fetchActivityDetail', this.activityId)
      },
      updateFormOptions() {
        if (this.currentActivity) {
          this.formOptions = deepCopy(this.currentActivity)
        }
      },
      buildTree(data = []) {
        return data.map(item => {
          const children = item.schools ? this.buildTree(item.schools) : []

          return {
            ...item,
            disabled: true,
            schools: children,
          }
        })
      },
      getSchoolTree() {
        return apiGetChildOrgAndSchool({
          eduSchoolId: this.schoolId,
        })
          .then(res => {
            this.schoolTree = [
              {
                schoolName: this.$store.state.user.schoolName,
                schoolId: this.schoolId,
                schools: res || [],
                schoolType: this.$store.state.user.schoolType,
              },
            ]
          })
          .catch(() => {
            this.schoolTree = []
          })
      },
      onCategoryAdd() {
        let categories = this.formOptions?.categories || []

        if (!categories.some(item => item.editing)) {
          categories.push({
            activityId: this.formOptions.id,
            name: '',
            id: '',
            editing: true,
            description: '',
            fields: staticFormItemOptions,
            sortCode: categories.length + 1,
          })
        }
      },
      onCategoryEdit(data) {
        let categories = this.formOptions?.categories || []
        const theCategory = categories.find(item => item.name === data.name)

        if (theCategory) {
          theCategory.editing = true
        }
      },
      onCategoryChange(data) {
        let categories = this.formOptions?.categories || []
        const theCategoryIdx = categories.findIndex(item => item.name === data.name)

        if (theCategoryIdx >= 0) {
          if (!data.name) {
            categories.splice(theCategoryIdx, 1)
          } else {
            categories[theCategoryIdx].editing = false
          }
        }
      },
      onCategoryDel(data) {
        let categories = this.formOptions?.categories || []
        const theCategoryIdx = categories.findIndex(item => item.name === data.name)

        if (theCategoryIdx >= 0) {
          categories.splice(theCategoryIdx, 1)
        }
      },
      onSubmit() {
        let baseInfoCache = PageCache.fetch(baseInfoComponentName)
        let schoolPickerCache = PageCache.fetch(schoolPickerComponentName)
        let formCustomCache = PageCache.fetch(formCustomComponentName)
        let categories = (baseInfoCache?.formOptions?.categories || []).map((item, idx) => {
          let theCategoryOptions = formCustomCache && formCustomCache[item.name]
          return {
            name: item.name,
            fields: (theCategoryOptions?.dynamicComponents || []).map((c, cIdx) => {
              let extraOptions = {}
              let tempOptions = {}
              try {
                extraOptions = JSON.parse(c.extraJson)
                for (const key in extraOptions) {
                  if (key === 'maxFileSize') {
                    tempOptions[key] = Number(extraOptions[key].value) * 1024 * 1024
                  } else {
                    tempOptions[key] = extraOptions[key].value
                  }
                }
              } catch (error) {}

              return {
                fieldLabel: c.fieldLabel,
                fieldType: c.fieldType,
                subType: c.subType,
                isRequired: c.isRequired,
                isReviewItem: c.isReviewItem,
                sortOrder: cIdx + 1,
                description: c.description,
                extraJson: JSON.stringify(tempOptions),
              }
            }),
            sortCode: idx + 1,
          }
        })

        let params = {
          ...(baseInfoCache?.formOptions || {}),
          registerStartTime: formatDateTime(new Date(baseInfoCache?.formOptions?.registerStartTime)),
          registerEndTime: formatDateTime(new Date(baseInfoCache?.formOptions?.registerEndTime)),
          submitEndTime: formatDateTime(new Date(baseInfoCache?.formOptions?.submitEndTime)),
          schoolIds: schoolPickerCache?.checkedKeys || [],
          categories,
        }

        apiCreateActivity(params).then(() => {
          this.$Message.success('创建成功')
          this.$router.back()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-review-create {
    --el-color-primary: #05c1ae;
    padding-top: 20px;
    background-color: #fff;

    :deep(.ivu-steps-item) {
      cursor: pointer;
    }

    .section-steps-bar {
      padding: 0 20px 20px;
    }

    .section-content {
      padding: 0 20px 20px;

      :deep(.header) {
        margin-bottom: 10px;
        font-size: 20px;
      }
    }
  }
</style>
