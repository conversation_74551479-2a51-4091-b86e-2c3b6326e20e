<template>
  <div class="block-detail">
    <div class="form-item form-item-block-name">
      <div class="form-item-label">题块名称</div>
      <div class="form-item-body">{{ block.blockName }}</div>
    </div>
    <div class="form-item form-item-question-name">
      <div class="form-item-label">包含题目</div>
      <div class="form-item-body">{{ questionsText }}</div>
    </div>
    <div class="form-item">
      <div class="form-item-label">题块组长（{{ block.leaders.length }} 人）</div>
      <div class="form-item-body" v-html="leadersText"></div>
    </div>
    <div v-if="needArbitrate" class="form-item form-item-arbitrators">
      <div class="form-item-label">仲裁老师（{{ block.arbitrators.length }} 人）</div>
      <div class="form-item-body" v-html="arbitratorsText"></div>
    </div>
    <div class="form-item form-item-teachers">
      <div class="form-item-label">评卷老师（{{ block.teachers.length }} 人）</div>
      <div class="form-item-body" v-html="teachersText"></div>
    </div>
  </div>
</template>

<script>
  import MarkModeEnum from '@/enum/emarking/mark_mode'
  import MarkingWayEnum from '@/enum/emarking/marking_way'

  export default {
    props: {
      block: Object,
      searchTeacherKeyword: String,
    },
    computed: {
      isMarkingByGroup() {
        return [MarkingWayEnum.Group.id, MarkingWayEnum.GroupAmount.id].includes(this.block.markingWay.id)
      },
      needArbitrate() {
        return [MarkModeEnum.DoubleArbitrate.id, MarkModeEnum.TripleArbitrate.id].includes(this.block.markMode.id)
      },
      questionsText() {
        return this.block.questions.map(q => q.questionName).join('，')
      },
      teachersText() {
        if (this.isMarkingByGroup) {
          return this.getGroupNamesText(this.block.teachers, t =>
            this.block.assignMode.id === 1 ? `${t.realName}（${t.assignedNumber}）` : t.realName
          )
        } else {
          return this.getNamesText(this.block.teachers, t =>
            this.block.assignMode.id === 1 ? `${t.realName}（${t.assignedNumber}）` : t.realName
          )
        }
      },
      arbitratorsText() {
        if (this.isMarkingByGroup) {
          return this.getGroupNamesText(this.block.arbitrators, t => t.realName)
        } else {
          return this.getNamesText(this.block.arbitrators, t => t.realName)
        }
      },
      leadersText() {
        if (this.isMarkingByGroup) {
          return this.getGroupNamesText(this.block.leaders, t => t.realName)
        } else {
          return this.getNamesText(this.block.leaders, t => t.realName)
        }
      },
    },
    methods: {
      getNamesText(teachers, itemTextFunction) {
        return teachers
          .map(t => {
            let className = 'teacher-item'
            if (
              this.searchTeacherKeyword &&
              (t.realName.includes(this.searchTeacherKeyword) ||
                (t.mobile && t.mobile.includes(this.searchTeacherKeyword)))
            ) {
              className += ' warning'
            }
            return `<span class="${className}">${itemTextFunction(t)}</span>`
          })
          .join('，')
      },
      getGroupNamesText(teachers, itemTextFunction) {
        return this.block.groups
          .map(g => {
            let groupTeachers = teachers.filter(t => t.groupId === g.groupId)
            let namesText = this.getNamesText(groupTeachers, itemTextFunction)
            return `<div class="group-item"><span class="label">${g.groupName}（${groupTeachers.length}人）&nbsp;&nbsp;</span>${namesText}</div>`
          })
          .join('')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-item {
    @include flex(row, flex-start, flex-start);
    line-height: 24px;

    &:not(:last-child) {
      margin-bottom: 10px;
    }

    .form-item-label {
      flex-grow: 0;
      flex-shrink: 0;
      min-width: 10em;
      font-weight: bold;

      &::after {
        content: '\ff1a';
      }
    }

    .form-item-body {
      flex-grow: 1;

      :deep(.group-item) {
        padding-left: 2em;

        .label {
          margin-left: -2em;
        }
      }

      :deep(.teacher-item) {
        display: inline-block;
      }

      :deep(.warning) {
        color: $color-warning;
      }
    }
  }

  .form-item.form-item-teachers,
  .form-item.form-item-arbitrators {
    .form-item-label {
      min-width: 10em;
    }
  }
</style>
