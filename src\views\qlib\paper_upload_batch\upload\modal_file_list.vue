<template>
  <Modal
    :model-value="props.modelValue"
    :width="800"
    footer-hide
    :styles="{ top: '50px' }"
    @on-visible-change="handleVisibleChange"
  >
    <template #header>
      <div class="header">
        <span class="title">批量上传试卷</span>
        <TextButton class="btn-export" type="primary" @click="handleExportExcel">导出Excel</TextButton>
      </div>
    </template>
    <Table
      class="table-file-list"
      :columns="tableColumns"
      :data="tableData"
      :max-height="tableMaxHeight"
      :row-class-name="tableRowClassName"
    >
      <template #file="{ row }">
        <div class="cell-file-name">
          {{ row.fileName }}
        </div>
      </template>
      <template #status="{ row }">
        <div class="status">
          <div class="status-main" :class="row.statusColor">{{ row.statusMainText }}</div>
          <div v-if="row.statusSubText" class="status-sub">{{ row.statusSubText }}</div>
        </div>
      </template>
      <template #action="{ row }">
        <TextButton
          v-if="row.canChangeIndex"
          :disabled="changeFileDisabled"
          type="primary"
          @click="changeCurrentFile(row)"
          >查看</TextButton
        >
      </template>
    </Table>
  </Modal>
</template>

<script setup>
  import { computed } from 'vue'
  import iView from '@/iview'
  import { useUploadBatchStore } from '@/store/qlib/upload_batch'

  import { exportExcel } from '@/utils/excel_export'
  import { formatDateTime } from '@/utils/date'
  import StepStatusEnum from '@/enum/qlib/upload_batch/step_status'

  const uploadBatchStore = useUploadBatchStore()

  const props = defineProps({
    modelValue: Boolean,
  })
  const emits = defineEmits(['update:modelValue'])

  const tableMaxHeight = window.innerHeight - 160
  const tableColumns = [
    {
      title: '序号',
      width: 60,
      key: 'order',
    },
    {
      title: '文件名',
      slot: 'file',
    },
    {
      title: '状态',
      width: 150,
      slot: 'status',
    },
    {
      title: '操作',
      width: 80,
      align: 'center',
      slot: 'action',
    },
  ]

  const changeFileDisabled = computed(() => Boolean(uploadBatchStore.changeCurrentFileTip.error))
  const tableRowClassName = row => {
    if (row.order == uploadBatchStore.currentFile?.order) {
      return 'active'
    }
    return ''
  }

  const tableData = computed(() => {
    return uploadBatchStore.files.map(file => {
      let status = getStatus(file)
      return {
        ...file,
        fileName: file.file.name,
        statusColor: status.color,
        statusText: status.text,
        statusMainText: status.mainText,
        statusSubText: status.subText,
        canChangeIndex:
          file.skiped ||
          (file.uploadStatus == StepStatusEnum.Succeeded.id && file.saveQuestionsStatus == StepStatusEnum.Succeeded.id),
      }
    })
  })

  function getStatus(file) {
    let mainText = ''
    let color = ''
    if (file.skiped) {
      mainText = '已跳过'
      color = 'warning'
    } else if (
      file.uploadStatus == StepStatusEnum.Succeeded.id &&
      file.saveQuestionsStatus == StepStatusEnum.Succeeded.id
    ) {
      mainText = '已执行'
      color = 'success'
    } else if (file.order == uploadBatchStore.currentFile?.order) {
      mainText = '执行中'
      color = 'primary'
    } else if (file.uploadStatus == StepStatusEnum.Ready.id) {
      mainText = '待执行'
      color = 'default'
    } else {
      mainText = '状态错误'
    }
    let subText = ''
    if (mainText != '待执行') {
      let uploadStatusText = ''
      if (file.uploadStatus == StepStatusEnum.Ready.id) {
        uploadStatusText = '待上传'
      } else if (file.uploadStatus == StepStatusEnum.Processing.id) {
        uploadStatusText = '上传中'
      } else if (file.uploadStatus == StepStatusEnum.Succeeded.id) {
        uploadStatusText = '已上传'
      } else if (file.uploadStatus == StepStatusEnum.Failed.id) {
        uploadStatusText = '上传失败'
      }
      let saveQuestionsStatusText = ''
      if (file.saveQuestionsStatus == StepStatusEnum.Ready.id) {
        saveQuestionsStatusText = '待划题'
      } else if (file.saveQuestionsStatus == StepStatusEnum.Processing.id) {
        saveQuestionsStatusText = '划题保存中'
      } else if (file.saveQuestionsStatus == StepStatusEnum.Succeeded.id) {
        saveQuestionsStatusText = '已划题'
      } else if (file.saveQuestionsStatus == StepStatusEnum.Failed.id) {
        saveQuestionsStatusText = '划题保存失败'
      }
      subText = `${uploadStatusText}，${saveQuestionsStatusText}`
    }

    return {
      color,
      mainText,
      subText,
      text: mainText + (subText ? `（${subText}）` : ''),
    }
  }

  function changeCurrentFile(file) {
    if (changeFileDisabled.value) {
      return
    }
    let warning = uploadBatchStore.changeCurrentFileTip.warning
    if (warning) {
      iView.Modal.confirm({
        title: '切换文件',
        content: `${warning}<br>是否切换文件？`,
        onOk: () => {
          emits('update:modelValue', false)
          uploadBatchStore.changeCurrentFileIndex(file.order - 1)
          emits('update:modelValue', false)
        },
      })
    } else {
      uploadBatchStore.changeCurrentFileIndex(file.order - 1)
      emits('update:modelValue', false)
    }
  }

  function handleExportExcel() {
    exportExcel(
      [
        {
          sheetName: '批量上传试卷',
          rows: tableData.value,
          columns: [
            {
              title: '序号',
              width: 'auto',
              key: 'order',
            },
            {
              title: '文件名',
              key: 'fileName',
              width: 'auto',
            },
            {
              title: '状态',
              key: 'statusText',
              width: 'auto',
            },
          ],
        },
      ],
      `${formatDateTime(new Date(), 'YYYY-MM-DD HH_mm_ss')} 批量上传试卷.xlsx`
    )
  }

  function handleVisibleChange(visibility) {
    if (!visibility) {
      emits('update:modelValue', false)
    }
  }
</script>

<style lang="scss" scoped>
  .header {
    height: 20px;
    line-height: 20px;

    .title {
      margin-right: 24px;
      font-size: $font-size-medium-x;
    }

    .btn-export {
      padding: 0;
      font-size: $font-size-medium;
    }
  }

  .table-file-list {
    :deep(.active td) {
      background-color: $color-iview-table-active-row;
    }

    :deep(.cell-file-name) {
      padding-top: 8px;
      padding-bottom: 8px;
      line-height: 1.2;
    }

    :deep(.status) {
      padding-top: 8px;
      padding-bottom: 8px;

      .status-main {
        font-weight: bold;
        line-height: 1;

        &.warning {
          color: $color-warning;
        }

        &.success {
          color: $color-success;
        }

        &.primary {
          color: $color-primary;
        }
      }

      .status-sub {
        margin-top: 6px;
        color: $color-icon;
        font-size: $font-size-medium-s;
        line-height: 1;
      }
    }
  }
</style>
