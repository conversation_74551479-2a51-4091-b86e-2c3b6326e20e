<template>
  <Modal
    :model-value="modelValue"
    :mask-closable="false"
    :closable="false"
    :width="modalWidth"
    :style="modalStyle"
    :footer-hide="isHailingSpecial"
  >
    <template #header>
      <div class="modal-header">
        <div class="modal-title">
          考生原卷<span v-if="subjectName">&nbsp;-&nbsp;{{ subjectName }}</span>
          <span class="student-attributes">
            （{{ schoolName + ', ' + className + ', ' + studentAdmissionNum + ', ' + studentName }}）
          </span>
          <span v-if="showGradeAssignmentExplain" class="grade-assignment-explain"
            >原始分：{{ (paperScore && Number(paperScore)) || '-' }} ； 等级分：{{
              (scaleScore && Number(scaleScore)) || '-'
            }}</span
          >
        </div>
        <Icon class="close-icon" type="md-close" @click="closeModal" />
      </div>
    </template>

    <div ref="canvasContainer" class="modal-content">
      <Spin v-if="shieldPlateShowed" fix>
        <Icon type="ios-loading" size="30" class="spin-icon-load" />
        图片加载中……
      </Spin>
      <canvas v-if="!shieldPlateShowed" ref="markedCanvas" class="marked-canvas"></canvas>
    </div>

    <template #footer>
      <div class="modal-footer">
        <Button type="primary" :disabled="shieldPlateShowed" @click="downloadCanvas">
          <Icon type="md-download" />下载图片
        </Button>
        <a ref="downloadShadow" style="display: none"></a>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiGetStudentPaper } from '@/api/emarking'
  import { apiGetExamPaperPicAndMarkSituation } from '@/api/report'
  import { apiGetExamSubjectScanTemplate } from '@/api/wrong_question_sheet_scan'

  import { drawBlockComments } from '@/helpers/report/comment'
  import { roundScore } from '@/utils/math'
  import { deepCopy } from '@/utils/object'
  import { groupArray } from '@/utils/array'

  import Role from '@/enum/user/role'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      studentId: {
        type: String,
        default: '',
      },
      examSubjectId: {
        type: String,
        default: '',
      },
      templateId: {
        type: String,
        default: '',
      },
      reportName: {
        type: String,
        default: '',
      },
      schoolName: {
        type: String,
        default: '',
      },
      className: {
        type: String,
        default: '',
      },
      studentName: {
        type: String,
        default: '',
      },
      studentAdmissionNum: {
        type: String,
        default: '',
      },
      isHailingSpecial: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        shieldPlateShowed: false,
        markedCanvas: {},
        downloadShadow: {},

        modalWidth: Math.min(window.innerWidth - 200, window.innerWidth * 0.85),
        modalStyle: {
          top: (window.innerHeight * 0.125) / 2 + 'px',
        },
        templateAttributes: {},
        pictureUrls: [],
        studentScore: 0,
        objectiveRects: [],
        subjectiveInfomations: [],

        scaleScore: null,
        paperScore: null,

        isSpecialExamSubjectNoShowMarker: false,
      }
    },

    computed: {
      isFeedbackProject() {
        return this.$store.getters['report/isFeedbackProject']
      },

      currentUserRoles() {
        return this.$store.getters['user/info'].roles
      },

      templateSubjects() {
        return this.$store.getters['report/templateSubjects'] || []
      },

      examGradeId() {
        return this.$store.state.report?.exam?.grade?.id || 0
      },

      subjectName() {
        let result = this.templateSubjects.find(subject => subject.examSubjectId === this.examSubjectId)
        return result ? result.subjectName : ''
      },

      examId() {
        return this.$store.getters['report/examId']
      },

      examName() {
        return this.$store.getters['report/examName']
      },

      isAdministrator() {
        return this.$store.getters['report/isAdministrator']
      },

      currentReport() {
        return this.$store.getters['report/currentReport']
      },

      showStudentPaperBlockScore() {
        return (
          this.isAdministrator ||
          (this.currentReport &&
            this.currentReport.rolePositions.some(role => role.detail && role.detail.showStudentPaperBlockScore))
        )
      },

      showStudentPaperMarker() {
        if (this.isSpecialExamSubjectNoShowMarker) {
          return (
            this.isAdministrator ||
            (this.currentReport && this.currentReport.rolePositions).some(
              role => role.roleId === Role.ResearcherLeader.id
            )
          )
        }

        return (
          this.isAdministrator ||
          (this.currentReport &&
            this.currentReport.rolePositions.some(role => role.detail && role.detail.showStudentPaperMarker))
        )
      },

      showGradeAssignmentExplain() {
        return (
          this.examGradeId > 9 &&
          this.examGradeId < 13 &&
          this.scaleScore &&
          this.subjectName &&
          ['政治', '地理', '化学', '生物'].includes(this.subjectName)
        )
      },
    },

    watch: {
      modelValue() {
        if (this.modelValue) {
          this.isSpecialExamSubjectNoShowMarker =
            (this.examId === '2kcjll6292ps' && this.examSubjectId === '2kcjll6ypr7m') || // 2025年阳西县初中学业水平考试第一次模拟检测（九年级）英语
            (this.examId === '2lqj2veghs00' && this.examSubjectId === '2lqj2vfcyghu') // 2025年阳西县初中学业水平考试第二次模拟检测（九年级）英语
          if (this.isFeedbackProject) {
            this.shieldPlateShowed = true
            Promise.all([
              apiGetStudentPaper({
                examId: this.examId,
                examSubjectId: this.examSubjectId,
                studentId: this.studentId,
              }),
              apiGetExamSubjectScanTemplate(this.examSubjectId),
            ])
              .then(([paperData, templateData]) => {
                this.pictureUrls = (paperData && paperData.imgUrls) || []

                let scoredQuestions = []
                // objectives
                scoredQuestions = scoredQuestions.concat(
                  ((paperData && paperData.objects) || [])
                    .filter(obj => obj.stuScore)
                    .map(obj => ({
                      questionCode: obj.objectiveCode.toString(),
                      questionScore: obj.stuScore,
                    }))
                )
                // subjectives
                ;((paperData && paperData.subjs) || []).forEach(sbj => {
                  scoredQuestions = scoredQuestions.concat(
                    (sbj.subScore || '')
                      .split(';')
                      .filter(String)
                      .map(q => {
                        let splitArray = q.split('=')
                        return {
                          questionCode: splitArray[0] || '',
                          questionScore: Number(splitArray[1] || 0),
                        }
                      })
                      .filter(x => x.questionCode && x.questionScore)
                  )
                })

                this.studentScore = scoredQuestions.reduce((acc, cur) => acc + cur.questionScore, 0)

                let templateQuestionLocations = []
                let tplObjectiveAreas = (
                  (templateData && templateData.tpl && templateData.tpl.tplFile && templateData.tpl.tplFile.pages) ||
                  []
                )
                  .map(x =>
                    x.objectiveAreas.map(y => {
                      y.pageIndex = x.pageIndex
                      return y
                    })
                  )
                  .reduce((acc, cur) => acc.concat(cur), [])
                ;((templateData && templateData.questions && templateData.questions.objectives) || []).forEach(qo => {
                  let tplRectAreas = tplObjectiveAreas.find(toa => toa.id === qo.areaId)
                  if (tplRectAreas) {
                    ;(qo.questions || []).forEach((q, qdx) => {
                      let questionRect = (tplRectAreas.options || [])[qdx] || null
                      if (questionRect) {
                        templateQuestionLocations.push({
                          pageIndex: tplRectAreas.pageIndex,
                          questionCode: q.branchCode ? `${q.questionCode}.${q.branchCode}` : q.questionCode.toString(),
                          questionRect: questionRect,
                        })
                      }
                    })
                  }
                })

                templateQuestionLocations = groupArray(templateQuestionLocations, x => x.pageIndex)
                templateQuestionLocations.sort((a, b) => a.key - b.key)
                templateQuestionLocations = templateQuestionLocations.map(x => x.group)

                this.objectiveRects = templateQuestionLocations.map(page => {
                  return page
                    .filter(x => !scoredQuestions.some(y => y.questionCode === x.questionCode))
                    .map(x => x.questionRect)
                })
              })
              .finally(() => {
                if (this.pictureUrls.length) {
                  this.drawCanvas()
                } else {
                  this.shieldPlateShowed = false
                }
              })
          } else {
            this.shieldPlateShowed = true
            apiGetExamPaperPicAndMarkSituation({
              studentId: this.studentId,
              examSubjectId: this.examSubjectId,
              templateId: this.templateId,
              reportName: this.reportName,
            })
              .then(response => {
                this.scaleScore = response.totalScoreMap && response.totalScoreMap.scaleScore
                this.paperScore = response.totalScoreMap && response.totalScoreMap.paperScore
                this.pictureUrls = response.paperUrls
                this.studentScore = response.totalScoreMap.paperScore
                this.templateAttributes = JSON.parse(response.templateStr)

                if (this.templateAttributes && this.templateAttributes.A) {
                  this.transferOriginalTemplate(
                    this.templateAttributes.A.pages,
                    response.stuObjectiveVos,
                    response.stuSubjectiveVos,
                    response.totalScoreMap
                  )
                } else {
                  let originPages = this.templateAttributes.tpl.tplFile.pages
                  let tiledOriginPagesSubjectiveAreas = originPages.reduce(
                    (acc, cur) => acc.concat(cur.subjectiveAreas || []),
                    []
                  )
                  let tplObjectiveQuestions = this.templateAttributes.questions.objectives
                  let tplSubjectiveQuestions = this.templateAttributes.questions.subjectives
                  let transPages = []

                  transPages = originPages.map(page => {
                    let transObjectiveAreas = page.objectiveAreas.map(objArea => {
                      objArea.questions = (
                        tplObjectiveQuestions.find(oq => oq.areaId === objArea.id) || { questions: [] }
                      ).questions.map((q, qIndex) => {
                        return {
                          questionCode: q.questionCode,
                          questionOptions: objArea.options.slice(
                            qIndex * objArea.optionCount,
                            (qIndex + 1) * objArea.optionCount
                          ),
                        }
                      })
                      return objArea
                    })
                    let transSubjectiveAreas = deepCopy(tplSubjectiveQuestions)
                      .filter(sq => {
                        return sq.areaIds.some(aid => page.subjectiveAreas.some(psa => psa.id == aid))
                      })
                      .map(sq => {
                        sq.rects = []
                        sq.part = 0
                        sq.commentY = 0
                        sq.areaIds.forEach((areaId, areaIndex) => {
                          let targetArea = page.subjectiveAreas.find(area => area.id === areaId)
                          if (targetArea) {
                            sq.rects.push(targetArea.area)
                            if (!sq.partSetted) {
                              sq.part = areaIndex
                            }
                            sq.partSetted = true
                            if (areaIndex) {
                              for (let i = 0; i < areaIndex; i++) {
                                sq.commentY += (
                                  tiledOriginPagesSubjectiveAreas.find(area => area.id === sq.areaIds[i]) || {
                                    area: { height: 0 },
                                  }
                                ).area.height
                              }
                            }
                          }
                        })

                        return sq
                      })

                    return {
                      objectiveAreas: transObjectiveAreas,
                      subjectiveAreas: transSubjectiveAreas,
                    }
                  })

                  this.transferNewTemplate(
                    transPages,
                    response.stuObjectiveVos,
                    response.stuSubjectiveVos,
                    response.totalScoreMap
                  )
                }
              })
              .finally(() => {
                if (this.pictureUrls.length) {
                  this.drawCanvas()
                } else {
                  this.shieldPlateShowed = false
                }
              })
          }
        }
      },
    },

    mounted() {
      if (!this.isHailingSpecial) {
        this.downloadShadow = this.$refs.downloadShadow
      }
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },

      transferOriginalTemplate(pages, stuObjectiveVos, stuSubjectiveVos, totalScoreMap) {
        let blockPartMap = new Map()
        pages.forEach((page, pageIndex) => {
          // 客观题整理
          this.objectiveRects[pageIndex] = []
          page.choises.forEach(chose => {
            let questionOptionCount = chose.mode.span0

            let questions = []
            for (let i = chose.qno1; i <= chose.qno2; i++) {
              let question = stuObjectiveVos.find(obj => obj.quesCode == i)
              if (question) {
                let answers = []
                try {
                  answers = question.standardAnswer
                    .split(';')
                    .filter(y => y.includes('='))[0]
                    .split('=')[0]
                    .split('')
                } catch {
                  answers = []
                }
                questions.push({
                  answers: answers,
                  questionType: question.questionType,
                })
              } else {
                questions.push({
                  answers: [],
                  questionType: 0,
                })
              }
            }

            if (questions && questions.length) {
              questions.forEach((question, qIndex) => {
                question.answers.forEach(answer => {
                  if (question.questionType === 13) {
                    // 判断题的适配 T / F => A / B
                    answer = ['T', 't'].some(x => x === answer) ? 'A' : 'B'
                  }
                  this.objectiveRects[pageIndex].push(
                    chose.opts[answer.charCodeAt() - 65 + qIndex * questionOptionCount]
                  )
                })
              })
            }
          })

          // 主观题整理
          this.subjectiveInfomations[pageIndex] = []
          page.fuzzys.forEach(fuzzy => {
            let targetQuestionInformation = stuSubjectiveVos.find(x => x.blockId == fuzzy.outKey)
            if (targetQuestionInformation) {
              // 子图可以由几块（fuzzy）组成，找当前是第几块
              let thePart = blockPartMap.get(fuzzy.outKey)
              let part = thePart ? thePart.part : 0
              let partOffsetY = thePart ? thePart.partOffsetY : 0
              blockPartMap.set(fuzzy.outKey, {
                part: part + 1,
                partOffsetY: partOffsetY + fuzzy.rect.r.height,
              })
              let scoreReviewed =
                targetQuestionInformation.markerList.find(marker => marker.markingType === '复核分') || null

              // 主观题框y值不能太小，以免无法显示文字
              fuzzy.rect.r.y = Math.max(36, fuzzy.rect.r.y)

              this.subjectiveInfomations[pageIndex].push({
                part,
                partOffsetY,
                rect: fuzzy.rect.r,
                questionInformation:
                  targetQuestionInformation.blockName +
                  '：' +
                  targetQuestionInformation.score +
                  '分' +
                  (scoreReviewed && scoreReviewed.score === targetQuestionInformation.score ? '【复核】' : '') +
                  '（满分：' +
                  targetQuestionInformation.fullScore +
                  '分）',
                markers: targetQuestionInformation.markerList.map(marker => ({
                  type: marker.markingType,
                  name: marker.markerName,
                  score: marker.score,
                })),
                commentJsons: targetQuestionInformation.commentJsons || [],
              })
            }
          })

          // 客观题加进主观题
          if (this.objectiveRects[pageIndex].length > 0) {
            let objectiveRect = page.choises[0].rect.r
            objectiveRect.y -= 45

            if (this.subjectiveInfomations[pageIndex].length > 0) {
              objectiveRect.x = this.subjectiveInfomations[pageIndex][0].rect.x
            } else {
              objectiveRect.x = Math.round(objectiveRect.x / 2)
            }

            this.subjectiveInfomations[pageIndex].push({
              part: 0,
              partOffsetY: 0,
              rect: objectiveRect,
              questionInformation:
                '客观题：' +
                roundScore(totalScoreMap.objectScore) +
                '分（满分：' +
                roundScore(stuObjectiveVos.reduce((acc, cur) => acc + cur.fullScore, 0)) +
                '分）[绿框选项为正确答案]',
              markers: [],
              commentJsons: [],
            })
          }
        })
      },

      transferNewTemplate(pages, stuObjectiveVos, stuSubjectiveVos, totalScoreMap) {
        pages.forEach((page, pageIndex) => {
          // 客观题整理
          this.objectiveRects[pageIndex] = []
          page.objectiveAreas.forEach(objArea => {
            objArea.questions.forEach(q => {
              let targetQuestion = stuObjectiveVos.find(obj => obj.quesCode == q.questionCode)
              if (targetQuestion) {
                let answers = []
                try {
                  answers = targetQuestion.standardAnswer
                    .split(';')
                    .filter(y => y.includes('='))[0]
                    .split('=')[0]
                    .split('')
                } catch {
                  answers = []
                }
                answers.forEach(answer => {
                  if (targetQuestion.questionType === 13) {
                    answer = ['T', 't'].includes(answer) ? 'A' : 'B'
                  }
                  this.objectiveRects[pageIndex].push(q.questionOptions[answer.charCodeAt() - 65])
                })
              }
            })
          })

          // 主观题整理
          this.subjectiveInfomations[pageIndex] = []
          page.subjectiveAreas.forEach(subjArea => {
            let targetSubjectiveInfo = stuSubjectiveVos.find(subj => subj.blockId === subjArea.subjectiveId)
            if (targetSubjectiveInfo) {
              subjArea.rects[0].y = Math.max(36, subjArea.rects[0].y)

              this.subjectiveInfomations[pageIndex].push({
                part: subjArea.part || 0,
                partOffsetY: 0,
                rect: subjArea.rects[0],
                questionInformation:
                  targetSubjectiveInfo.blockName +
                  '：' +
                  targetSubjectiveInfo.score +
                  '分（满分：' +
                  targetSubjectiveInfo.fullScore +
                  '分）',
                markers: targetSubjectiveInfo.markerList.map(marker => ({
                  type: marker.markingType,
                  name: marker.markerName,
                  score: marker.score,
                })),
                commentJsons: targetSubjectiveInfo.commentJsons || [],
                commentY: subjArea.commentY || 0,
              })
            }
          })

          // 客观题加进主观题
          if (this.objectiveRects[pageIndex].length > 0) {
            let objectiveRect = deepCopy(page.objectiveAreas[0].questions[0].questionOptions[0])
            objectiveRect.y -= 45

            const FirstSubjectiveInformationAreaX = this.subjectiveInfomations[pageIndex].length
              ? this.subjectiveInfomations[pageIndex][0].rect.x
              : 0
            if (FirstSubjectiveInformationAreaX && FirstSubjectiveInformationAreaX - objectiveRect.x < 500) {
              objectiveRect.x = FirstSubjectiveInformationAreaX
            } else {
              objectiveRect.x = Math.round(objectiveRect.x / 2)
            }

            this.subjectiveInfomations[pageIndex].unshift({
              part: 0,
              partOffsetY: 0,
              rect: objectiveRect,
              questionInformation:
                '客观题：' +
                roundScore(totalScoreMap.objectScore) +
                '分（满分：' +
                roundScore(stuObjectiveVos.reduce((acc, cur) => acc + cur.fullScore, 0)) +
                '分）[绿框选项为正确答案]',
              markers: [],
              commentJsons: [],
            })
          }
        })
      },

      drawCanvas() {
        if (!this.pictureUrls.length) {
          this.$Message.error({
            content: '试卷图片为空',
          })
          this.shieldPlateShowed = false
          return
        }

        // 加载图片
        Promise.all(
          this.pictureUrls.map(x => {
            return new Promise((resolve, reject) => {
              let tempImage = new Image()
              tempImage.crossOrigin = 'anonymous'
              tempImage.src = x
              tempImage.onload = () => resolve(tempImage)
              tempImage.onerror = () => reject()
            })
          })
        )
          .then(images => {
            this.shieldPlateShowed = false
            return images
          })
          .then(async images => {
            let markedCanvas = this.$refs.markedCanvas
            let canvasContainer = this.$refs.canvasContainer
            let ctx = markedCanvas && markedCanvas.getContext('2d')

            let naturalWidth = images[0].naturalWidth
            let naturalHeight = images[0].naturalHeight
            let aspectRatio = naturalWidth / naturalHeight
            let wrapWidth = markedCanvas.clientWidth
            let wrapHeight = wrapWidth / aspectRatio
            let widthRatio = wrapWidth / naturalWidth
            let sumWrapHeight = images.length * wrapHeight

            canvasContainer.scrollTo(0, 0)

            markedCanvas.width = naturalWidth
            markedCanvas.height = sumWrapHeight / widthRatio
            markedCanvas.style.width = wrapWidth + 'px'
            markedCanvas.style.height = sumWrapHeight + 'px'

            if (this.isFeedbackProject) {
              for (let i = 0; i < images.length; i++) {
                ctx.drawImage(
                  images[i],
                  0,
                  (i * wrapHeight) / widthRatio,
                  wrapWidth / widthRatio,
                  wrapHeight / widthRatio
                )

                ctx.fillStyle = 'red'
                ctx.font = '80px san-serif'
                ctx.fillText(this.studentScore, 70, 120)

                ctx.strokeStyle = 'red'
                ctx.lineWidth = 2
                this.objectiveRects[i].forEach(rect => {
                  if (rect) {
                    ctx.strokeRect(rect.x, rect.y + i * (wrapHeight / widthRatio), rect.width, rect.height)
                  }
                })
              }
            } else {
              for (let i = 0; i < images.length; i++) {
                let image = images[i]

                // 绘图
                ctx.drawImage(image, 0, (i * wrapHeight) / widthRatio, wrapWidth / widthRatio, wrapHeight / widthRatio)

                if (!this.showStudentPaperBlockScore) {
                  continue
                }

                // 总分
                ctx.fillStyle = 'red'
                ctx.font = '127px san-serif'
                ctx.fillText(this.studentScore, aspectRatio > 1 ? 50 : 100, aspectRatio > 1 ? 160 : 180)

                // 客观题
                ctx.strokeStyle = '#00C800'
                ctx.lineWidth = aspectRatio > 1 ? 5 : 3
                this.objectiveRects[i].forEach(rect => {
                  if (rect) {
                    ctx.strokeRect(rect.x, rect.y + i * (wrapHeight / widthRatio), rect.width, rect.height)
                  }
                })

                // 主观题
                ctx.fillStyle = 'rgba(255, 21, 38, 0.8)'
                for (let question of this.subjectiveInfomations[i]) {
                  const IsSmallRectArea = !question.questionInformation.includes('客观题') && question.rect.width < 500
                  ctx.font = IsSmallRectArea
                    ? 'bold 15px san-serif'
                    : `bold ${aspectRatio > 1 ? '45px' : '40px'} san-serif`

                  // 题目信息
                  if (question.part == 0) {
                    ctx.fillText(
                      question.questionInformation,
                      question.rect.x,
                      question.rect.y + i * (wrapHeight / widthRatio)
                    )
                  }

                  // 评卷人信息
                  if (question.part == 0 && this.showStudentPaperMarker) {
                    let blockMarker = ''
                    if (IsSmallRectArea) {
                      question.markers.forEach((marker, markerIndex) => {
                        if (marker.name) {
                          blockMarker =
                            (question.markers.length === 1 ? '评卷人' : marker.type) +
                            '：' +
                            marker.name +
                            ' ' +
                            marker.score +
                            '分'
                          ctx.fillText(
                            blockMarker,
                            question.rect.x,
                            question.rect.y + i * (wrapHeight / widthRatio) + (markerIndex + 1) * 18
                          )
                        } else if (marker.type === '复核') {
                          blockMarker = marker.type + '：' + marker.score + '分'
                          ctx.fillText(
                            blockMarker,
                            question.rect.x,
                            question.rect.y + i * (wrapHeight / widthRatio) + (markerIndex + 1) * 18
                          )
                        }
                      })
                    } else if (question.markers.length > 1) {
                      question.markers.forEach((marker, markerIndex) => {
                        if (marker.name) {
                          blockMarker = marker.type + '：' + marker.name + ' ' + marker.score + '分'
                          ctx.fillText(
                            blockMarker,
                            question.rect.x + question.rect.width - ctx.measureText(blockMarker).width,
                            question.rect.y + i * (wrapHeight / widthRatio) + markerIndex * 50
                          )
                        } else if (marker.type === '复核') {
                          blockMarker = marker.type + '：' + marker.score + '分'
                          ctx.fillText(
                            blockMarker,
                            question.rect.x + question.rect.width - ctx.measureText(blockMarker).width,
                            question.rect.y + i * (wrapHeight / widthRatio) + markerIndex * 50
                          )
                        }
                      })
                    } else if (question.markers.length) {
                      if (question.markers[0].name) {
                        blockMarker = '评卷人：' + question.markers[0].name
                        ctx.fillText(
                          blockMarker,
                          question.rect.x + question.rect.width - ctx.measureText(blockMarker).width,
                          question.rect.y + i * (wrapHeight / widthRatio)
                        )
                      }
                    }
                  }

                  // 批注
                  let comments = []
                  ;(question.commentJsons || []).forEach(s => {
                    try {
                      let teacherComments = JSON.parse(s)
                      teacherComments.forEach(c => {
                        c.y -= question.commentY || 0
                        comments.push(c)
                      })
                    } catch {
                      // do nothing
                    }
                  })
                  if (comments.length > 0) {
                    // 限定区域
                    ctx.save()
                    ctx.beginPath()
                    ctx.rect(
                      question.rect.x,
                      question.rect.y + i * (wrapHeight / widthRatio),
                      question.rect.width,
                      question.rect.height
                    )
                    ctx.clip()
                    await drawBlockComments(
                      ctx,
                      comments,
                      question.rect.x,
                      question.rect.y + i * (wrapHeight / widthRatio) - question.partOffsetY
                    )
                    ctx.restore()
                  }
                }
              }
            }

            // 下载
            if (!this.isHailingSpecial) {
              this.downloadShadow.download = `${this.examName}-${this.schoolName}-${this.className}-${this.studentAdmissionNum}-${this.studentName}-${this.subjectName}.jpg`
              markedCanvas.toBlob(
                data => {
                  this.downloadShadow.href = window.URL.createObjectURL(data)
                },
                'image/jpeg',
                0.72
              )
            }
          })
          .catch(() => {
            this.$Message.error({
              content: '加载图片失败',
            })
            this.shieldPlateShowed = false
          })
      },

      downloadCanvas() {
        this.downloadShadow.click()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    @include flex(row, space-between, center);
    padding: 0 1%;
    font-size: $font-size-large-s;

    .modal-title {
      min-width: 450px;
      white-space: nowrap;
    }

    .student-attributes {
      font-size: $font-size-medium;
    }

    .grade-assignment-explain {
      color: $color-primary;
      font-size: $font-size-medium;
    }

    .close-icon {
      font-size: $font-size-large-x;
    }

    .close-icon:hover {
      color: $color-primary-light;
      cursor: pointer;
    }
  }

  .modal-content {
    height: 65vh;
    border: 1px solid $color-disabled;
    overflow-y: auto;

    .marked-canvas {
      width: 100%;
    }
  }

  .spin-icon-load {
    animation: load-spin 1s linear infinite;
  }

  @keyframes load-spin {
    from {
      transform: rotate(0deg);
    }

    50% {
      transform: rotate(180deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
