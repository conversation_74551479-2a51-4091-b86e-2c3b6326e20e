<script>
  import { roundNumber } from '@/utils/math'

  export default {
    props: {
      rate: {
        type: Number,
        default: 0,
      },
    },

    computed: {
      percent() {
        return this.rate > 1 ? this.rate : roundNumber(this.rate * 100, 1)
      },

      progressColorStyle() {
        return {
          width: `${this.percent}%`,
          'background-color':
            this.percent >= 70 ? '#19be6b' : this.percent < 70 && this.percent >= 40 ? '#2db7f5' : '#ed4014',
        }
      },
    },
  }
</script>

<template>
  <div class="component-progress">
    <div class="progress-color" :style="progressColorStyle"></div>
    <div class="progress-text">{{ percent }}%</div>
  </div>
</template>

<style lang="scss" scoped>
  .component-progress {
    position: relative;
    display: inline-block;
    width: 100%;
    height: 14px;
    border-radius: 7px;
    overflow: hidden;
    line-height: 14px;
    vertical-align: middle;
    background-color: #ccc;

    .progress-color {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 7px;
      background-color: $color-primary;
      transition: width 0.2s ease;
    }

    .progress-text {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      color: white;
      font-size: 10px;
      text-align: center;
    }
  }
</style>
