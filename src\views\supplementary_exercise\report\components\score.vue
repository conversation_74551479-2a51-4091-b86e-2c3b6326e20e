<script>
  import NoData from '@/components/no_data'

  import { exportExcel } from '@/utils/excel_export'

  export default {
    components: {
      NoData,
    },

    props: {
      examInfo: Object,
      classDetail: Object,
    },

    data() {
      return {
        tableMaxHeight: window.innerHeight - 280,
      }
    },

    computed: {
      tableData() {
        return this.classDetail.studentScoreList || []
      },

      tableColumns() {
        const QuesScores = this.classDetail.quesScores || []
        const ObjectiveQuesScores = QuesScores.filter(ques => !('branchCode' in ques))
        const SubjectiveQuesScores = QuesScores.filter(ques => 'branchCode' in ques)
        const ObjectiveScore = ObjectiveQuesScores.reduce((acc, cur) => acc + cur.fullScore, 0)
        const SubjectiveScore = SubjectiveQuesScores.reduce((acc, cur) => acc + cur.fullScore, 0)
        const FullScore = this.classDetail.fullScore

        const _Columns = [
          {
            title: '姓名',
            key: 'studentName',
            fixed: 'left',
            minWidth: 80,
            align: 'center',
          },
          {
            title: '班级',
            render: h => h('span', {}, this.classDetail.className),
            fixed: 'left',
            minWidth: 80,
            align: 'center',
          },
          {
            title: '考号',
            key: 'admissionNum',
            fixed: 'left',
            minWidth: 80,
            align: 'center',
          },
          {
            title: '成绩',
            align: 'center',
            fixed: 'left',
            children: [
              {
                title: FullScore,
                key: 'score',
                minWidth: 80,
                align: 'center',
                fixed: 'left',
              },
            ],
          },
          {
            title: '客观题',
            align: 'center',
            fixed: 'left',
            children: [
              {
                title: ObjectiveScore,
                key: 'objectiveScore',
                minWidth: 70,
                align: 'center',
                fixed: 'left',
              },
            ],
          },
          {
            title: '主观题',
            align: 'center',
            fixed: 'left',
            children: [
              {
                title: SubjectiveScore,
                key: 'subjectiveScore',
                minWidth: 70,
                align: 'center',
                fixed: 'left',
              },
            ],
          },
        ]

        QuesScores.forEach(ques => {
          const ColumnQuestionName = ques.branchName || ques.questionName || ''
          _Columns.push({
            title: ColumnQuestionName,
            align: 'center',
            children: [
              {
                title: ques.fullScore,
                minWidth: 60,
                align: 'center',
                render: (h, params) => {
                  let spanText = '-'
                  if (
                    params.row.codeScoreMap &&
                    (params.row.codeScoreMap[ColumnQuestionName] || params.row.codeScoreMap[ColumnQuestionName] === 0)
                  ) {
                    spanText = params.row.codeScoreMap[ColumnQuestionName]
                  }
                  return h('span', {}, spanText)
                },
              },
            ],
          })
        })

        return _Columns
      },
    },

    methods: {
      downloadExcel() {
        if (!this.tableData.length) {
          this.$Message.warning({
            duration: 3,
            content: '暂无数据',
          })
          return
        }

        exportExcel(
          [
            {
              sheetName: '学生成绩',
              rows: this.tableData,
              columns: this.tableColumns.map(column => {
                let key = column.key
                if (column.title === '班级') {
                  key = () => this.classDetail.className
                }

                return {
                  title: column.title,
                  key: key,
                  children:
                    column.children && column.children.length
                      ? column.children.map(cc => {
                          return {
                            title: cc.title,
                            key: cc.render
                              ? row =>
                                  row.codeScoreMap &&
                                  (row.codeScoreMap[column.title] || row.codeScoreMap[column.title] === 0)
                                    ? row.codeScoreMap[column.title]
                                    : '-'
                              : cc.key,
                          }
                        })
                      : undefined,
                }
              }),
            },
          ],
          `${this.examInfo.examName}_${this.classDetail.className}_学生成绩单.xlsx`
        )
      },
    },
  }
</script>

<template>
  <div class="container-coach-report-score">
    <template v-if="tableData.length">
      <div class="oper-bar">
        <TextButton icon="md-download" @click="downloadExcel">导出Excel</TextButton>
      </div>
      <Table :columns="tableColumns" :data="tableData" :max-height="tableMaxHeight" border></Table>
    </template>
    <NoData v-else></NoData>
  </div>
</template>

<style lang="scss" scoped>
  .container-coach-report-score {
    .oper-bar {
      width: 100%;
      margin-bottom: 8px;
      text-align: right;
    }
  }
</style>
