<template>
  <div class="room">
    <div class="selector-student-status">
      <span v-show="roomNo" class="room-no"
        ><span class="number">{{ roomNo }}</span
        >考场</span
      >
      <RadioGroup :model-value="studentStatusId" @on-change="changeStatus">
        <Radio v-for="s in studentStatusList" :key="s.id" :label="s.id" class="radio-item">{{ s.name }}</Radio>
      </RadioGroup>
      <TextButton
        class="btn-show-room-students"
        :disabled="!roomNo"
        type="primary"
        @click="showModalRoomStudents = true"
        >查看考生答卷</TextButton
      >
    </div>
    <Table
      class="table-students"
      :columns="tableStudentColumns"
      :data="tableStudentData"
      :height="tableMaxHeight"
    ></Table>

    <ModalRoomStudents
      v-model="showModalRoomStudents"
      :room-no="roomNo"
      :student-id="firstTableStudentId"
    ></ModalRoomStudents>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import ModalRoomStudents from '../../scan_student/modal_room_students.vue'

  import { apiSetStudentsMarkAbsent, apiUnsetStudentMarkAbsent } from '@/api/scan/scan_batch'

  import { mapGetters } from 'vuex'
  import PageStatusEnum from '@/enum/scan/page_status'

  export default {
    components: {
      ModalRoomStudents,
    },
    props: {
      roomNo: Number,
      roomStudents: Array,
      top: Number,
      studentStatusId: String,
    },
    emits: ['change-status', 'refresh'],
    data() {
      return {
        selectorHeight: 37,
        studentStatusList: [
          {
            id: 'all',
            name: '全部考生',
            filter: () => true,
          },
          {
            id: 'scanned',
            name: '已扫',
            filter: this.isScanned,
          },
          {
            id: 'markAbsent',
            name: '标记缺考',
            filter: this.isMarkAbsent,
          },
          {
            id: 'unScan',
            name: '未扫',
            filter: this.isUnScan,
          },
        ],
        showModalRoomStudents: false,
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'isInSchoolExam', 'hasPermissionScan']),
      activeStudentStatus() {
        return this.studentStatusList.find(x => x.id == this.studentStatusId)
      },
      tableMaxHeight() {
        return window.innerHeight - this.top - this.selectorHeight
      },
      tableStudentColumns() {
        let columns = [
          {
            title: '准考号',
            key: 'admissionNum',
            minWidth: 100,
            maxWidth: 180,
          },
          {
            title: '姓名',
            key: 'studentName',
            minWidth: 80,
            maxWidth: 180,
          },
          {
            title: '学校',
            key: 'schoolName',
            minWidth: 200,
          },
          {
            title: '班级',
            key: 'className',
            minWidth: 90,
            maxWidth: 180,
          },
          {
            title: '座位',
            key: 'seatNum',
            minWidth: 60,
            maxWidth: 180,
            align: 'center',
          },
          {
            title: '状态',
            width: 110,
            align: 'center',
            render: (h, params) =>
              h(
                'span',
                {
                  class: {
                    warning: params.row.warning,
                    error: params.row.error,
                  },
                },
                params.row.statusText
              ),
          },
          {
            title: '操作',
            minWidth: 110,
            align: 'center',
            render: (h, params) => {
              if (!this.hasPermissionScan) {
                return null
              }
              if (this.isMarkAbsent(params.row)) {
                return h(
                  TextButton,
                  {
                    type: 'default',
                    icon: 'ios-bookmark-outline',
                    onClick: () => {
                      this.unMarkStudentAbsent(params.row)
                    },
                  },
                  () => '取消标记'
                )
              } else if (this.isUnScan(params.row)) {
                return h(
                  TextButton,
                  {
                    type: 'default',
                    icon: 'ios-bookmark',
                    onClick: () => {
                      this.markStudentAbsent(params.row)
                    },
                  },
                  () => '标记缺考'
                )
              }
            },
          },
        ]
        if (this.isInSchoolExam) {
          columns = columns.filter(col => col.title != '学校')
        }
        return columns
      },
      tableStudentData() {
        if (!this.activeStudentStatus) {
          return []
        }
        return this.roomStudents.filter(this.activeStudentStatus.filter).map(stu => {
          let statusText = ''
          let warning = false
          let error = false
          if (this.isUnScan(stu)) {
            statusText = '未扫'
            warning = true
          } else if (this.isMarkAbsent(stu)) {
            statusText = '标记缺考'
          } else if (stu.pageStatus == PageStatusEnum.Duplicate.id) {
            statusText = '已扫（多页）'
            error = true
          } else if (stu.pageStatus == PageStatusEnum.Missing.id) {
            statusText = '已扫（缺页）'
            error = true
          } else if (stu.scanAbsent) {
            statusText = '已扫（缺考）'
          } else {
            statusText = '已扫'
          }
          return {
            ...stu,
            statusText,
            warning,
            error,
          }
        })
      },
      firstTableStudentId() {
        if (this.tableStudentData.length == 0) {
          return ''
        }
        return this.tableStudentData[0].studentId
      },
    },
    methods: {
      isScanned(stu) {
        return stu.scanned
      },
      isMarkAbsent(stu) {
        return !stu.scanned && stu.markAbsent
      },
      isUnScan(stu) {
        return !stu.scanned && !stu.markAbsent
      },
      changeStatus(status) {
        this.$emit('change-status', status)
      },
      markStudentAbsent(stu) {
        if (!this.isUnScan(stu)) {
          return
        }
        this.$Modal.confirm({
          title: '标记缺考',
          content: `是否标记缺考<br><strong>${stu.studentName} (${stu.admissionNum})</strong>？`,
          onOk: () => {
            apiSetStudentsMarkAbsent({
              examSubjectId: this.examSubjectId,
              studentIds: [stu.studentId],
            })
              .then(() => {
                this.$Message.success({
                  content: '操作成功',
                })
              })
              .finally(() => {
                this.$emit('refresh')
              })
          },
        })
      },
      unMarkStudentAbsent(stu) {
        if (!this.isMarkAbsent(stu)) {
          return
        }
        this.$Modal.confirm({
          title: '取消标记缺考',
          content: `是否取消标记缺考<br><strong>${stu.studentName} (${stu.admissionNum})</strong>？`,
          onOk: () => {
            apiUnsetStudentMarkAbsent({
              examSubjectId: this.examSubjectId,
              studentId: stu.studentId,
            })
              .then(() => {
                this.$Message.success({
                  content: '操作成功',
                })
              })
              .finally(() => {
                this.$emit('refresh')
              })
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .room {
    /** 避免表格切换显示滚动条引起外部宽度变化 */
    overflow-x: hidden;
  }

  .selector-student-status {
    @include flex(row, flex-start, center);
    margin-top: 8px;
    margin-bottom: 8px;

    .room-no {
      margin-right: 30px;
      line-height: 21px;

      .number {
        margin-right: 5px;
        font-weight: bold;
        font-size: $font-size-large;
      }
    }

    .radio-item {
      margin-right: 20px;
    }

    .btn-show-room-students {
      margin-left: auto;
    }
  }

  .table-students {
    line-height: 1.2;

    :deep(.warning) {
      color: $color-warning;
    }

    :deep(.error) {
      color: $color-error;
    }
  }
</style>
