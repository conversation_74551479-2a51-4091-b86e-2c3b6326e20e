<template>
  <div class="page-award-management">
    <div class="section-tab">
      <Tabs v-model="activeTab">
        <TabPane label="评审结果" name="award-list"></TabPane>
        <TabPane label="学校获奖统计" name="school-award-stat"></TabPane>
      </Tabs>
      <div class="box-btn">
        <span v-if="awardStatusText" class="award-status" :class="{ success: awardSuccess, fail: awardFail }">{{
          awardStatusText
        }}</span>
        <Button v-if="showBtnAction" type="primary" :disabled="isBtnGenerateDisabled" @click="onBtnGenerateClick">{{
          btnGenerateText
        }}</Button>
        <Button type="primary" @click="onShowModalAwardConfig">奖项设置</Button>
        <!-- <Button @click="onExport">导出</Button> -->
      </div>
    </div>
    <div class="section-tab-pane">
      <AwardList v-if="activeTab === 'award-list'" />
      <SchoolAwardStat v-else />
    </div>

    <ModalAwardConfig v-if="modalAwardConfigVisible" v-model="modalAwardConfigVisible" />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import AwardList from '../../components/award/award_list.vue'
  import SchoolAwardStat from '../../components/award/school_award_stat.vue'
  import ModalAwardConfig from '../../components/award/modal_award_config.vue'
  import ReviewProjectStatusEnum from '@/enum/review/review_project_status'
  import RevewAwardStatusEnum from '@/enum/review/review_award_status'
  import { apiUpdateAwardStatus } from '@/api/review/activity'

  export default {
    components: {
      AwardList,
      SchoolAwardStat,
      ModalAwardConfig,
    },
    data() {
      return {
        activeTab: 'award-list',
        modalAwardConfigVisible: false,
      }
    },
    computed: {
      ...mapGetters('review', [
        'isAdmin',
        'isRegisterAuditor',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'enableSchoolAudit',
        'isMultipleSchool',
        'userList',
        'categories',
        'participantIdentity',
        'activityId',
        'schoolList',
        'currentActivity',
      ]),
      showBtnAction() {
        return this.currentActivity?.status === ReviewProjectStatusEnum.ActivityEnded.id && !this.awardSuccess
      },
      btnGenerateText() {
        if (this.currentActivity?.status !== ReviewProjectStatusEnum.ActivityEnded.id) return
        if (this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardPending.id) {
          return '开始生成结果'
        }
        if (
          [RevewAwardStatusEnum.AwardSuccess.id, RevewAwardStatusEnum.AwardFail.id].includes(
            this.currentActivity?.awardStatus
          )
        ) {
          return '重新生成结果'
        }
        return '正在生成中'
      },
      isBtnGenerateDisabled() {
        return (
          this.currentActivity?.status !== ReviewProjectStatusEnum.ActivityEnded.id ||
          this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardInProgress.id
        )
      },
      awardStatusText() {
        if (this.awardSuccess) {
          return '结果生成成功'
        } else if (this.awardFail) {
          return '结果生成失败'
        } else if (this.awardInProgress) {
          return '结果生成中'
        }
        return '结果尚未生成'
      },
      awardPending() {
        return this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardPending.id
      },
      awardInProgress() {
        return this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardInProgress.id
      },
      awardSuccess() {
        return this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardSuccess.id
      },
      awardFail() {
        return this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardFail.id
      },
    },
    methods: {
      onShowModalAwardConfig() {
        this.modalAwardConfigVisible = true
      },
      onBtnGenerateClick() {
        let newStatus = RevewAwardStatusEnum.AwardInProgress.id
        let currentStatus = ''

        if (this.awardPending) {
          // 开始生成
          currentStatus = RevewAwardStatusEnum.AwardPending.id // pending
        } else if (this.awardSuccess) {
          currentStatus = RevewAwardStatusEnum.AwardSuccess.id // success
        } else if (this.awardFail) {
          currentStatus = RevewAwardStatusEnum.AwardFail.id // fail
        }

        apiUpdateAwardStatus({
          activityId: this.activityId,
          currentStatus,
          newStatus,
        }).then(() => {
          this.$store.dispatch('review/refreshActivityDetail')
        })
      },
      onExport() {},
    },
  }
</script>

<style lang="scss" scoped>
  .page-award-management {
    padding: 20px;
    background-color: #fff;

    .section-tab {
      position: relative;

      .box-btn {
        position: absolute;
        top: -2px;
        right: 0;
      }

      .award-status {
        margin-right: 6px;
        vertical-align: middle;
      }

      .award-status.success {
        color: $color-success;
      }

      .award-status.fail {
        color: $color-warning;
      }
    }
  }
</style>
