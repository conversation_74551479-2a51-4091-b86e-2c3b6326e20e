<template>
  <div class="scan-student-container" :style="containerStyle">
    <div class="frame-left">
      <ImageContainer
        :scan-unit="activeScanUnit"
        show-absent
        show-objective
        show-score
        show-select
        :is-scan-absent="isActiveScanUnitAbsent"
        :objectives="objectivesForPaperShow"
        :scores="scoresForPaperShow"
        :selects="selectsForPaperShow"
        :empty-text="fetching ? '' : '暂无答卷'"
        @change-scan-absent="handleChangeAbsent"
        @change-objective-answer="handleChangeObjectiveAnswer"
        @change-subjective-score="handleChangeSubjectiveScore"
        @change-select-block-checked="handleChangeSelectBlockChecked"
      >
        <template #header>
          <div class="header">
            <span class="description"
              ><template v-if="activeScanUnitStuInfo"
                ><span class="stu-info">{{ activeScanUnitStuInfo }}</span
                ><Divider class="divider" type="vertical"></Divider>
                <span class="btn-batch-paper" @click="showModalBatchPaper = true"
                  >{{ activeScanUnit.batchNumberText }} 批次</span
                >，<span class="btn-raw-paper" @click="showModalRawPaper = true"
                  >第 {{ activeScanUnit.paperNoText }} 张</span
                >，扫描员：{{ activeScanUnit.scanUserName }}，时间：{{ activeScanUnit.scanTimeText }}
              </template>
            </span>
            <TextButton v-if="roomStudents.length > 0 && hasBlock" type="primary" @click="showModalSubPaper = true"
              >查看子图</TextButton
            >
            <span v-if="activeScanUnit && canChangeActiveScanUnit" class="action">
              <TextButton
                v-if="hasPermissionHandleOtherEx"
                type="warning"
                :disabled="activeScanUnitChangedObjectives.length == 0"
                @click="handleChangeObjective"
                >保存客观题</TextButton
              >
              <TextButton
                v-if="isPostScan && hasPermissionHandleOtherEx"
                type="warning"
                :disabled="!isActiveScanUnitSubjectivesChanged || !isActiveScanUnitAllScoresValid"
                @click="handleChangeSubjective"
                >保存主观题</TextButton
              >
              <TextButton
                v-if="hasSelectQuestion && hasPermissionHandleOtherEx"
                type="warning"
                :disabled="!isActiveScanUnitSelectsChanged || !isActiveScanUnitAllSelectsValid"
                @click="handleChangeSelect"
                >保存选做题</TextButton
              >
              <Dropdown v-if="hasPermissionHandleLocateEx" @on-click="handleDropdownClick">
                <span class="dropdown-label">操作<Icon type="ios-arrow-down"></Icon></span>
                <template #list>
                  <DropdownMenu>
                    <DropdownItem name="changeAdmissionNum">修改准考号</DropdownItem>
                    <DropdownItem name="delete">删除</DropdownItem>
                  </DropdownMenu>
                </template>
              </Dropdown>
            </span>
          </div>
        </template>
      </ImageContainer>
    </div>
    <div class="frame-right">
      <div class="section-room">
        <Form :label-width="70" label-position="left">
          <FormItem label="选择考场">
            <div class="form-item-select">
              <Select :model-value="currentRoomNo" filterable @on-change="changeCurrentRoomNo">
                <Option v-for="room in rooms" :key="room.roomNo" :value="room.roomNo">{{ room.roomNo }}</Option>
              </Select>
              <Button class="btn-refresh" type="primary" size="small" @click="handleBtnRefreshClick">刷新</Button>
            </div>
          </FormItem>
          <FormItem label="考场统计">
            <div v-if="currentRoomStats" class="form-item-stats">
              <span class="stats-item">已扫:{{ currentRoomStats.scanned }}/{{ currentRoomStats.total }}</span>
              <span v-if="currentRoomStats.notScan > 0" class="stats-item warning"
                >未扫:{{ currentRoomStats.notScan }}</span
              >
              <span v-if="currentRoomStats.markAbsent > 0" class="stats-item warning"
                >标记缺考:{{ currentRoomStats.markAbsent }}</span
              >
              <span v-if="currentRoomStats.scanAbsent > 0" class="stats-item warning"
                >扫描缺考:{{ currentRoomStats.scanAbsent }}</span
              >
              <span v-if="currentRoomStats.duplicate > 0" class="stats-item error"
                >多页:{{ currentRoomStats.duplicate }}</span
              >
              <span v-if="currentRoomStats.missing > 0" class="stats-item error"
                >缺页:{{ currentRoomStats.missing }}</span
              >
            </div>
          </FormItem>
        </Form>
      </div>
      <div class="section-list">
        <div
          v-for="s in roomStudents"
          :key="s.studentId"
          class="student-item"
          :class="{ current: s.studentId == currentStudentId }"
        >
          <div class="student-info" @click="changeCurrentStudentIdAndActiveScanUnitId(s.studentId)">
            <span class="admission-num">{{ s.admissionNum }}</span>
            <span class="student-name">{{ s.studentName }}</span>
            <span v-if="s.seatNum" class="seat-num" :title="'座位：' + s.seatNum">({{ s.seatNum }})</span>
            <span v-if="s.isMarkAbsent" class="status warning">[标记缺考]</span>
            <span v-else-if="s.isEmpty" class="status warning">[未扫]</span>
            <span v-if="s.isScanAbsent" class="status warning">[扫描缺考]</span>
            <span v-if="s.isDuplicate" class="status error">[多页]</span>
            <span v-if="s.isMissing" class="status error">[缺页]</span>
          </div>
          <div v-if="s.scanUnits.length > 1" class="student-scan-units">
            <div
              v-for="unit in s.scanUnits"
              :key="unit.unitId"
              class="scan-unit-item"
              :class="{ active: unit.unitId == activeScanUnitId }"
              @click="changeCurrentStudentIdAndActiveScanUnitId(s.studentId, unit.unitId)"
            >
              第 {{ unit.pageNoText }} 面，{{ unit.batchNumberText }} 批次，第 {{ unit.paperNoText }} 张
            </div>
          </div>
        </div>
      </div>
    </div>

    <ModalChangeAdmissionNum
      v-model="showModalChangeAdmissionNum"
      :scan-unit="activeScanUnit"
      @change="handleModalChangeAdmissionNumOK"
    ></ModalChangeAdmissionNum>

    <ModalSubPaper
      v-model="showModalSubPaper"
      :room-students="roomStudents"
      :init-student-id="currentStudentId"
      @refresh="handleBtnRefreshClick"
    ></ModalSubPaper>

    <ModalRawPaper v-model="showModalRawPaper" :scan-unit="activeScanUnit"></ModalRawPaper>

    <ModalBatchPaper
      v-model="showModalBatchPaper"
      :batch-number-text="activeScanUnit && activeScanUnit.batchNumberText"
    ></ModalBatchPaper>
  </div>
</template>

<script>
  import ImageContainer from '../../../answer_sheet/exception/components/image_container'
  import ModalChangeAdmissionNum from '../../../answer_sheet/exception/components/modal_change_admission_num'
  import ModalSubPaper from './modal_sub_paper.vue'
  import ModalRawPaper from '../../../answer_sheet/exception/components/modal_raw_paper'
  import ModalBatchPaper from '../../../answer_sheet/exception/components/modal_batch_paper'

  import {
    apiGetSubjectRooms,
    apiGetRoomStudents,
    apiGetRoomStudentsInfo,
    apiDeleteScanUnit,
    apiChangeAdmissionNum,
    apiChangeAbsent,
    apiChangeObjectives,
    apiChangeSubjectives,
    apiChangeSelects,
  } from '@/api/scan'

  import { mapGetters } from 'vuex'
  import {
    prefetchScanUnitAdjustImages,
    addScanUnitObjectivesInfo,
    addScanUnitScoresInfo,
    addScanUnitSelectsInfo,
    resetScanUnitNewObjectivesForEdit,
    resetScanUnitNewScoresForEdit,
    resetScanUnitNewSelectsForEdit,
    isScanUnitAbsent,
    setObjectiveNewAnswer,
    getScanUnitSubjectives,
    getScanUnitScoresDataForShow,
    isScanUnitNewScoresValid,
    getScanUnitChangedSubjectives,
    getScanUnitChangedSubjectivesTextContent,
    changeScanUnitSubjectiveScore,
    isSelectValid,
    setSelectNewAnswer,
    getScanUnitChangedSelectsTextContent,
  } from '@/helpers/scan'
  import { showDeleteResult } from '@/helpers/scan/delete_result'
  import { deepCopy } from '@/utils/object'
  import PageStatusEnum from '@/enum/scan/page_status'

  export default {
    components: {
      ImageContainer,
      ModalChangeAdmissionNum,
      ModalSubPaper,
      ModalRawPaper,
      ModalBatchPaper,
    },
    props: {
      initRoomNo: Number,
      initStudentId: String,
      initUnitId: String,
      top: Number,
    },
    emits: ['clear-init'],
    data() {
      return {
        // 考场
        rooms: [],
        currentRoomNo: null,

        // 学生
        roomStudents: [],
        currentStudentId: '',

        // 扫描单元
        activeScanUnitId: '',

        // 正在获取
        fetching: false,

        // 显示修改准考号弹窗
        showModalChangeAdmissionNum: false,

        // 显示查看子图弹窗
        showModalSubPaper: false,

        // 显示批次弹窗
        showModalBatchPaper: false,

        // 显示原卷弹窗
        showModalRawPaper: false,
      }
    },
    computed: {
      ...mapGetters('scan', [
        'examId',
        'examSubjectId',
        'objectiveDefine',
        'subjectiveDefine',
        'selectDefine',
        'isPostScan',
        'hasBlock',
        'hasSelectQuestion',
        'templatePaperCount',
        'viewMode',
        'hasPermissionHandleLocateEx',
        'hasPermissionHandleOtherEx',
        'hasPermissionChangeScanUnit',
        'isInSchoolExam',
      ]),
      viewModeScanStationId() {
        return this.viewMode.scanStationId
      },
      containerStyle() {
        return {
          height: `calc(100vh - ${this.top}px)`,
        }
      },

      // 考场统计
      currentRoomStats() {
        if (!this.currentRoomNo || this.roomStudents.length == 0 || this.roomStudents[0].roomNo != this.currentRoomNo) {
          return null
        }
        let total = 0,
          scanned = 0,
          notScan = 0,
          markAbsent = 0,
          scanAbsent = 0,
          duplicate = 0,
          missing = 0
        this.roomStudents.forEach(s => {
          total++
          if (s.isEmpty) {
            if (s.isMarkAbsent) {
              markAbsent++
            } else {
              notScan++
            }
          } else {
            scanned++
            if (s.isScanAbsent) {
              scanAbsent++
            }
            if (s.isDuplicate) {
              duplicate++
            }
            if (s.isMissing) {
              missing++
            }
          }
        })
        return {
          total,
          scanned,
          notScan,
          markAbsent,
          scanAbsent,
          duplicate,
          missing,
        }
      },

      // 当前学生
      currentStudent() {
        return this.roomStudents.find(s => s.studentId == this.currentStudentId) || null
      },

      // 当前单元
      activeScanUnit() {
        if (!this.currentStudent) {
          return null
        }
        return this.currentStudent.scanUnits.find(unit => unit.unitId == this.activeScanUnitId) || null
      },
      activeScanUnitStuInfo() {
        if (!this.activeScanUnit) {
          return
        }
        let { admissionNum, studentName, schoolName, pageNoText } = this.activeScanUnit
        let text = `${admissionNum}，${studentName}`
        if (!this.isInSchoolExam) {
          text += '，' + schoolName
        }
        if (this.templatePaperCount > 1) {
          text += `，第 ${pageNoText} 面`
        }
        return text
      },
      isActiveScanUnitAbsent() {
        return this.activeScanUnit && isScanUnitAbsent(this.activeScanUnit)
      },

      // 当前单元客观题
      activeScanUnitObjectives() {
        return (this.activeScanUnit && this.activeScanUnit.objectives) || []
      },
      objectivesForPaperShow() {
        return this.activeScanUnitObjectives.map(q => ({
          questionCode: q.questionCode,
          answer: q.newAnswer,
          status: q.newStatus,
        }))
      },
      activeScanUnitChangedObjectives() {
        return this.activeScanUnitObjectives.filter(q => q.answer != q.newAnswer)
      },

      // 当前单元主观题
      activeScanUnitSubjectives() {
        if (!this.activeScanUnit) {
          return []
        }
        return getScanUnitSubjectives(this.activeScanUnit)
      },
      scoresForPaperShow() {
        return getScanUnitScoresDataForShow(this.activeScanUnitSubjectives)
      },
      isActiveScanUnitAllScoresValid() {
        return isScanUnitNewScoresValid(this.activeScanUnitSubjectives)
      },
      activeScanUnitChangedSubjectives() {
        return getScanUnitChangedSubjectives(this.activeScanUnitSubjectives)
      },
      isActiveScanUnitSubjectivesChanged() {
        return (
          this.activeScanUnitChangedSubjectives.manualScores.length > 0 ||
          this.activeScanUnitChangedSubjectives.barScores.length > 0 ||
          this.activeScanUnitChangedSubjectives.trueFalseScores.length > 0
        )
      },

      // 当前单元选做题
      activeScanUnitSelects() {
        if (!this.activeScanUnit || !this.activeScanUnit.selects) {
          return []
        }
        return this.selectDefine
          .map(select => {
            return {
              selectGroupName: select.selectGroupName,
              selectCount: select.selectCount,
              blocks: select.blocks
                .map(({ blockId }) => this.activeScanUnit.selects.find(b => b.blockId == blockId))
                .filter(Boolean),
            }
          })
          .filter(select => select.blocks.length > 0)
      },
      selectsForPaperShow() {
        let list = []
        this.activeScanUnitSelects.forEach(select => {
          select.blocks.forEach(block => {
            list.push({
              blockId: block.blockId,
              answer: block.newAnswer,
              status: block.newStatus,
            })
          })
        })
        return list
      },
      isActiveScanUnitAllSelectsValid() {
        return this.activeScanUnitSelects.every(select =>
          isSelectValid(
            select.selectCount,
            select.blocks.map(block => block.newAnswer)
          )
        )
      },
      isActiveScanUnitSelectsChanged() {
        if (!this.activeScanUnit || !this.activeScanUnit.selects) {
          return false
        }
        return this.activeScanUnit.selects.some(block => block.answer != block.newAnswer)
      },

      // 当前单元操作权限
      canChangeActiveScanUnit() {
        if (!this.activeScanUnit) {
          return false
        }
        return this.hasPermissionChangeScanUnit(this.activeScanUnit)
      },
    },
    watch: {
      viewModeScanStationId() {
        this.rooms = []
        this.currentRoomNo = null
        this.roomStudents = []
        this.currentStudentId = ''
        this.activeScanUnitId = ''
        this.loadRooms().then(this.changeCurrentRoomNo)
      },
    },
    created() {
      this.initParams()
    },
    mounted() {
      document.addEventListener('keydown', this.handleKeyDown, true)
    },
    beforeUnmount() {
      document.removeEventListener('keydown', this.handleKeyDown, true)
    },
    methods: {
      /**
       * 加载数据
       */
      async initParams() {
        await this.loadRooms()
        if (this.initStudentId) {
          this.currentStudentId = this.initStudentId
        }
        if (this.initUnitId) {
          this.activeScanUnitId = this.initUnitId
        }
        await this.changeCurrentRoomNo((this.initRoomNo && Number(this.initRoomNo)) || undefined)
        this.$emit('clear-init')
      },
      loadRooms() {
        this.fetching = true
        return apiGetSubjectRooms({
          examSubjectId: this.examSubjectId,
          scanStationId: this.viewModeScanStationId,
        })
          .then(rooms => {
            this.rooms = rooms
          })
          .finally(() => {
            this.fetching = false
          })
      },
      loadRoomStudents() {
        if (!this.currentRoomNo) {
          this.roomStudents = []
          return Promise.resolve()
        }
        this.fetching = true
        this.$TransparentSpin.show()
        apiGetRoomStudentsInfo({
          examSubjectId: this.examSubjectId,
          roomNo: this.currentRoomNo,
        })
        return apiGetRoomStudents({
          examSubjectId: this.examSubjectId,
          roomNo: this.currentRoomNo,
        })
          .then(students => {
            students.forEach(stu => {
              stu.isEmpty = stu.scanUnits.length == 0
              stu.isDuplicate = stu.pageStatus == PageStatusEnum.Duplicate.id
              stu.isMissing = stu.pageStatus == PageStatusEnum.Missing.id
              stu.isScanAbsent = stu.scanUnits.some(isScanUnitAbsent)

              stu.scanUnits.forEach(unit => {
                // 转换客观题以便修改
                addScanUnitObjectivesInfo(unit, this.objectiveDefine)
                resetScanUnitNewObjectivesForEdit(unit)

                // 转换打分以便修改
                addScanUnitScoresInfo(unit, this.subjectiveDefine)
                resetScanUnitNewScoresForEdit(unit)

                // 转换选做题以便修改
                addScanUnitSelectsInfo(unit, this.selectDefine)
                resetScanUnitNewSelectsForEdit(unit)
              })
            })
            return this.changeCurrentStudentIdAndActiveScanUnitId(
              this.currentStudentId,
              this.activeScanUnitId,
              students
            )
          })
          .catch(err => {
            this.roomStudents = []
            this.currentStudentId = ''
            this.activeScanUnitId = ''
            throw err
          })
          .finally(() => {
            this.fetching = false
            this.$TransparentSpin.hide()
          })
      },

      /**
       * 设置当前项指针
       */
      changeCurrentRoomNo(roomNo) {
        let nextRoomNo = roomNo || this.currentRoomNo
        if (this.rooms.some(room => room.roomNo == nextRoomNo)) {
          this.currentRoomNo = nextRoomNo
        } else if (this.rooms.length > 0) {
          this.currentRoomNo = this.rooms[0].roomNo
        } else {
          this.currentRoomNo = null
        }
        return this.loadRoomStudents()
      },
      changeCurrentStudentIdAndActiveScanUnitId(studentId, scanUnitId, roomStudents) {
        let nextStudentId = studentId || this.currentStudentId
        let nextScanUnitId = scanUnitId || this.activeScanUnitId
        roomStudents = roomStudents || this.roomStudents

        if (roomStudents.every(stu => stu.studentId != nextStudentId)) {
          if (roomStudents.length > 0) {
            nextStudentId = roomStudents[0].studentId
          } else {
            nextStudentId = ''
          }
        }
        let nextStudent = roomStudents.find(stu => stu.studentId == nextStudentId)

        if (!nextStudent || nextStudent.scanUnits.every(unit => unit.unitId != nextScanUnitId)) {
          if (nextStudent && nextStudent.scanUnits.length > 0) {
            nextScanUnitId = nextStudent.scanUnits[0].unitId
          } else {
            nextScanUnitId = ''
          }
        }
        let nextScanUnit = nextStudent && nextStudent.scanUnits.find(unit => unit.unitId == nextScanUnitId)

        return prefetchScanUnitAdjustImages(nextScanUnit).then(() => {
          this.roomStudents = roomStudents
          this.currentStudentId = nextStudentId
          this.activeScanUnitId = nextScanUnitId
          resetScanUnitNewObjectivesForEdit(this.activeScanUnit)
          resetScanUnitNewScoresForEdit(this.activeScanUnit)
          this.$nextTick().then(this.scrollToCurrentItem)
        })
      },
      scrollToCurrentItem() {
        let elList = this.$el.querySelector('.section-list')
        if (!elList) {
          return
        }
        let elCurrentItem = elList.querySelector('.student-item.current')
        if (!elCurrentItem) {
          return
        }
        // 计算显示区域offsetTop范围
        let padding = 16
        let viewMinOffsetTop = elList.scrollTop + padding
        let viewMaxOffsetTop = elList.offsetHeight + elList.scrollTop - padding
        // 判断是否在显示区域内
        let currentItemTop = elCurrentItem.offsetTop
        let currentItemBottom = elCurrentItem.offsetTop + elCurrentItem.offsetHeight
        if (currentItemTop < viewMinOffsetTop) {
          elList.scrollTop -= viewMinOffsetTop - currentItemTop
        } else if (currentItemBottom > viewMaxOffsetTop) {
          elList.scrollTop += currentItemBottom - viewMaxOffsetTop
        }
      },
      handleKeyDown(e) {
        let keyName = e.key
        if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', ' '].includes(keyName)) {
          e.preventDefault()
        }
        if (!['ArrowUp', 'ArrowDown'].includes(keyName)) {
          return
        }
        e.stopPropagation()
        if (this.fetching) {
          return
        }
        if (!this.currentStudent) {
          return
        }
        let studentIndex = this.roomStudents.findIndex(s => s.studentId == this.currentStudentId)
        let scanUnitIndex = this.currentStudent.scanUnits.findIndex(unit => unit.unitId == this.activeScanUnitId)
        if (keyName == 'ArrowUp') {
          if (scanUnitIndex > 0) {
            let preScanUnitId = this.currentStudent.scanUnits[scanUnitIndex - 1].unitId
            this.changeCurrentStudentIdAndActiveScanUnitId(this.currentStudentId, preScanUnitId)
          } else if (studentIndex > 0) {
            let preStudent = this.roomStudents[studentIndex - 1]
            let preStudentId = preStudent.studentId
            let preScanUnits = preStudent.scanUnits
            let preScanUnitId = preScanUnits.length > 0 ? preScanUnits[preScanUnits.length - 1].unitId : ''
            this.changeCurrentStudentIdAndActiveScanUnitId(preStudentId, preScanUnitId)
          }
        } else if (keyName == 'ArrowDown') {
          if (scanUnitIndex < this.currentStudent.scanUnits.length - 1) {
            let nextScanUnitId = this.currentStudent.scanUnits[scanUnitIndex + 1].unitId
            this.changeCurrentStudentIdAndActiveScanUnitId(this.currentStudentId, nextScanUnitId)
          } else if (studentIndex < this.roomStudents.length - 1) {
            let nextStudent = this.roomStudents[studentIndex + 1]
            let nextStudentId = nextStudent.studentId
            let nextScanUnits = nextStudent.scanUnits
            let nextScanUnitId = nextScanUnits.length > 0 ? nextScanUnits[0].unitId : ''
            this.changeCurrentStudentIdAndActiveScanUnitId(nextStudentId, nextScanUnitId)
          }
        }
      },

      /**
       * 操作响应
       */
      // 刷新
      handleBtnRefreshClick() {
        this.loadRoomStudents().then(() => {
          this.$Message.info({
            content: '已刷新',
          })
        })
      },

      handleDropdownClick(name) {
        if (name == 'changeAdmissionNum') {
          this.handleChangeAdmissionNum()
        } else if (name == 'delete') {
          this.handleDelete()
        }
      },

      // 删除
      handleDelete() {
        if (!this.hasPermissionHandleLocateEx || !this.canChangeActiveScanUnit) {
          return
        }
        this.$Modal.confirm({
          title: '删除答卷',
          content: '确定删除当前答卷？',
          onOk: () => {
            apiDeleteScanUnit({
              examSubjectId: this.examSubjectId,
              scanUnitId: this.activeScanUnitId,
            })
              .then(res => {
                showDeleteResult(res, () => '已删除', {
                  examSubjectId: this.examSubjectId,
                  isInSchoolExam: this.isInSchoolExam,
                })
              })
              .finally(() => {
                this.loadRoomStudents()
              })
          },
        })
      },

      // 修改准考号
      handleChangeAdmissionNum() {
        if (!this.hasPermissionHandleLocateEx || !this.canChangeActiveScanUnit) {
          return
        }
        this.showModalChangeAdmissionNum = true
      },
      handleModalChangeAdmissionNumOK(stu) {
        if (!this.hasPermissionHandleLocateEx || !this.canChangeActiveScanUnit) {
          return
        }
        apiChangeAdmissionNum({
          examSubjectId: this.examSubjectId,
          scanUnitId: this.activeScanUnitId,
          admissionNum: stu.admissionNum,
        })
          .then(() => {
            this.$Message.success({
              content: '已修改',
            })
            this.showModalChangeAdmissionNum = false
          })
          .finally(() => {
            this.loadRoomStudents()
          })
      },

      // 修改缺考
      handleChangeAbsent(isAbsent) {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        let title = ''
        let content = ''
        let message = ''
        if (isAbsent) {
          title = '转为缺考'
          content = '确定将当前正常答卷转为缺考答卷？'
          message = '已转为缺考'
        } else {
          title = '转为正常'
          content = '确定将当前缺考答卷转为正常答卷？'
          message = '已转为正常'
        }

        this.$Modal.confirm({
          title,
          content,
          onOk: () => {
            apiChangeAbsent({
              examSubjectId: this.examSubjectId,
              scanUnitId: this.activeScanUnitId,
              isAbsent,
            })
              .then(() => {
                this.$Message.success({
                  content: message,
                })
              })
              .finally(() => {
                this.loadRoomStudents()
              })
          },
        })
      },

      // 修改客观题
      handleChangeObjectiveAnswer({ questionCode, answer }) {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        let question = this.activeScanUnitObjectives.find(q => q.questionCode == questionCode)
        if (question) {
          setObjectiveNewAnswer(question, answer)
        }
      },
      handleChangeObjective() {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        let content = '以下客观题答案已修改，是否确定保存更改？<br><br>'
        this.activeScanUnitChangedObjectives.forEach(q => {
          content += `${q.questionCode}：${q.answer === '' ? '[未选]' : q.answer} 改为 ${
            q.newAnswer === '' ? '[未选]' : q.newAnswer
          }<br>`
        })
        this.$Modal.confirm({
          title: '修改客观题答案',
          content,
          onOk: () => {
            apiChangeObjectives({
              examSubjectId: this.examSubjectId,
              scanUnitId: this.activeScanUnitId,
              objectives: this.activeScanUnitChangedObjectives.map(x => ({
                questionId: x.questionCode,
                answer: x.newAnswer,
              })),
            })
              .then(() => {
                this.$Message.success({
                  content: '已修改',
                })
              })
              .finally(() => {
                this.loadRoomStudents()
              })
          },
        })
      },

      // 修改打分
      handleChangeSubjectiveScore(data) {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        let subjectives = this.getChangeScoreSubjectivesByFullCode(data.fullCode)
        subjectives.forEach(q => {
          let copy = deepCopy(data)
          copy.fullCode = q.fullCode
          changeScanUnitSubjectiveScore(this.activeScanUnitSubjectives, copy)
        })
      },
      // 找要修改分数的主观题，考虑选做题
      getChangeScoreSubjectivesByFullCode(fullCode) {
        let fullCodes = []
        let selectGroup = this.selectDefine.find(select => select.blocks.some(block => block.questionCode == fullCode))
        if (selectGroup) {
          fullCodes = selectGroup.blocks.map(block => `${block.questionCode}`)
        } else {
          fullCodes = [fullCode]
        }
        return this.activeScanUnitSubjectives.filter(x => fullCodes.includes(x.fullCode))
      },
      handleChangeSubjective() {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        if (!this.isActiveScanUnitAllScoresValid || !this.isActiveScanUnitSubjectivesChanged) {
          return
        }
        this.$Modal.confirm({
          title: '修改主观题打分',
          content: `以下主观题打分已修改，是否确定保存更改？<br>${getScanUnitChangedSubjectivesTextContent(
            this.activeScanUnitSubjectives
          )}`,
          onOk: () => {
            apiChangeSubjectives({
              examSubjectId: this.examSubjectId,
              scanUnitId: this.activeScanUnitId,
              scores: this.activeScanUnitChangedSubjectives,
            })
              .then(() => {
                this.$Message.success({
                  content: '已修改',
                })
              })
              .finally(() => {
                this.loadRoomStudents()
              })
          },
        })
      },

      // 修改选做
      handleChangeSelectBlockChecked({ blockId, checked }) {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        let selectGroup = this.activeScanUnitSelects.find(x => x.blocks.some(b => b.blockId == blockId))
        if (selectGroup) {
          setSelectNewAnswer(selectGroup, blockId, checked)
        }
      },
      handleChangeSelect() {
        if (!this.hasPermissionHandleOtherEx || !this.canChangeActiveScanUnit) {
          return
        }
        if (!this.isActiveScanUnitSelectsChanged || !this.isActiveScanUnitAllSelectsValid) {
          return
        }
        this.$Modal.confirm({
          title: '修改选做',
          content: `以下选做题所选题目已修改，是否确定保存更改？<br>${getScanUnitChangedSelectsTextContent(
            this.activeScanUnitSelects
          )}`,
          onOk: () => {
            apiChangeSelects({
              examSubjectId: this.examSubjectId,
              scanUnitId: this.activeScanUnitId,
              selects: this.activeScanUnit.selects.map(block => ({
                blockId: block.blockId,
                answer: block.newAnswer,
              })),
            })
              .then(() => {
                this.$Message.success({
                  content: '已修改',
                })
              })
              .finally(() => {
                this.loadRoomStudents()
              })
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-student-container {
    @include flex(row, flex-start, flex-start);
    overflow-y: hidden;
  }

  .frame-left {
    flex-grow: 1;
    flex-shrink: 1;
    height: 100%;

    .header {
      @include flex(row, flex-start, center);

      .description {
        margin-right: auto;

        .divider {
          margin-right: 10px;
          margin-left: 10px;
        }
      }

      .dropdown-label {
        color: $color-warning;
        cursor: pointer;
      }

      .btn-batch-paper,
      .btn-raw-paper {
        display: inline-block;
        padding-bottom: 2px;
        border-bottom: 1px solid;
        line-height: 1;
        cursor: pointer;
      }
    }
  }

  .frame-right {
    @include flex(column, flex-start, stretch);
    flex-grow: 0;
    flex-shrink: 0;
    width: 400px;
    height: 100%;
    border-left: 1px solid $color-border;
  }

  .section-room {
    flex: 0 0 auto;
    padding: 16px;
    padding-bottom: 6px;
    border-bottom: 1px dashed $color-border;

    :deep(.ivu-form-item) {
      margin-bottom: 0;
    }

    .form-item-select {
      @include flex(row, space-between, center);

      .btn-refresh {
        margin-left: 20px;
      }
    }

    .form-item-stats {
      .stats-item {
        display: inline-block;
        margin-right: 5px;

        &.warning {
          color: $color-warning;
        }

        &.error {
          color: $color-error;
        }
      }
    }
  }

  .section-list {
    position: relative;
    flex: 1 1 0;
    padding: 16px;
    overflow-y: auto;

    .student-item {
      margin-bottom: 10px;

      .student-info {
        cursor: pointer;

        .student-name {
          display: inline-block;
          min-width: 3em;
          margin-left: 5px;
        }

        .seat-num {
          display: inline-block;
          min-width: 2em;
          margin-left: 5px;
        }

        .status {
          margin-left: 5px;

          &.warning {
            color: $color-warning;
          }

          &.error {
            color: $color-error;
          }
        }
      }

      &.current .student-info {
        background-color: $color-iview-table-active-row;
      }

      .scan-unit-item {
        margin-left: 20px;
        font-size: $font-size-small;
        cursor: pointer;

        &.active {
          color: $color-primary;
        }
      }
    }
  }
</style>
