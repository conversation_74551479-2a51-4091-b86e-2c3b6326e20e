<template>
  <div class="coach-book-ingestion-header">
    <div class="back">
      <Icon class="icon-back" title="返回" type="ios-arrow-back" @click="back"></Icon>
    </div>
    <div class="title-bar">
      <div class="title">{{ title }}</div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  const emit = defineEmits(['back'])

  const ingestionStore = useCoachBookIngestionStore()

  const title = computed(() => {
    let bookName = ingestionStore.coachBookInfo?.bookName
    if (bookName) {
      return `教辅录入——${bookName}`
    } else {
      return '教辅录入'
    }
  })

  function back() {
    emit('back')
  }
</script>

<style lang="scss" scoped>
  .coach-book-ingestion-header {
    @include flex(row, flex-start, center);
    position: relative;
    min-height: 36px;
    padding: 0 16px;
    color: white;
    line-height: 1;
    background-color: $color-primary-dark;

    .back {
      flex-shrink: 0;
      margin-right: 10px;
      font-size: $font-size-large;
      cursor: pointer;
    }

    .title-bar {
      flex-shrink: 1;
      font-weight: bold;
      font-size: $font-size-medium-x;
    }
  }
</style>
