<template>
  <div class="container-form-base-info">
    <div class="header">
      <span>基本信息</span>
      <div v-if="isDetail" class="box-btn-edit">
        <Button type="primary" ghost @click="onEdit">{{ canEdit ? '取消编辑' : '编辑' }}</Button>
      </div>
    </div>
    <div class="body">
      <el-form
        ref="formBaseInfo"
        :model="newFormOptions"
        :rules="formRules"
        :disabled="isFormDisabled"
        label-width="200px"
      >
        <el-form-item label="活动名称" required prop="name">
          <el-input
            v-model="newFormOptions.name"
            :maxlength="40"
            placeholder="请输入评审活动名称，例如XX评选"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="活动简介">
          <el-input
            v-model="newFormOptions.description"
            type="textarea"
            :autosize="{ minRows: 3 }"
            placeholder="请输入评审活动的简单说明"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="参赛者" required prop="participantIdentity">
          <el-radio-group v-model="newFormOptions.participantIdentity" :disabled="isDetail">
            <el-radio value="student">学生</el-radio>
            <el-radio value="teacher">教师</el-radio>
            <!-- <el-radio value="other">其他</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名者" required prop="registrantIdentity">
          <el-radio-group v-model="newFormOptions.registrantIdentity" :disabled="isDetail">
            <el-radio value="student" disabled>学生</el-radio>
            <el-radio value="teacher">教师</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名开始时间" required prop="registerStartTime">
          <el-col :span="6">
            <!-- <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="newFormOptions.registerStartTime"
              style="width: 100%"
            ></el-date-picker> -->
            <DatePicker
              v-model="newFormOptions.registerStartTime"
              :disabled="isFormDisabled"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="报名截止时间" required prop="registerEndTime">
          <el-col :span="6">
            <!-- <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="newFormOptions.registerEndTime"
              style="width: 100%"
            ></el-date-picker> -->
            <DatePicker
              v-model="newFormOptions.registerEndTime"
              :disabled="isFormDisabled"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="提交截止时间" required prop="submitEndTime">
          <el-col :span="6">
            <!-- <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="newFormOptions.submitEndTime"
              style="width: 100%"
            ></el-date-picker> -->
            <DatePicker
              v-model="newFormOptions.submitEndTime"
              :disabled="isFormDisabled"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="评审类别">
          <div v-if="newFormOptions?.categories?.length" class="box-category">
            <div v-for="(item, idx) in newFormOptions.categories" :key="idx" class="category-item">
              <div v-if="item.editing" class="category-item-content-editing">
                <el-input
                  :ref="'input_' + idx"
                  v-model="item.name"
                  :maxlength="10"
                  style="width: 150px"
                  clearable
                  @blur="onInputCategoryChange(item)"
                  @keydown="onInputCategoryKeydown($event, item)"
                ></el-input>
              </div>
              <div
                v-else
                :class="['category-item-content-finished', isFormDisabled ? 'category-item-content-readonly' : '']"
                @click="onCategoryEdit(item, idx)"
              >
                <span>{{ item.name }}</span>
                <span v-if="!isFormDisabled" class="btn-del" @click="onCategoryDelete($event, item)">
                  <Icon type="md-close" size="16px" />
                </span>
              </div>
            </div>
          </div>
          <div v-if="!isFormDisabled" class="box-btn-add-category">
            <Button @click="onCategoryAdd">添加类别</Button>
          </div>
        </el-form-item>
        <el-form-item v-if="newFormOptions?.categories?.length > 1" label="允许参赛者报名多个评选类别">
          <el-switch v-model="newFormOptions.allowMultipleCategories"></el-switch>
        </el-form-item>
        <el-form-item label="参评学校范围" required prop="scope">
          <el-radio-group v-model="newFormOptions.scope" :disabled="!isBureauInstitution">
            <el-radio :value="1">本校</el-radio>
            <el-radio :value="2">下属学校</el-radio>
            <!-- <el-radio value="other">其他</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="报名后需审核">
          <el-switch v-model="newFormOptions.needAudit"></el-switch>
        </el-form-item>
        <el-form-item v-if="newFormOptions.needAudit" label="允许本校审核报名">
          <el-switch v-model="newFormOptions.enableSchoolAudit"></el-switch>
        </el-form-item>
        <el-form-item label="发布">
          <el-switch v-model="newFormOptions.isPublic"></el-switch>
        </el-form-item>
        <el-form-item v-if="!isDetail">
          <Button type="primary" @click="onNext">下一步</Button>
        </el-form-item>
        <el-form-item v-if="canEdit">
          <Button type="primary" @click="onSave">保存</Button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
  import { nextTick } from 'vue'
  import {
    ElForm,
    ElFormItem,
    ElInput,
    ElRadioGroup,
    ElRadio,
    ElSwitch,
    ElDatePicker,
    ElButton,
    ElCol,
    ElIcon,
  } from 'element-plus'
  import 'element-plus/es/components/form/style/css'
  import 'element-plus/es/components/form-item/style/css'
  import 'element-plus/es/components/input/style/css'
  import 'element-plus/es/components/radio-group/style/css'
  import 'element-plus/es/components/radio/style/css'
  import 'element-plus/es/components/switch/style/css'
  import 'element-plus/es/components/date-picker/style/css'
  import 'element-plus/es/components/button/style/css'
  import 'element-plus/es/components/col/style/css'
  import 'element-plus/es/components/icon/style/css'
  import { formatDate } from '@/utils/date'
  import { deepCopy } from '@/utils/object'
  import {
    apiUpdateActivityBaseInfo,
    apiUpdateActivityCategory,
    apiAddActivityCategory,
    apiDeleteActivityCategory,
  } from '@/api/review/activity'

  export default {
    components: {
      'el-form': ElForm,
      'el-form-item': ElFormItem,
      'el-input': ElInput,
      'el-radio-group': ElRadioGroup,
      'el-radio': ElRadio,
      'el-switch': ElSwitch,
      'el-date-picker': ElDatePicker,
      'el-button': ElButton,
      'el-col': ElCol,
      'el-icon': ElIcon,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      formOptions: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['on-next', 'on-category-add', 'on-category-change', 'on-category-del', 'on-category-edit', 'on-refresh'],
    data() {
      return {
        newFormOptions: {},
        formRules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
            { min: 1, max: 40, message: '长度在 1 到 40 个字符', trigger: 'blur' },
          ],
          registerStartTime: [{ required: true, message: '请选择报名开始时间', trigger: 'blur' }],
          registerEndTime: [{ required: true, message: '请选择报名截止时间', trigger: 'blur' }],
          submitEndTime: [{ required: true, message: '请选择提交截止时间', trigger: 'blur' }],
        },
        canEdit: false,
      }
    },
    computed: {
      isFormDisabled() {
        return this.isDetail && !this.canEdit
      },
      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },
    },
    watch: {
      formOptions: {
        handler: function (newVal) {
          this.newFormOptions = this.isDetail ? deepCopy(newVal) : newVal
        },
        immediate: true,
        deep: true,
      },
    },
    beforeUnmount() {
      this.$store.dispatch('review/setBaseFormInfo', this.newFormOptions)
    },
    methods: {
      onNext() {
        this.$refs.formBaseInfo.validate(valid => {
          if (valid) {
            let result = this.checkFormInfo(this.newFormOptions)

            if (result) {
              this.$Message.info(result)
              return
            }
            this.$store.dispatch('review/setBaseFormInfo', this.newFormOptions)

            this.$emit('on-next')
          } else {
            return false
          }
        })
      },
      onEdit() {
        this.canEdit = !this.canEdit
        this.newFormOptions = deepCopy(this.formOptions)
      },
      onSave() {
        const { newFormOptions } = this
        let result = this.checkFormInfo(newFormOptions)

        if (result) {
          this.$Message.info(result)
          return
        }

        apiUpdateActivityBaseInfo({
          allowMultipleCategories: newFormOptions.allowMultipleCategories,
          description: newFormOptions.description,
          id: newFormOptions.id,
          isPublic: newFormOptions.isPublic,
          name: newFormOptions.name,
          needAudit: newFormOptions.needAudit,
          enableSchoolAudit: newFormOptions.enableSchoolAudit,
          scope: newFormOptions.scope,
          registerStartTime: `${formatDate(new Date(newFormOptions?.registerStartTime))} 00:00:00`,
          registerEndTime: `${formatDate(new Date(newFormOptions?.registerEndTime))} 23:59:59`,
          submitEndTime: `${formatDate(new Date(newFormOptions?.submitEndTime))} 23:59:59`,
        })
          .then(() => {
            this.$Message.success('已保存')
          })
          .finally(() => {
            this.canEdit = false
            this.$emit('on-refresh')
          })
      },
      checkFormInfo(baseInfo = {}) {
        if (!baseInfo.name) {
          return '请填写活动名称'
        }
        if (!baseInfo.registerStartTime) {
          return '请选择报名开始时间'
        }
        if (!baseInfo.registerEndTime) {
          return '请选择报名截止时间'
        }
        if (!baseInfo.submitEndTime) {
          return '请选择提交截止时间'
        }

        let registerStartTimestamp = new Date(baseInfo.registerStartTime).getTime()
        let registerEndtTimestamp = new Date(baseInfo.registerEndTime).getTime()
        let submitEndTimestamp = new Date(baseInfo.submitEndTime).getTime()
        const targetDiffTimestamp = 24 * 60 * 60 * 1000

        if (registerEndtTimestamp - registerStartTimestamp < targetDiffTimestamp) {
          return '报名截止时间需晚于报名开始时间'
        }
        if (submitEndTimestamp - registerStartTimestamp < targetDiffTimestamp) {
          return '提交截止时间需晚于报名开始时间'
        }

        if (!baseInfo?.categories?.length) {
          return '请至少添加一个评审类别'
        }
      },
      onCategoryAdd() {
        this.$emit('on-category-add')
        nextTick(() => {
          let idx = this.newFormOptions?.categories?.length - 1
          this.$refs['input_' + idx][0].focus()
        })
      },
      checkCategory() {
        let categoryNames = this.newFormOptions?.categories.map(c => c.name)
        if (new Set(categoryNames).size !== categoryNames.length) {
          return '评审类别名称不能重复'
        }
      },
      onInputCategoryChange(item) {
        let msg = this.checkCategory()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        if (this.isDetail) {
          if (item.id) {
            apiUpdateActivityCategory(item)
              .then(() => {
                this.$Message.success('已保存类别')
                this.$emit('on-category-change', item)
              })
              .finally(() => {
                this.$emit('on-refresh')
              })
          } else {
            apiAddActivityCategory(item)
              .then(() => {
                this.$Message.success('已添加类别')
                this.$emit('on-category-change', item)
              })
              .finally(() => {
                this.$emit('on-refresh')
              })
          }
        } else {
          this.$emit('on-category-change', item)
        }
      },
      onInputCategoryKeydown(event, item) {
        if (event.keyCode === 13) {
          let msg = this.checkCategory()
          if (msg) {
            this.$Message.info(msg)
            return
          }

          if (this.isDetail) {
            if (item.id) {
              apiUpdateActivityCategory(item)
                .then(() => {
                  this.$Message.success('已保存类别')
                  this.$emit('on-category-change', item)
                })
                .finally(() => {
                  this.$emit('on-refresh')
                })
            } else {
              apiAddActivityCategory(item)
                .then(() => {
                  this.$Message.success('已添加类别')
                  this.$emit('on-category-change', item)
                })
                .finally(() => {
                  this.$emit('on-refresh')
                })
            }
          } else {
            this.$emit('on-category-change', item)
          }
        }
      },
      onCategoryDelete(event, item) {
        event.stopPropagation()
        if (this.isDetail) {
          this.$Modal.confirm({
            title: '删除评审类别',
            content: `确定删除 ${item.name} 评审类别吗？`,
            onOk: () => {
              apiDeleteActivityCategory(item.id)
                .then(() => {
                  this.$Message.success('已删除')
                  this.$emit('on-category-del', item)
                })
                .finally(() => {
                  this.$emit('on-refresh')
                })
            },
          })
        } else {
          this.$emit('on-category-del', item)
        }
      },
      onCategoryEdit(item, idx) {
        if (this.isFormDisabled) return
        this.$emit('on-category-edit', item)
        nextTick(() => {
          this.$refs['input_' + idx][0].focus()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-form-base-info {
    .header {
      @include flex(row, space-between, center);
      padding: 10px 0;
    }

    .box-category {
      @include flex(row, flex-start, center);
      flex-wrap: wrap;
      margin-bottom: 10px;

      .category-item {
        height: 32px;
        margin-right: 10px;
      }

      .category-item-content-finished {
        @include flex(row, flex-start, center);
        box-sizing: border-box;
        padding-right: 4px;
        padding-left: 10px;
        background-color: #f2f2f2;

        .btn-del {
          @include flex(row, center, center);
          margin-left: 10px;
          cursor: pointer;
        }
      }

      .category-item-content-readonly {
        padding-right: 10px;
      }
    }

    .box-btn-add-category {
      width: 100%;
    }
  }
</style>
