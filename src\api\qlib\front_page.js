import ajax from '@/api/ajax'

export function apiGetFrontPageBanners({ stageId, subjectId }) {
  return ajax
    .get({
      url: 'ques/frontPage/imageList',
      params: {
        gradeLevel: stageId,
        subjectId,
      },
      requestName: '获取题库首页顶部banner',
    })
    .then(data => {
      return data
    })
}

export function apiGetFrontPagePaperList({ stageId, subjectId, collectionName }) {
  return ajax
    .get({
      url: 'ques/frontPage/paperList',
      params: {
        gradeLevel: stageId,
        subjectId,
        collectionName,
      },
      requestName: '获取题库首页试卷列表',
    })
    .then(data => {
      return data
    })
}

export function apiUploadImage({ stageId, subjectId, file }) {
  return ajax.upload({
    url: 'ques/frontPage/uploadImage',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: {
      file,
    },
    requestName: '上传图片',
  })
}

export function apiSaveImages({ stageId, subjectId, images }) {
  return ajax.put({
    url: 'ques/frontPage/saveImages',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: images,
    requestName: '保存图片',
  })
}

export function apiSavePapers({ stageId, subjectId, collectionName, paperIds, collectionSortCode }) {
  return ajax.put({
    url: 'ques/frontPage/savePapers',
    params: {
      gradeLevel: stageId,
      subjectId,
      collectionName,
      collectionSortCode,
    },
    data: paperIds,
    requestName: '保存合集试卷',
  })
}

export function apiChangeCollectionOrder({ stageId, subjectId, collectionNames }) {
  return ajax.put({
    url: 'ques/frontPage/changePaperCollectionOrder',
    params: {
      gradeLevel: stageId,
      subjectId,
    },
    data: collectionNames,
    requestName: '调整合集顺序',
  })
}

export function apiDeleteCollection({ stageId, subjectId, collectionName }) {
  return ajax.delete({
    url: 'ques/frontPage/deletePaperCollection',
    params: {
      gradeLevel: stageId,
      subjectId,
      collectionName,
    },
    requestName: '删除合集',
  })
}
