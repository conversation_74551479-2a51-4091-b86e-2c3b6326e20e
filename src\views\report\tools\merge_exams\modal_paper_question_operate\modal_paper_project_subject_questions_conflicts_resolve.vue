<script>
  import ComRadioGroup from '@/components/radio_group'

  import { randomId } from '@/utils/string'
  import { deepCopy } from '@/utils/object'
  import { groupArray } from '@/utils/array'

  import QuestionMergeResolutions from '@/enum/emarking/merge_exam/question_merge_resolutions.js'
  import SubjectiveSplitMethods from '@/enum/emarking/merge_exam/subjective_score_split_methods.js'
  import SubjectScoreCopyTypes from '@/enum/emarking/merge_exam/subject_score_copy_types'

  export default {
    components: {
      'com-radio-group': ComRadioGroup,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      examId: {
        type: String,
        default: '',
      },
      standardObjectives: {
        type: Array,
        default: () => [],
      },
      standardSubjectives: {
        type: Array,
        default: () => [],
      },
      matchStructs: {
        type: Array,
        default: () => [],
      },
      copyType: {
        type: String,
        default: SubjectScoreCopyTypes.QuestionScoreOnly.id,
      },
    },

    emits: ['update:modelValue', 'update-subject-resolves'],

    data() {
      return {
        isEditingResolve: false,

        modalMatchStructs: {},

        selectedProjectId: null, // 选中的考试项目ID

        objSourceOperQuestions: [],
        objStandardOperQuestions: [],
        subjSourceOperQuestions: [],
        subjStandardOperQuestions: [],
      }
    },

    computed: {
      questionMergeResolutions() {
        const QuestionMergeResolutionsEntries = QuestionMergeResolutions.getEntries()
        return this.copyType === SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id
          ? QuestionMergeResolutionsEntries
          : QuestionMergeResolutionsEntries.filter(item => item.key !== 'AsOther')
      },
      subjectiveSplitMethods() {
        return SubjectiveSplitMethods.getEntries()
      },

      selectedProjectStruct() {
        return (this.selectedProjectId && this.modalMatchStructs.find(s => s.id === this.selectedProjectId)) || null
      },
      selectedProjectObjectives() {
        return this.selectedProjectStruct?.subjectConfig?.objectives || []
      },
      selectedProjectSubjectives() {
        return this.selectedProjectStruct?.subjectConfig?.subjectives || []
      },
      selectedProjectStructNoConflict() {
        return (
          this.objSourceOperQuestions &&
          !this.objSourceOperQuestions.length &&
          this.objStandardOperQuestions &&
          !this.objStandardOperQuestions.length &&
          this.subjSourceOperQuestions &&
          !this.subjSourceOperQuestions.length &&
          this.subjStandardOperQuestions &&
          !this.subjStandardOperQuestions.length
        )
      },

      showBtnMapSimilarObjectiveQuestions() {
        return (
          this.objSourceOperQuestions.length === this.selectedProjectObjectives.length &&
          this.objStandardOperQuestions.length === this.standardObjectives.length
        )
      },
      showBtnMapSimilarSubjectiveQuestions() {
        return (
          this.subjSourceOperQuestions.length === this.selectedProjectSubjectives.length &&
          this.subjStandardOperQuestions.length === this.standardSubjectives.length
        )
      },

      selectedSubjSourceOperQuestions() {
        return this.subjSourceOperQuestions.filter(q => q.isSelected)
      },
      selectedSubjStandardOperQuestions() {
        return this.subjStandardOperQuestions.filter(q => q.isSelected)
      },
    },

    watch: {
      selectedProjectId() {
        this.buildOperQuestions()
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.isEditingResolve = false
          this.selectedProjectId = this.examId
          this.modalMatchStructs = deepCopy(this.matchStructs)
          this.buildOperQuestions()
        } else {
          this.onCloseModal()
        }
      },

      buildOperQuestions() {
        this.buildObjectiveOperQuestions()
        this.buildSubjectiveOperQuestions()
      },
      buildObjectiveOperQuestions() {
        const TargetMapperObjectives = this.selectedProjectStruct?.mapper?.objectives || []

        this.objSourceOperQuestions = deepCopy(this.selectedProjectObjectives).filter(
          q => !TargetMapperObjectives.some(mo => mo.srcQuestionCode === q.questionCode)
        )

        this.objStandardOperQuestions = deepCopy(this.standardObjectives).filter(
          q => !TargetMapperObjectives.some(mo => mo.dstQuestionCode === q.questionCode)
        )
      },
      buildSubjectiveOperQuestions() {
        const TargetMapperSubjectives = this.selectedProjectStruct?.mapper?.subjectives || []

        this.subjSourceOperQuestions = this.selectedProjectSubjectives
          .filter(
            q =>
              !TargetMapperSubjectives.some(ms =>
                ms.srcQuestions.some(mssq => mssq.questionCode === q.questionCode && mssq.branchCode === q.branchCode)
              )
          )
          .map(q => ({
            ...q,
            code: q.branchCode ? q.questionCode + '.' + q.branchCode : q.questionCode,
          }))

        this.subjStandardOperQuestions = this.standardSubjectives
          .filter(
            q =>
              !TargetMapperSubjectives.some(ms =>
                ms.dstQuestions.some(msdq => msdq.questionCode === q.questionCode && msdq.branchCode === q.branchCode)
              )
          )
          .map(q => ({
            ...q,
            code: q.branchCode ? q.questionCode + '.' + q.branchCode : q.questionCode,
          }))
      },

      selectObjSouceOperQuestion(questionCode) {
        const TargetMapperObjectives = this.selectedProjectStruct?.mapper?.objectives || []
        if (this.isEditingResolve && TargetMapperObjectives.some(item => item.isEditing)) {
          const TargetQuestion = this.objSourceOperQuestions.find(q => q.questionCode === questionCode)
          if (TargetQuestion) {
            TargetQuestion.isSelected = !TargetQuestion.isSelected
          }
        }
      },
      selectSubjSouceOperQuestion(code) {
        const TargetMapperSubjectives = this.selectedProjectStruct?.mapper?.subjectives || []
        if (this.isEditingResolve && TargetMapperSubjectives.some(item => item.isEditing)) {
          const TargetQuestion = this.subjSourceOperQuestions.find(q => q.code === code)
          if (TargetQuestion) {
            TargetQuestion.isSelected = !TargetQuestion.isSelected
          }
        }
      },

      selectObjStandardOperQuestion(questionCode) {
        const TargetMapperObjectives = this.selectedProjectStruct?.mapper?.objectives || []
        if (this.isEditingResolve && TargetMapperObjectives.some(item => item.isEditing)) {
          const TargetQuestion = this.objStandardOperQuestions.find(q => q.questionCode === questionCode)
          if (TargetQuestion) {
            TargetQuestion.isSelected = !TargetQuestion.isSelected
          }
        }
      },
      selectSubjStandardOperQuestion(code) {
        const TargetMapperSubjectives = this.selectedProjectStruct?.mapper?.subjectives || []
        if (this.isEditingResolve && TargetMapperSubjectives.some(item => item.isEditing)) {
          const TargetQuestion = this.subjStandardOperQuestions.find(q => q.code === code)
          if (TargetQuestion) {
            TargetQuestion.isSelected = !TargetQuestion.isSelected
          }
        }
      },

      mapSimilarObjectiveQuestions() {
        const TargetMapper = this.selectedProjectStruct?.mapper
        if (TargetMapper) {
          TargetMapper.objectives = []
          this.objSourceOperQuestions.forEach(sq => {
            const TargetQuestion = this.objStandardOperQuestions.find(dq => dq.questionCode === sq.questionCode)
            if (TargetQuestion) {
              TargetMapper.objectives.push({
                id: randomId(),
                isEditing: false,
                action: QuestionMergeResolutions.Map.id,
                srcQuestionCode: sq.questionCode,
                dstQuestionCode: TargetQuestion.questionCode,
              })
            }
          })
          this.buildObjectiveOperQuestions()
        }
      },
      mapSimilarSubjectiveQuestions() {
        const TargetMapper = this.selectedProjectStruct?.mapper
        if (TargetMapper) {
          TargetMapper.subjectives = []

          const SrcQuestionGroup = groupArray(this.subjSourceOperQuestions, x => x.questionCode)
          const DstQuestionGroup = groupArray(this.subjStandardOperQuestions, x => x.questionCode)
          SrcQuestionGroup.forEach(sg => {
            const TargetDstGroup = DstQuestionGroup.find(dg => dg.key === sg.key)
            if (TargetDstGroup) {
              if (sg.group.length === TargetDstGroup.group.length) {
                sg.group.forEach(sq => {
                  const TargetQuestion = TargetDstGroup.group.find(dq => dq.code === sq.code)
                  if (TargetQuestion) {
                    TargetMapper.subjectives.push({
                      id: randomId(),
                      isEditing: false,
                      action: QuestionMergeResolutions.Map.id,
                      splitScoreMethod: SubjectiveSplitMethods.Avg.id,
                      srcQuestions: [
                        {
                          questionCode: sq.questionCode,
                          branchCode: sq.branchCode,
                        },
                      ],
                      dstQuestions: [
                        {
                          questionCode: TargetQuestion.questionCode,
                          branchCode: TargetQuestion.branchCode,
                        },
                      ],
                    })
                  }
                })
              } else if (!(sg.group.length > 1 && TargetDstGroup.group.length > 1)) {
                TargetMapper.subjectives.push({
                  id: randomId(),
                  isEditing: false,
                  action: QuestionMergeResolutions.Map.id,
                  splitScoreMethod: SubjectiveSplitMethods.Avg.id,
                  srcQuestions: sg.group.map(q => ({
                    questionCode: q.questionCode,
                    branchCode: q.branchCode,
                  })),
                  dstQuestions: TargetDstGroup.group.map(q => ({
                    questionCode: q.questionCode,
                    branchCode: q.branchCode,
                  })),
                })
              }
            }
          })

          this.buildSubjectiveOperQuestions()
        }
      },

      saveResolveObjectives(resolveId) {
        const SelectedObjSourceOperQuestions = this.objSourceOperQuestions.filter(q => q.isSelected)
        const SelectedObjStandardOperQuestions = this.objStandardOperQuestions.filter(q => q.isSelected)

        if (!SelectedObjSourceOperQuestions.length) {
          this.$Message.warning({
            duration: 3,
            content: '请至少选择一个源项目小题',
          })
          return
        }

        const TargetMapperObjectives = this.selectedProjectStruct?.mapper?.objectives
        const TargetResolve = (TargetMapperObjectives || []).find(r => r.id === resolveId)
        const QuestionMergeResolutionIsMap = TargetResolve.action === QuestionMergeResolutions.Map.id

        if (
          QuestionMergeResolutionIsMap &&
          SelectedObjSourceOperQuestions.length !== SelectedObjStandardOperQuestions.length
        ) {
          this.$Message.warning({
            duration: 3,
            content: '已选源项目小题与已选合并项目小题数目必须相等',
          })
          return
        }

        TargetResolve.srcQuestionCode = SelectedObjSourceOperQuestions[0].questionCode
        TargetResolve.dstQuestionCode = QuestionMergeResolutionIsMap
          ? SelectedObjStandardOperQuestions[0].questionCode
          : 0
        TargetResolve.isEditing = false

        if (SelectedObjSourceOperQuestions.length > 1) {
          SelectedObjSourceOperQuestions.forEach((sq, sdx) => {
            if (sdx) {
              TargetMapperObjectives.push({
                isEditing: false,
                id: randomId(),
                action: TargetResolve.action,
                srcQuestionCode: sq.questionCode,
                dstQuestionCode: QuestionMergeResolutionIsMap ? SelectedObjStandardOperQuestions[sdx].questionCode : 0,
              })
            }
          })
        }

        this.buildObjectiveOperQuestions()

        if (
          this.objSourceOperQuestions &&
          this.objSourceOperQuestions.length &&
          this.objStandardOperQuestions &&
          this.objStandardOperQuestions.length
        ) {
          this.addObjectiveResolve()
        } else {
          this.isEditingResolve = false
        }
      },
      saveResolveSubjectives(resolveId) {
        const TargetMapperSubjectives = this.selectedProjectStruct?.mapper?.subjectives
        const TargetResolve = (TargetMapperSubjectives || []).find(r => r.id === resolveId)
        if (TargetResolve.action === QuestionMergeResolutions.Map.id) {
          if (this.selectedSubjSourceOperQuestions.length < 1 || this.selectedSubjStandardOperQuestions.length < 1) {
            this.$Message.warning({
              duration: 3,
              content: '请至少选择一个源项目小题和一个合并项目小题',
            })
            return
          } else if (
            this.selectedSubjSourceOperQuestions.length > 1 &&
            this.selectedSubjStandardOperQuestions.length > 1 &&
            this.selectedSubjSourceOperQuestions.length !== this.selectedSubjStandardOperQuestions.length
          ) {
            this.$Message.warning({
              duration: 3,
              content: '所选源项目小题和合并项目小题不可以同时选择多个且数量不匹配',
            })
            return
          }

          if (this.selectedSubjSourceOperQuestions.length > 1 && this.selectedSubjStandardOperQuestions.length > 1) {
            this.selectedSubjSourceOperQuestions.forEach((sq, sdx) => {
              if (sdx) {
                TargetMapperSubjectives.push({
                  isEditing: false,
                  id: randomId(),
                  srcQuestions: [
                    {
                      questionCode: sq.questionCode,
                      branchCode: sq.branchCode,
                    },
                  ],
                  dstQuestions: [
                    {
                      questionCode: this.selectedSubjStandardOperQuestions[sdx].questionCode,
                      branchCode: this.selectedSubjStandardOperQuestions[sdx].branchCode,
                    },
                  ],
                  action: TargetResolve.action,
                  splitScoreMethod: SubjectiveSplitMethods.Avg.id,
                })
              } else {
                TargetResolve.srcQuestions = [
                  {
                    questionCode: sq.questionCode,
                    branchCode: sq.branchCode,
                  },
                ]
                TargetResolve.dstQuestions = [
                  {
                    questionCode: this.selectedSubjStandardOperQuestions[sdx].questionCode,
                    branchCode: this.selectedSubjStandardOperQuestions[sdx].branchCode,
                  },
                ]
                TargetResolve.splitScoreMethod = SubjectiveSplitMethods.Avg.id
                TargetResolve.isEditing = false
              }
            })
          } else {
            TargetResolve.srcQuestions = this.selectedSubjSourceOperQuestions.map(sq => ({
              questionCode: sq.questionCode,
              branchCode: sq.branchCode,
            }))
            TargetResolve.dstQuestions = this.selectedSubjStandardOperQuestions.map(dq => ({
              questionCode: dq.questionCode,
              branchCode: dq.branchCode,
            }))
            TargetResolve.isEditing = false
          }
        } else {
          if (this.selectedSubjSourceOperQuestions.length < 1) {
            this.$Message.warning({
              duration: 3,
              content: '请至少选择一个源项目小题',
            })
            return
          }

          TargetResolve.srcQuestions = this.selectedSubjSourceOperQuestions.map(sq => ({
            questionCode: sq.questionCode,
            branchCode: sq.branchCode,
          }))
          TargetResolve.dstQuestions = []
          TargetResolve.splitScoreMethod = SubjectiveSplitMethods.Avg.id
          TargetResolve.isEditing = false
        }

        this.buildSubjectiveOperQuestions()

        if (
          this.subjSourceOperQuestions &&
          this.subjSourceOperQuestions.length &&
          this.subjStandardOperQuestions &&
          this.subjStandardOperQuestions.length
        ) {
          this.addSubjectiveResolve()
        } else {
          this.isEditingResolve = false
        }
      },

      deleteResolveObjectives(resolveId) {
        const TargetMapper = this.selectedProjectStruct?.mapper
        if (TargetMapper) {
          TargetMapper.objectives = TargetMapper.objectives.filter(r => r.id !== resolveId)
        }
        this.isEditingResolve = false
        this.buildObjectiveOperQuestions()
      },
      deleteResolveSubjectives(resolveId) {
        const TargetMapper = this.selectedProjectStruct?.mapper
        if (TargetMapper) {
          TargetMapper.subjectives = TargetMapper.subjectives.filter(r => r.id !== resolveId)
        }
        this.isEditingResolve = false
        this.buildSubjectiveOperQuestions()
      },

      addObjectiveResolve() {
        this.isEditingResolve = true
        const TargetMapperObjectives = this.selectedProjectStruct?.mapper?.objectives
        if (TargetMapperObjectives) {
          TargetMapperObjectives.push({
            isEditing: true,
            id: randomId(),
            action: QuestionMergeResolutions.Map.id,
            srcQuestionCode: 0,
            dstQuestionCode: 0,
          })
        }
      },
      addSubjectiveResolve() {
        this.isEditingResolve = true
        const TargetMapperSubjectives = this.selectedProjectStruct?.mapper?.subjectives
        if (TargetMapperSubjectives) {
          TargetMapperSubjectives.push({
            isEditing: true,
            id: randomId(),
            srcQuestions: [],
            dstQuestions: [],
            action: QuestionMergeResolutions.Map.id,
            splitScoreMethod: SubjectiveSplitMethods.Avg.id,
          })
        }
      },

      sourceObjectiveAllClick() {
        if (this.isEditingResolve && (this.selectedProjectStruct?.mapper?.objectives || []).some(r => r.isEditing)) {
          const FirstStatus = this.objSourceOperQuestions[0].isSelected
          this.objSourceOperQuestions.forEach(q => (q.isSelected = !FirstStatus))
        }
      },
      standardObjectiveAllClick() {
        if (this.isEditingResolve && (this.selectedProjectStruct?.mapper?.objectives || []).some(r => r.isEditing)) {
          const FirstStatus = this.objStandardOperQuestions[0].isSelected
          this.objStandardOperQuestions.forEach(q => (q.isSelected = !FirstStatus))
        }
      },
      sourceSubjectiveAllClick() {
        if (this.isEditingResolve && (this.selectedProjectStruct?.mapper?.subjectives || []).some(r => r.isEditing)) {
          const FirstStatus = this.subjSourceOperQuestions[0].isSelected
          this.subjSourceOperQuestions.forEach(q => (q.isSelected = !FirstStatus))
        }
      },
      standardSubjectiveAllClick() {
        if (this.isEditingResolve && (this.selectedProjectStruct?.mapper?.subjectives || []).some(r => r.isEditing)) {
          const FirstStatus = this.subjStandardOperQuestions[0].isSelected
          this.subjStandardOperQuestions.forEach(q => (q.isSelected = !FirstStatus))
        }
      },

      updateSubjectQuestionConflictsResolves() {
        this.modalMatchStructs.forEach(struct => {
          struct.mapper.subjectives.forEach(ms => {
            if (ms.action !== QuestionMergeResolutions.Map.id) {
              ms.splitScoreMethod = undefined
            }
          })
        })
        this.$emit('update-subject-resolves', this.modalMatchStructs)
        this.onCloseModal()
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" title="小题冲突处理" width="85" @on-visible-change="handleModalVisibleChange">
    <div class="modal-paper-project-subject-questions-conflicts-resolve">
      <Form :label-width="120" label-position="left" class="form">
        <FormItem label="考试项目">
          <Select v-model="selectedProjectId" transfer class="project-selector">
            <Option v-for="item of modalMatchStructs" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
          <Icon v-show="selectedProjectStructNoConflict" type="md-checkmark" class="project-struct-no-conflict-sign" />
        </FormItem>
        <FormItem label="客观题">
          <div class="resolve-part">
            <div class="label">
              源项目小题
              <Icon type="md-radio-button-on" title="全选" class="icon-select-all" @click="sourceObjectiveAllClick" />
            </div>
            <div class="panel">
              <template v-if="objSourceOperQuestions.length">
                <div
                  v-for="q of objSourceOperQuestions"
                  :key="q.id"
                  :class="{ 'selected-question-item': q.isSelected }"
                  class="question-item"
                  @click="selectObjSouceOperQuestion(q.questionCode)"
                >
                  {{ q.questionCode }}
                </div>
              </template>
              <div v-else class="explain-tips">无可处理的相关小题</div>
            </div>
          </div>
          <div class="resolve-part">
            <div class="label">
              合并项目小题
              <Icon type="md-radio-button-on" title="全选" class="icon-select-all" @click="standardObjectiveAllClick" />
            </div>
            <div class="panel">
              <template v-if="objStandardOperQuestions.length">
                <div
                  v-for="q of objStandardOperQuestions"
                  :key="q.id"
                  :class="{ 'selected-question-item': q.isSelected }"
                  class="question-item"
                  @click="selectObjStandardOperQuestion(q.questionCode)"
                >
                  {{ q.questionCode }}
                </div>
              </template>
              <div v-else class="explain-tips">无可处理的相关小题</div>
            </div>
          </div>
          <div class="resolve-part">
            <div class="label">
              冲突处理
              <Button
                v-if="showBtnMapSimilarObjectiveQuestions"
                ghost
                type="primary"
                size="small"
                style="margin-left: 0.4em"
                @click="mapSimilarObjectiveQuestions"
                >相同题号匹配</Button
              >
            </div>
            <div class="panel">
              <div
                v-if="
                  selectedProjectStruct &&
                  selectedProjectStruct.mapper &&
                  selectedProjectStruct.mapper.objectives &&
                  selectedProjectStruct.mapper.objectives.length
                "
                class="resolve-list"
              >
                <ul>
                  <li v-for="r of selectedProjectStruct.mapper.objectives" :key="r.id">
                    <div v-if="r.isEditing" class="resolve-editing-subjective">
                      <div class="explain-tips">
                        （请在上方选择要处理的源项目小题及要对应的合并项目小题）
                        <TextButton type="warning" @click="deleteResolveObjectives(r.id)">取消</TextButton>
                        <TextButton type="primary" @click="saveResolveObjectives(r.id)">确定</TextButton>
                      </div>
                      <div class="resolve-editing-subjective-item">
                        <div class="label">处理方式：</div>
                        <com-radio-group v-model="r.action" :radioes="questionMergeResolutions"></com-radio-group>
                      </div>
                    </div>
                    <div v-else class="resolve-subjective">
                      <div class="item-label">
                        {{ '[ ' + questionMergeResolutions.find(q => q.id === r.action).name + ' ]：' }}
                      </div>
                      <div>
                        {{ r.action === 'map' ? r.srcQuestionCode + ' => ' + r.dstQuestionCode : r.srcQuestionCode }}
                      </div>
                      <TextButton
                        v-if="!isEditingResolve"
                        type="error"
                        style="margin-left: 1em"
                        @click="deleteResolveObjectives(r.id)"
                        >删除</TextButton
                      >
                    </div>
                  </li>
                </ul>
              </div>
              <TextButton
                v-if="!isEditingResolve && objSourceOperQuestions && objSourceOperQuestions.length"
                underline
                type="content"
                icon="md-add"
                @click="addObjectiveResolve"
                >新增</TextButton
              >
            </div>
          </div>
        </FormItem>
        <FormItem label="主观题">
          <div class="resolve-part">
            <div class="label">
              源项目小题
              <Icon type="md-radio-button-on" title="全选" class="icon-select-all" @click="sourceSubjectiveAllClick" />
            </div>
            <div class="panel">
              <template v-if="subjSourceOperQuestions.length">
                <div
                  v-for="q of subjSourceOperQuestions"
                  :key="q.id"
                  :class="{ 'selected-question-item': q.isSelected }"
                  class="question-item"
                  @click="selectSubjSouceOperQuestion(q.code)"
                >
                  {{ q.code }}
                </div>
              </template>
              <div v-else class="explain-tips">无可处理的相关小题</div>
            </div>
          </div>
          <div class="resolve-part">
            <div class="label">
              合并项目小题
              <Icon
                type="md-radio-button-on"
                title="全选"
                class="icon-select-all"
                @click="standardSubjectiveAllClick"
              />
            </div>
            <div class="panel">
              <template v-if="subjStandardOperQuestions.length">
                <div
                  v-for="q of subjStandardOperQuestions"
                  :key="q.id"
                  :class="{ 'selected-question-item': q.isSelected }"
                  class="question-item"
                  @click="selectSubjStandardOperQuestion(q.code)"
                >
                  {{ q.code }}
                </div>
              </template>
              <div v-else class="explain-tips">无可处理的相关小题</div>
            </div>
          </div>
          <div class="resolve-part">
            <div class="label">
              冲突处理
              <Button
                v-if="showBtnMapSimilarSubjectiveQuestions"
                ghost
                type="primary"
                size="small"
                style="margin-left: 0.4em"
                @click="mapSimilarSubjectiveQuestions"
                >相同题号匹配</Button
              >
            </div>
            <div class="panel">
              <div
                v-if="
                  selectedProjectStruct &&
                  selectedProjectStruct.mapper &&
                  selectedProjectStruct.mapper.subjectives &&
                  selectedProjectStruct.mapper.subjectives.length
                "
                class="resolve-list"
              >
                <ul>
                  <li v-for="r of selectedProjectStruct.mapper.subjectives" :key="r.id">
                    <div v-if="r.isEditing" class="resolve-editing-subjective">
                      <div class="explain-tips">
                        （请在上方选择要处理的源项目小题及要对应的合并项目小题）
                        <TextButton type="warning" @click="deleteResolveSubjectives(r.id)">取消</TextButton>
                        <TextButton type="primary" @click="saveResolveSubjectives(r.id)">确定</TextButton>
                      </div>
                      <div class="resolve-editing-subjective-item">
                        <div class="label">处理方式：</div>
                        <com-radio-group v-model="r.action" :radioes="questionMergeResolutions"></com-radio-group>
                      </div>
                      <div
                        v-if="
                          r.action === 'map' &&
                          selectedSubjSourceOperQuestions.length === 1 &&
                          selectedSubjStandardOperQuestions.length > 1
                        "
                        class="resolve-editing-subjective-item"
                      >
                        <div class="label">分数拆分方式：</div>
                        <com-radio-group
                          v-model="r.splitScoreMethod"
                          :radioes="subjectiveSplitMethods"
                        ></com-radio-group>
                      </div>
                    </div>
                    <div v-else class="resolve-subjective">
                      <div class="item-label">
                        {{ '[ ' + questionMergeResolutions.find(q => q.id === r.action).name + ' ]：' }}
                      </div>
                      <div>
                        {{
                          r.action === 'map'
                            ? r.srcQuestions
                                .map(item =>
                                  item.branchCode ? item.questionCode + '.' + item.branchCode : item.questionCode
                                )
                                .join('、') +
                              ' => ' +
                              r.dstQuestions
                                .map(item =>
                                  item.branchCode ? item.questionCode + '.' + item.branchCode : item.questionCode
                                )
                                .join('、')
                            : r.srcQuestions
                                .map(item =>
                                  item.branchCode ? item.questionCode + '.' + item.branchCode : item.questionCode
                                )
                                .join('、')
                        }}
                        <span v-if="r.action === 'map' && r.srcQuestions.length === 1 && r.dstQuestions.length > 1"
                          >（ 拆分方式：{{
                            subjectiveSplitMethods.find(item => item.id === r.splitScoreMethod).name
                          }}
                          ）</span
                        >
                        <TextButton
                          v-if="!isEditingResolve"
                          type="error"
                          style="margin-left: 1em"
                          @click="deleteResolveSubjectives(r.id)"
                          >删除</TextButton
                        >
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <TextButton
                v-if="
                  !isEditingResolve &&
                  subjSourceOperQuestions &&
                  subjSourceOperQuestions.length &&
                  subjStandardOperQuestions &&
                  subjStandardOperQuestions.length
                "
                underline
                type="content"
                icon="md-add"
                @click="addSubjectiveResolve"
                >新增</TextButton
              >
            </div>
          </div>
        </FormItem>
      </Form>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary" @click="updateSubjectQuestionConflictsResolves">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-paper-project-subject-questions-conflicts-resolve {
    .form {
      .project-selector {
        width: 480px;
      }

      .project-struct-no-conflict-sign {
        margin-left: 1em;
        color: $color-primary;
      }

      .resolve-part {
        user-select: none;

        .label {
          color: $color-primary;
          font-weight: bold;

          .icon-select-all:hover {
            cursor: pointer;
          }
        }

        .panel {
          max-width: 86%;

          .question-item {
            display: inline-block;
            height: 24px;
            margin-right: 6px;
            margin-bottom: 2px;
            padding: 1px 4px;
            border: 1px solid $color-border;
            border-radius: 4px;
            line-height: 20px;

            &:hover {
              cursor: pointer;
            }
          }

          .selected-question-item {
            border-color: $color-primary;
            color: #fff;
            background-color: $color-primary;
          }

          .resolve-list {
            padding-left: 1em;

            .resolve-editing-subjective {
              .resolve-editing-subjective-item {
                @include flex(row, flex-start, center);

                .label {
                  margin-right: 1em;
                  color: $color-primary-dark;
                  font-weight: normal;
                }
              }
            }

            .resolve-subjective {
              @include flex(row, flex-start, center);

              .item-label {
                margin-right: 0.4em;
                color: $color-content;
                font-weight: bold;
              }
            }
          }

          .explain-tips {
            color: $color-second-title;
          }
        }
      }
    }
  }
</style>
