<template>
  <div class="page-review-create">
    <div class="section-btn-back">
      <Button icon="md-close" title="关闭" @click="onBack"></Button>
    </div>
    <div class="section-steps-bar">
      <Steps :current="currentStep">
        <Step
          v-for="(item, idx) in steps"
          :key="idx"
          :title="item.title"
          :content="item.content"
          :status="currentStep !== idx ? 'wait' : 'process'"
          @click="onStepClick(idx)"
        ></Step>
      </Steps>
    </div>
    <div class="section-content">
      <template v-if="currentStep === 0">
        <FormBaseInfo
          :form-options="formOptions"
          @on-next="onNext"
          @on-category-add="onCategoryAdd"
          @on-category-edit="onCategoryEdit"
          @on-category-change="onCategoryChange"
          @on-category-del="onCategoryDel"
        />
      </template>
      <template v-else-if="currentStep === 1">
        <FormSchoolPicker :school-tree="schoolTree" @on-next="onNext" @on-prev="onPrev" />
      </template>
      <template v-else-if="currentStep === 2">
        <FormCustom :categories="formOptions.categories" @on-prev="onPrev" @on-submit="onSubmit" />
      </template>
    </div>
  </div>
</template>

<script>
  import FormBaseInfo from '../components/form_base_info.vue'
  import FormSchoolPicker from '../components/form_school_picker.vue'
  import FormCustom from '../components/form_custom.vue'
  import { apiGetChildOrgAndSchool } from '@/api/user/school'
  import { apiCreateActivity } from '@/api/review/activity'
  import { formatDate } from '@/utils/date'
  import ActivityScope from '@/enum/review/activity_scope'

  const staticFormItemOptions = [
    {
      fieldLabel: '作品名称',
      fieldType: 'text',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 1,
      description: '',
      extraJson: '{"placeholder": "请输入作品名称", "minLength": 1, "maxLength": 100}',
    },
    {
      fieldLabel: '作品上传',
      fieldType: 'upload',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 2,
      description: '',
      isDefault: true,
      extraJson: '{"accepts": ["image","pdf"], "maxFileSize": 10485760, "maxFileCount": 2}',
    },
  ]

  export default {
    components: {
      FormBaseInfo,
      FormSchoolPicker,
      FormCustom,
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.$store.dispatch('review/clearStore')
      })
    },
    data() {
      return {
        steps: [
          {
            title: '基本信息',
            content: '活动名称、时间、类别',
          },
          {
            title: '选择参赛学校',
            content: '参赛学校',
          },
          {
            title: '提交内容设置',
            content: '设置报名表单、文件上传等',
          },
        ],
        currentStep: 0,
        formOptions: {
          name: '',
          description: '',
          participantIdentity: 'student',
          registrantIdentity: 'teacher',
          registerStartTime: '',
          registerEndTime: '',
          submitEndTime: '',
          categories: [],
          allowMultipleCategories: false,
          isPublic: true,
          needAudit: true,
          enableSchoolAudit: true,
          scope: '',
        },
        schoolTree: [],
      }
    },
    computed: {
      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },
      schoolId() {
        return this.$store.state.user.schoolId || ''
      },
    },
    created() {
      this.getSchoolTree()
      this.initParams()
    },
    methods: {
      initParams() {
        this.formOptions.scope = this.isBureauInstitution ? ActivityScope.Multiple.id : ActivityScope.Single.id
      },
      onBack() {
        this.$router.back()
      },
      onNext() {
        if (!this.formOptions?.categories?.length) {
          this.$Message.info('请至少设置一个评审类别')
          return
        }
        this.currentStep++
      },
      onPrev() {
        this.currentStep--
      },
      onStepClick(idx) {
        if (idx === 2 && !this.formOptions?.categories?.length) {
          this.$Message.info('请至少设置一个评审类别')
          this.currentStep = 0
          return
        }
        this.currentStep = idx
      },
      getSchoolTree() {
        apiGetChildOrgAndSchool({
          eduSchoolId: this.schoolId,
        })
          .then(res => {
            this.schoolTree = [
              {
                schoolName: this.$store.state.user.schoolName,
                schoolId: this.schoolId,
                schools: res || [],
                schoolType: this.$store.state.user.schoolType,
              },
            ]
          })
          .catch(() => {
            this.schoolTree = []
          })
      },
      onCategoryAdd() {
        let categories = this.formOptions?.categories || []

        if (!categories.some(item => item.editing)) {
          categories.push({
            name: '',
            id: '',
            editing: true,
            description: '',
            fields: staticFormItemOptions,
          })
        }
      },
      onCategoryEdit(data) {
        let categories = this.formOptions?.categories || []
        const theCategory = categories.find(item => item.name === data.name)

        if (theCategory) {
          theCategory.editing = true
        }
      },
      onCategoryChange(data) {
        let categories = this.formOptions?.categories || []
        const theCategoryIdx = categories.findIndex(item => item.name === data.name)

        if (theCategoryIdx >= 0) {
          if (!data.name) {
            categories.splice(theCategoryIdx, 1)
          } else {
            categories[theCategoryIdx].editing = false
          }
        }
      },
      onCategoryDel(data) {
        let categories = this.formOptions?.categories || []
        const theCategoryIdx = categories.findIndex(item => item.name === data.name)

        if (theCategoryIdx >= 0) {
          categories.splice(theCategoryIdx, 1)
        }
      },
      onSubmit() {
        let baseInfoCache = this.$store.getters['review/baseFormInfo']
        let schoolPickerCache = this.$store.getters['review/checkedSchools']
        let formCustomCache = this.$store.getters['review/allCategoryComponents']
        let categories = (baseInfoCache?.categories || []).map((item, idx) => {
          let theCategoryComponents = formCustomCache && formCustomCache[item.name]
          return {
            name: item.name,
            fields: (theCategoryComponents || []).map((c, cIdx) => {
              return {
                fieldLabel: c.fieldLabel,
                fieldType: c.fieldType,
                subType: c.subType,
                isRequired: c.isRequired,
                isReviewItem: c.isReviewItem,
                sortOrder: cIdx + 1,
                description: c.description,
                extraJson: c.extraJson,
              }
            }),
            sortCode: idx + 1,
          }
        })

        let result = this.checkFormInfo(baseInfoCache, schoolPickerCache)

        if (result) {
          this.$Message.info(result)
          return
        }

        let params = {
          ...baseInfoCache,
          registerStartTime: `${formatDate(baseInfoCache?.registerStartTime)} 00:00:00`,
          registerEndTime: `${formatDate(baseInfoCache?.registerEndTime)} 23:59:59`,
          submitEndTime: `${formatDate(baseInfoCache?.submitEndTime)} 23:59:59`,
          schoolIds: (schoolPickerCache || []).map(item => item.schoolId),
          categories,
        }

        apiCreateActivity(params).then(() => {
          this.$Message.success('创建成功')
          this.$router.back()
        })
      },
      checkFormInfo(baseInfo = {}, schools = []) {
        if (!baseInfo.name) {
          return '请填写活动名称'
        }
        if (!baseInfo.registerStartTime) {
          return '请选择报名开始时间'
        }
        if (!baseInfo.registerEndTime) {
          return '请选择报名截止时间'
        }
        if (!baseInfo.submitEndTime) {
          return '请选择提交截止时间'
        }

        let registerStartTimestamp = new Date(baseInfo.registerStartTime).getTime()
        let registerEndtTimestamp = new Date(baseInfo.registerEndTime).getTime()
        let submitEndTimestamp = new Date(baseInfo.submitEndTime).getTime()
        const targetDiffTimestamp = 24 * 60 * 60 * 1000

        if (registerEndtTimestamp - registerStartTimestamp < targetDiffTimestamp) {
          return '报名截止时间需晚于报名开始时间'
        }
        if (submitEndTimestamp - registerStartTimestamp < targetDiffTimestamp) {
          return '提交截止时间需晚于报名开始时间'
        }

        if (!baseInfo?.categories?.length) {
          return '请至少添加一个评审类别'
        }
        if (!schools.length) {
          return '请选择活动参赛学校'
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-review-create {
    --el-color-primary: #05c1ae;
    padding-top: 20px;
    background-color: #fff;

    :deep(.ivu-steps-item) {
      cursor: pointer;
    }

    .section-btn-back {
      @include flex(row, flex-end, start);
      padding: 0 15px 0 20px;
    }

    .section-steps-bar {
      padding: 10px 20px 20px;
    }

    .section-content {
      padding: 0 20px 20px;

      :deep(.header) {
        margin-bottom: 10px;
        font-size: 20px;
      }
    }
  }
</style>
