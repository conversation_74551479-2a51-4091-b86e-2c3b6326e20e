<script>
  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      paper: {
        type: Object,
        default: () => null,
      },
    },

    emits: ['update:modelValue'],

    computed: {
      modalTitle() {
        let title = '评分详情'
        if (this.paper?.No) {
          title += `（ 第 ${this.paper.No} 份 ）`
        }
        return title
      },
      paperAIResultList() {
        return (this.paper && this.paper.aiResultList) || []
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          // do something
        } else {
          this.onCloseModal()
        }
      },

      getQuestionFullScore(question) {
        let fullScore = 0

        if (question && this.paper?.questions?.length) {
          const TargetQuestion = this.paper.questions.find(q => q.questionCode === question.questionCode)
          if (TargetQuestion) {
            fullScore = TargetQuestion?.fullScore || 0
          }
        }

        return fullScore
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    width="1300"
    class="modal-normal-block-detail"
    footer-hide
    @on-visible-change="handleModalVisibleChange"
  >
    <div class="modal-content">
      <template v-if="paper">
        <img :src="paper.imgUrl" />
        <div class="text-info">
          <span class="label"> 识别作答： </span>
          <div class="normal-content">
            <div v-for="item of paperAIResultList" :key="'q-answer-' + item.questionCode">
              {{ item.questionCode }}.
              <span v-html="item.studentAnswer || ''"></span>
            </div>
          </div>
        </div>
        <div class="text-info">
          <span class="label"> 评分理由： </span>
          <div class="normal-content">
            <div v-for="item of paperAIResultList" :key="'q-score-reason-' + item.questionCode">
              {{ item.questionCode }}.（{{ getQuestionFullScore(item) }} 分）得 {{ item.studentScore || 0 }} 分，
              <span v-html="item.scoreReason || ''"></span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-normal-block-detail {
    .modal-content {
      .text-info {
        margin-top: 1em;

        .label {
          color: $color-small-title;
          font-weight: bold;
        }
      }
    }
  }
</style>
