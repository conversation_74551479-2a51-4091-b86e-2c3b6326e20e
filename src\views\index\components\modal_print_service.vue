<template>
  <Modal
    :model-value="props.modelValue"
    title="AI练习卷"
    :width="1000"
    mask-closable
    cancel-text=""
    ok-text="我知道了"
    @on-visible-change="handleChangeVisibility"
  >
    <template v-if="initialized">
      <video
        class="media"
        controls
        src="https://suremark.igrade.cn/static/exercisebrochure/同级生单元卷先阅后扫.mp4"
      ></video>
      <img class="media img-1" src="https://suremark.igrade.cn/static/exercisebrochure/同级生单元卷宣传册1.png" />
      <img class="media img-2" src="https://suremark.igrade.cn/static/exercisebrochure/同级生单元卷宣传册2.png" />
    </template>
  </Modal>
</template>

<script setup>
  import { ref, watch } from 'vue'

  const props = defineProps({
    modelValue: Boolean,
  })
  const emit = defineEmits(['update:modelValue'])

  const initialized = ref(false)

  // 首次打开弹窗后才显示
  watch(
    () => props.modelValue,
    () => {
      if (props.modelValue) {
        initialized.value = true
      }
    }
  )

  function handleChangeVisibility(visibility) {
    if (!visibility) {
      emit('update:modelValue', visibility)
    }
  }
</script>

<style lang="scss" scoped>
  .media {
    display: block;
    width: 100%;

    &.img-1 {
      margin-top: 8px;
    }

    &.img-2 {
      margin-top: -6px;
      margin-left: 1px;
      transform: scaleX(1.002);
      transform-origin: left top;
    }
  }
</style>
