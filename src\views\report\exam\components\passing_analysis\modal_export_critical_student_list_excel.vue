<script>
  import {
    apiGetCriticalStudentCountByScore,
    apiGetCriticalStudentCountByRank,
    apiGetCriticalStudentListByScore,
    apiGetCriticalStudentListByRank,
  } from '@/api/report'

  import J<PERSON>Z<PERSON> from 'jszip'
  import { mapGetters } from 'vuex'

  import { deepCopy } from '@/utils/object'
  import { downloadBlob } from '@/utils/download'
  import { generateExcelBlob } from '@/utils/excel_export'

  import { UUID_ZERO } from '@/const/string'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      isModeScoreCalculate: {
        type: Boolean,
        default: true,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        modalCalculateMode: 'score',
        subjectListForDownload: [],
        selectedExportSubjects: [],
      }
    },

    computed: {
      ...mapGetters('report', [
        'examAttributes',
        'currentLevelName',
        'currentExamSubjectId',
        'currentExamSubjectName',
        'isSeniorHighSchoolSubjectCombinationSelectableProject',
        'subjectCombinationCode',
        'subjectCombinationName',
        'currentSeniorHighSchoolExamCategorySubject',
        'currentSchoolId',
        'currentSchoolName',
        'currentClassId',
        'currentClassName',
        'subjectList',
        'examId',
        'templateId',
        'examName',
        'templateName',
      ]),

      isModalModeScoreCalculate() {
        return this.modalCalculateMode === 'score'
      },

      reportSeniorHighSchoolExamCategories() {
        return (this.$store.state.report.reportSeniorHighSchoolExamCategories || []).filter(category => category.show)
      },
    },

    watch: {
      reportSeniorHighSchoolExamCategories() {
        this.initSubjectListForDownload()
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleStatusChanged(visible) {
        if (visible) {
          this.modalCalculateMode = this.isModeScoreCalculate ? 'score' : 'rank'
          this.initSubjectListForDownload()
        } else {
          this.closeModal()
        }
      },

      initSubjectListForDownload() {
        if (this.isSeniorHighSchoolSubjectCombinationSelectableProject) {
          this.subjectListForDownload = []
          if (this.reportSeniorHighSchoolExamCategories.length) {
            this.reportSeniorHighSchoolExamCategories.forEach(category => {
              if (category.subjects && category.subjects.length) {
                category.subjects.forEach(subject => {
                  this.subjectListForDownload.push({
                    categoryId: category.id,
                    categoryName: category.name,
                    examSubjectId: subject.examSubjectId,
                    subjectCode: subject.subjectCode,
                    subjectName: subject.subjectName,
                    fullScore: subject.fullScore,
                    studentNum: subject.present,
                    line: Math.round((this.isModalModeScoreCalculate ? subject.fullScore : subject.present) * 0.6),
                    float: 10,
                    id: subject.examSubjectId + category.id + subject.subjectCode,
                    showName: `${category.name && category.name !== '全体' ? category.name : ''}${subject.subjectName}`,
                  })
                })
              }
            })
          }
        } else {
          this.subjectListForDownload = (this.subjectList || []).map(subject => {
            const FullScore =
              (this.examAttributes &&
                this.examAttributes.fullScoreMap &&
                this.examAttributes.fullScoreMap[subject.subjectId]) ||
              0
            const StudentNum = (this.examAttributes && this.examAttributes.studentNum) || 0

            return {
              examSubjectId: subject.examSubjectId || UUID_ZERO,
              subjectId: subject.subjectId,
              subjectName: subject.subjectName,
              fullScore: FullScore,
              studentNum: StudentNum,
              line: Math.round((this.isModalModeScoreCalculate ? FullScore : StudentNum) * 0.6),
              float: 10,
              id: subject.examSubjectId || UUID_ZERO,
              showName: subject.subjectName,
            }
          })
        }
      },

      handleSelecteSubjectAllBtnClick() {
        if (this.selectedExportSubjects.length) {
          this.selectedExportSubjects = []
        } else {
          this.selectedExportSubjects = this.subjectListForDownload.map(item => item.id)
        }
      },

      handleSubjectItemClick(id) {
        if (this.selectedExportSubjects.includes(id)) {
          this.selectedExportSubjects = this.selectedExportSubjects.filter(item => item !== id)
        } else {
          this.selectedExportSubjects.push(id)
        }
      },

      calSubjectValue() {
        ;(this.subjectListForDownload || []).forEach(item => {
          item.line = Math.round((this.isModalModeScoreCalculate ? item.fullScore : item.studentNum) * 0.6)
          item.float = 10
        })
      },

      checkParams(subjects = []) {
        for (let i = 0; i < subjects.length; i++) {
          const ModeText = this.isModalModeScoreCalculate ? '分数' : '名次'
          const CompareValue = this.isModalModeScoreCalculate ? subjects[i].fullScore : subjects[i].studentNum
          if (subjects[i].line === null || subjects[i].line === undefined) {
            return `${subjects[i].showName}的临界${ModeText}尚未设置`
          }
          if (subjects[i].float === null || subjects[i].float === undefined) {
            return `${subjects[i].showName}的浮动${ModeText}尚未设置`
          }
          if (subjects[i].line <= 0 || subjects[i].line >= CompareValue) {
            return `临界${ModeText}的值应是1至${CompareValue}间的正整数`
          }
        }
        return ''
      },

      async generateSubjectExcelSheets(subject) {
        const Sheets = []

        const RequestParams = {
          examId: this.examId,
          templateId: this.templateId,
          schoolId: this.currentSchoolId,
          classId: this.currentLevelName === 'class' ? this.currentClassId : undefined,
          criticalLine: subject.line,
          criticalValue: subject.float,
          subjectCategory: this.isSeniorHighSchoolSubjectCombinationSelectableProject
            ? this.subjectCombinationCode
            : undefined,
          examSubjectId:
            subject.examSubjectId === null || subject.examSubjectId === null
              ? undefined
              : subject.examSubjectId === '0' || !subject.examSubjectId
                ? UUID_ZERO
                : subject.examSubjectId,
        }
        const CriticalRequest = this.isModalModeScoreCalculate
          ? apiGetCriticalStudentCountByScore
          : apiGetCriticalStudentCountByRank
        const CriticalData = await CriticalRequest(RequestParams)
        if (this.currentLevelName === 'school') {
          CriticalData.unshift({
            className: this.currentSchoolName || '',
            lowerStudentNum: CriticalData.reduce((acc, cur) => acc + cur.lowerStudentNum, 0),
            upperStudentNum: CriticalData.reduce((acc, cur) => acc + cur.upperStudentNum, 0),
            studentNum: CriticalData.reduce((acc, cur) => acc + cur.studentNum, 0),
          })
        }

        const Keyword = this.isModalModeScoreCalculate ? '分' : '名'
        Sheets.push({
          sheetName: (subject.showName || '') + '临界生统计',
          rows: CriticalData,
          columns: [
            {
              title: '班级',
              key: 'className',
              width: 'auto',
            },
            {
              title: '分段人数与占比',
              children: [
                {
                  title: `${subject.criticalInterval.lowerText}${Keyword} 人数`,
                  key: this.isModalModeScoreCalculate ? 'lowerStudentNum' : 'upperStudentNum',
                  width: 'auto',
                },
                {
                  title: `${subject.criticalInterval.lowerText}${Keyword} 占比`,
                  key: row =>
                    (this.isModalModeScoreCalculate ? row.lowerStudentNum : row.upperStudentNum) / row.studentNum,
                  cellNumberFormat: value =>
                    isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
                  width: 'auto',
                },
                {
                  title: `${subject.criticalInterval.upperText}${Keyword} 人数`,
                  key: this.isModalModeScoreCalculate ? 'upperStudentNum' : 'lowerStudentNum',
                  width: 'auto',
                },
                {
                  title: `${subject.criticalInterval.upperText}${Keyword} 占比`,
                  key: row =>
                    (this.isModalModeScoreCalculate ? row.upperStudentNum : row.lowerStudentNum) / row.studentNum,
                  cellNumberFormat: value =>
                    isNaN(value) ? undefined : Number.isInteger((value * 1000) / 10) ? '0%' : '0.##%',
                  width: 'auto',
                },
              ],
            },
          ],
        })

        const StudentListRequest = this.isModalModeScoreCalculate
          ? apiGetCriticalStudentListByScore
          : apiGetCriticalStudentListByRank
        const StudentList = (await StudentListRequest(RequestParams)) || []
        let lowerInteravlStudentList = StudentList.filter(item =>
          this.isModalModeScoreCalculate
            ? item.totalScore >= subject.criticalInterval.lowerLeft &&
              item.totalScore < subject.criticalInterval.lowerRight
            : item.schoolRank >= subject.criticalInterval.lowerLeft &&
              item.schoolRank <= subject.criticalInterval.lowerRight
        )
        if (lowerInteravlStudentList.length) {
          const LowerIntervalStudentSheetChildrenColumns = [
            {
              title: '姓名',
              key: 'studentName',
              width: 'auto',
            },
            {
              title: this.isModalModeScoreCalculate ? '分数' : '排名',
              key: this.isModalModeScoreCalculate ? 'totalScore' : 'schoolRank',
              width: 12,
            },
          ]
          if (this.currentLevelName === 'school') {
            lowerInteravlStudentList.sort(
              (a, b) => Number(a.className.replace(/[^0-9]/gi, '')) - Number(b.className.replace(/[^0-9]/gi, ''))
            )
            LowerIntervalStudentSheetChildrenColumns.unshift({
              title: '班级',
              key: 'className',
              width: 'auto',
            })
          }
          Sheets.push({
            sheetName: `${subject.showName} ${subject.criticalInterval.lowerText}${Keyword} 学生名单`,
            rows: lowerInteravlStudentList,
            columns: [
              {
                title: `${subject.showName} ${subject.criticalInterval.lowerText}${Keyword} 学生名单`,
                children: LowerIntervalStudentSheetChildrenColumns,
              },
            ],
          })
        }
        let upperInteravlStudentList = StudentList.filter(item =>
          this.isModalModeScoreCalculate
            ? item.totalScore >= subject.criticalInterval.upperLeft &&
              item.totalScore <= subject.criticalInterval.upperRight
            : item.schoolRank >= subject.criticalInterval.upperLeft &&
              item.schoolRank <= subject.criticalInterval.upperRight
        )
        if (upperInteravlStudentList.length) {
          const UpperIntervalStudentSheetChildrenColumns = [
            {
              title: '姓名',
              key: 'studentName',
              width: 'auto',
            },
            {
              title: this.isModalModeScoreCalculate ? '分数' : '排名',
              key: this.isModalModeScoreCalculate ? 'totalScore' : 'schoolRank',
              width: 12,
            },
          ]
          if (this.currentLevelName === 'school') {
            upperInteravlStudentList.sort(
              (a, b) => Number(a.className.replace(/[^0-9]/gi, '')) - Number(b.className.replace(/[^0-9]/gi, ''))
            )
            UpperIntervalStudentSheetChildrenColumns.unshift({
              title: '班级',
              key: 'className',
              width: 'auto',
            })
          }
          Sheets.push({
            sheetName: `${subject.showName} ${subject.criticalInterval.upperText}${Keyword} 学生名单`,
            rows: upperInteravlStudentList,
            columns: [
              {
                title: `${subject.showName} ${subject.criticalInterval.upperText}${Keyword} 学生名单`,
                children: UpperIntervalStudentSheetChildrenColumns,
              },
            ],
          })
        }

        const ExcelBlob = await generateExcelBlob(Sheets)

        return Promise.resolve({
          ...subject,
          sheets: Sheets,
          fileBlob: ExcelBlob,
        })
      },

      async generateAndDownloadFile() {
        const SelectedSubjects = deepCopy(
          this.subjectListForDownload.filter(item => this.selectedExportSubjects.includes(item.id))
        ).map(s => {
          s.criticalInterval = {
            lowerLeft: Math.max(s.line - s.float, this.isModalModeScoreCalculate ? 0 : 1),
            lowerRight: s.line,
            upperLeft: this.isModalModeScoreCalculate ? s.line : s.line + 1,
            upperRight: Math.min(s.line + s.float, this.isModalModeScoreCalculate ? s.fullScore : s.studentNum),
          }
          s.criticalInterval.lowerText = `${s.criticalInterval.lowerLeft}-${s.criticalInterval.lowerRight}`
          s.criticalInterval.upperText =
            s.criticalInterval.upperLeft == (this.isModalModeScoreCalculate ? s.fullScore : s.studentNum)
              ? '' + s.criticalInterval.upperLeft
              : `${s.criticalInterval.upperLeft}-${s.criticalInterval.upperRight}`
          return s
        })

        const ParamsWarning = this.checkParams(SelectedSubjects)
        if (ParamsWarning) {
          this.$Message.warning({
            duration: 8,
            closabled: true,
            content: ParamsWarning,
          })
          return
        }

        const Zipper = new JSZip()
        const ZipBlob = await Promise.all(SelectedSubjects.map(s => this.generateSubjectExcelSheets(s)))
          .then(results => {
            results.forEach(item => {
              try {
                Zipper.file(`${this.examName}_${this.templateName}_临界生统计名单_${item.showName}.xlsx`, item.fileBlob)
              } catch (err) {
                console.log('打包出错: ', err)
              }
            })
          })
          .then(() => Zipper.generateAsync({ type: 'blob' }))
        downloadBlob(ZipBlob, this.examName + '_' + this.templateName + '_' + '临界生统计名单.zip')
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    class="modal-export-critical-student-list-excel"
    title="导出临界生名单"
    :cloasable="false"
    :mask-closable="false"
    @on-visible-change="handleModalVisibleStatusChanged"
  >
    <div class="modal-content-panel">
      <div class="top-bar">
        <RadioGroup
          v-model="modalCalculateMode"
          type="button"
          size="small"
          button-style="solid"
          @on-change="calSubjectValue"
        >
          <Radio label="score">按分数统计</Radio>
          <Radio label="rank">按名次统计</Radio>
        </RadioGroup>

        <div class="selected-explain">
          已选择科目<span class="color-primary">{{ selectedExportSubjects.length }}</span
          >个（<TextButton underline style="text-underline-offset: 3px" @click="handleSelecteSubjectAllBtnClick"
            >全选</TextButton
          >）
        </div>
      </div>

      <div class="subject-settings">
        <div v-for="item of subjectListForDownload" :key="item.id" class="setting-item">
          <Checkbox
            class="check-box-la"
            :model-value="selectedExportSubjects.includes(item.id)"
            @click="handleSubjectItemClick(item.id)"
          ></Checkbox>
          <div class="subject-name" @click="handleSubjectItemClick(item.id)">
            {{ item.showName }}
          </div>
          <div class="setting-value">
            <div class="sv-item">
              <span>临界{{ isModalModeScoreCalculate ? '分数' : '名次' }}：</span
              ><InputNumber
                v-model="item.line"
                style="margin: 0 0.5em 0 1em"
                :precision="0"
                :disabled="!selectedExportSubjects.includes(item.id)"
              ></InputNumber
              ><span>{{ isModalModeScoreCalculate ? '分' : '名' }}</span>
            </div>
            <div class="sv-item">
              <span>浮动{{ isModalModeScoreCalculate ? '分数' : '名次' }}：</span
              ><InputNumber
                v-model="item.float"
                style="margin: 0 0.5em 0 1em"
                :precision="0"
                :min="1"
                :disabled="!selectedExportSubjects.includes(item.id)"
              ></InputNumber
              ><span>{{ isModalModeScoreCalculate ? '分' : '名' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" :disabled="!selectedExportSubjects.length" @click="generateAndDownloadFile">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-export-critical-student-list-excel {
    .modal-content-panel {
      .top-bar {
        @include flex(row, space-between, center);
        margin-bottom: 6px;
        user-select: none;

        .selected-explain {
          .color-primary {
            margin: 0 0.5em;
            color: $color-primary;
          }
        }
      }

      .subject-settings {
        max-height: calc(60vh);
        overflow-y: auto;

        .setting-item {
          @include flex(row, flex-start, center);
          padding: 8px 0 8px 2em;

          &:nth-child(even) {
            background-color: $color-background-light;
          }

          .check-box-la {
            &:hover {
              cursor: pointer;
            }
          }

          .subject-name {
            width: 180px;
            margin-left: 20px;
            text-align: center;

            &:hover {
              cursor: pointer;
            }
          }

          .setting-value {
            .sv-item {
              &:not(:last-child) {
                margin-bottom: 6px;
              }
            }
          }
        }
      }
    }
  }
</style>
