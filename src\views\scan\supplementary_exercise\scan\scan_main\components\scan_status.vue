<template>
  <span class="scan-status">
    <span class="status-left">
      <span class="status-label">状态：</span>
      <span class="status-text" :class="statusTextClass">{{ scannerStatusName }}</span>
      <OpenScanClient
        class="btn-open-client"
        :scanner-status-id="scannerStatusId"
        :show-download-link="true"
      ></OpenScanClient>
    </span>
    <TextButton class="btn-refresh" icon="md-refresh" @click="handleBtnRefreshClick">刷新</TextButton>
  </span>
</template>

<script>
  import OpenScanClient from '@/views/scan/components/open_scan_client.vue'

  import ScannerStatusEnum from '@/enum/emarking/scanner_status'

  export default {
    components: {
      OpenScanClient,
    },
    props: {
      scannerStatusId: String,
    },
    emits: ['refresh'],
    computed: {
      statusTextClass() {
        return {
          success: this.scannerStatusId == ScannerStatusEnum.Ready.id,
          error: this.scannerStatusId == ScannerStatusEnum.UnReady.id,
        }
      },
      scannerStatusName() {
        return ScannerStatusEnum.getNameById(this.scannerStatusId) || ''
      },
    },
    methods: {
      handleBtnRefreshClick() {
        this.$emit('refresh')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-status {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 0;

    .status-left {
      @include flex(row, flex-start, center);
      min-width: 360px;

      .status-text {
        &.error {
          color: $color-warning;
        }

        &.success {
          color: $color-primary;
        }
      }

      .btn-open-client {
        margin-left: 20px;
      }
    }

    .btn-refresh {
      margin-left: 20px;
    }
  }
</style>
