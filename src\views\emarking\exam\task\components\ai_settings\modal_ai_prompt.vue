<script>
  import PromptContent from '@/views/class_practise/setting/components/prompt_content.vue'

  import { apiGetAiBlockPrompt, apiGenerateAiBlockPrompt, apiModifyAiBlockPrompt } from '@/api/emarking/ai'

  import { randomId } from '@/utils/string'
  import { deepCopy } from '@/utils/object'
  import { getFullCode } from '@/helpers/emarking/miscellaneous'

  export default {
    components: {
      'prompt-content': PromptContent,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      examSubjectId: {
        type: String,
        default: '',
      },
      blocks: {
        type: Array,
        default: () => [],
      },
      initBlockId: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue', 'refresh-prompt'],

    data() {
      return {
        loadingPrompt: false,

        currentBlockId: '',
        currentBlockPrompt: null,
        originPromptJson: '',

        isPoptipAddQuestionShowed: false,
        addQuestionCodes: [],

        isPoptipAddAnswersShowed: false,
        addAnswerCodes: [],
      }
    },

    computed: {
      currentBlock() {
        return this.blocks.find(b => b.blockId === this.currentBlockId)
      },
      currentBlockIndex() {
        return this.blocks.findIndex(b => b.blockId === this.currentBlockId)
      },
      modalTitle() {
        return `提示词${this.currentBlock?.blockName ? '【' + this.currentBlock.blockName + '】' : ''}`
      },
      hasPrevious() {
        return this.currentBlockIndex > 0
      },
      hasNext() {
        return this.currentBlockIndex >= 0 && this.currentBlockIndex < this.blocks.length - 1
      },
      currentBlockQuestionCodes() {
        const Questions = this.currentBlock?.questions
        if (!Questions) {
          return []
        }

        const ExistQuestionCodes = []
        this.currentBlockPrompt?.questions?.forEach(q => q.codes?.forEach(code => ExistQuestionCodes.push(code)))

        const ExistAnswerCodes = []
        this.currentBlockPrompt?.answers?.forEach(q => q.codes?.forEach(code => ExistAnswerCodes.push(code)))

        return (Questions || []).map(q => {
          const Code = getFullCode(q.questionCode, q.branchCode)
          return {
            code: Code,
            questionCode: q.questionCode,
            branchCode: q.branchCode,
            disabledQ: ExistQuestionCodes.includes(Code),
            disabledA: ExistAnswerCodes.includes(Code),
          }
        })
      },
      currentBlockPromptQuestions() {
        return this.currentBlockPrompt?.questions || []
      },
      currentBlockPromptAnswers() {
        return this.currentBlockPrompt?.answers || []
      },
      currentBlockPromptStandard() {
        return this.currentBlockPrompt?.standard || null
      },
    },

    methods: {
      // modal
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.initModal(this.initBlockId)
        } else {
          this.onCloseModal()
        }
      },
      onModalConfirm() {
        if (this.currentBlock) {
          const PurePrompt = this.getPromptWithoutAdditionalKey(this.currentBlockPrompt)

          const CurrentPromptEditableAttributesJson = this.getPromptEditableAttributesJson(PurePrompt)
          if (CurrentPromptEditableAttributesJson === this.originPromptJson) {
            this.$Message.warning({
              duration: 3,
              content: '未作修改',
            })
            return
          }

          ;(PurePrompt?.answers || []).forEach(a =>
            (a.contents || []).forEach(c => (c.value = c.value.replace(/(<br\s*\/?>|\n)+/g, '\n')))
          )

          this.$Modal.confirm({
            title: '修改提示词',
            content: `确定修改题块【${this.currentBlock.blockName}】的AI评卷提示词？`,
            onOk: () => {
              this.loading = true
              apiModifyAiBlockPrompt({
                examSubjectId: this.examSubjectId,
                prompt: PurePrompt,
              })
                .then(() => {
                  this.$Message.success('提示词已修改')
                  this.onCloseModal()
                })
                .finally(() => {
                  this.loading = false
                  this.$emit('refresh-prompt')
                })
            },
          })
        }
      },

      // func
      initModal(blockId) {
        const TargetBlockIndex = this.blocks.findIndex(b => b.blockId == blockId)
        if (TargetBlockIndex >= 0) {
          this.toBlockSpecifyIndex(TargetBlockIndex)
        } else if (this.blocks.length) {
          this.toBlockSpecifyIndex(0)
        } else {
          this.currentBlockId = ''
        }
      },
      toBlockSpecifyIndex(index) {
        if (this.blocks[index]) {
          this.currentBlockId = this.blocks[index].blockId
          if (this.examSubjectId && this.currentBlockId) {
            this.fetchBlockPrompt()
          } else {
            this.currentBlockPrompt = null
          }
        }
      },
      toPreviousBlock() {
        this.toBlockSpecifyIndex(this.currentBlockIndex - 1)
      },
      toNextBlock() {
        this.toBlockSpecifyIndex(this.currentBlockIndex + 1)
      },
      selectQuestionsAll() {
        this.addQuestionCodes = this.currentBlockQuestionCodes.filter(x => !x.disabledQ).map(x => x.code)
      },
      handleAddQuestionOK() {
        if (!this.currentBlock) {
          return
        }

        const FiltedAddQuestionCodes = (this.addQuestionCodes || []).filter(q =>
          this.currentBlockQuestionCodes.some(c => c.code == q && !c.disabledQ)
        )
        if (FiltedAddQuestionCodes.length == 0) {
          if (this.currentBlockQuestionCodes.every(x => x.disabledQ)) {
            this.$Message.warning('所有小题都已添加')
          } else {
            this.$Message.warning('请选择小题')
          }
          return
        }

        const QuestionCodes = []
        FiltedAddQuestionCodes.forEach(code => {
          let q = this.currentBlockQuestionCodes.find(x => x.code == code)
          if (q && !QuestionCodes.includes(q.questionCode)) {
            QuestionCodes.push(q.questionCode)
          }
        })
        QuestionCodes.sort((a, b) => a - b)

        let codeText = ''
        if (QuestionCodes.length == 1) {
          codeText = `${QuestionCodes[0]}题`
        } else if (QuestionCodes.length == QuestionCodes[QuestionCodes.length - 1] - QuestionCodes[0] + 1) {
          codeText = `${QuestionCodes[0]}至${QuestionCodes[QuestionCodes.length - 1]}题`
        } else {
          codeText = QuestionCodes.map(questionCode => `${questionCode}题`).join('、')
        }

        this.currentBlockPromptQuestions.push({
          key: this.getKey(this.currentBlockPromptQuestions.length),
          codes: FiltedAddQuestionCodes.slice(),
          intro: `以下是${codeText}内容：`,
          contents: [],
        })
      },
      removeQuestion(question) {
        const QuestionIndex = this.currentBlockPromptQuestions.indexOf(question)
        if (QuestionIndex >= 0) {
          this.currentBlockPromptQuestions.splice(QuestionIndex, 1)
        }
      },
      selectAnswersAll() {
        this.addQuestionCodes = this.currentBlockQuestionCodes.filter(x => !x.disabledA).map(x => x.code)
      },
      handleAddAnswerOK() {
        if (this.currentBlock) {
          const FiltedAddAnswerCodes = (this.addAnswerCodes || []).filter(q =>
            this.currentBlockQuestionCodes.some(c => c.code == q && !c.disabledA)
          )
          if (FiltedAddAnswerCodes.length == 0) {
            if (this.currentBlockQuestionCodes.every(x => x.disabledA)) {
              this.$Message.warning('所有小题都已添加')
            } else {
              this.$Message.warning('请选择小题')
            }
            return
          }

          const QuestionCodes = []
          FiltedAddAnswerCodes.forEach(code => {
            let q = this.currentBlockQuestionCodes.find(x => x.code == code)
            if (q && !QuestionCodes.includes(q.questionCode)) {
              QuestionCodes.push(q.questionCode)
            }
          })
          QuestionCodes.sort((a, b) => a - b)

          let codeText = ''
          if (QuestionCodes.length == 1) {
            codeText = `${QuestionCodes[0]}题`
          } else if (QuestionCodes.length == QuestionCodes[QuestionCodes.length - 1] - QuestionCodes[0] + 1) {
            codeText = `${QuestionCodes[0]}至${QuestionCodes[QuestionCodes.length - 1]}题`
          } else {
            codeText = QuestionCodes.map(questionCode => `${questionCode}题`).join('、')
          }

          this.currentBlockPromptAnswers.push({
            key: this.getKey(this.currentBlockPromptAnswers.length),
            codes: FiltedAddAnswerCodes.slice(),
            intro: `以下是${codeText}参考答案：`,
            contents: [],
          })
        }
      },
      removeAnswer(answer) {
        const AnswerIndex = this.currentBlockPromptAnswers.indexOf(answer)
        if (AnswerIndex >= 0) {
          this.currentBlockPromptAnswers.splice(AnswerIndex, 1)
        }
      },

      // tools
      getKey(index) {
        return `${index}-${randomId()}`
      },
      getPromptWithoutAdditionalKey() {
        const PromptCopy = deepCopy(this.currentBlockPrompt)

        PromptCopy?.questions?.forEach(q => delete q.key)
        PromptCopy?.answers?.forEach(q => delete q.key)
        PromptCopy?.standard?.items?.forEach(item => delete item.key)

        return PromptCopy
      },
      getPromptEditableAttributesJson(prompt) {
        if (prompt) {
          return JSON.stringify({
            questions: prompt.questions || [],
            answers: prompt.answers || [],
            standard: prompt.standard || null,
          })
        } else {
          return ''
        }
      },

      // data exchange
      fetchBlockPrompt() {
        this.currentBlockPrompt = null
        this.originPromptJson = ''
        apiGetAiBlockPrompt({
          examSubjectId: this.examSubjectId,
          blockId: this.currentBlockId,
        }).then(response => {
          if (response) {
            this.currentBlockPrompt = response
            if (this.currentBlockPrompt?.answers?.length) {
              this.currentBlockPrompt.answers.forEach(a => {
                ;(a.contents || []).forEach(c => {
                  c.value = c.value.replace(/\n/g, '<br/>')
                })
              })
            }
            this.currentBlockPrompt.workflow = this.currentBlockPrompt.workflow || ''
            this.currentBlockPrompt.responseRule = this.currentBlockPrompt.responseRule || ''
            this.originPromptJson = this.getPromptEditableAttributesJson(
              this.getPromptWithoutAdditionalKey(this.currentBlockPrompt)
            )
          }
        })
      },
      regeneratePrompt() {
        if (this.currentBlock) {
          this.$Modal.confirm({
            title: '重新生成提示词',
            content: `确定重新生成题块【${this.currentBlock.blockName}】的AI评卷提示词？`,
            onOk: () => {
              this.loading = true
              apiGenerateAiBlockPrompt({ examSubjectId: this.examSubjectId, blockId: this.currentBlockId })
                .then(() => this.$Message.success('已重新生成提示词'))
                .finally(() => {
                  this.loadingPrompt = false
                  this.fetchBlockPrompt()
                })
            },
          })
        }
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    :mask-closable="!loadingPrompt"
    width="800"
    class="modal-ai-prompt"
    @on-visible-change="handleModalVisibleChange"
  >
    <div class="modal-content">
      <div class="action-bar">
        <TextButton type="primary" @click="regeneratePrompt">重新生成提示词</TextButton>
        <div>
          <TextButton :disabled="!hasPrevious" icon="md-arrow-back" @click="toPreviousBlock">上个题块</TextButton>
          <TextButton :disabled="!hasNext" icon="md-arrow-forward" @click="toNextBlock">下个题块</TextButton>
        </div>
      </div>

      <div class="prompt-panel">
        <div class="prompt-item">
          <div class="prompt-header">
            <div class="prompt-title">题目</div>
            <Poptip
              placement="bottom-end"
              width="300"
              trigger="click"
              title="选择小题"
              @on-popper-show="addQuestionCodes = []"
            >
              <TextButton type="primary" icon="md-add" @click="isPoptipAddQuestionShowed = true">添加</TextButton>
              <template #content>
                <TextButton type="primary" @click="selectQuestionsAll">全选</TextButton>
                <CheckboxGroup v-model="addQuestionCodes" class="poptip-check">
                  <Checkbox
                    v-for="item in currentBlockQuestionCodes"
                    :key="item.code"
                    :label="item.code"
                    :disabled="item.disabledQ"
                  ></Checkbox>
                </CheckboxGroup>
                <div class="poptip-footer">
                  <Button type="primary" size="small" @click="handleAddQuestionOK">确定</Button>
                </div>
              </template>
            </Poptip>
          </div>
          <div class="content-panel">
            <div v-for="q of currentBlockPromptQuestions" :key="q.key" class="prompt-question">
              <div class="question-title">
                <TextButton type="warning" icon="md-close" title="删除" @click="removeQuestion(q)"></TextButton>
                <div class="question-codes">主观题：{{ (q.codes || []).join('、') }}</div>
              </div>
              <prompt-content v-model="q.contents"></prompt-content>
            </div>
          </div>
        </div>

        <div class="prompt-item">
          <div class="prompt-header">
            <div class="prompt-title">答案</div>
            <Poptip
              placement="bottom-end"
              width="300"
              trigger="click"
              title="选择小题"
              @on-popper-show="addAnswerCodes = []"
            >
              <TextButton type="primary" icon="md-add" @click="isPoptipAddAnswersShowed = true">添加</TextButton>
              <template #content>
                <TextButton type="primary" @click="selectAnswersAll">全选</TextButton>
                <CheckboxGroup v-model="addAnswerCodes" class="poptip-check">
                  <Checkbox
                    v-for="item in currentBlockQuestionCodes"
                    :key="item.code"
                    :label="item.code"
                    :disabled="item.disabledA"
                  ></Checkbox>
                </CheckboxGroup>
                <div class="poptip-footer">
                  <Button type="primary" size="small" @click="handleAddAnswerOK">确定</Button>
                </div>
              </template>
            </Poptip>
          </div>
          <div class="content-panel">
            <div v-for="q of currentBlockPromptAnswers" :key="q.key" class="prompt-question">
              <div class="question-title">
                <TextButton type="warning" icon="md-close" title="删除" @click="removeAnswer(q)"></TextButton>
                <div class="question-codes">主观题：{{ (q.codes || []).join('、') }}</div>
              </div>
              <prompt-content v-model="q.contents"></prompt-content>
            </div>
          </div>
        </div>

        <div class="prompt-item">
          <div class="prompt-header">
            <div class="prompt-title">评分标准</div>
          </div>
          <div v-if="currentBlockPromptStandard" class="content-panel">
            <div v-for="item of currentBlockPromptStandard.items" :key="item.key" class="standard-item">
              <div class="question-code">主观题：{{ item.code }}</div>
              <div class="question-full-score">{{ item.fullScoreText }}</div>
              <prompt-content v-model="item.contents" class="standard-item-contents"></prompt-content>
            </div>
            <div class="standard-extra">
              <div class="standard-extra-title">补充评分标准</div>
              <prompt-content v-model="currentBlockPromptStandard.extra"></prompt-content>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button :disabled="loadingPrompt" type="text" @click="onCloseModal">取消</Button>
      <Button :loading="loadingPrompt" type="primary" @click="onModalConfirm">确定</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-ai-prompt {
    .modal-content {
      .action-bar {
        @include flex(row, space-between, center);
        padding-bottom: 9px;
        border-bottom: 1px solid $color-border;
      }

      .prompt-panel {
        .prompt-item {
          margin-top: 20px;

          .prompt-header {
            @include flex(row, space-between, center);

            .prompt-title {
              padding: 8px;
              border: 2px solid $color-primary;
              border-radius: 4px;
              color: $color-primary;
              font-size: 16px;
              line-height: 1;
            }
          }

          .content-panel {
            margin-top: 16px;
            margin-bottom: 24px;

            &.pre-wrap {
              white-space: pre-wrap;
            }

            .prompt-question {
              &:not(:first-child) {
                padding-top: 16px;
                border-top: 1px dashed $color-primary;
              }

              &:not(:last-child) {
                margin-bottom: 16px;
              }

              .question-title {
                @include flex(row, flex-start, center);
                margin-bottom: 8px;

                .question-codes {
                  flex-grow: 0;
                  flex-shrink: 0;
                  margin-right: 16px;
                  color: $color-primary;
                }

                .question-intro {
                  flex-grow: 1;
                }
              }
            }

            .standard-item {
              @include flex(row, flex-start, center);
              margin-bottom: 8px;

              .question-code {
                flex-grow: 0;
                flex-shrink: 0;
                margin-right: 16px;
                color: $color-primary;
              }

              .question-full-score {
                flex-grow: 0;
                flex-shrink: 0;
                margin-right: 8px;
              }

              .standard-item-contents {
                flex-grow: 1;
              }
            }

            .standard-extra {
              .standard-extra-title {
                margin-bottom: 8px;
                color: $color-primary;
              }
            }
          }

          .poptip-check {
            margin-top: 8px;
          }

          .poptip-footer {
            margin-top: 16px;
            text-align: right;
          }
        }
      }
    }
  }
</style>
