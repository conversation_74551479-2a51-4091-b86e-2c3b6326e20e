<template>
  <div class="panel-paper">
    <div class="panel-header">
      <div v-if="activeBatch" class="active-batch-info">
        <span>{{ activeBatchUserInfo }}</span
        ><template v-if="activeBatchScanInfo"
          ><Divider type="vertical"></Divider><span>{{ activeBatchScanInfo }}</span></template
        >
      </div>
    </div>
    <Table class="table-paper" :columns="tableColumns" :data="papers" :max-height="tableMaxHeight"></Table>
    <div class="panel-footer">
      <span v-if="paperTotal > 0" class="summary">共 {{ paperTotal }} 张答卷</span>
      <Page :model-value="currentPage" :page-size="pageSize" :total="paperTotal" @on-change="changeCurrentPage"></Page>
    </div>

    <ModalRawImage
      v-model="showModalImage"
      :active-batch="activeBatch"
      :paper="paperInModalImage"
      :has-next="hasNext"
      :has-previous="hasPrevious"
      @go-next="goNext"
      @go-previous="goPrevious"
    ></ModalRawImage>

    <ModalRoomStudents
      v-model="showModalRoomStudents"
      :room-no="modalRoomNo"
      :student-id="modalStudentId"
      :unit-id="modalUnitId"
    ></ModalRoomStudents>
  </div>
</template>

<script>
  import TextButton from '@/components/text_button'
  import ModalRawImage from './modal_raw_image.vue'
  import ModalRoomStudents from '../../scan_student/modal_room_students.vue'

  import { apiGetBatchPaperPage } from '@/api/scan/scan_batch'

  import { mapGetters } from 'vuex'
  import { formatDateTime } from '@/utils/date'
  import RecognizeStatusEnum from '@/enum/scan/scan_unit_recognize_status'

  export default {
    components: {
      ModalRawImage,
      ModalRoomStudents,
    },
    props: {
      activeBatch: Object,
      tableMaxHeight: Number,
    },
    data() {
      return {
        paperTotal: 0,
        papers: [],
        currentPage: 1,
        pageSize: 100,

        // 显示图像弹窗
        showModalImage: false,
        paperInModalImage: null,

        // 显示识别答卷弹窗
        showModalRoomStudents: false,
        modalRoomNo: 0,
        modalStudentId: '',
        modalUnitId: '',
      }
    },
    computed: {
      ...mapGetters('scan', ['examSubjectId', 'isInSchoolExam']),
      activeBatchUserInfo() {
        if (!this.activeBatch) {
          return ''
        }
        let { batchNumber, scanRealName, scanTime } = this.activeBatch
        let scanTimeText = scanTime ? formatDateTime(new Date(scanTime)) : ''
        return `${batchNumber} 批次，${scanRealName}，${scanTimeText}`
      },
      activeBatchScanInfo() {
        if (!this.activeBatch) {
          return ''
        }
        let info = ''
        let { clientVersion, deviceName } = this.activeBatch
        if (clientVersion) {
          info += `扫描端版本：${clientVersion}`
        }
        if (deviceName) {
          if (info) {
            info += '，'
          }
          info += `扫描仪型号：${deviceName}`
        }
        return info
      },
      tableColumns() {
        let columns = [
          {
            title: '纸张序号',
            key: 'paperNo',
            width: 50,
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                '纸张\n序号'
              ),
          },
          {
            title: '页面',
            key: 'pageNos',
            width: 80,
            align: 'center',
            render: (h, params) => h('span', {}, params.row.pageNos ? `第${params.row.pageNos}面` : ''),
          },
          {
            title: '准考号',
            key: 'admissionNum',
            minWidth: 80,
            maxWidth: 150,
            align: 'center',
          },
          {
            title: '姓名',
            key: 'studentName',
            minWidth: 65,
            maxWidth: 120,
            align: 'center',
          },
          {
            title: '学校',
            key: 'schoolName',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '班级',
            key: 'className',
            minWidth: 90,
            maxWidth: 120,
            align: 'center',
          },
          {
            title: '考场',
            key: 'roomNo',
            minWidth: 70,
            maxWidth: 100,
            align: 'center',
          },
          {
            title: '座位',
            key: 'seatNum',
            width: 60,
            align: 'center',
          },
          {
            title: '状态',
            align: 'center',
            width: 80,
            render: (h, params) =>
              h(
                'span',
                {
                  class: params.row.statusTextClass,
                },
                params.row.statusText
              ),
          },
          {
            title: '扫描时间',
            key: 'scanTimeText',
            align: 'center',
            width: 80,
            render: (h, params) =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                  },
                },
                params.row.scanTimeText
              ),
          },
          {
            title: '操作',
            align: 'center',
            width: 100,
            render: (h, params) => {
              let btns = [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => {
                      this.showRawImage(params.row)
                    },
                  },
                  () => '查看原图'
                ),
              ]
              let { isDelete, recogStatus } = params.row
              if (!isDelete && recogStatus == RecognizeStatusEnum.RecognizeSuccess.id) {
                btns.push(
                  h(
                    TextButton,
                    {
                      type: 'primary',
                      style: {
                        marginTop: '10px',
                        lineHeight: 1.5,
                      },
                      onClick: () => {
                        this.showRecognizedImage(params.row)
                      },
                    },
                    () => '查看识别图'
                  )
                )
              }
              return h('div', {}, btns)
            },
          },
        ]
        if (this.isInSchoolExam) {
          columns = columns.filter(col => col.title != '学校')
          columns.forEach(col => {
            if (['准考号', '姓名', '班级', '考场'].includes(col.title)) {
              col.maxWidth = undefined
            }
          })
        }
        return columns
      },

      /**
       * 查看原图
       */
      paperIndex() {
        if (!this.paperInModalImage) {
          return -1
        }
        return this.papers.findIndex(p => p.paperNo == this.paperInModalImage.paperNo)
      },
      hasNext() {
        return this.paperIndex >= 0 && this.paperIndex < this.papers.length - 1
      },
      hasPrevious() {
        return this.paperIndex > 0
      },
    },
    watch: {
      activeBatch() {
        this.currentPage = 1
        this.getBatchPapers()
      },
    },
    methods: {
      getBatchPapers() {
        if (!this.activeBatch) {
          this.paperTotal = 0
          this.papers = []
          this.currentPage = 1
          return
        }
        return apiGetBatchPaperPage({
          examSubjectId: this.examSubjectId,
          batchId: this.activeBatch.batchId,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
        }).then(paperPage => {
          this.paperTotal = paperPage.total
          this.papers = paperPage.records.map(item => {
            // 扫描时间
            let scanTimeText = item.scanTime ? formatDateTime(new Date(item.scanTime), 'MM-DD\nHH:mm:ss') : ''
            // 状态
            let statusText = ''
            let statusTextClass = ''
            let { isDelete, recogStatus } = item
            if (isDelete) {
              statusText = '已删除'
              statusTextClass = 'dark'
            } else if (
              [
                RecognizeStatusEnum.MatchFail.id,
                RecognizeStatusEnum.SubjectException.id,
                RecognizeStatusEnum.CornerException.id,
                RecognizeStatusEnum.AdmissionNumException.id,
              ].includes(recogStatus)
            ) {
              statusText = RecognizeStatusEnum.getNameById(recogStatus)
              statusTextClass = 'warning'
            } else if (recogStatus == RecognizeStatusEnum.RecognizeSuccess.id) {
              statusText = '已识别'
            } else {
              statusText = '识别中'
            }
            return {
              ...item,
              scanTimeText,
              statusText,
              statusTextClass,
            }
          })
        })
      },
      changeCurrentPage(currentPage) {
        this.currentPage = currentPage
        this.getBatchPapers()
      },
      showRawImage(page) {
        this.paperInModalImage = page
        this.showModalImage = true
      },
      goPrevious() {
        if (this.hasPrevious) {
          this.paperInModalImage = this.papers[this.paperIndex - 1]
        }
      },
      goNext() {
        if (this.hasNext) {
          this.paperInModalImage = this.papers[this.paperIndex + 1]
        }
      },
      showRecognizedImage(page) {
        this.modalRoomNo = page.roomNo
        this.modalStudentId = page.studentId
        this.modalUnitId = page.unitId
        this.showModalRoomStudents = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  .panel-paper {
    padding-right: 16px;
    padding-left: 16px;

    .active-batch-info {
      color: $color-primary;
      font-weight: bold;
    }

    .table-paper {
      line-height: 1.2;

      :deep(.ivu-table-cell .dark) {
        color: $color-disabled;
      }

      :deep(.ivu-table-cell .warning) {
        color: $color-warning;
      }
    }
  }

  .modal-image {
    img {
      width: 100%;
      cursor: zoom-in;

      &:not(:first-child) {
        margin-top: 20px;
      }
    }
  }
</style>
