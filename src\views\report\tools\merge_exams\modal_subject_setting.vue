<script>
  import ComRadioGroup from '@/components/radio_group'
  import ModalSubjectCopyProjectSettings from './modal_subject_copy_project_settings.vue'
  import ModalpaperAddObjectives from './modal_paper_question_operate/modal_paper_add_objectives.vue'
  import ModalPaperProjectSubjectQuestionsConflictsResolves from './modal_paper_question_operate/modal_paper_project_subject_questions_conflicts_resolve.vue'
  import { Tooltip } from 'view-ui-plus'

  import { deepCopy } from '@/utils/object'

  import SubjectScoreCopyTypes from '@/enum/emarking/merge_exam/subject_score_copy_types'
  import BranchType from '@/enum/qlib/branch_type'

  export default {
    components: {
      'com-radio-group': ComRadioGroup,
      'modal-subject-copy-project-settings': ModalSubjectCopyProjectSettings,
      'modal-paper-add-objecives': ModalpaperAddObjectives,
      'modal-paper-project-subject-questions-conflicts-resolves': ModalPaperProjectSubjectQuestionsConflictsResolves,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      examDetails: {
        type: Array,
        default: () => [],
      },
      modalSettingSubjectConfig: {
        type: Object,
        default: null,
      },
    },

    emits: ['update:modelValue', 'update-exam-subject-config'],

    data() {
      return {
        config: {
          copyType: '',
          subjectId: 0,
          subjectType: 0,
          subjectName: '',
          foreignSubjectBranch: false,
          writtenFullScore: 0,
          otherFullScore: 0,
          objectives: [],
          subjectives: [],
          projectSubjectSettings: [],
          settingDone: false,
        },
        objectiveQuestionColumns: [
          {
            title: '大题',
            key: 'topicName',
            align: 'center',
          },
          {
            title: '题号',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '题型',
            align: 'center',
            render: (h, params) =>
              h('span', {}, this.optionalBranchType.find(x => x.id === params.row.questionType)?.name),
          },
          {
            title: '选项个数',
            key: 'optionCount',
            align: 'center',
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '答案',
            key: 'answer',
            align: 'center',
          },
          {
            title: '其它答案分数',
            align: 'center',
            render: (h, params) => {
              if (params.row.branchTypeId === BranchType.MultipleChoice.id && params.row.answerScoreList) {
                return h(
                  Tooltip,
                  {
                    content: params.row.answerScoreList,
                    'max-width': '800',
                    transfer: true,
                  },
                  [
                    h('span', {}, () =>
                      params.row.answerScoreList.length > 21
                        ? params.row.answerScoreList.slice(0, 21) + '……'
                        : params.row.answerScoreList
                    ),
                  ]
                )
              } else {
                return h('span', {}, '-')
              }
            },
          },
          {
            title: '附加题',
            align: 'center',
            render: (h, params) => h('span', {}, params.row.isAdditional ? '附加题' : '-'),
          },
        ],
        subjectiveQuestionColumns: [
          {
            title: '大题',
            key: 'topicName',
            align: 'center',
          },
          {
            title: '题号',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '小题号',
            key: 'branchName',
            align: 'center',
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '附加题',
            align: 'center',
            render: (h, params) => h('span', {}, params.row.isAdditional ? '附加题' : '-'),
          },
        ],
        structConfirmed: false,

        isModalCopyProjectSettingsVisible: false,
        modalExamDetails: [],

        isModalPaperAddObjectivesShow: false,

        matchStructs: [],
        modalResolveStructExamId: '',
        isModalPaperProjectSubjectQuestionsConflictsResolveShowed: false,
      }
    },

    computed: {
      modalTitle() {
        return (
          '科目配置' +
          (this.modalSettingSubjectConfig && this.modalSettingSubjectConfig.subjectName
            ? ' —— ' + this.modalSettingSubjectConfig.subjectName
            : '')
        )
      },
      subjectScoreCopyTypes() {
        return SubjectScoreCopyTypes.getEntries()
      },
      writtenFullScoreCanModify() {
        return ![
          SubjectScoreCopyTypes.QuestionScoreOnly.id,
          SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id,
        ].includes(this.config.copyType)
      },
      showFormItemWrittenFullScore() {
        return this.config.copyType !== SubjectScoreCopyTypes.OtherScoreOnly.id
      },
      showFormItemOtherFullScore() {
        return [
          SubjectScoreCopyTypes.OtherScoreOnly.id,
          SubjectScoreCopyTypes.WrittenScoreAndOtherScore.id,
          SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id,
        ].includes(this.config.copyType)
      },
      showFormItemQuestionScore() {
        return [
          SubjectScoreCopyTypes.QuestionScoreOnly.id,
          SubjectScoreCopyTypes.QuestionScoreAndOtherScore.id,
        ].includes(this.config.copyType)
      },
      optionalBranchType() {
        return BranchType.getIdNames().filter(x => x.id > 10 && x.id <= 13)
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(visible) {
        if (visible) {
          this.config = deepCopy(this.modalSettingSubjectConfig)
          if (!this.config.copyType) {
            this.config.copyType = SubjectScoreCopyTypes.WrittenScoreOnly.id
          }
          this.modalExamDetails = this.examDetails.map(d => ({
            id: d.examId,
            name: d.examName,
            subjectConfig: (d.subjects || []).find(s => s.subjectId === this.modalSettingSubjectConfig?.subjectId),
          }))
          if (this.modalSettingSubjectConfig?.projectSubjectSettings?.length) {
            this.matchStructs = deepCopy(this.modalSettingSubjectConfig.projectSubjectSettings)
            this.structConfirmed = true
          }
        } else {
          this.onCloseModal()
        }
      },

      copyUserSelectedProjectSetting(setting) {
        if (setting && this.config) {
          this.config.foreignSubjectBranch = setting.foreignSubjectBranch || false
          this.config.writtenFullScore = setting.writtenFullScore || 0
          this.config.otherFullScore = setting.otherFullScore || 0
          this.config.objectives = deepCopy(setting.objectives)
          this.config.subjectives = deepCopy(setting.subjectives)
        }
      },

      addObjectives(objectives = []) {
        // TODO
        if (objectives.length) {
          // nothing right now.
        }
      },
      addSubjectives() {},

      onQuestionStructConfirmedChanged(status) {
        this.structConfirmed = status

        this.matchStructs = []
        if (status) {
          this.matchStructs = this.modalExamDetails.map(detail => ({
            ...detail,
            mapper: {
              objectives: [],
              subjectives: [],
            },
            isConflictResolved: false,
          }))
        }
      },

      updateMatchProjectSubjectQuestionStructs(resolves = []) {
        this.matchStructs.forEach(item => {
          const TargetResolve = resolves.find(r => r.id === item.id)
          if (TargetResolve) {
            item.mapper = deepCopy(TargetResolve.mapper)
            item.isConflictResolved =
              item.subjectConfig.objectives.every(q =>
                item.mapper.objectives.some(r => r.srcQuestionCode === q.questionCode)
              ) &&
              item.subjectConfig.subjectives.every(sq =>
                item.mapper.subjectives.some(r =>
                  (r.srcQuestions || []).some(
                    mq => mq.questionCode === sq.questionCode && mq.branchCode === sq.branchCode
                  )
                )
              )
          }
        })
      },

      resolveProjectSubjectQuestionsConflicts(examId) {
        this.modalResolveStructExamId = examId
        this.isModalPaperProjectSubjectQuestionsConflictsResolveShowed = true
      },

      finishSubjectSetting() {
        if (this.showFormItemQuestionScore && this.matchStructs.some(struct => !struct.isConflictResolved)) {
          this.$Message.warning({
            duration: 3,
            content: '请先处理所有项目的小题冲突',
          })
          return
        }

        const NewConfig = deepCopy(this.config)
        NewConfig.projectSubjectSettings = this.matchStructs
        NewConfig.settingDone = true
        this.$emit('update-exam-subject-config', NewConfig)
        this.onCloseModal()
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" :title="modalTitle" fullscreen @on-visible-change="handleModalVisibleChange">
    <div class="modal-subject-setting">
      <Button
        ghost
        type="primary"
        size="small"
        class="tbtn-copy-project-settings"
        @click="isModalCopyProjectSettingsVisible = true"
        >复制源项目设置</Button
      >

      <Form :label-width="130" label-position="left" class="form">
        <FormItem label="是否选科">
          <Switch v-model="config.subjectType" :true-value="1" :false-value="0">
            <template #open>
              <span>是</span>
            </template>
            <template #close>
              <span>否</span>
            </template>
          </Switch>
        </FormItem>
        <FormItem label="是否分科外语">
          <Switch v-model="config.foreignSubjectBranch">
            <template #open>
              <span>是</span>
            </template>
            <template #close>
              <span>否</span>
            </template>
          </Switch>
        </FormItem>
        <FormItem label="成绩复制类型">
          <com-radio-group
            v-model="config.copyType"
            :radioes="subjectScoreCopyTypes"
            class="radio-group"
          ></com-radio-group>
        </FormItem>
        <template v-if="config.copyType">
          <FormItem v-if="showFormItemWrittenFullScore" label="笔试满分">
            <InputNumber
              v-if="writtenFullScoreCanModify"
              v-model="config.writtenFullScore"
              :min="0"
              :max="999"
              class="input-number"
            />
            <span v-else>{{ config.writtenFullScore }}</span>
          </FormItem>
          <FormItem v-if="showFormItemOtherFullScore" label="其它满分">
            <InputNumber v-model="config.otherFullScore" :min="0" :max="999" class="input-number" />
          </FormItem>
          <FormItem v-if="showFormItemQuestionScore" label="锁定试卷结构">
            <Switch :model-value="structConfirmed" @on-change="onQuestionStructConfirmedChanged">
              <template #open>
                <Icon type="md-lock" />
              </template>
              <template #close>
                <Icon type="md-unlock" />
              </template>
            </Switch>
          </FormItem>
          <FormItem v-if="showFormItemQuestionScore && structConfirmed" label="小题冲突处理">
            <template v-if="matchStructs.length">
              <div v-for="s of matchStructs" :key="s.id">
                <TextButton
                  :type="s.isConflictResolved ? 'primary' : 'warning'"
                  @click="resolveProjectSubjectQuestionsConflicts(s.id)"
                  >{{ s.name }}</TextButton
                >
              </div>
            </template>
            <div v-else>无</div>
          </FormItem>
          <FormItem v-if="showFormItemQuestionScore" label="客观题" class="padding-right-form-item">
            <div class="tool-bar">
              <TextButton
                v-show="!structConfirmed"
                type="primary"
                icon="md-add"
                @click="isModalPaperAddObjectivesShow = true"
                >新增客观题</TextButton
              >
            </div>
            <Table :columns="objectiveQuestionColumns" :data="config.objectives" border></Table>
          </FormItem>
          <FormItem v-if="showFormItemQuestionScore" label="主观题" class="padding-right-form-item">
            <Table :columns="subjectiveQuestionColumns" :data="config.subjectives" border></Table>
          </FormItem>
        </template>
      </Form>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary" @click="finishSubjectSetting">确定</Button>
    </template>

    <modal-subject-copy-project-settings
      v-model="isModalCopyProjectSettingsVisible"
      :struct-confirmed="structConfirmed"
      :project-subject-details="modalExamDetails"
      @copy-project-setting="copyUserSelectedProjectSetting"
    ></modal-subject-copy-project-settings>

    <modal-paper-add-objecives
      v-model="isModalPaperAddObjectivesShow"
      @add-objectives="addObjectives"
    ></modal-paper-add-objecives>

    <modal-paper-project-subject-questions-conflicts-resolves
      v-model="isModalPaperProjectSubjectQuestionsConflictsResolveShowed"
      :exam-id="modalResolveStructExamId"
      :standard-objectives="config && config.objectives"
      :standard-subjectives="config && config.subjectives"
      :match-structs="matchStructs"
      :copy-type="config.copyType"
      @update-subject-resolves="updateMatchProjectSubjectQuestionStructs"
    ></modal-paper-project-subject-questions-conflicts-resolves>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-subject-setting {
    .tbtn-copy-project-settings {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 1;
    }

    .form {
      .radio-group {
        padding-top: 4px;
        padding-bottom: 4px;
      }

      .padding-right-form-item {
        padding-right: 16px;

        .tool-bar {
          margin-bottom: 10px;
          text-align: right;
        }
      }
    }
  }
</style>
