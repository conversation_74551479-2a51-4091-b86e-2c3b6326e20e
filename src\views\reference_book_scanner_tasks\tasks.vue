<script>
  import { ElProgress } from 'element-plus'
  import NoData from '@/components/no_data'
  import SearchInput from '@/components/searchInput.vue'

  import { apiGetReferenceScannerTasks } from '@/api/emarking'
  import { apiGetSchoolReferenceBookByPage } from '@/api/qlib/reference_book'

  import iView from '@/iview'
  import Store from '@/store/index'

  import UploadModeEnum from '@/enum/emarking/upload_mode'

  export default {
    components: {
      NoData,
      SearchInput,
    },

    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      Store.dispatch('emarking/ensureBasicData').finally(() => {
        iView.LoadingBar.finish()
        next()
      })
    },

    data() {
      return {
        // 选中学年学期名称
        currentTermValue: '',
        // // 个人任教班级
        // classList: [],
        // // 选中班级
        // currentClassId: '',
        currentGradeId: '',
        // 选中学年学期任教相关教辅
        coachBooks: [],
        // 选中教辅
        currentCoachBookId: '',
        // 选中上传模式
        currentUploadModeId: '',
        // 关键词
        keyword: '',

        // 分页
        // pageSize: 10,
        currentPage: 1,

        // 教辅作业项目
        exerciseList: [],
        total: 1,
        loading: true,

        uploadModes: [
          {
            id: UploadModeEnum.PostScan.id,
            name: '先阅后扫',
          },
          {
            id: UploadModeEnum.Scan.id,
            name: '线上阅卷',
          },
        ],
      }
    },

    computed: {
      terms() {
        return this.$store.getters['emarking/terms']()
      },

      selectedTerm() {
        let _term = null

        if (this.terms && this.terms.length) {
          _term = this.terms.find(item => item.termName === this.currentTermValue) || null
        }

        return _term
      },

      grades() {
        return this.$store.getters['emarking/gradeSubjects']()
      },

      filterCoachBooks() {
        if (this.currentGradeId) {
          return this.coachBooks.filter(book => book.gradeId == this.currentGradeId)
        }
        return this.coachBooks
      },
    },

    created() {
      if (this.terms.length > 0) {
        this.currentTermValue = this.terms[0].termName
      }
      this.fetchCoachBooks()
      this.onChangePage()
    },

    methods: {
      // 查相关教辅
      fetchCoachBooks() {
        if (!this.selectedTerm) {
          return
        }
        return apiGetSchoolReferenceBookByPage({
          semesterId: this.selectedTerm.semesterId,
          term: this.selectedTerm.term,
          page: 1,
          size: 1000,
          onlyOpen: true,
        }).then(({ records }) => {
          this.coachBooks = records
        })
      },

      fetchTasks() {
        if (!this.selectedTerm) {
          return
        }

        this.loading = true
        apiGetReferenceScannerTasks({
          semesterId: this.selectedTerm.semesterId,
          term: this.selectedTerm.term,
          gradeId: this.currentGradeId || undefined,
          bookId: this.currentCoachBookId || undefined,
          uploadMode: this.currentUploadModeId || undefined,
          key: (this.keyword && this.keyword.trim()) || undefined,
          currentPage: this.currentPage,
          // pageSize: this.pageSize,
        })
          .then(response => {
            this.exerciseList = ((response && response.records) || []).map(item => {
              item.isPostScan = item.uploadMode == UploadModeEnum.PostScan.id
              item.uploadModeText = item.isPostScan ? '先阅后扫' : '线上阅卷'
              return item
            })
            this.total = (response && response.total) || 0
          })
          .catch(() => {
            this.exerciseList = []
            this.total = 0
          })
          .finally(() => (this.loading = false))
      },

      async onChangeTerm() {
        await this.fetchCoachBooks()
        this.resetCurrentCoachBook()
        this.onChangePage()
      },

      onChangeGrade() {
        this.resetCurrentCoachBook()
        this.onChangePage()
      },

      resetCurrentCoachBook() {
        if (this.filterCoachBooks.every(book => book.id !== this.currentCoachBookId)) {
          this.currentCoachBookId = ''
        }
      },

      onChangeCoachBook() {
        this.onChangePage()
      },

      onChangeUploadMode() {
        this.onChangePage()
      },

      onChangeKeyword() {
        this.onChangePage()
      },

      onChangePage(page = 1) {
        this.currentPage = page
        this.fetchTasks()
      },

      // 进入扫描页面
      gotoScanPage({ exam, mode }) {
        let examId
        let examSubjectId
        if (exam) {
          examId = exam.examId
          examSubjectId = exam.examSubjectId
        } else if (mode == 'online') {
          examId = 'newonline'
          examSubjectId = 'newonline'
        } else {
          examId = 'newpostscan'
          examSubjectId = 'newpostscan'
        }

        this.$router.push({
          name: 'scan-supplementary-exercise',
          params: {
            examId,
            examSubjectId,
          },
        })
      },
    },
  }
</script>

<template>
  <div class="container-reference-scanner-tasks">
    <div class="section-header">
      <div class="title">数智教辅扫描任务</div>
      <div class="actions">
        <Poptip placement="bottom-end">
          <Button type="primary" class="btn-normal-height btn-success" ghost icon="md-qr-scanner">扫描教辅作业</Button>
          <template #content>
            <div class="select-scan-mode-container">
              <Button class="btn-scan" type="default" @click="gotoScanPage({ mode: 'postScan' })"
                >先阅后扫（老师在纸质答题卡上批改主观题）</Button
              >
              <Button class="btn-scan" type="default" @click="gotoScanPage({ mode: 'online' })"
                >线上阅卷（老师在PC端或小程序批改主观题）</Button
              >
            </div>
          </template>
        </Poptip>
      </div>
    </div>

    <div class="filter-bar">
      <div class="filter-bar-left">
        <div class="filter-item">
          <span class="filter-item-label">学期</span>
          <Select v-model="currentTermValue" class="filter-item-body term" transfer @on-change="onChangeTerm">
            <Option v-for="t in terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
          </Select>
        </div>
        <!-- <div class="filter-item">
          <span class="filter-item-label">班级</span>
          <Select v-model="currentClassId" class="filter-item-body" clearable transfer @on-change="onChangeClass">
            <Option v-for="cls in classList" :key="cls.classId" :value="cls.classId">{{ cls.className }}</Option>
          </Select>
        </div> -->
        <div class="filter-item">
          <span class="filter-item-label">年级</span>
          <Select v-model="currentGradeId" class="filter-item-body" clearable transfer @on-change="onChangeGrade">
            <Option v-for="g of grades" :key="g.id" :value="g.id">{{ g.name }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">教辅图书</span>
          <Select
            v-model="currentCoachBookId"
            class="filter-item-body book"
            clearable
            transfer
            @on-change="onChangeCoachBook"
          >
            <Option v-for="book in filterCoachBooks" :key="book.id" :value="book.id">{{ book.bookName }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">阅卷模式</span>
          <Select
            v-model="currentUploadModeId"
            class="filter-item-body upload-mode"
            clearable
            transfer
            @on-change="onChangeUploadMode"
          >
            <Option v-for="m in uploadModes" :key="m.id" :value="m.id">{{ m.name }}</Option>
          </Select>
        </div>
      </div>
      <SearchInput
        v-model="keyword"
        class="filter-bar-right"
        placeholder="测验名称"
        clearable
        @on-change="onChangeKeyword"
      ></SearchInput>
    </div>

    <div class="section-content">
      <div v-for="e of exerciseList" :key="e.examId" class="exercise-item">
        <div class="box-icon">
          <img src="@/assets/images/supplementary_exercise/supplementary_exercise_item_icon.svg" alt="" />
        </div>
        <div class="info">
          <div class="info-top">
            <span class="test-title" @click="gotoReportPage(e)">{{ e.examName }}</span>
            <span class="grade-subject-text">{{ e.gradeName }}{{ e.subjectName }}</span>
            <span class="upload-mode" :class="{ 'post-scan': e.isPostScan }">{{ e.uploadModeText }}</span>
          </div>
          <div class="info-bottom">
            <div class="info-bottom-item">已扫人数：{{ e.scanned || 0 }} / {{ e.total || 0 }}</div>
          </div>
        </div>
        <div class="text-operations">
          <Button ghost class="btn btn-info" @click="gotoScanPage({ exam: e })">扫描</Button>
        </div>
      </div>
      <NoData v-if="total == 0" :visible="!loading">暂无数智教辅项目</NoData>
    </div>

    <div v-if="total > 0" class="section-page">
      <Page :model-value="currentPage" :total="total" show-total @on-change="onChangePage"></Page>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .container-reference-scanner-tasks {
    padding: 10px 20px 20px;
    background-color: white;
  }

  .section-header {
    @include flex(row, space-between, center);
    margin-bottom: 15px;
    padding: 0 0 10px;
    border-bottom: 1px solid #dfdfdf;

    .title {
      font-size: $font-size-medium-x;
    }

    .actions {
      .btn-success {
        border-color: #13ce66;
        color: #13ce66;
        background-color: #e8faf0;
      }

      .btn-info {
        border-color: #409eff;
        color: #409eff;
        background-color: #ecf5ff;
      }

      .btn-normal-height {
        height: 28px;
        font-size: 14px;
      }

      .select-scan-mode-container {
        padding: 12px 8px 6px 8px;

        .btn-scan {
          display: block;

          &:not(:first-child) {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .filter-bar {
    @include flex(row, space-between, center); // can change
    margin-bottom: 16px;

    .filter-bar-left {
      @include flex(row, flex-start, center);

      .filter-item {
        @include flex(row, flex-start, center);
        margin-right: 20px;

        .filter-item-label {
          flex-grow: 0;
          flex-shrink: 0;
          margin-right: 10px;
        }

        .filter-item-body {
          width: 100px;

          &.term {
            width: 250px;
          }

          &.book {
            width: 300px;
          }

          &.sort {
            width: 120px;
          }
        }
      }
    }
  }

  .section-content {
    .exercise-item {
      @include flex(row, flex-start, center);
      padding: 24px;
      border: 1px solid transparent;
      border-radius: 4px;
      box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        border-color: $color-primary;
        transform: scale(1.02);
        transform-origin: center center;
      }

      &:not(:last-child) {
        margin-bottom: 24px;
      }

      .box-icon {
        flex-shrink: 0;
        margin-right: 16px;
        font-size: 0;

        img {
          width: 74px;
          height: 74px;
        }
      }

      .info {
        flex-grow: 1;
        flex-shrink: 1;
        margin-right: 16px;

        .info-top {
          min-height: 30px;
          margin-bottom: 16px;

          .test-title {
            font-weight: bold;
            font-size: $font-size-medium-x;
          }

          .grade-subject-text {
            display: inline-block;
            margin-left: 4px;
            padding: 0.3em 0.6em;
            border: 1px solid #36a390;
            border-radius: 1.2em;
            color: #36a390;
            line-height: 1.3;
            text-align: center;
          }

          .upload-mode {
            display: inline-block;
            margin-left: 4px;
            padding: 0.3em 0.6em;
            border: 1px solid #13ce66;
            border-radius: 4px;
            color: #13ce66;
            line-height: 1.3;
            text-align: center;
            background-color: #e8faf0;

            &.post-scan {
              border-color: $color-warning;
              color: $color-warning;
              background-color: #fff7eb;
            }
          }
        }

        .info-bottom {
          @include flex(row, flex-start, center);
          height: 20px;
          color: $color-icon;
          font-size: 12px;
        }
      }

      .text-operations {
        @include flex(row, flex-end, center);
        flex-shrink: 0;

        .btn-text-color {
          border-color: #dbdbdb;
          color: $color-content;
          background-color: rgba(255, 255, 255, 0.4);
        }

        .btn-setting {
          margin-right: 24px;
        }

        .btn-info {
          border-color: #409eff;
          color: #409eff;
          background-color: #ecf5ff;
        }

        .btn-success {
          border-color: #13ce66;
          color: #13ce66;
          background-color: #e8faf0;
        }

        .btn {
          height: 28px;
        }

        .progress-bar {
          width: 100px;
          margin-right: 20px;
          margin-left: 8px;

          :deep(.el-progress__text) {
            min-width: 28px;
            font-size: 13px !important;
          }
        }
      }
    }
  }

  .section-page {
    margin-top: 24px;
    text-align: right;
  }
</style>
