<template>
  <Modal
    class="modal-award-setting"
    :model-value="modelValue"
    title="奖项设置"
    :width="700"
    @on-visible-change="handleChangeVisibility"
  >
    <div class="container-content">
      <div class="box-category">
        <Tabs v-model="currentCategoryId" @on-click="onChangeCategory">
          <TabPane v-for="item in categories" :key="item.id" :label="item.name" :name="item.id"></TabPane>
        </Tabs>
      </div>
      <div class="box-tab">
        <RadioGroup v-model="activeTab" type="button" button-style="solid" @on-change="onTabChange">
          <Radio v-for="tab of configTypes" :key="tab.value" :label="tab.value">{{ tab.name }}</Radio>
        </RadioGroup>
        <!-- <Select v-model="currentCategoryId" style="width: 120px; margin-left: 40px" @on-change="onChangeCategory">
          <Option v-for="item in categories" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select> -->
      </div>
      <div class="box-config">
        <div v-for="(item, idx) in configItems" :key="item.sortOrder" class="config-item">
          <div class="item-name">
            <span>奖项{{ idx + 1 }}：</span>
            <Input v-model="item.name" placeholder="奖项名称" style="width: 130px" @on-blur="onInputBlur" />
          </div>
          <div class="item-count">
            <template v-if="item.awardType === 1">
              <span>占比（%）：</span>
              <InputNumber v-model="item.percent" :max="100" style="width: 100px" @on-blur="onInputBlur" />
            </template>
            <template v-else-if="item.awardType === 2">
              <span>人数：</span>
              <InputNumber v-model="item.fixedCount" style="width: 100px" @on-blur="onInputBlur" />
            </template>
            <template v-else>
              <span>分数（大于等于）：</span>
              <InputNumber v-model="item.fixedScore" style="width: 100px" @on-blur="onInputBlur" />
            </template>
          </div>
          <div class="item-action">
            <TextButton class="btn-del" icon="ios-trash" @click="onConfigItemDel(idx)"></TextButton>
          </div>
        </div>
      </div>
      <div class="box-btn">
        <Button icon="md-add" @click="onConfigAdd">添加奖项</Button>
      </div>
    </div>
    <template #footer>
      <div class="modal-footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK(false)">确认提交</Button>
      </div>
    </template>
  </Modal>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import store from '@/store/index'
  import { Message, Modal } from 'view-ui-plus'
  import { apiGetAwardConfig, apiSaveAwardConfig } from '@/api/review/activity'
  import RevewAwardStatusEnum from '@/enum/review/review_award_status'

  defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const configTypes = ref([
    {
      value: 1,
      name: '按人数占比',
    },
    {
      value: 2,
      name: '按人数',
    },
    {
      value: 3,
      name: '按分数',
    },
  ])
  const activeTab = ref(1)
  const configItems = ref([])
  const currentCategoryId = ref('')

  const currentActivity = computed(() => {
    return store.getters['review/currentActivity']
  })
  const activityId = computed(() => {
    return store.getters['review/activityId']
  })
  const categories = computed(() => {
    return store.getters['review/categories']
  })
  const awardConfig = computed(() => {
    return store.getters['review/awardConfig']
  })

  // 取消
  function handleCancel() {
    let configs = { ...awardConfig.value }
    configs[currentCategoryId.value] = configItems.value
    store.dispatch('review/setAwardConfig', configs)
    emit('update:modelValue', false)
  }
  // 关闭弹窗
  function handleChangeVisibility(visible) {
    if (!visible) {
      handleCancel()
    }
  }
  // 确定
  async function handleOK() {
    let params
    try {
      await store.dispatch('review/refreshActivityDetail')
      params = checkParams()
    } catch (e) {
      if (e.type === 'message') {
        Message.warning({
          content: e.msg,
          duration: 3,
        })
      } else if (e.type === 'modal') {
        Modal.confirm({
          title: '更改奖项设置',
          content: e.msg,
          onOk: async () => {
            let result = []
            for (let key in awardConfig.value) {
              result = result.concat(awardConfig.value[key])
            }

            params = result.map(item => ({
              ...item,
              percent: item.awardType === 1 && item.percent != null ? item.percent / 100 : null,
            }))
            await apiSaveAwardConfig(params)
            Message.success('已保存')
            store.dispatch('review/refreshActivityDetail')
            store.dispatch('review/clearAwardConfig')
            handleCancel()
          },
        })
      }
      return
    }
    try {
      await apiSaveAwardConfig(params)
      Message.success('已保存')
      store.dispatch('review/clearAwardConfig')
    } finally {
      // emit('on-add-users')
    }

    handleCancel()
  }

  function checkParams() {
    if (currentActivity.value.awardStatus === RevewAwardStatusEnum.AwardInProgress.id) {
      // 生成中
      throw {
        type: 'message',
        msg: '评审结果生成中，请稍后再设置',
      }
    }

    if (currentActivity.value.awardStatus === RevewAwardStatusEnum.AwardSuccess.id) {
      // 生成成功
      throw {
        type: 'modal',
        msg: '更改奖项设置将清空已有评审结果，需重新生成结果，是否确认提交？',
      }
    }

    let result = []
    for (let key in awardConfig.value) {
      result = result.concat(awardConfig.value[key])
      const theCategory = categories.value.find(c => c.id === key)

      if (awardConfig.value[key].some(item => !item.name)) {
        throw {
          type: 'message',
          msg: `请把 ${theCategory.name} 类别中的奖项名称填写完整`,
        }
      }

      if (awardConfig.value[key].some(item => item.awardType === 1 && item.percent == null)) {
        throw {
          type: 'message',
          msg: `请把 ${theCategory.name} 类别中的人数占比填写完整`,
        }
      }

      if (awardConfig.value[key].some(item => item.awardType === 2 && item.fixedCount == null)) {
        throw {
          type: 'message',
          msg: `请把 ${theCategory.name} 类别中的人数填写完整`,
        }
      }

      if (awardConfig.value[key].some(item => item.awardType === 3 && item.fixedScore == null)) {
        throw {
          type: 'message',
          msg: `请把 ${theCategory.name} 类别中的分数填写完整`,
        }
      }

      if (new Set(awardConfig.value[key].map(item => item.name)).size !== awardConfig.value[key].length) {
        throw {
          type: 'message',
          msg: `${theCategory.name} 类别中的奖项名称不能重复`,
        }
      }
    }

    return result.map(item => ({
      ...item,
      percent: item.awardType === 1 && item.percent != null ? item.percent / 100 : null,
    }))
  }

  function onTabChange() {
    configItems.value.forEach(item => {
      item.awardType = activeTab.value
    })
  }

  function onChangeCategory() {
    fetchAwardConfig()
  }

  function onConfigAdd() {
    configItems.value.push({
      activityId: activityId.value,
      awardType: activeTab.value,
      categoryId: currentCategoryId.value,
      fixedCount: null,
      fixedScore: null,
      minScore: null,
      name: '',
      percent: null,
      sortOrder: configItems.value.length + 1,
    })
  }

  function onConfigItemDel(idx) {
    configItems.value.splice(idx, 1)
  }

  function fetchAwardConfig() {
    apiGetAwardConfig({
      activityId: activityId.value,
      categoryId: currentCategoryId.value,
    }).then(res => {
      if (Array.isArray(res) && res.length) {
        configItems.value = res.map(item => ({
          ...item,
          percent: item.percent != null ? Math.round(item.percent * 1000) / 10 : null,
        }))
        activeTab.value = res[0]?.awardType || 1

        let configs = { ...awardConfig.value }
        configs[currentCategoryId.value] = configItems.value
        store.dispatch('review/setAwardConfig', configs)
      } else {
        let theAwardConfigItems = awardConfig.value[currentCategoryId.value]

        if (theAwardConfigItems) {
          configItems.value = theAwardConfigItems
          activeTab.value = theAwardConfigItems[0]?.awardType || 1
        } else {
          configItems.value = []
        }
      }
    })
  }

  function onInputBlur() {
    let configs = { ...awardConfig.value }
    configs[currentCategoryId.value] = configItems.value
    store.dispatch('review/setAwardConfig', configs)
  }

  watch(
    categories.value,
    val => {
      if (Array.isArray(val) && val.length) {
        currentCategoryId.value = val[0].id
        fetchAwardConfig()
      }
    },
    {
      immediate: true,
      deep: true,
    }
  )

  watch(
    currentCategoryId,
    (newVal, oldVal) => {
      if (!oldVal) return
      let configs = { ...awardConfig.value }
      configs[oldVal] = configItems.value
      store.dispatch('review/setAwardConfig', configs)
    },
    {
      immediate: true,
    }
  )
</script>

<style lang="scss" scoped>
  .box-tab {
    margin-bottom: 8px;
  }

  .box-config {
    margin-bottom: 18px;

    .config-item {
      @include flex(row, flex-start, center);
      padding: 10px 0;
      border-bottom: 1px solid #dfdfdf;
    }

    .item-name,
    .item-count {
      @include flex(row, flex-start, center);
      margin-right: 20px;
    }

    .item-action {
      @include flex(row, flex-end, center);
      flex: 1;
    }

    .btn-del {
      &:hover {
        color: $color-error;
      }
    }
  }
</style>

<style lang="scss">
  .modal-award-setting {
    .ivu-modal-body {
      padding-top: 6px;
    }
  }
</style>
