<template>
  <div class="ckeditor-instance" contenteditable="true" @focus="handleFocus" @blur="handleBlur"></div>
</template>

<script>
  // ckeditor自定义插件处理粘贴用到清理dom方法
  import { QDOM } from '@/helpers/qlib/qdom'
  window._qdomClone = QDOM.clone

  export default {
    name: 'CKEditor',
    props: {
      type: {
        type: String,
        default: 'inline',
      },
      value: {
        type: String,
        default: '',
      },
      config: {
        type: Object,
        default: () => ({}),
      },
    },
    emits: ['change', 'ready', 'focus', 'blur'],
    data() {
      return {
        editor: null,
        lastEditorData: undefined,
      }
    },
    watch: {
      value() {
        if (this.value !== this.lastEditorData) {
          this.lastEditorData = this.value
          this.editor.setData(this.value)
        }
      },
    },
    mounted() {
      this.lastEditorData = this.value
      this.$el.innerHTML = this.value
      this.createEditor()
    },
    beforeUnmount() {
      if (this.editor) {
        this.editor.destroy()
        this.editor = null
      }
    },
    methods: {
      createEditor() {
        if (!window.CKEDITOR || window.CKEDITOR.status === 'unloaded') {
          return
        }
        this.editor = window.CKEDITOR.inline(this.$el, {
          allowedContent: true,
          enterMode: window.CKEDITOR.ENTER_P,
          extraPlugins: 'answer-sheet-mathjax, lineheight, image3, newimage, text-emphasis, wavy-underline',
          removePlugins: 'mathjax, image, uploadimage',
          //[字号、行距], [加粗、倾斜、下划线、上标、下标], [居左、居中、居右], [图片、表格、特殊符号], [数学公式]
          toolbar: [
            {
              name: 'fontline',
              items: ['Font', 'FontSize', 'lineheight'],
            },
            {
              name: 'basicstyles',
              items: [
                'Bold',
                'Italic',
                'Underline',
                'Subscript',
                'Superscript',
                'text-emphasis',
                'wavy-underline',
                'RemoveFormat',
              ],
            },
            {
              name: 'Justify',
              items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            },
            {
              name: 'insert',
              items: ['Image', 'NewImage', 'Table', 'SpecialChar', 'AnswerSheetMathjax'],
            },
            {
              name: 'answer-sheet-mathjax',
              items: ['answer-sheet-mathjax'],
            },
            {
              name: 'save',
              items: ['Undo', 'Redo'],
            },
          ],
          font_names:
            '中文宋体+英文Times/"Times New Roman",Times,宋体,simsun,serif;中文宋体+英文宋体/宋体,simsun,serif;中文楷体+英文Times/"Times New Roman",Times,楷体,KaiTi,SimKai,serif;中文黑体+英文Times/"Times New Roman",Times,黑体,sans-serif;拼音/pinyin,"Times New Roman",serif',
        })
        this.editor.on('change', () => {
          this.lastEditorData = this.editor.getData()
          if (this.value !== this.lastEditorData) {
            this.$emit('change', this.lastEditorData)
          }
        })
        this.editor.on('instanceReady', () => {
          this.$emit('ready')
        })
      },
      handleFocus() {
        this.$emit('focus')
      },
      handleBlur() {
        this.$emit('blur')
      },
    },
  }
</script>

<style lang="scss" scoped>
  // .ckeditor-instance {
  //   padding: 10px;
  // }

  .ckeditor-instance:focus {
    outline: none;
  }

  .ckeditor-instance:after {
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    line-height: 0;
    visibility: hidden;
    content: '';
  }

  /** 着重号 */

  :deep(.char-emphasis) {
    position: relative;
    display: inline-block;
  }

  :deep(.char-emphasis::after) {
    position: absolute;
    bottom: 0;
    left: 50%;
    font-size: 12px;
    line-height: 4px;
    transform: translateX(-50%);
    content: '•';
  }

  /** 波浪线 */

  :deep(.wavy-underline) {
    position: relative;
    display: inline-block;
  }

  :deep(.wavy-underline::after) {
    position: absolute;
    bottom: 0px; /* 控制波浪线与文字的间距 */
    left: 0;
    width: 400%;
    height: 20px; /* 波浪线的高度 */
    background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='40' height='10' viewBox='0 0 40 10'><path d='M0 5 Q5 0, 10 5 T20 5 T30 5 T40 5' fill='none' stroke='black' stroke-width='2'/></svg>");
    background-repeat: repeat-x;
    background-size: 4em 20px; /* 确保波浪线与字体大小匹配 */
    transform: scale(0.25); /* 背景图片尺寸设为实际尺寸的4倍再缩小，避免html2canvas导出后模糊 */
    transform-origin: left bottom;
    content: '';
  }

  /** 下划线与文字距离 */

  :deep(u, [style*='underline']) {
    text-underline-offset: 2px;
  }
</style>

<style>
  @font-face {
    font-family: 'pinyin';
    src: url('@/assets/font/pinyin.ttf');
  }
</style>
