<template>
  <div class="container-judge-audit">
    <div class="section-action-bar">
      <div class="action-bar-left">
        <Button type="primary" ghost icon="md-add" @click="onJudgeAdd">添加评委</Button>
        <Button class="btn-batch-del" type="error" ghost icon="md-trash" @click="onBatchDelete">批量删除</Button>
        <Button class="btn-batch-del" icon="ios-create-outline" @click="onBatchEdit">批量调整最高任务量</Button>
      </div>
      <div class="action-bar-right">
        <div class="category-bar">
          <RadioGroup v-model="currentCategory" type="button" button-style="solid" @on-change="onCategoryChange">
            <Radio v-for="c of categories" :key="c.id" :label="c.id">{{ c.name }}</Radio>
          </RadioGroup>
        </div>
        <div class="search-bar">
          <Input
            v-model="searchKeyword"
            clearable
            placeholder="请输入姓名或手机号查找"
            style="width: 250px"
            @on-enter="onSearch"
            @on-clear="onSearch"
          />
        </div>
      </div>
    </div>
    <div class="section-table">
      <Table :data="tableData" :columns="tableColumns" @on-selection-change="onSelectionChange">
        <template #action="{ row }">
          <TextButton type="error" @click="onDelete(row)">删除</TextButton>
        </template>
      </Table>
      <div class="box-page">
        <Page
          v-model="pageNum"
          :total="total"
          :page-size="pageSize"
          show-total
          show-sizer
          show-elevator
          :page-size-opts="[15, 30, 60, 100]"
          @on-page-size-change="changePageSize"
          @on-change="changePage"
        />
      </div>
    </div>

    <Modal v-model="modalMaxTaskEditVisible" title="调整最高任务量">
      <div class="container-content">
        <div class="box-input">
          <span class="label">最高任务量：</span>
          <InputNumber v-model="maxTask" :min="1" style="width: 150px" />
        </div>
      </div>
      <template #footer>
        <div class="container-footer">
          <Button @click="modalMaxTaskEditVisible = false">取消</Button>
          <Button type="primary" @click="onMaxTaskEditConfirm">修改</Button>
        </div>
      </template>
    </Modal>

    <ModalJudgeAdd v-model="modalJudgeAddVisible" :active-category="currentCategory" @on-refresh="onRefresh" />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import TextButton from '@/components/text_button'
  import ModalJudgeAdd from './modal_judge_add.vue'
  import { apiGetChildOrgAndSchool } from '@/api/user/school'
  import { apiGetJudges, apiDeleteJudges, apiUpdateMaxTask } from '@/api/review/judge'
  import { deepCopy } from '@/utils/object'
  import JudgeStatus from '@/enum/review/judge_status'

  export default {
    components: {
      ModalJudgeAdd,
    },
    data() {
      return {
        tableData: [],
        currentCategory: '0',
        modalJudgeAddVisible: false,
        searchKeyword: '',
        pageNum: 1,
        pageSize: 15,
        total: 0,
        selection: [],
        modalMaxTaskEditVisible: false,
        maxTask: null,
        currentEditJudges: [],
      }
    },
    computed: {
      ...mapGetters('review', ['currentActivity']),
      schoolId() {
        return this.$store.state.user.schoolId || ''
      },
      tableColumns() {
        let columns = [
          {
            type: 'selection',
            width: '50px',
          },
          {
            title: '姓名',
            key: 'realName',
          },
          {
            title: '类别',
            key: 'categoryName',
          },
          {
            title: '学校',
            key: 'schoolName',
          },
          {
            title: '手机号',
            key: 'mobile',
          },
          {
            title: '最高任务量',
            key: 'maxTask',
            render: (h, params) => {
              return h(
                TextButton,
                {
                  type: 'primary',
                  onClick: () => {
                    this.modalMaxTaskEditVisible = true
                    this.currentEditJudges = [params.row]
                    this.maxTask = params.row.maxTask
                  },
                },
                () => params.row.maxTask
              )
            },
          },
          {
            title: '审核情况',
            render: (h, params) => {
              let statusText = ''
              let color = '#515a6e'
              if (params.row.status === JudgeStatus.AuditPending.id) {
                statusText = '待审核'
              } else if (params.row.status === JudgeStatus.AuditApproved.id) {
                statusText = '审核通过'
                color = '#06C270'
              } else if (params.row.status === JudgeStatus.AuditRejected.id) {
                statusText = '审核不通过'
                color = '#F56C6C'
              }

              return h(
                'span',
                {
                  style: {
                    color,
                  },
                },
                statusText
              )
            },
          },
          {
            title: '操作',
            slot: 'action',
          },
        ]

        return columns
      },
      categories() {
        let list = deepCopy(this.$store.getters['review/categories'] || [])
        if (Array.isArray(list) && list.length && !list.some(c => c.id === '0')) {
          list.unshift({
            id: '0',
            name: '全部',
          })
        }
        return list
      },
    },
    created() {
      this.getSchoolTree()
      this.getJudges()
    },
    methods: {
      changePage(page) {
        this.pageNum = page
        this.getJudges()
      },
      changePageSize(size) {
        this.pageNum = 1
        this.pageSize = size
        this.getJudges()
      },
      onSearch() {
        this.pageNum = 1
        this.getJudges()
      },
      onJudgeAdd() {
        this.modalJudgeAddVisible = true
      },
      onCategoryChange() {
        this.pageNum = 1
        this.getJudges()
      },
      buildTree(data = []) {
        return data.map(item => {
          const children = item.schools ? this.buildTree(item.schools) : []

          return {
            ...item,
            schools: children,
          }
        })
      },
      getSchoolTree() {
        return apiGetChildOrgAndSchool({
          eduSchoolId: this.schoolId,
        })
          .then(res => {
            let children = this.buildTree(res)
            let schoolTree = [
              {
                schoolName: this.$store.state.user.schoolName,
                schoolId: this.schoolId,
                schools: children || [],
                schoolType: this.$store.state.user.schoolType,
              },
            ]

            this.$store.dispatch('review/setFlattenSchoolList', schoolTree)
          })
          .catch(() => {
            this.schoolTree = []
          })
      },
      onRefresh() {
        this.pageNum = 1
        this.getJudges()
      },
      getJudges() {
        const { pageNum, pageSize, currentCategory, searchKeyword } = this

        apiGetJudges({
          activityId: this.currentActivity?.id,
          categoryId: currentCategory === '0' ? undefined : currentCategory,
          keyword: searchKeyword,
          pageNum,
          pageSize,
        }).then(res => {
          this.tableData = res.list
          this.total = Number(res.total)
        })
      },
      onDelete(row) {
        this.$Modal.confirm({
          title: '删除评委',
          content: `确定删除 ${row.realName} 吗？`,
          onOk: () => {
            apiDeleteJudges({
              ids: [row.id],
            }).then(() => {
              this.$Message.success('已删除')
              this.getJudges()
            })
          },
        })
      },
      onBatchDelete() {
        if (!this.selection.length) {
          this.$Message.info('未选择需要删除的评委')
          return
        }

        this.$Modal.confirm({
          title: '删除评委',
          content: `确定删除已勾选的评委吗？`,
          onOk: () => {
            apiDeleteJudges({
              ids: this.selection.map(item => item.id),
            }).then(() => {
              this.$Message.success('已删除')
              this.getJudges()
              this.selection = []
            })
          },
        })
      },
      onBatchEdit() {
        if (!this.selection.length) {
          this.$Message.info('未选择需要调整的评委')
          return
        }
        this.currentEditJudges = deepCopy(this.selection)
        this.modalMaxTaskEditVisible = true
      },
      onSelectionChange(selection) {
        this.selection = selection
      },
      onMaxTaskEditConfirm() {
        if (!this.maxTask) {
          this.$Message.info('请输入最高任务量')
          return
        }

        let params = this.currentEditJudges.map(item => ({
          id: item.id,
          count: this.maxTask,
        }))
        apiUpdateMaxTask(params).then(() => {
          this.modalMaxTaskEditVisible = false
          this.selection = []
          this.$Message.success('已修改')
          this.getJudges()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-judge-audit {
    .section-action-bar {
      @include flex(row, space-between, center);
      margin-bottom: 20px;

      .action-bar-left,
      .action-bar-right {
        @include flex(row, flex-start, center);
      }

      .btn-batch-del {
        margin-left: 10px;
      }

      .category-bar {
        margin-right: 20px;
      }
    }

    .box-page {
      @include flex(row, flex-end, center);
      margin-top: 20px;
    }
  }
</style>
