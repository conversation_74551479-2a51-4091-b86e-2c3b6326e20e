<script>
  import { apiGetExamPaperPicAndMarkSituation } from '@/api/report'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: true,
      },

      schoolName: {
        type: String,
        default: '',
      },

      className: {
        type: String,
        default: '',
      },

      reportName: {
        type: String,
        default: '',
      },

      templateId: {
        type: String,
        default: '',
      },

      examSubjectId: {
        type: String,
        default: '',
      },

      studentId: {
        type: String,
        default: '',
      },

      studentName: {
        type: String,
        default: '',
      },

      studentAdmissionNum: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        modalWidth: Math.min(window.innerWidth - 200, window.innerWidth * 0.85),
        modalStyle: {
          top: (window.innerHeight * 0.125) / 2 + 'px',
        },

        paperScore: null,
        scaleScore: null,

        tableData: [],
        tableColumns: [
          {
            title: '题名',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '参考答案',
            key: 'standardAnswer',
            align: 'center',
          },
          {
            title: '学生答案',
            key: 'studentAnswer',
            align: 'center',
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '得分',
            key: 'studentScore',
            align: 'center',
          },
        ],
        tableMaxHeight: Math.min(window.innerHeight - 180, window.innerHeight * 0.85),

        subjectives: [],
        imagePanelStyle: {
          height: Math.min(window.innerHeight - 180, window.innerHeight * 0.85) + 'px',
          'overflow-y': 'auto',
        },
      }
    },

    computed: {
      templateSubjects() {
        return this.$store.getters['report/templateSubjects'] || []
      },

      subjectName() {
        return (this.templateSubjects.find(ts => ts.examSubjectId === this.examSubjectId) || { subjectName: '' })
          .subjectName
      },

      examGradeId() {
        return this.$store.state.report?.exam?.grade?.id || 0
      },

      showGradeAssignmentExplain() {
        return (
          this.examGradeId > 9 &&
          this.examGradeId < 13 &&
          this.scaleScore &&
          this.subjectName &&
          ['政治', '地理', '化学', '生物'].includes(this.subjectName)
        )
      },
    },

    watch: {
      modelValue() {
        if (this.modelValue) {
          this.tableData = []
          this.subjectives = []

          apiGetExamPaperPicAndMarkSituation({
            templateId: this.templateId,
            examSubjectId: this.examSubjectId,
            studentId: this.studentId,
            reportName: this.reportName,
          }).then(response => {
            this.scaleScore = response.totalScoreMap && response.totalScoreMap.scaleScore
            this.paperScore = response.totalScoreMap && response.totalScoreMap.paperScore

            this.tableData = ((response && response.stuObjectiveVos) || []).map(s => ({
              questionName: s.quesName,
              standardAnswer: s.standardAnswer
                .split(';')
                .filter(a => a && a.includes('='))
                .map(a => a.split('=')[0])
                .join(''),
              studentAnswer: s.studentAnswer,
              fullScore: s.fullScore,
              studentScore: s.score,
            }))

            this.subjectives = ((response && response.stuSubjectiveVos) || []).map(s => ({
              blockId: s.blockId,
              blockName: s.blockName + '：' + s.score + '分（满分：' + s.fullScore + '分）',
              imgUrl: s.imgUrl,
            }))
          })
        }
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :mask-closable="false"
    :closable="false"
    :width="modalWidth"
    :style="modalStyle"
    footer-hide
  >
    <template #header>
      <div class="modal-header">
        <div class="modal-title">
          考生原卷<span v-if="subjectName">&nbsp;-&nbsp;{{ subjectName }}</span>
          <span class="student-attributes">
            （{{ schoolName + ', ' + className + ', ' + studentAdmissionNum + ', ' + studentName }}）
          </span>
          <span v-if="showGradeAssignmentExplain" class="grade-assignment-explain"
            >原始分：{{ (paperScore && Number(paperScore)) || '-' }} ； 等级分：{{
              (scaleScore && Number(scaleScore)) || '-'
            }}</span
          >
        </div>
        <Icon class="close-icon" type="md-close" @click="closeModal" />
      </div>
    </template>

    <div class="modal-content">
      <div class="left-panel">
        <Table :columns="tableColumns" :data="tableData" :max-height="tableMaxHeight"></Table>
      </div>

      <div class="right-panel" :style="imagePanelStyle">
        <div v-for="block in subjectives" :key="block.blockId" class="block-item">
          <div class="block-name" :class="{ 'no-img': !block.imgUrl }">{{ block.blockName }}</div>
          <img v-if="block.imgUrl" :src="block.imgUrl" />
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-header {
    @include flex(row, space-between, center);
    padding: 0 1%;
    font-size: $font-size-large-s;

    .modal-title {
      min-width: 450px;
    }

    .student-attributes {
      font-size: $font-size-medium;
    }

    .grade-assignment-explain {
      color: $color-primary;
      font-size: $font-size-medium;
    }

    .close-icon {
      font-size: $font-size-large-x;
    }

    .close-icon:hover {
      color: $color-primary-light;
      cursor: pointer;
    }
  }

  .modal-content {
    @include flex(row, space-between, stretch);

    .left-panel {
      min-width: 400px;
      max-width: 410px;
      margin-right: 6px;
    }

    .right-panel {
      min-width: 400px;

      .block-item {
        &:not(:first-child) {
          margin-top: 16px;
        }

        .block-name {
          color: $color-error;
          font-weight: bold;
          font-size: $font-size-large;

          &.no-img::after {
            content: '（无图）';
          }
        }

        img {
          width: 100%;
        }
      }
    }
  }
</style>
