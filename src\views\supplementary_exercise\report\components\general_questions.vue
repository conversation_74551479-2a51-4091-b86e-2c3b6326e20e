<template>
  <div class="card-general-questions">
    <div class="card-title">小题得分分析</div>
    <div class="card-content">
      <div
        v-for="q in questions"
        :key="q.questionName"
        class="question-item"
        title="点击查看题目讲评"
        @click="toQuestion(q)"
      >
        <div class="circle" :class="{ warning: q.warning }" :style="q.style">
          <div class="score-rate">{{ q.scoreRatePercent }}%</div>
        </div>
        <div class="question-name">{{ q.questionName }}</div>
      </div>
    </div>
    <div class="card-footer">
      <span class="warning-sample" :style="warningSampleStyle"></span>
      色的题目得分率小于科目总分得分率 {{ paperScoreRatePercent }}%
    </div>
  </div>
</template>

<script>
  import { roundNumber } from '@/utils/math'

  export default {
    props: {
      classDetail: Object,
    },
    emits: ['to-question'],
    data() {
      return {
        normalBackgroundColor1: '#05C1AE',
        normalBackgroundColor2: '#cafffa',
        warningBackgroundColor1: 'rgba(255, 59, 59, 0.60)',
        warningBackgroundColor2: 'rgba(255, 225, 203, 1)',
      }
    },
    computed: {
      paperScoreRate() {
        return this.classDetail.avgScore / this.classDetail.fullScore
      },
      paperScoreRatePercent() {
        return roundNumber(this.paperScoreRate * 100, 0)
      },
      questions() {
        return this.classDetail.quesScores.map(q => {
          let scoreRatePercent = roundNumber(q.scoreRate * 100, 0)
          let warning = q.scoreRate < this.paperScoreRate
          let backgroundColor1 = this.normalBackgroundColor1
          let backgroundColor2 = this.normalBackgroundColor2
          if (warning) {
            backgroundColor1 = this.warningBackgroundColor1
            backgroundColor2 = this.warningBackgroundColor2
          }
          return {
            ...q,
            scoreRatePercent,
            warning,
            style: {
              background: `conic-gradient(${backgroundColor1} 0 ${scoreRatePercent}%, ${backgroundColor2} ${scoreRatePercent}% 100%)`,
            },
          }
        })
      },
      warningSampleStyle() {
        return {
          backgroundColor: this.warningBackgroundColor1,
        }
      },
    },
    methods: {
      toQuestion(q) {
        this.$emit('to-question', q)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .card-content {
    flex-wrap: wrap;
    justify-content: flex-start !important;
  }

  .question-item {
    flex-grow: 0;
    flex-shrink: 0;
    padding: 16px;
    cursor: pointer;

    .circle {
      @include flex(row, center, center);
      width: 64px;
      height: 64px;
      border-radius: 50% 50%;

      .score-rate {
        @include flex(row, center, center);
        width: 50px;
        height: 50px;
        border-radius: 50% 50%;
        background-color: white;
      }
    }

    .question-name {
      margin-top: 8px;
      text-align: center;
    }
  }

  .card-footer {
    margin-top: 16px;
    margin-left: 16px;
    color: $color-icon;
    line-height: 16px;

    .warning-sample {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: bottom;
    }
  }
</style>
