<script>
  import {
    apiAddReferenceBookScanner,
    apiModifyReferenceBookScannerRealName,
    apiModifyReferenceBookScannerPassword,
  } from '@/api/user'

  import { checkReferenceBookScannerPasswordIsLegal } from '@/helpers/school/index'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      mode: {
        type: String,
        default: 'add',
      },
      scannerInfo: {
        type: Object,
        default: () => null,
      },
    },

    emits: ['update:modelValue', 'fetch-scanners'],

    data() {
      return {
        userName: '',
        realName: '',
        password: '',
        confirmPassword: '',
      }
    },

    computed: {
      modalTitle() {
        return this.mode === 'add' ? '添加扫描员' : this.mode === 'modifyInfo' ? '修改扫描员信息' : '修改扫描员密码'
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },

      handleModalVisibleStatusChanged(isVisible) {
        if (isVisible) {
          if (this.mode === 'add') {
            this.userName = ''
            this.realName = ''
            this.password = ''
            this.confirmPassword = ''
          } else {
            this.userName = (this.scannerInfo && this.scannerInfo.userName) || ''
            this.realName = (this.scannerInfo && this.scannerInfo.realName) || ''
            this.password = ''
            this.confirmPassword = ''
          }
        } else {
          this.closeModal()
        }
      },

      checkInputAttributes() {
        if (this.mode === 'add' && !this.userName) {
          return '请输入账号'
        } else if (this.mode !== 'modifyPassword' && !this.realName) {
          return '请输入姓名'
        } else if (this.mode !== 'modifyInfo' && !this.password) {
          return '请输入密码'
        } else if (this.mode !== 'modifyInfo' && this.password !== this.confirmPassword) {
          return '两次输入的密码不一致，请检查'
        } else if (this.mode !== 'modifyInfo' && !checkReferenceBookScannerPasswordIsLegal(this.password)) {
          return '输入的密码过于简单（不建议是单调 递增递减 [12345, 54321] 或者 重复字符 [如 aaa ] 的密码，），请重新输入'
        }
      },

      handleModalConfirm() {
        if (this.mode === 'add') {
          this.handleAddScanner()
        } else if (this.mode === 'modifyInfo') {
          this.handleModifyScannerInfo()
        } else {
          this.handleModifyScannerPassword()
        }
      },

      handleAddScanner() {
        const CheckAttributes = this.checkInputAttributes()
        if (CheckAttributes) {
          this.$Message.warning({
            duration: CheckAttributes.length > 14 ? 10 : 4,
            closable: true,
            content: CheckAttributes,
          })
          return
        }

        apiAddReferenceBookScanner({
          userName: 's' + this.userName,
          realName: this.realName,
          password: this.password,
        }).then(() => {
          this.$Message.success({
            duration: 3,
            content: '添加扫描员成功',
          })
          this.$emit('fetch-scanners')
          this.closeModal()
        })
      },

      handleModifyScannerInfo() {
        const CheckAttributes = this.checkInputAttributes()
        if (CheckAttributes) {
          this.$Message.warning({
            duration: CheckAttributes.length > 14 ? 10 : 4,
            closable: true,
            content: CheckAttributes,
          })
          return
        }

        apiModifyReferenceBookScannerRealName({
          userName: this.userName,
          realName: this.realName,
        }).then(() => {
          this.$Message.success({
            duration: 3,
            content: '修改扫描员信息成功',
          })
          this.$emit('fetch-scanners')
          this.closeModal()
        })
      },

      handleModifyScannerPassword() {
        const CheckAttributes = this.checkInputAttributes()
        if (CheckAttributes) {
          this.$Message.warning({
            duration: CheckAttributes.length > 14 ? 10 : 4,
            closable: true,
            content: CheckAttributes,
          })
          return
        }

        apiModifyReferenceBookScannerPassword({
          userId: this.scannerInfo.teacherId,
          password: this.password,
        }).then(() => {
          this.$Message.success({
            duration: 3,
            content: '修改扫描员密码成功',
          })
          this.$emit('fetch-scanners')
          this.closeModal()
        })
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    class="modal-scanner"
    @on-visible-change="handleModalVisibleStatusChanged"
  >
    <div class="modal-form">
      <div class="form-item">
        <div class="form-label">账号</div>
        <div class="form-content">
          <Input v-if="mode === 'add'" v-model="userName" maxlength="49">
            <template #prepend>s</template>
          </Input>
          <div v-else>{{ userName }}</div>
        </div>
      </div>

      <div class="form-item">
        <div class="form-label">姓名</div>
        <div class="form-content">
          <Input v-if="mode !== 'modifyPassword'" v-model="realName" />
          <div v-else>{{ realName }}</div>
        </div>
      </div>

      <template v-if="mode !== 'modifyInfo'">
        <div class="form-item">
          <div class="form-label">{{ mode === 'add' ? '密码' : '新密码' }}</div>
          <div class="form-content">
            <Input v-model="password" type="password" password />
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">{{ mode === 'add' ? '确认密码' : '确认新密码' }}</div>
          <div class="form-content">
            <Input v-model="confirmPassword" type="password" password />
          </div>
        </div>
      </template>
    </div>

    <template #footer>
      <div>
        <Button type="text" @click="closeModal">取消</Button>
        <Button type="primary" @click="handleModalConfirm">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-scanner {
    .modal-form {
      .form-item {
        @include flex(row, flex-start, center);

        &:not(:first-child) {
          margin-top: 20px;
        }

        .form-label {
          flex-grow: 0;
          flex-shrink: 0;
          width: 5em;
          margin-right: 1.5em;
          text-align: right;
        }

        .form-content {
          flex-grow: 1;
          flex-shrink: 1;
        }
      }
    }
  }
</style>
