<template>
  <RouterView v-if="isServiceNormal"></RouterView>
  <div v-else class="service-unavailable">
    <Alert v-if="alertContent" type="warning" show-icon>{{ alertContent }}</Alert>
    <div class="inner">
      <img class="post" src="@/assets/images/supplementary_exercise/post.svg" />
    </div>
  </div>
</template>

<script>
  import { getNavs } from '@/utils/router'

  import ModuleEnum from '@/enum/user/module'

  export default {
    data() {
      return { routeNavs: [] }
    },

    computed: {
      isServiceNormal() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.CoachHomework.id)
      },

      isServiceExpired() {
        return this.$store.getters['user/isModuleExpired'](ModuleEnum.CoachHomework.id)
      },

      alertContent() {
        if (this.isServiceExpired) {
          const ServiceEndDate = this.$store.getters['user/moduleEndDate'](ModuleEnum.CoachHomework.id)
          return `您的${ModuleEnum.CoachHomework.name}已于 ${ServiceEndDate} 到期`
        } else {
          return ''
        }
      },
    },

    created() {
      if (this.$route.name) {
        this.routeNavs = getNavs(this.$route.name)
        if (this.isServiceNormal) {
          const FirstChildRoute = this.routeNavs[0]
          if (FirstChildRoute) {
            this.$router.replace(FirstChildRoute)
          }
        }
      }
    },
  }
</script>

<style lang="scss" scoped>
  .service-unavailable {
    .inner {
      background-color: white;

      .post {
        width: 100%;
      }
    }
  }
</style>
