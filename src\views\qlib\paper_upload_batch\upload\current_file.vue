<template>
  <div class="current-file">
    <div class="file-info">
      <div class="order">
        <span>
          第<span class="number">{{ uploadBatchStore.currentFile.order }}</span
          >份</span
        >
        <Button size="small" type="default" shape="circle" @click="handleSkip">跳过</Button>
      </div>
      <div class="name">{{ uploadBatchStore.currentFile.file.name }}</div>
    </div>
    <div class="paper-info">
      <div class="info-header">
        <div class="title">试卷信息</div>
        <div class="actions">
          <span class="switcher">
            <span class="label">自动确认</span>
            <i-switch v-model="autoConfirm" size="small"></i-switch>
          </span>
          <Button
            v-if="showBtnConfirmPaperInfo"
            size="small"
            type="primary"
            :loading="btnConfirmPaperInfoLoading"
            @click="handleBtnConfirmPaperInfoClick"
            >确认</Button
          >
        </div>
      </div>
      <ComPaperInfo
        v-if="paperInfo"
        ref="paperInfoRef"
        :disabled="disableChangePaperInfo"
        :paper-info="paperInfo"
      ></ComPaperInfo>
    </div>
    <ul class="message-list">
      <li v-for="(msg, idx) in uploadBatchStore.currentFile.messages" :key="idx" class="message-item" :class="msg.type">
        <span class="time">{{ formatTime(msg.time) }}</span
        ><span class="content" v-html="msg.message"></span>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import { ref, watch, useTemplateRef, computed } from 'vue'
  import iView from '@/iview'
  import store from '@/store'
  import { useUploadBatchStore } from '@/store/qlib/upload_batch'

  import ComPaperInfo from './paper_info.vue'

  import { apiGetDividePaperContent, apiUploadPaper } from '@/api/qlib/paper_upload'

  import { PaperInfo } from '@/helpers/qlib/paper_info'
  import { sleep } from '@/utils/promise'
  import { formatTime } from '@/utils/date'
  import StepStatusEnum from '@/enum/qlib/upload_batch/step_status'

  const uploadBatchStore = useUploadBatchStore()

  const paperInfoRef = useTemplateRef('paperInfoRef')

  // 自动确认试卷信息
  const autoConfirm = ref(false)
  // 试卷信息
  const paperInfo = ref(null)

  const currentStageSubject = computed(() => store.getters['qlib/currentStageSubject'])
  const showBtnConfirmPaperInfo = computed(() => {
    return uploadBatchStore.currentFile.uploadStatus != StepStatusEnum.Succeeded.id
  })
  const btnConfirmPaperInfoLoading = computed(() => {
    return uploadBatchStore.currentFile.uploadStatus == StepStatusEnum.Processing.id
  })
  const disableChangePaperInfo = computed(() => {
    return [StepStatusEnum.Processing.id, StepStatusEnum.Succeeded.id].includes(
      uploadBatchStore.currentFile.uploadStatus
    )
  })

  // 切换文件时初始化
  watch(() => uploadBatchStore.currentFile, init, { immediate: true })

  async function init() {
    let newPaperInfo = new PaperInfo()
    newPaperInfo.changeStage(currentStageSubject.value.stageId)
    newPaperInfo.changeSubject(currentStageSubject.value.subjectId)
    paperInfo.value = newPaperInfo

    if (!uploadBatchStore.currentFile) {
      return
    }
    if (uploadBatchStore.currentFile.uploadStatus == StepStatusEnum.Succeeded.id) {
      loadPaper()
    } else {
      extractPaperInfo()
    }
  }

  async function loadPaper() {
    uploadBatchStore.addCurrentFileMessage(`加载试卷中...`)
    try {
      let { rawHtml, paper } = await apiGetDividePaperContent({
        paperId: uploadBatchStore.currentFile.paperId,
        addRawHtml: true,
      })
      uploadBatchStore.setCurrentFilePaper({ paperHTML: rawHtml, paper })
      paperInfo.value = paper.paperInfo
      uploadBatchStore.addCurrentFileMessage(`加载试卷成功`)
    } catch (err) {
      uploadBatchStore.addCurrentFileMessage(`加载试卷失败: ${err.message || err.msg}`)
      throw err
    }
  }

  async function extractPaperInfo() {
    uploadBatchStore.addCurrentFileMessage(`提取试卷信息`)
    await sleep(0)
    paperInfo.value = paperInfoRef.value.getPaperInfoFromFileName(uploadBatchStore.currentFile.file.name)
    await sleep(0)
    if (autoConfirm.value && paperInfoRef.value.checkPaperInfo(false)) {
      uploadPaper()
    }
  }

  function handleBtnConfirmPaperInfoClick() {
    if (paperInfoRef.value.checkPaperInfo()) {
      uploadPaper()
    }
  }

  async function uploadPaper() {
    uploadBatchStore.addCurrentFileMessage('上传试卷中...')

    let { stage, subject, grade, term, paperType, year, region } = paperInfo.value
    let postData = {
      file: uploadBatchStore.currentFile.file,
      gradeLevel: stage.id,
      subjectId: subject.id,
      gradeId: grade.id,
      term: term.id,
      paperTypeId: paperType.id,
      year: year,
      regionId: region.length > 0 ? region[region.length - 1].value : '',
    }

    try {
      uploadBatchStore.setCurrentFileUploading()
      let paperId = await apiUploadPaper(postData)
      uploadBatchStore.setCurrentFileUploadSucceeded(paperId)
      uploadBatchStore.addCurrentFileMessage('上传试卷成功')
    } catch (err) {
      let message = err.message || err.msg
      uploadBatchStore.setCurrentFileUploadFailed(message)
      uploadBatchStore.addCurrentFileMessage('上传试卷失败: ' + message, 'error')
      throw err
    }
    await loadPaper()
  }

  function handleSkip() {
    iView.Modal.confirm({
      title: '请您确认',
      content: '是否跳过当前试卷？',
      onOk: () => {
        uploadBatchStore.skipCurrentFile()
      },
    })
  }
</script>

<style lang="scss" scoped>
  .current-file {
    @include flex(column, flex-start, stretch);
    gap: 16px;

    .file-info {
      flex-shrink: 0;

      .order {
        @include flex(row, space-between, center);

        .number {
          display: inline-block;
          margin-right: 8px;
          margin-left: 8px;
          color: $color-primary;
          font-weight: bold;
          transform: scale(1.5);
        }
      }

      .name {
        margin-top: 4px;
        line-height: 1.2;
      }
    }

    .paper-info {
      flex-shrink: 0;
      border: 1px solid $color-border;
      border-radius: 4px;

      .info-header {
        @include flex(row, space-between, center);
        padding: 12px 16px;
        border-bottom: 1px solid $color-border;

        .actions {
          @include flex(row, flex-end, center);
          gap: 16px;

          .switcher {
            @include flex(row, flex-start, center);
            gap: 4px;
          }
        }
      }
    }

    .message-list {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: auto;
      list-style-position: inside;
      list-style-type: disc;

      .message-item {
        line-height: 1.2;

        &:not(:first-child) {
          margin-top: 8px;
        }

        &.error {
          color: $color-error;
        }

        &.warn {
          color: $color-warning;
        }

        .time {
          margin-right: 8px;
        }
      }
    }
  }
</style>
