<template>
  <div class="page-review-mark">
    <div class="section-header">
      <div class="nav">
        <TextButton class="btn-back" type="primary" icon="md-arrow-back" @click="backToList">任务列表</TextButton>
        <span class="block-selector"> 当前评审：{{ taskDetail?.categoryName }} </span>
      </div>

      <div class="toolbar">
        <span class="button-group button-group-progress">
          <span v-if="progressText" class="progress-text">评审进度：{{ progressText }}</span>
          <TextButton v-show="showBtnBackToMark" class="btn" type="primary" @click="handleBtnBackToMarkClick"
            >继续评审
          </TextButton>

          <template v-if="isSubmitted">
            <TextButton
              v-if="hasFile"
              class="btn"
              type="default"
              icon="md-arrow-back"
              :disabled="!showBtnPrevious"
              @click="handleBtnPreviousClick"
              >上一份</TextButton
            >
            <TextButton
              v-if="hasFile"
              class="btn"
              type="default"
              icon="md-arrow-forward"
              :disabled="!showBtnNext"
              @click="handleBtnNextClick"
              >下一份</TextButton
            >
          </template>

          <TextButton class="btn" type="default" icon="md-time" @click="handleBtnHistoryClick"> 评卷历史</TextButton>
        </span>

        <Divider type="vertical"></Divider>

        <span class="button-group">
          <TextButton
            class="btn"
            type="default"
            icon="md-resize"
            title="全屏切换"
            @click="handleBtnFullScreenClick"
          ></TextButton>
        </span>
      </div>
    </div>
    <div class="section-body">
      <template v-if="hasFile">
        <div class="box-creation">
          <div class="box-creation-name">
            <div class="title">作品名称：</div>
            <div class="creation-name">{{ creationName }}</div>
          </div>
          <div class="box-creation-file">
            <div class="title">作品文件：</div>
            <div class="file">
              <div
                v-for="(item, idx) in creationFileReresult"
                :key="item.fileName"
                class="file-item"
                :style="item.imageOuterStyle"
              >
                <div v-if="item.fileType === 'image'" class="box-action">
                  <!-- <TextButton
                    class="btn-anticlockwise"
                    icon="md-refresh"
                    title="逆时针旋转90度"
                    @click="rotate(idx, -90)"
                  ></TextButton> -->
                  <TextButton
                    class="btn-clockwise"
                    icon="md-refresh"
                    title="顺时针旋转90度"
                    @click="rotate(idx, 90)"
                  ></TextButton>
                </div>
                <div class="box-file" :style="item.imageBoxStyle">
                  <object
                    v-if="item.fileType === 'pdf'"
                    class="pdf-viewer-object"
                    :data="item.filePath"
                    type="application/pdf"
                    width="100%"
                    height="800px"
                  >
                    <p>您的浏览器不支持查看PDF文件，请<a :href="item.filePath">下载PDF文件</a>查看。</p>
                  </object>
                  <img v-else-if="item.fileType === 'image'" class="image" :src="item.filePath" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <ScorePanel
          :task-detail="taskDetail"
          :judge-review-detail="judgeReviewDetail"
          :score-items="scoreItems"
          @on-submit="onSubmitScore"
        />
      </template>

      <template v-else>
        <div class="message-box" v-html="loadMarkPaperErrorMessage"></div>
      </template>
    </div>

    <DrawerHistory
      v-model="showDrawerHistory"
      :history-total="historyTotal"
      :current-page="historyCurrentPage"
      :page-size="historyPageSize"
      :records="historyRecords"
      :history-filted="historyFilted"
      :current-assign-order="judgeReviewDetail && judgeReviewDetail.judgeAssignOrder"
      @change-history-filted="changeHistoryFilted"
      @change-page="changeHistoryPage"
      @go="gotoRemarkPaper"
    ></DrawerHistory>
  </div>
</template>

<script>
  import fscreen from 'fscreen'
  import { round } from 'lodash'
  import ScorePanel from '../components/mark/score_panel.vue'
  import DrawerHistory from '../components/mark/drawer_history.vue'
  import {
    apiGetReviewTask,
    apiGetScoreSettings,
    apiGetJudgeReview,
    apiSubmitScore,
    apiReSubmitScore,
    apiGetJudgeHistory,
    apiGetOneReviewed,
  } from '@/api/review/review'
  import { getImageBoxStyle } from '@/helpers/emarking/marking'

  const LoadMarkPaperErrorCode = {
    Prepare: 4000,
    Finish_All: 4001,
    Finish_Self: 4002,
    Finish_Wait_Other: 4003,
    NoTask: 4004,
    Suspended: 4005,
    NoPaper: 4006,
    Subject_Suspended: 4020,
    Subject_Finish: 4021,
  }

  export default {
    components: {
      ScorePanel,
      DrawerHistory,
    },
    data() {
      return {
        taskDetail: null,
        scoreItems: [],
        judgeReviewDetail: null,
        showDrawerHistory: false,

        // 评卷历史
        historyPageSize: 10,
        historyCurrentPage: 1,
        historyRecords: [],
        historyTotal: 0,
        historyTotalPages: 0,
        historyFilted: {
          minScore: null,
          maxScore: null,
          date: ['', ''],
        },

        //状态
        loadMarkPaperErrorCode: 0, // 加载未评作品失败时具体情况
        loadMarkPaperErrorMessage: '', // 加载未评作品失败消息

        rotation: 0,
        creationFileReresult: [],
      }
    },
    computed: {
      creationName() {
        const { judgeReviewDetail } = this
        if (!judgeReviewDetail || !judgeReviewDetail.formDataList || !judgeReviewDetail.formDataList.length) {
          return ''
        }

        const theFormData = judgeReviewDetail.formDataList.find(item => item.fieldLabel === '作品名称')
        return theFormData && theFormData.fieldValue
      },
      creationFile() {
        const { judgeReviewDetail } = this
        if (!judgeReviewDetail || !judgeReviewDetail.formDataList || !judgeReviewDetail.formDataList.length) {
          return null
        }

        let file = null
        const theFormData = judgeReviewDetail.formDataList.find(item => item.fieldLabel === '作品上传')

        try {
          file = JSON.parse(theFormData.fieldValue)
          if (Array.isArray(file)) {
            file.forEach(item => {
              item.fileType = ['.jpg', 'jpeg', '.png', '.gif'].some(extension => item.filePath.endsWith(extension))
                ? 'image'
                : item.filePath.endsWith('pdf')
                  ? 'pdf'
                  : ''
            })
          }
        } catch {}

        return file
      },
      hasFile() {
        return Boolean(this.creationFile)
      },
      currentHistoryRecordIndex() {
        if (!this.isSubmitted || !this.judgeReviewDetail) {
          return -1
        }
        return this.historyRecords.findIndex(x => x.judgeAssignOrder === this.judgeReviewDetail.judgeAssignOrder)
      },
      showBtnPrevious() {
        return (
          this.isSubmitted &&
          !(
            this.currentHistoryRecordIndex === this.historyRecords.length - 1 &&
            this.historyCurrentPage === this.historyTotalPages
          )
        )
      },
      showBtnNext() {
        return this.isSubmitted && !(this.currentHistoryRecordIndex === 0 && this.historyCurrentPage === 1)
      },
      showBtnBackToMark() {
        return this.isSubmitted
      },
      progressText() {
        if (!this.taskDetail || !this.judgeReviewDetail) {
          return ''
        }

        if (this.judgeReviewDetail.status === 'assigned') {
          return `${this.judgeReviewDetail.judgeAssignOrder} / ${this.taskDetail.myProgress?.total}`
        } else if (this.isSubmitted) {
          return ''
        }
        return ''
      },
      isSubmitted() {
        return this.judgeReviewDetail && this.judgeReviewDetail.status === 'submitted'
      },
    },
    watch: {
      creationFile: {
        handler: function (newVal) {
          if (Array.isArray(newVal) && newVal.length) {
            this.getImageDimensions(newVal).then(result => {
              this.creationFileReresult = result.map(item => {
                return {
                  ...item,
                  imageBoxStyle: item.fileType === 'image' ? getImageBoxStyle(item) : '',
                }
              })
            })
          }
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      this.getReview()
      this.getJudgeReview()
    },
    methods: {
      getImageDimensions(list) {
        return Promise.all(
          list.map(item => {
            if (item.fileType === 'image') {
              return new Promise(resolve => {
                const img = new Image(item.filePath)
                img.onload = () => {
                  resolve({
                    ...item,
                    src: item.filePath,
                    naturalWidth: img.naturalWidth,
                    naturalHeight: img.naturalHeight,
                    rotate: 0,
                    width: 860,
                  })
                }
                img.onerror = () => {
                  resolve({
                    ...item,
                    src: item.filePath,
                    naturalWidth: 0,
                    naturalHeight: 0,
                    error: true,
                  })
                }
                img.src = item.filePath
              })
            }
            return item
          })
        )
      },

      rotate(idx, degrees) {
        let item = this.creationFileReresult[idx]
        item.rotate = (item.rotate + degrees) % 360
        item.imageBoxStyle = getImageBoxStyle(item)
        item.imageOuterStyle = `height: ${(item.rotate / 90) % 2 === 1 ? 888 + 'px' : 'auto'}`
      },
      backToList() {
        this.$router.back()
      },
      getReview() {
        const { categoryId } = this.$route?.params || {}

        apiGetReviewTask({
          categoryId,
        }).then(res => {
          this.taskDetail = res || null
          if (this.taskDetail.scoreMode === 'multiple') {
            this.getScoreConfig()
          }
        })
      },
      getScoreConfig() {
        const { categoryId } = this.$route?.params || {}

        apiGetScoreSettings({
          categoryId,
        }).then(res => {
          this.scoreItems = res?.scoreItems || []
        })
      },
      getJudgeReview() {
        const { categoryId } = this.$route?.params || {}

        apiGetJudgeReview({
          categoryId,
        })
          .then(res => {
            this.judgeReviewDetail = res
          }) // 处理特定类型错误
          .catch(err => {
            if (
              err.code !== LoadMarkPaperErrorCode.Prepare &&
              Object.keys(LoadMarkPaperErrorCode).some(key => LoadMarkPaperErrorCode[key] == err.code)
            ) {
              this.judgeReviewDetail = null
              this.loadMarkPaperErrorCode = err.code
              this.loadMarkPaperErrorMessage = err.msg
            } else {
              throw err
            }
          })
          // 一般错误
          .catch(err => {
            this.paper = null
            this.$Modal.error({
              title: '取卷错误',
              content: `获取试卷出错了：${(err && err.msg) || ''} <br>请先返回至评卷任务列表页面再重新进入!`,
              onOk: () => {
                this.backToList()
              },
            })
            throw err
          })
      },
      onSubmitScore(data) {
        if (this.isSubmitted) {
          // 回评
          apiReSubmitScore({
            comment: data.comment,
            judgeReviewId: this.judgeReviewDetail?.judgeReviewId || '',
            scores: data.scores,
          }).then(() => {
            // this.getJudgeReview()
            this.$Message.success('已提交')
          })
        } else {
          apiSubmitScore({
            comment: data.comment,
            judgeReviewId: this.judgeReviewDetail?.judgeReviewId || '',
            scores: data.scores,
          }).then(() => {
            this.getJudgeReview()
          })
        }
      },
      handleBtnHistoryClick() {
        this.loadHistory()
        this.showDrawerHistory = true
      },
      async handleBtnPreviousClick() {
        let previousOrder = 0
        let previousRecord = this.historyRecords[this.currentHistoryRecordIndex + 1]

        if (previousRecord) {
          previousOrder = previousRecord.judgeAssignOrder
        } else if (this.historyCurrentPage < this.historyTotalPages) {
          this.historyCurrentPage++
          await this.loadHistory()
          if (this.historyRecords.length > 0) {
            previousOrder = this.historyRecords[0].judgeAssignOrder
          }
        }

        if (previousOrder) {
          this.loadRemarkPaper(previousOrder)
        }
      },
      async handleBtnNextClick() {
        let nextOrder = 0
        let nextRecord = this.historyRecords[this.currentHistoryRecordIndex - 1]

        if (nextRecord) {
          nextOrder = nextRecord.judgeAssignOrder
        } else if (this.historyCurrentPage > 1) {
          this.historyCurrentPage--
          await this.loadHistory()
          if (this.historyRecords.length > 0) {
            nextOrder = this.historyRecords[this.historyRecords.length - 1].judgeAssignOrder
          }
        }

        if (nextOrder) {
          this.loadRemarkPaper(nextOrder)
        }
      },
      handleBtnFullScreenClick() {
        if (fscreen.fullscreenElement) {
          fscreen.exitFullscreen()
        } else {
          fscreen.requestFullscreen(document.documentElement)
        }
      },
      loadRemarkPaper(order) {
        apiGetOneReviewed({
          categoryId: this.taskDetail?.categoryId || '',
          judgeAssignOrder: order,
        }).then(res => {
          this.judgeReviewDetail = res
        })
      },
      handleBtnBackToMarkClick() {
        this.getJudgeReview()
      },

      /**
       * 评卷历史
       */
      changeHistoryPage(page) {
        this.historyCurrentPage = page
        this.loadHistory().then(() => {
          // if (this.historyRecords.length > 0) {
          //   this.loadRemarkPaper(this.historyRecords[0].judgeAssignOrder)
          // }
        })
      },

      changeHistoryFilted(params) {
        if (!params || !params.key) {
          return
        }

        if (params.key === 'date') {
          this.historyFilted.date = params.value
        } else {
          this.historyFilted[params.key] = typeof params.value !== 'number' ? null : round(params.value, 2)
        }

        this.historyCurrentPage = 1

        this.loadHistory().then(() => {
          // if (this.historyRecords.length > 0) {
          //   this.loadRemarkPaper(this.historyRecords[0].No)
          // }
        })
      },

      resetHistoryParams() {
        this.historyFilted = {
          minScore: null,
          maxScore: null,
          date: ['', ''],
        }
        this.historyCurrentPage = 1
        this.historyRecords = []
      },

      loadHistory() {
        return apiGetJudgeHistory({
          categoryId: this.taskDetail?.categoryId || '',
          size: this.historyPageSize,
          page: this.historyCurrentPage,
          minScore: this.historyFilted.minScore,
          maxScore: this.historyFilted.maxScore,
          beginTime: this.historyFilted.date[0],
          endTime: this.historyFilted.date[1],
        }).then(data => {
          this.historyTotal = data.total
          this.historyTotalPages = data.pages
          this.historyRecords = data.list
        })
      },

      gotoRemarkPaper(order) {
        this.loadRemarkPaper(order)
        this.showDrawerHistory = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-review-mark {
    position: relative;
    padding-top: 50px;
    // user-select: none;

    .section-header {
      @include flex(row, space-between, center);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1;
      flex-wrap: wrap;
      width: 100%;
      height: 50px;
      padding-right: 50px;
      padding-left: 50px;
      background-color: white;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);

      .nav {
        @include flex(row, flex-start, center);

        .block-selector {
          margin-right: 2px;
        }
      }

      .btn-back {
        margin-right: 20px;
      }

      .rehearsal-marking-tips {
        color: red;
      }

      .block-current {
        cursor: pointer;
      }

      .toolbar {
        .button-group {
          margin-right: 5px;
        }

        .button-group-progress > *:not(:first-child) {
          margin-left: 5px;
        }
      }
    }

    .section-body {
      position: relative;
      margin: 10px 0;

      .message-box {
        padding-top: 100px;
        font-weight: bold;
        font-size: 30px;
        text-align: center;
      }

      .box-creation {
        position: relative;
        top: 0;
        left: 0;
        width: 900px;
        transform-origin: 0 0;

        .box-creation-name {
          padding: 15px 20px;
          background-color: #fff;
        }

        .title {
          display: inline-block;
          font-size: 16px;
        }

        .creation-name {
          display: inline-block;
          font-size: 16px;
        }

        .box-file-item-name {
          margin-bottom: 10px;
        }

        .file-name {
          font-size: 16px;
        }

        .file-item {
          height: 100%;

          &:not(:last-child) {
            margin-bottom: 20px;
          }
        }

        .box-creation-file {
          padding: 0 20px 15px;
          background-color: #fff;

          .title {
            margin-bottom: 4px;
          }

          .box-action {
            @include flex(row, flex-end, center);
            margin-bottom: 4px;

            .btn-anticlockwise {
              transform: rotateY(180deg);
            }
          }

          .box-file {
            // height: 100%;
            // overflow: hidden;
            transform-origin: 0 0;

            .image {
              // display: inline-block;
              width: 100%;
              // height: 100%;
              transition: transform 0.3s ease;
            }
          }
        }
      }
    }
  }
</style>
