<template>
  <div class="panel-region-base">
    <div class="section-summary">
      <div class="summary-row">
        <div class="summary-item">
          <div class="box-count">
            <span class="count">{{ examCount }}</span>
            <span class="unit">次</span>
          </div>
          <div class="name">考试次数</div>
          <span class="line"></span>
        </div>
        <div class="summary-item">
          <div class="box-count">
            <span class="count">{{ generalInfo.schoolCount || 0 }}</span>
            <span class="unit">所</span>
          </div>
          <div class="name">学校数量</div>
          <span class="line"></span>
        </div>
      </div>
      <div class="summary-row">
        <div class="summary-item">
          <div class="box-count">
            <span class="count">{{ generalInfo.teacherCount || 0 }}</span>
            <span class="unit">人</span>
          </div>
          <div class="name">教师人数</div>
          <span class="line"></span>
        </div>
        <div class="summary-item">
          <div class="box-count">
            <span class="count">{{ generalInfo.studentCount || 0 }}</span>
            <span class="unit">人</span>
          </div>
          <div class="name">学生人数</div>
          <span class="line"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { useDashboardStore } from '../../../store/dashboard'
  import { apiGetSchoolGeneralInfo } from '@/api/user'

  const dashboardStore = useDashboardStore()
  const generalInfo = ref({})

  const currentSemester = computed(() => {
    return dashboardStore.currentSemester
  })
  const currentSchool = computed(() => {
    return dashboardStore.currentSchool
  })
  const examCount = computed(() => {
    return dashboardStore.examCount
  })

  const fetchGeneralInfo = () => {
    apiGetSchoolGeneralInfo({
      semesterId: currentSemester.value,
      childSchoolId:
        currentSchool.value && currentSchool.value.schoolId !== '0' ? currentSchool.value.schoolId : undefined,
    })
      .then(res => {
        generalInfo.value = res
      })
      .catch(() => {
        generalInfo.value = {}
      })
  }

  watch([currentSemester, currentSchool], ([newSemester, newSchool]) => {
    if (newSemester && newSchool) {
      fetchGeneralInfo()
    }
  })
</script>

<style lang="scss" scoped>
  .panel-region-base {
    @include flex(row, center, center);
    flex: 1 1 140px;
    box-sizing: border-box;

    .summary-row {
      @include flex(row, center, center);
      color: #7db7db;

      &:not(:last-child) {
        margin-bottom: 20px;
      }

      .summary-item {
        position: relative;

        .name {
          margin-bottom: 6px;
          font-size: 13px;
          text-align: center;
        }

        .box-count {
          @include flex(row, center, baseline);
          width: 100px;
          padding-bottom: 6px;
        }

        .count {
          margin-right: 6px;
          color: #b4a0da;
          font-size: 24px;
          line-height: 1;
        }

        .unit {
          font-size: 14px;
          line-height: 1;
        }

        .line {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 1px;
          background-color: #7db7db;

          &::after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 8px;
            height: 8px;
            margin: auto;
            border-radius: 50%;
            background-color: #7db7db;
            content: '';
          }
        }
      }

      .summary-item:first-child {
        margin-right: 60px;
      }
    }
  }
</style>
