<template>
  <div class="container-form-custom">
    <div class="header">
      <div class="header-left">
        <span>提交内容设置</span>
        <span class="desc">（可拖动自定义表单项名称调整顺序）</span>
      </div>
      <div class="header-right">
        <div v-if="isDetail" class="box-btn-edit">
          <Button type="primary" ghost @click="onEdit">{{ canEdit ? '取消编辑' : '编辑' }}</Button>
        </div>
      </div>
    </div>
    <div class="body">
      <div v-if="categories.length" class="box-tab">
        <el-tabs v-model="activeTab" class="demo-tabs">
          <el-tab-pane v-for="tab in categories" :key="tab.name" :label="tab.name" :name="tab.name"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="box-form">
        <el-form :show-message="false" label-width="140px" label-position="top">
          <template v-if="isFormDisabled">
            <el-form-item
              v-for="item in dynamicComponents"
              :key="item.fieldLabel"
              :label="item.fieldLabel"
              :required="item.isRequired"
            >
              <template #label>
                <span class="form-item-label">{{ `${item.sortOrder}、${item.fieldLabel}` }}</span>
                <el-tag v-if="item.isReviewItem" type="primary" size="small" class="tag-review-item">评审项</el-tag>
              </template>
              <div class="box-component-custom">
                <div class="component-custom">
                  <component :is="item.component" :options="item.extraOptions" />
                </div>
              </div>
            </el-form-item>
          </template>
          <draggable
            v-else
            v-model="dynamicComponents"
            item-key="fieldLabel"
            :component-data="{ type: 'transition-group' }"
            ghost-class="ghost"
            disabled
            @change="onMove"
          >
            <template #item="{ element }">
              <el-form-item :label="element.fieldLabel" :required="element.isRequired">
                <template #label>
                  <span class="form-item-label">{{ `${element.sortOrder}、${element.fieldLabel}` }}</span>
                  <el-tag v-if="element.isReviewItem" type="primary" size="small" class="tag-review-item"
                    >评审项</el-tag
                  >
                </template>
                <div class="box-component-custom">
                  <div class="component-custom">
                    <component :is="element.component" :options="element.extraOptions" />
                  </div>

                  <div v-if="!isFormDisabled" class="box-btn-action">
                    <Button
                      title="编辑"
                      class="btn-edit"
                      ghost
                      type="primary"
                      icon="ios-create-outline"
                      @click="onEditFormItem(element)"
                    ></Button>
                    <Button
                      title="删除"
                      class="btn-delete"
                      ghost
                      type="error"
                      icon="ios-trash"
                      :disabled="staticFormItemLabels.includes(element.fieldLabel)"
                      @click="onDeleteFormItem(element)"
                    ></Button>
                  </div>
                </div>
              </el-form-item>
            </template>
          </draggable>
        </el-form>
      </div>
      <div v-if="!isFormDisabled" class="box-btn-add">
        <Button icon="md-add" @click="onFormItemAdd">添加表单项</Button>
      </div>
      <div v-if="!isDetail" class="box-button-footer">
        <Button @click="onPrev">上一步</Button>
        <Button type="primary" @click="onSubmit">立即创建</Button>
      </div>
    </div>
  </div>

  <Modal v-model="dialogFormItemVisible" title="添加报名表单项" width="700">
    <div class="container-dialog">
      <div class="box-form-item">
        <div class="title">预设表单：</div>
        <div class="content">
          <el-radio-group v-model="currentFormItemSubType" @change="onChangeSubType">
            <el-radio-button v-for="item in presetFormItems" :key="item.type" :label="item.name" :value="item.type" />
          </el-radio-group>
        </div>
      </div>
      <div class="box-form-item">
        <div class="title">基础表单：</div>
        <div class="content">
          <el-radio-group v-model="dynamicFormItemOptions.fieldType" @change="onChangeFieldType">
            <el-radio-button v-for="item in baseFormItems" :key="item.type" :label="item.name" :value="item.type" />
          </el-radio-group>
        </div>
      </div>
      <div class="box-form-item">
        <div class="title">表单项选项：</div>
        <div class="content">
          <el-form ref="formEdit" :model="dynamicFormItemOptions" :rules="formDynamicRules" label-width="120px">
            <el-form-item label="表单项名称" prop="fieldLabel" required>
              <el-input
                v-model="dynamicFormItemOptions.fieldLabel"
                placeholder="请输入表单项名称"
                :maxlength="20"
                :readonly="staticFormItemLabels.includes(dynamicFormItemOptions.fieldLabel)"
                clearable
                @blur="onInputFieldLabelBlur"
              ></el-input>
            </el-form-item>
            <el-form-item label="必填项" prop="isRequired" required>
              <el-radio-group v-model="dynamicFormItemOptions.isRequired">
                <el-radio-button :value="true">是</el-radio-button>
                <el-radio-button :value="false">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="评审项" prop="isReviewItem" required>
              <el-radio-group v-model="dynamicFormItemOptions.isReviewItem">
                <el-radio-button :value="true">是</el-radio-button>
                <el-radio-button :value="false">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <template v-for="(optionVal, optionKey) in dynamicFormItemOptions.extraOptions">
              <el-form-item v-if="optionVal.show" :key="optionKey" :label="optionVal.name">
                <el-checkbox-group v-if="optionKey === 'accepts'" v-model="optionVal.value">
                  <el-checkbox label="image">图片</el-checkbox>
                  <el-checkbox label="pdf">pdf</el-checkbox>
                </el-checkbox-group>
                <el-input
                  v-else-if="optionVal.type === 'string'"
                  v-model="optionVal.value"
                  :placeholder="optionVal.name"
                  clearable
                ></el-input>
                <el-input-number
                  v-else-if="optionVal.type === 'number'"
                  v-model="optionVal.value"
                  :min="optionVal.min || 1"
                  :max="optionVal.max || 10000"
                >
                  <template v-if="optionKey === 'maxFileSize'" #suffix>
                    <span>M</span>
                  </template>
                </el-input-number>
                <el-input-tag
                  v-else-if="optionVal.type === 'array'"
                  v-model="optionVal.value"
                  placeholder="回车键添加输入内容为选项"
                />
              </el-form-item>
            </template>
          </el-form>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="box-footer-btn">
        <Button @click="onDialogCancel">取消</Button>
        <Button type="primary" @click="onDialogConfirm">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { nextTick, shallowRef } from 'vue'
  import draggable from 'vuedraggable'
  import {
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElInputTag,
    ElRadioGroup,
    ElRadio,
    ElRadioButton,
    ElCheckboxGroup,
    ElCheckbox,
    ElSwitch,
    ElDatePicker,
    ElButton,
    ElCol,
    ElIcon,
    ElUpload,
    ElDialog,
    ElTabs,
    ElTabPane,
    ElTag,
  } from 'element-plus'
  import 'element-plus/es/components/form/style/css'
  import 'element-plus/es/components/form-item/style/css'
  import 'element-plus/es/components/input/style/css'
  import 'element-plus/es/components/input-number/style/css'
  import 'element-plus/es/components/input-tag/style/css'
  import 'element-plus/es/components/radio-group/style/css'
  import 'element-plus/es/components/radio/style/css'
  import 'element-plus/es/components/radio-button/style/css'
  import 'element-plus/es/components/checkbox-group/style/css'
  import 'element-plus/es/components/checkbox/style/css'
  import 'element-plus/es/components/switch/style/css'
  import 'element-plus/es/components/date-picker/style/css'
  import 'element-plus/es/components/button/style/css'
  import 'element-plus/es/components/col/style/css'
  import 'element-plus/es/components/icon/style/css'
  import 'element-plus/es/components/upload/style/css'
  import 'element-plus/es/components/dialog/style/css'
  import 'element-plus/es/components/tabs/style/css'
  import 'element-plus/es/components/tab-pane/style/css'
  import 'element-plus/es/components/tag/style/css'
  import Input from './form_component/input.vue'
  import Textarea from './form_component/textarea.vue'
  import RadioGroup from './form_component/radio_group.vue'
  import CheckboxGroup from './form_component/checkbox_group.vue'
  import Upload from './form_component/upload.vue'
  import DatePicker from './form_component/date_picker.vue'
  import { deepCopy } from '@/utils/object'

  import { apiAddFormField, apiDeleteFormField, apiEditFormField, apiSaveFormFieldOrder } from '@/api/review/activity'

  const extraFormOptions = {
    text: {
      placeholder: {
        value: '',
        name: '文本输入占位符',
        type: 'string',
        show: true,
      },
      minLength: {
        value: 1,
        name: '文本最小长度',
        type: 'number',
        max: 99,
        show: true,
      },
      maxLength: {
        value: 50,
        name: '文本最大长度',
        type: 'number',
        min: 2,
        max: 100,
        show: true,
      },
    },
    textarea: {
      placeholder: {
        value: '',
        name: '文本输入占位符',
        type: 'string',
        show: true,
      },
      minLength: {
        value: 1,
        name: '文本最小长度',
        type: 'number',
        max: 499,
        show: true,
      },
      maxLength: {
        value: 200,
        name: '文本最大长度',
        type: 'number',
        min: 2,
        max: 500,
        show: true,
      },
    },
    upload: {
      accepts: {
        value: [],
        name: '文件类型',
        type: 'array',
        show: true,
      },
      maxFileSize: {
        value: 5,
        name: '最大文件大小',
        type: 'number',
        max: 10,
        show: true,
      },
      maxFileCount: {
        value: 1,
        name: '最多上传个数',
        type: 'number',
        max: 5,
        show: true,
      },
    },
    radio: {
      options: {
        value: [],
        name: '选项',
        type: 'array',
        show: true,
      },
    },
    checkbox: {
      options: {
        value: [],
        name: '选项',
        type: 'array',
        show: true,
      },
    },
    date: {
      placeholder: {
        value: '',
        name: '文本占位符',
        type: 'string',
        show: true,
      },
      format: {
        value: 'yyyy-MM-dd',
        name: '显示格式',
        type: 'string',
        show: false,
      },
    },
  }
  const presetFormItemOptions = {
    idcard: {
      fieldLabel: '身份证号',
      fieldType: 'text',
      subType: 'idcard',
      isRequired: true,
      isReviewItem: false,
      extraOptions: {
        placeholder: {
          value: '请输入身份证号',
          name: '文本输入占位符',
          type: 'string',
          show: true,
        },
        minLength: {
          value: 18,
          name: '文本最小长度',
          type: 'number',
          show: false,
        },
      },
      sortOrder: '',
      description: '',
      extraJson: '',
    },
    mobile: {
      fieldLabel: '手机号',
      fieldType: 'text',
      subType: 'mobile',
      isRequired: true,
      isReviewItem: false,
      extraOptions: {
        placeholder: {
          value: '请输入手机号',
          name: '文本输入占位符',
          type: 'string',
          show: true,
        },
        minLength: {
          value: 11,
          name: '文本最小长度',
          type: 'number',
          show: false,
        },
      },
      sortOrder: '',
      description: '',
      extraJson: '',
    },
    sex: {
      fieldLabel: '性别',
      fieldType: 'radio',
      subType: 'sex',
      isRequired: true,
      isReviewItem: false,
      extraOptions: {
        options: {
          value: ['男', '女'],
          name: '选项',
          type: 'array',
          show: false,
        },
      },
      sortOrder: '',
      description: '',
      extraJson: '',
    },
  }
  const staticFormItemOptions = [
    {
      fieldLabel: '作品名称',
      fieldType: 'text',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 1,
      description: '',
      extraJson: '{"placeholder": "请输入作品名称", "minLength": 1, "maxLength": 100}',
    },
    {
      fieldLabel: '作品上传',
      fieldType: 'upload',
      subType: '',
      isRequired: true,
      isReviewItem: true,
      sortOrder: 2,
      description: '',
      isDefault: true,
      extraJson: '{"accepts": ["image","pdf"], "maxFileSize": 10485760, "maxFileCount": 2}',
    },
  ]

  export default {
    components: {
      'el-form': ElForm,
      'el-form-item': ElFormItem,
      'el-input': ElInput,
      'el-input-number': ElInputNumber,
      'el-input-tag': ElInputTag,
      'el-radio-group': ElRadioGroup,
      'el-radio': ElRadio,
      'el-radio-button': ElRadioButton,
      'el-checkbox-group': ElCheckboxGroup,
      'el-checkbox': ElCheckbox,
      'el-switch': ElSwitch,
      'el-date-picker': ElDatePicker,
      'el-button': ElButton,
      'el-col': ElCol,
      'el-icon': ElIcon,
      'el-upload': ElUpload,
      'el-dialog': ElDialog,
      'el-tabs': ElTabs,
      'el-tab-pane': ElTabPane,
      'el-tag': ElTag,
      draggable,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      activityDetail: {
        type: Object,
        default: () => {},
      },
      categories: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['on-comfirm', 'on-prev', 'on-submit', 'on-refresh'],
    data() {
      return {
        activeTab: '',
        fileList: [],
        dialogFormItemVisible: false,
        currentFormItemSubType: '',
        presetFormItems: [
          {
            type: 'mobile',
            name: '手机号',
          },
          {
            type: 'idcard',
            name: '身份证号',
          },
          {
            type: 'sex',
            name: '性别',
          },
        ],
        baseFormItems: [
          {
            type: 'text',
            name: '单行文本',
          },
          {
            type: 'textarea',
            name: '多行文本',
          },
          {
            type: 'radio',
            name: '单选项',
          },
          {
            type: 'checkbox',
            name: '多选项',
          },
          {
            type: 'date',
            name: '日期选择',
          },
          // {
          //   type: 'switch',
          //   name: '开关',
          // },
          {
            type: 'upload',
            name: '文件上传',
          },
        ],
        dynamicFormItemOptions: {
          fieldLabel: '',
          fieldType: '',
          subType: '',
          isRequired: false,
          isReviewItem: false,
          extraOptions: {},
          sortOrder: '',
          description: '',
          extraJson: '',
        },
        dynamicComponents: [],
        allCategoryComponents: {},
        formDynamicRules: {
          fieldLabel: [{ required: true, message: '请输入表单项名称', trigger: 'blur' }],
        },
        canEdit: false,
      }
    },
    computed: {
      isFormDisabled() {
        return this.isDetail && !this.canEdit
      },
      staticFormItemLabels() {
        return staticFormItemOptions.map(item => item.fieldLabel)
      },
    },
    watch: {
      categories: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            if (!this.activeTab || !val.some(t => t.name === this.activeTab)) {
              this.activeTab = val[0].name
            }
          }
        },
        immediate: true,
      },
      activeTab: {
        handler: function (newVal, oldVal) {
          if (this.isDetail) {
            const currentCategory = this.categories.find(c => c.name === newVal)

            if (currentCategory) {
              this.dynamicComponents = (currentCategory.fields || []).map(item => {
                let extraOptions = this.transOptions(item)

                return {
                  ...item,
                  component: this.getComponent(item.fieldType),
                  extraOptions,
                }
              })
            } else {
              this.dynamicComponents = []
            }
          } else {
            if (oldVal) {
              this.allCategoryComponents[oldVal] = this.dynamicComponents
              this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
            }

            this.allCategoryComponents = this.$store.getters['review/allCategoryComponents'] || {}
            this.categories.forEach(c => {
              let currentCategoryComponents = this.allCategoryComponents[c.name] || []

              if (!currentCategoryComponents || !currentCategoryComponents.length) {
                this.allCategoryComponents[c.name] = staticFormItemOptions
              }
            })

            let dynamicComponents = this.allCategoryComponents[newVal]
            this.dynamicComponents = dynamicComponents.map(item => {
              let extraOptions = this.transOptions(item)
              return {
                ...item,
                component: this.getComponent(item.fieldType),
                extraOptions,
              }
            })

            this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
          }
        },
        immediate: true,
      },
    },
    beforeUnmount() {
      this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
    },
    methods: {
      onMove() {
        this.dynamicComponents.forEach((item, idx) => {
          item.sortOrder = idx + 1
        })
        if (this.isDetail) {
          const list = this.dynamicComponents.map(item => ({
            id: item.id,
            sortOrder: item.sortOrder,
          }))
          apiSaveFormFieldOrder(list)
        }
      },
      transOptions(item) {
        let tempOptions = {}
        let extraOptions = {}

        try {
          tempOptions = JSON.parse(item.extraJson)
          for (const key in tempOptions) {
            let optionsVal = extraFormOptions[item.fieldType] || {}
            let theOptions = optionsVal[key]
            let show = true

            if (
              (['idcard', 'mobile'].includes(item.subType) && key === 'minLength') ||
              (item.subType === 'sex' && key === 'options')
            ) {
              show = false
            }

            if (theOptions) {
              extraOptions[key] = {
                ...theOptions,
                value: tempOptions[key],
                show,
              }
            }
          }
        } catch (error) {}

        return extraOptions
      },
      onPrev() {
        this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
        this.$emit('on-prev')
      },
      onEdit() {
        this.canEdit = !this.canEdit
      },
      onFormItemAdd() {
        const { dynamicComponents } = this

        this.dynamicFormItemOptions = {
          fieldLabel: '',
          fieldType: '',
          subType: '',
          isRequired: false,
          isReviewItem: false,
          extraOptions: {},
          sortOrder: dynamicComponents.length + 1,
          description: '',
          extraJson: '',
        }
        this.currentFormItemSubType = ''
        this.dialogFormItemVisible = true
        nextTick(() => {
          this.$refs.formEdit.clearValidate()
        })
      },
      onSubmit() {
        this.$emit('on-submit')
      },
      onChangeSubType(val) {
        this.dynamicFormItemOptions = {
          ...deepCopy(this.dynamicFormItemOptions),
          ...deepCopy(presetFormItemOptions[val]),
          sortOrder:
            this.dynamicFormItemOptions.sortOrder == ''
              ? this.dynamicComponents.length + 1
              : this.dynamicFormItemOptions.sortOrder,
        }
      },
      onChangeFieldType() {
        this.currentFormItemSubType = ''
        this.dynamicFormItemOptions.extraOptions = deepCopy(extraFormOptions[this.dynamicFormItemOptions.fieldType])
      },
      onDialogCancel() {
        this.dialogFormItemVisible = false
      },
      onInputFieldLabelBlur() {
        if (
          this.dynamicComponents.some(
            c =>
              c.fieldLabel === this.dynamicFormItemOptions.fieldLabel &&
              c.sortOrder !== this.dynamicFormItemOptions.sortOrder
          )
        ) {
          this.$Message.info(`${this.dynamicFormItemOptions.fieldLabel} 已存在，不能重复添加`)
          this.dynamicFormItemOptions.fieldLabel = ''
        }
      },
      checkForm() {
        const { dynamicFormItemOptions, dynamicComponents } = this
        let extraOptions = dynamicFormItemOptions.extraOptions

        if (!dynamicFormItemOptions.fieldType) {
          return '请选择一种基础表单'
        }

        if (!dynamicFormItemOptions.fieldLabel) {
          return '请填写表单项名称'
        }

        let index = dynamicComponents.findIndex(
          c => c.fieldLabel === dynamicFormItemOptions.fieldLabel && c.sortOrder !== dynamicFormItemOptions.sortOrder
        )
        if (index >= 0) {
          return `${dynamicFormItemOptions.fieldLabel} 已存在，不能重复添加`
        }

        if (
          ['radio', 'checkbox'].includes(dynamicFormItemOptions.fieldType) &&
          (!extraOptions?.options?.value || !extraOptions?.options?.value?.length)
        ) {
          return '请设置选项'
        }

        if (
          dynamicFormItemOptions.fieldType === 'upload' &&
          (!extraOptions['accepts'].value || !extraOptions['accepts'].value.length)
        ) {
          return '请至少选择一个文件上传的文件类型'
        }

        if (
          ['text', 'textarea'].includes(dynamicFormItemOptions.fieldType) &&
          !dynamicFormItemOptions.subType &&
          !extraOptions['maxLength'].value
        ) {
          return '请填写文本最大长度'
        }

        if (
          Array.isArray(extraOptions?.options?.value) &&
          extraOptions?.options?.value.length &&
          new Set(extraOptions?.options.value).size !== extraOptions?.options.value.length
        ) {
          return '单选项或者多选项不能重复'
        }
      },
      onDialogConfirm() {
        const { dynamicFormItemOptions } = this
        let component = this.getComponent(dynamicFormItemOptions.fieldType)
        let extraJson = ''
        let extraOptions = deepCopy(dynamicFormItemOptions.extraOptions)

        let msg = this.checkForm()
        if (msg) {
          this.$Message.info(msg)
          return
        }

        let tempOptions = {}
        try {
          for (const key in extraOptions) {
            if (key === 'maxFileSize') {
              tempOptions[key] = Number(extraOptions[key].value) * 1024 * 1024
            } else {
              tempOptions[key] = extraOptions[key].value
            }
          }
          extraJson = JSON.stringify(tempOptions)
        } catch (error) {}

        let newItem = {
          id: dynamicFormItemOptions.id,
          fieldLabel: dynamicFormItemOptions.fieldLabel,
          fieldName: dynamicFormItemOptions.fieldLabel,
          fieldType: dynamicFormItemOptions.fieldType,
          subType: dynamicFormItemOptions.subType,
          isRequired: dynamicFormItemOptions.isRequired,
          isReviewItem: dynamicFormItemOptions.isReviewItem,
          sortOrder: dynamicFormItemOptions.sortOrder,
          description: dynamicFormItemOptions.description,
          extraOptions,
          extraJson,
          component,
        }

        if (this.isDetail) {
          const theCategory = this.categories.find(c => c.name === this.activeTab)

          if (dynamicFormItemOptions.id) {
            let theComponentIdx = this.dynamicComponents.findIndex(c => c.id === dynamicFormItemOptions.id)

            apiEditFormField({
              id: newItem.id,
              defaultValue: newItem.defaultValue || '',
              description: newItem.description || '',
              extraJson: newItem.extraJson,
              fieldLabel: newItem.fieldLabel,
              fieldType: newItem.fieldType,
              isRequired: newItem.isRequired,
              isReviewItem: newItem.isReviewItem,
              sortOrder: newItem.sortOrder,
              subType: newItem.subType,
            }).then(() => {
              this.$Message.success('已保存')
              this.dynamicComponents[theComponentIdx] = newItem
              this.$emit('on-refresh')
            })
          } else {
            apiAddFormField({
              activityId: this.activityDetail.id,
              categoryId: theCategory?.id,
              defaultValue: newItem.defaultValue || '',
              description: newItem.description || '',
              extraJson: newItem.extraJson,
              fieldLabel: newItem.fieldLabel,
              fieldType: newItem.fieldType,
              isRequired: newItem.isRequired,
              isReviewItem: newItem.isReviewItem,
              sortOrder: newItem.sortOrder,
              subType: newItem.subType,
            }).then(() => {
              this.$Message.success('已添加')
              this.dynamicComponents.push(newItem)
              this.$emit('on-refresh')
            })
          }
        } else {
          let theFormItemIdx = this.dynamicComponents.findIndex(c => c.sortOrder === newItem.sortOrder)

          if (theFormItemIdx >= 0) {
            this.dynamicComponents[theFormItemIdx] = newItem
          } else {
            this.dynamicComponents.push(newItem)
          }

          this.allCategoryComponents[this.activeTab] = this.dynamicComponents
          this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
        }

        this.dialogFormItemVisible = false
      },
      getComponent(type) {
        let component = null

        switch (type) {
          case 'text':
            component = shallowRef(Input)
            break
          case 'textarea':
            component = shallowRef(Textarea)
            break
          case 'radio':
            component = shallowRef(RadioGroup)
            break
          case 'checkbox':
            component = shallowRef(CheckboxGroup)
            break
          case 'upload':
            component = shallowRef(Upload)
            break
          case 'date':
            component = shallowRef(DatePicker)
            break
          case 'switch':
            break
          default:
            break
        }

        return component
      },
      onEditFormItem(item) {
        this.currentFormItemSubType = ''
        this.dynamicFormItemOptions = deepCopy(item)
        this.dialogFormItemVisible = true
        nextTick(() => {
          this.$refs.formEdit.clearValidate()
        })
      },
      onDeleteFormItem(item) {
        if (this.isDetail) {
          this.$Modal.confirm({
            title: '删除表单项',
            content: `确定删除该表单项吗？`,
            onOk: () => {
              apiDeleteFormField(item.id).then(() => {
                this.$Message.success('已删除')
                const idx = this.dynamicComponents.findIndex(c => c.fieldLabel === item.fieldLabel)
                this.dynamicComponents.splice(idx, 1)
                this.$emit('on-refresh')
              })
            },
          })
        } else {
          const idx = this.dynamicComponents.findIndex(c => c.fieldLabel === item.fieldLabel)
          this.dynamicComponents.splice(idx, 1)
          this.allCategoryComponents[this.activeTab] = this.dynamicComponents
          this.$store.dispatch('review/setAllCategoryComponents', this.allCategoryComponents)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-form-custom {
    .header {
      @include flex(row, space-between, center);
      padding: 10px 0;

      .desc {
        color: #999;
        font-size: 12px;
      }
    }

    .box-btn-add {
      @include flex(row, center, center);
    }

    .box-button-footer {
      padding: 30px 50px;
    }

    .box-tab {
      margin-bottom: 15px;
      padding-left: 60px;
    }

    .box-form {
      padding-left: 60px;
    }

    .form-item-label {
      margin-right: 10px;
    }

    .box-component-custom {
      @include flex(row, flex-start, flex-start);
      width: 100%;
    }

    .component-custom {
      flex: 1;
    }

    .box-btn-action {
      @include flex(row, flex-start, center);
      margin-left: 30px;
    }

    .btn-delete {
      margin-left: 10px;
    }
  }

  .container-dialog {
    --el-color-primary: #05c1ae;

    .box-form-item {
      margin-bottom: 20px;

      .title {
        margin-bottom: 15px;
      }
    }
  }
</style>
