import { deepCopy } from '@/utils/object'

export const statsWays = [
  {
    id: 2,
    name: '分数比例',
  },
  {
    id: 1,
    name: '具体分数',
  },
  {
    id: 4,
    name: '排名比例',
  },
  {
    id: 3,
    name: '具体排名',
  },
]

export class ReportParameterConfig {
  constructor() {
    this.totalScores = []
    this.scoreRates = []
    this.scoreLevels = []
    this.scoreIntervals = []
    this.rankIntervals = []
    this.lines = []
    this.scoreIntervals = []
    this.rankIntervals = []
    this.statsStudents = []
    this.isChildSchExp = false
    this.compareExam = null
    this.skipCalConfig = {
      skipClassLastCount: null,
      skipClassLastPercent: null,
    }
    this.skipStudentCalcScore = false
    this.misc = {
      calcSchoolZScore: false,
    }
    this.roundConfigs = []
  }

  static import(exam, parameterConfigJSON) {
    let resParameters = JSON.parse(parameterConfigJSON)

    // 总分
    let totalScores = exam.subjects.map(s => {
      let totalScoreSubject = (resParameters.totalItems || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        enabled: !!totalScoreSubject,
        writtenScoreRatio: totalScoreSubject ? totalScoreSubject.writtenScoreRatio : 0,
        otherScoreRatio: totalScoreSubject ? totalScoreSubject.otherScoreRatio : 0,
        subjectRatio: totalScoreSubject ? totalScoreSubject.ratio : 0,
      }
    })

    // 新高考等级分
    let gradeScores = resParameters.gradeScores || []

    // 成绩率
    let subjects = exam.subjects.map(s => ({
      subjectId: s.subjectId,
      subjectName: s.subjectName,
    }))
    subjects.unshift({
      subjectId: 0,
      subjectName: '总分',
    })
    let scoreRates = subjects.map(s => {
      let subjectRates = (resParameters.rates || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        statsWay: (subjectRates && subjectRates.way) || 0,
        items: ((subjectRates && subjectRates.beans) || [])
          .map(x => ({
            name: x.name,
            range: x.range,
            sortCode: x.sortCode,
            ...splictRange(x.range),
          }))
          .sort((a, b) => a.sortCode - b.sortCode),
      }
    })

    // 教学点是否单独统计
    let isChildSchExp = resParameters.isChildSchExp || false

    // 成绩分档
    let scoreLevels = subjects.map(s => {
      let subjectLevels = (resParameters.levels || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        statsWay: (subjectLevels && subjectLevels.way) || 0,
        items: ((subjectLevels && subjectLevels.beans) || [])
          .map(x => ({
            name: x.name,
            range: x.range,
            sortCode: x.sortCode,
            ...splictRange(x.range),
          }))
          .sort((a, b) => a.sortCode - b.sortCode),
      }
    })

    // 分数段
    let scoreIntervals = subjects.map(s => {
      const SubjectScoreIntervals = (resParameters.scoreIntervals || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        intervals: (SubjectScoreIntervals && SubjectScoreIntervals.intervals) || undefined,
      }
    })

    // 名次段
    let rankIntervals = subjects.map(s => {
      const SubjectRankIntervals = (resParameters.rankIntervals || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        intervals: (SubjectRankIntervals && SubjectRankIntervals.intervals) || undefined,
      }
    })

    // 分数线
    let lines = (resParameters.lines || []).map(line => {
      ;(line.beans || []).forEach(bean => (bean.key = bean.name))
      return line
    })

    // 统计学生
    let statsStudents = subjects.map(s => {
      let subjectConfig = (resParameters.statsConfig || []).find(x => x.id === s.subjectId)
      return {
        subjectId: s.subjectId,
        subjectName: s.subjectName,
        statsAbsentStudents: subjectConfig ? subjectConfig.isAbnormalCount : s.subjectId === 0,
        statsAllSubjectAbsentStudents: subjectConfig ? subjectConfig.allAbnormalCount : false,
        countOnAllNormal: subjectConfig ? subjectConfig.countOnAllNormal : false,
        excludeStudentIds: (subjectConfig && subjectConfig.students) || [],
        excludeClassIds: (subjectConfig && subjectConfig.classIds) || [],
        excludeSchoolIds: (subjectConfig && subjectConfig.schoolIds) || [],
      }
    })

    let compareExam = resParameters.compareExam || {}

    // 分数舍入规则
    const RoundConfigs = (exam.subjects || []).map(s => {
      const TargetRoundConfig = (resParameters.roundConfigs || []).find(c => c.id === s.subjectId)
      return (
        TargetRoundConfig || {
          id: s.subjectId,
          roundMode: '',
          roundTo: '',
        }
      )
    })

    let config = new ReportParameterConfig()
    config.totalScores = totalScores
    config.gradeScores = gradeScores
    config.scoreRates = scoreRates
    config.scoreLevels = scoreLevels
    config.scoreIntervals = scoreIntervals
    config.rankIntervals = rankIntervals
    config.lines = lines
    config.statsStudents = statsStudents
    config.isChildSchExp = isChildSchExp
    config.compareExam = compareExam
    config.roundConfigs = RoundConfigs

    if (resParameters.skipCalConfig) {
      Object.keys(config.skipCalConfig).forEach(key => {
        if (resParameters.skipCalConfig[key] != null) {
          config.skipCalConfig[key] = resParameters.skipCalConfig[key]
        }
      })
    }
    if (resParameters.skipStudentCalcScore) {
      config.skipStudentCalcScore = true
    }
    if (resParameters.misc) {
      config.misc.calcSchoolZScore = Boolean(resParameters.misc.calcSchoolZScore)
    }

    return config
  }

  static getDefaultGradeScores() {
    return [
      {
        name: 'A',
        proportion: 0.17,
        maxScore: 100,
        minScore: 83,
      },
      {
        name: 'B',
        proportion: 0.33,
        maxScore: 82,
        minScore: 71,
      },
      {
        name: 'C',
        proportion: 0.33,
        maxScore: 70,
        minScore: 59,
      },
      {
        name: 'D',
        proportion: 0.15,
        maxScore: 58,
        minScore: 41,
      },
      {
        name: 'E',
        proportion: 0.02,
        maxScore: 40,
        minScore: 30,
      },
    ]
  }

  static copyParameter(parameter) {
    let config = new ReportParameterConfig()
    config.totalScores = deepCopy(parameter.totalScores)
    config.gradeScores = deepCopy(parameter.gradeScores)
    config.scoreRates = deepCopy(parameter.scoreRates)
    config.scoreLevels = deepCopy(parameter.scoreLevels)
    config.lines = deepCopy(parameter.lines)
    config.statsStudents = deepCopy(parameter.statsStudents)
    config.isChildSchExp = deepCopy(parameter.isChildSchExp)
    config.compareExam = deepCopy(parameter.compareExam)
    config.skipCalConfig = deepCopy(parameter.skipCalConfig)
    config.skipStudentCalcScore = Boolean(parameter.skipStudentCalcScore)
    config.misc = deepCopy(parameter.misc)
    return config
  }

  checkTotalScores() {
    let enabledSubjects = this.totalScores.filter(s => s.enabled)
    if (enabledSubjects.length === 0) {
      return '请至少统计一个科目'
    }

    for (let subject of enabledSubjects) {
      if (!isStrictNonNegative(subject.writtenScoreRatio)) {
        return `${subject.subjectName}笔试分比例设置错误`
      } else if (!isStrictNonNegative(subject.otherScoreRatio)) {
        return `${subject.subjectName}其他分比例设置错误`
      } else if (!isStrictNonNegative(subject.subjectRatio)) {
        return `${subject.subjectName}科目比例设置错误`
      }
    }
  }

  checkGradeScores() {
    if (this.gradeScores.length === 0) {
      return
    }

    let previousGradeScoremin = 101
    for (let gradeScore of this.gradeScores) {
      if (!(gradeScore.proportion > 0 && gradeScore.proportion < 100)) {
        return `${gradeScore.name}人数比例必须在0-100之间`
      }

      if (!(gradeScore.maxScore > 0 && gradeScore.maxScore <= 100)) {
        return `${gradeScore.name}等级最高分必须在0-100之间`
      }

      if (!(gradeScore.minScore >= 0 && gradeScore.minScore < 100)) {
        return `${gradeScore.name}等级最低分必须在0-100之间`
      }

      if (!(gradeScore.maxScore > gradeScore.minScore)) {
        return `${gradeScore.name}等级最高分必须大于最低分`
      }

      if (previousGradeScoremin <= gradeScore.maxScore) {
        return `${gradeScore.name}等级最高分必须小于上一等级最低分`
      } else {
        previousGradeScoremin = gradeScore.minScore
      }
    }

    let proportionTotal = this.gradeScores.reduce((a, c) => a + c.proportion)
    if (Math.abs(proportionTotal - 1) >= 0.01) {
      return '所有等级人数比例之和不为100%'
    }
  }

  checkScoreRates() {
    let hasRatesSubjects = this.scoreRates.filter(s => s.items.length > 0)

    let statsWayIds = statsWays.map(x => x.id)
    for (let subject of hasRatesSubjects) {
      if (!statsWayIds.includes(subject.statsWay)) {
        return `${subject.subjectName}未选择统计方式`
      }

      if (subject.items.some(item => !item.name)) {
        return `${subject.subjectName}成绩率名称不能为空`
      }

      let distinctItemNames = Array.from(new Set(subject.items.map(item => item.name)))
      if (distinctItemNames.length < subject.items.length) {
        return `${subject.subjectName}成绩率名称不能重复`
      }

      for (let item of subject.items) {
        if (subject.statsWay === 1) {
          if (!(isScoreNumber(item.minValue) && isScoreNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是分数`
          }
        } else if (subject.statsWay === 3) {
          if (!(isRankNumber(item.minValue) && isRankNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是名次`
          }
        } else if (subject.statsWay === 2 || subject.statsWay === 4) {
          if (!(isRateNumber(item.minValue) && isRateNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是比例`
          }
        }

        if (typeof item.minValueIncluded !== 'boolean' || typeof item.maxValueIncluded !== 'boolean') {
          return `${subject.subjectName}【${item.name}】范围设置错误：未选择是否包含端点值`
        }

        if (item.minValue > item.maxValue) {
          return `${subject.subjectName}【${item.name}】范围设置错误：范围左端点不应大于范围右端点`
        }
      }
    }
  }

  checkScoreLevels() {
    let hasRatesSubjects = this.scoreRates.filter(s => s.items.length > 0)

    let statsWayIds = statsWays.map(x => x.id)
    for (let subject of hasRatesSubjects) {
      if (!statsWayIds.includes(subject.statsWay)) {
        return `${subject.subjectName}未选择统计方式`
      }

      if (subject.items.some(item => !item.name)) {
        return `${subject.subjectName}成绩档名称不能为空`
      }

      let distinctItemNames = Array.from(new Set(subject.items.map(item => item.name)))
      if (distinctItemNames.length < subject.items.length) {
        return `${subject.subjectName}成绩档名称不能重复`
      }

      for (let item of subject.items) {
        if (subject.statsWay === 1) {
          if (!(isScoreNumber(item.minValue) && isScoreNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是分数`
          }
        } else if (subject.statsWay === 3) {
          if (!(isRankNumber(item.minValue) && isRankNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是名次`
          }
        } else if (subject.statsWay === 2 || subject.statsWay === 4) {
          if (!(isRateNumber(item.minValue) && isRateNumber(item.maxValue))) {
            return `${subject.subjectName}【${item.name}】范围设置错误：输入不是比例`
          }
        }

        if (typeof item.minValueIncluded !== 'boolean' || typeof item.maxValueIncluded !== 'boolean') {
          return `${subject.subjectName}【${item.name}】范围设置错误：未选择是否包含端点值`
        }

        if (item.minValue > item.maxValue) {
          return `${subject.subjectName}【${item.name}】范围设置错误：范围左端点不应大于范围右端点`
        }
      }
    }
  }

  checkScoreLines() {
    for (let line of this.lines) {
      const LineBeans = line.beans || []

      if (LineBeans.some(bean => !bean.name)) {
        return '分档线名称不能为空'
      }

      const DistinctItemNames = Array.from(new Set(LineBeans.map(bean => bean.name)))
      if (DistinctItemNames.length < LineBeans.length) {
        return '分档线名称不能重复'
      }

      if (LineBeans.some(bean => !isScoreNumber(bean.value))) {
        return `设置分档线数据格式错误`
      }
    }
  }

  checkStatsStudents() {
    return ''
  }

  checkSkipCalConfig() {
    if (this.skipCalConfig.skipClassLastCount != null && this.skipCalConfig.skipClassLastPercent != null) {
      return '不统计班级成绩后N人和不统计班级成绩后百分比只能选择一个'
    }
    if (this.skipCalConfig.skipClassLastCount != null) {
      if (!isStrictNonNegative(this.skipCalConfig.skipClassLastCount) || this.skipCalConfig.skipClassLastCount <= 0) {
        return '不统计班级成绩后N人输入错误'
      }
    }
    if (this.skipCalConfig.skipClassLastPercent != null) {
      if (
        !isPercentNumber(this.skipCalConfig.skipClassLastPercent) ||
        this.skipCalConfig.skipClassLastPercent == 0 ||
        this.skipCalConfig.skipClassLastPercent == 100
      ) {
        return '不统计班级成绩后百分比输入错误'
      }
    }
  }

  export() {
    let totalItems = this.totalScores
      .filter(s => s.enabled)
      .map(s => ({
        id: s.subjectId,
        writtenScoreRatio: s.writtenScoreRatio,
        otherScoreRatio: s.otherScoreRatio,
        ratio: s.subjectRatio,
      }))
    totalItems.unshift({
      id: 0,
      writtenScoreRatio: 1,
      otherScoreRatio: 1,
      ratio: 1,
    })

    let gradeScores = this.gradeScores

    let rates = this.scoreRates.map(s => {
      let items = s.items.map((item, idx) => {
        item.range = mergeRange(item)
        return {
          name: item.name,
          range: item.range,
          sortCode: idx,
        }
      })
      return {
        id: s.subjectId,
        way: s.statsWay,
        beans: items,
      }
    })

    let levels = this.scoreLevels.map(s => {
      let items = s.items.map((item, idx) => {
        item.range = mergeRange(item)
        return {
          name: item.name,
          range: item.range,
          sortCode: idx,
        }
      })
      return {
        id: s.subjectId,
        way: s.statsWay,
        beans: items,
      }
    })

    let scoreIntervals = this.scoreIntervals.map(s => ({
      id: s.subjectId,
      intervals: s.intervals
        ? s.intervals.map((interval, idx) => {
            interval.sortCode = idx + 1
            return interval
          })
        : undefined,
    }))

    let rankIntervals = this.rankIntervals.map(s => ({
      id: s.subjectId,
      intervals: s.intervals
        ? s.intervals.map((interval, idx) => {
            interval.sortCode = idx + 1
            return interval
          })
        : undefined,
    }))

    let lines = this.lines || []

    let statsConfig = this.statsStudents.map(s => ({
      id: s.subjectId,
      isAbnormalCount: s.statsAbsentStudents,
      allAbnormalCount: s.statsAllSubjectAbsentStudents,
      countOnAllNormal: s.countOnAllNormal,
      students: s.excludeStudentIds,
      classIds: s.excludeClassIds,
      schoolIds: s.excludeSchoolIds,
    }))

    let config = {
      totalItems,
      gradeScores,
      rates,
      levels,
      scoreIntervals: scoreIntervals.length ? scoreIntervals : undefined,
      rankIntervals: rankIntervals.length ? rankIntervals : undefined,
      lines,
      statsConfig,
      isChildSchExp: this.isChildSchExp,
      compareExam: this.compareExam,
      skipCalConfig: this.skipCalConfig,
      skipStudentCalcScore: this.skipStudentCalcScore,
      misc: this.misc,
      roundConfigs: this.roundConfigs,
    }

    return JSON.stringify(config)
  }

  importByJSON(importedParameters, isSenior = false, showChildSchExp = false) {
    if (importedParameters) {
      // total scores
      if (
        this.totalScores &&
        this.totalScores.length &&
        importedParameters.totalScores &&
        importedParameters.totalScores.length
      ) {
        this.totalScores.forEach(originTotalScore => {
          let targetTotalScore = importedParameters.totalScores.find(
            importedTotalScore => importedTotalScore.subjectId === originTotalScore.subjectId
          )
          if (
            targetTotalScore &&
            typeof targetTotalScore.enabled === 'boolean' &&
            typeof targetTotalScore.otherScoreRatio === 'number' &&
            typeof targetTotalScore.subjectRatio === 'number' &&
            typeof targetTotalScore.writtenScoreRatio === 'number'
          ) {
            originTotalScore.enabled = targetTotalScore.enabled
            originTotalScore.otherScoreRatio = targetTotalScore.otherScoreRatio
            originTotalScore.subjectRatio = targetTotalScore.subjectRatio
            originTotalScore.writtenScoreRatio = targetTotalScore.writtenScoreRatio
          }
        })
      }

      // grade scores
      if (
        isSenior &&
        importedParameters.gradeScores &&
        importedParameters.gradeScores.length &&
        importedParameters.gradeScores.every(
          importedGradeScore =>
            typeof importedGradeScore.name === 'string' &&
            typeof importedGradeScore.maxScore === 'number' &&
            typeof importedGradeScore.minScore === 'number' &&
            typeof importedGradeScore.proportion === 'number'
        )
      ) {
        this.gradeScores = importedParameters.gradeScores
      }

      // score rates
      if (
        this.scoreRates &&
        this.scoreRates.length &&
        importedParameters.scoreRates &&
        importedParameters.scoreRates.length
      ) {
        this.scoreRates.forEach(originScoreRate => {
          let targetScoreRate = importedParameters.scoreRates.find(
            importedScoreRate => importedScoreRate.subjectId === originScoreRate.subjectId
          )
          if (
            targetScoreRate &&
            typeof targetScoreRate.statsWay === 'number' &&
            typeof targetScoreRate.items &&
            typeof targetScoreRate.items.length &&
            targetScoreRate.items.every(
              item =>
                typeof item.maxValue === 'number' &&
                typeof item.maxValueIncluded === 'boolean' &&
                typeof item.minValue === 'number' &&
                typeof item.minValueIncluded === 'boolean' &&
                typeof item.name === 'string' &&
                typeof item.range === 'string' &&
                typeof item.sortCode === 'number'
            )
          ) {
            originScoreRate.statsWay = targetScoreRate.statsWay
            originScoreRate.items = targetScoreRate.items
          }
        })
      }

      // score levels
      if (
        this.scoreLevels &&
        this.scoreLevels.length &&
        importedParameters.scoreLevels &&
        importedParameters.scoreLevels.length
      ) {
        this.scoreLevels.forEach(originScoreLevel => {
          let targetScoreLevel = importedParameters.scoreLevels.find(
            importedScoreLevel => importedScoreLevel.subjectId === originScoreLevel.subjectId
          )
          if (
            targetScoreLevel &&
            typeof targetScoreLevel.statsWay === 'number' &&
            typeof targetScoreLevel.items &&
            typeof targetScoreLevel.items.length &&
            targetScoreLevel.items.every(
              item =>
                typeof item.maxValue === 'number' &&
                typeof item.maxValueIncluded === 'boolean' &&
                typeof item.minValue === 'number' &&
                typeof item.minValueIncluded === 'boolean' &&
                typeof item.name === 'string' &&
                typeof item.range === 'string' &&
                typeof item.sortCode === 'number'
            )
          ) {
            originScoreLevel.statsWay = targetScoreLevel.statsWay
            originScoreLevel.items = targetScoreLevel.items
          }
        })
      }

      // lines
      if (
        importedParameters.lines &&
        importedParameters.lines.length &&
        importedParameters.lines.every(importedLines => importedLines.beans && importedLines.beans.length)
      ) {
        this.lines = importedParameters.lines
      }

      // score interval
      if (importedParameters.scoreIntervals) {
        this.scoreIntervals.forEach(x => {
          const TargetSetting = importedParameters.scoreIntervals.find(y => y.subjectId === x.subjectId)
          if (TargetSetting) {
            x.intervals = deepCopy(TargetSetting.intervals)
          }
        })
      }

      // rank interval
      if (importedParameters.rankIntervals) {
        this.rankIntervals.forEach(x => {
          const TargetSetting = importedParameters.rankIntervals.find(y => y.subjectId === x.subjectId)
          if (TargetSetting) {
            x.intervals = deepCopy(TargetSetting.intervals)
          }
        })
      }

      // child exp
      if (showChildSchExp && typeof importedParameters.isChildSchExp === 'boolean') {
        this.isChildSchExp = importedParameters.isChildSchExp
      }

      // stats students
      if (
        this.statsStudents &&
        this.statsStudents.length &&
        importedParameters.statsStudents &&
        importedParameters.statsStudents.length
      ) {
        this.statsStudents.forEach(originStatsStudent => {
          let targetStatsStudent = importedParameters.statsStudents.find(
            importedStatsStudent => importedStatsStudent.subjectId === originStatsStudent.subjectId
          )
          if (
            targetStatsStudent &&
            typeof targetStatsStudent.statsAbsentStudents === 'boolean' &&
            typeof targetStatsStudent.statsAllSubjectAbsentStudents === 'boolean'
          ) {
            originStatsStudent.statsAbsentStudents = targetStatsStudent.statsAbsentStudents
            originStatsStudent.statsAllSubjectAbsentStudents = targetStatsStudent.statsAllSubjectAbsentStudents
          }
        })
      }

      if (importedParameters.skipStudentCalcScore) {
        this.skipStudentCalcScore = true
      }

      if (importedParameters.misc) {
        this.misc.calcSchoolZScore = Boolean(importedParameters.misc.calcSchoolZScore)
      }
    }
  }
}

export function splictRange(range) {
  let minValueIncluded = range[0] === '['
  let maxValueIncluded = range[range.length - 1] === ']'
  let numbers = range
    .substring(1, range.length - 1)
    .split(',')
    .map(n => parseFloat(n))
  let minValue = numbers[0],
    maxValue = numbers[1]
  return {
    minValue,
    minValueIncluded,
    maxValue,
    maxValueIncluded,
  }
}

export function mergeRange(item) {
  let minValueIncludedStr = item.minValueIncluded ? '[' : '('
  let maxValueIncludedStr = item.maxValueIncluded ? ']' : ')'
  return `${minValueIncludedStr}${item.minValue},${item.maxValue}${maxValueIncludedStr}`
}

function isStrictNonNegative(n) {
  return typeof n === 'number' && n >= 0
}

function isRateNumber(n) {
  return isStrictNonNegative(n) && n <= 1
}

function isScoreNumber(n) {
  return isStrictNonNegative(n)
}

function isRankNumber(n) {
  return isStrictNonNegative(n) && Number.isInteger(n)
}

function isPercentNumber(n) {
  return typeof n === 'number' && n >= 0 && n <= 100
}
