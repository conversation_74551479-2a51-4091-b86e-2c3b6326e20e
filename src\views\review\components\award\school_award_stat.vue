<template>
  <div class="container-school-award-stat">
    <div class="section-filter-bar">
      <div class="box-bar-left">
        <div class="filter-school">
          <Select
            v-model="currentSchoolId"
            style="width: 200px"
            clearable
            filterable
            placeholder="学校"
            @on-change="onChangeSchool"
          >
            <Option v-for="item in schoolListForSelect" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </div>
      </div>
      <div class="box-bar-right">
        <Button :disabled="!awardList.length" @click="onExport">导出</Button>
      </div>
    </div>
    <div class="section-table">
      <Table :data="awardList" :columns="tableColumns" :span-method="handleSpan"></Table>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { apiGetAwardSchoolStatistics, apiExportAwardSchoolStatistics, apiGetAwardConfig } from '@/api/review/activity'
  import { exportExcel } from '@/utils/excel_export'
  import { downloadBlob } from '@/utils/download'
  import { groupArray } from '@/utils/array'
  import RevewAwardStatusEnum from '@/enum/review/review_award_status'

  export default {
    data() {
      return {
        awardList: [],
        currentSchoolId: '',
        awardConfig: [],
      }
    },
    computed: {
      ...mapGetters('review', [
        'isAdmin',
        'isRegisterAuditor',
        'isSchoolAdmin',
        'isSchoolPersonInCharge',
        'enableSchoolAudit',
        'isMultipleSchool',
        'userList',
        'categories',
        'participantIdentity',
        'activityId',
        'schoolList',
        'currentActivity',
      ]),
      isAwardSuccess() {
        return this.currentActivity?.awardStatus === RevewAwardStatusEnum.AwardSuccess.id
      },
      activitySchoolId() {
        return this.$store.getters['review/currentActivity']?.schoolId
      },
      activitySchoolName() {
        return this.$store.getters['review/currentActivity']?.schoolName
      },
      userSchoolId() {
        return this.$store.getters['user/info'].schoolId
      },
      schoolListForSelect() {
        let list = this.schoolList.slice()
        return list
      },
      tableColumns() {
        let columns = [
          {
            key: 'schoolName',
            title: '学校',
          },
          // {
          //   key: 'awardName',
          //   title: '奖项',
          //   align: 'center',
          // },
          // {
          //   key: 'awardCount',
          //   title: '奖项人数',
          //   align: 'center',
          // },
          // {
          //   key: 'categoryName',
          //   title: '参评类别',
          // },
        ]

        this.categories.forEach(c => {
          let categoryAwards = this.awardConfig
            .filter(a => a.categoryId === c.id)
            .map(a => ({
              categoryId: a.categoryId,
              name: a.name,
              title: a.name,
              sortOrder: a.sortOrder,
              align: 'center',
              render: (h, params) => {
                const theawardItem = params.row.list.find(
                  item => item.categoryId === a.categoryId && item.awardName === a.name
                )
                return h('span', {}, theawardItem ? theawardItem.awardCount : '-')
              },
            }))

          columns.push({
            title: c.name,
            children: categoryAwards,
            align: 'center',
          })
        })

        return columns
      },
    },
    created() {
      if (this.isAwardSuccess) {
        Promise.all([this.fetchSchoolStatistics(), this.fetchAwardConfig()])
      }
    },
    methods: {
      onChangeSchool() {
        this.awardList = []
        this.fetchSchoolStatistics()
      },
      fetchAwardConfig() {
        apiGetAwardConfig({
          activityId: this.activityId,
        }).then(res => {
          this.awardConfig = res
        })
      },
      fetchSchoolStatistics() {
        apiGetAwardSchoolStatistics({
          activityId: this.activityId,
          schoolId: this.currentSchoolId || undefined,
        }).then(res => {
          this.awardList = groupArray(res || [], k => k.schoolId).map(item => ({
            schoolId: item.key,
            schoolName: (item.group && item.group.length && item.group[0].schoolName) || '',
            list: item.group || [],
          }))
          // this.awardList = res || []
        })
      },
      handleSpan({ row, rowIndex, columnIndex }) {
        // const index = this.awardList.findIndex(item => item.schoolId === row.schoolId)
        // const len = this.awardList.filter(item => item.schoolId === row.schoolId).length
        // if (columnIndex === 0) {
        //   if (index === rowIndex) {
        //     return [len, 1]
        //   }

        //   return [0, 0]
        // }
        return [1, 1]
      },
      onExport() {
        // const sheets = [
        //   {
        //     sheetName: '学校获奖统计',
        //     rows: this.awardList,
        //     columns: [
        //       {
        //         key: 'schoolName',
        //         title: '学校',
        //         group: true,
        //       },
        //       {
        //         key: 'awardName',
        //         title: '奖项',
        //         align: 'center',
        //       },
        //       {
        //         key: 'awardCount',
        //         title: '奖项人数',
        //         align: 'center',
        //       },
        //       {
        //         key: 'categoryName',
        //         title: '参评类别',
        //       },
        //     ],
        //   },
        // ]
        // exportExcel(sheets, `${this.currentActivity.name}学校获奖统计.xlsx`)

        apiExportAwardSchoolStatistics({
          activityId: this.activityId,
        }).then(res => {
          downloadBlob(res, `${this.currentActivity.name}学校获奖统计.xlsx`)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-school-award-stat {
    .section-filter-bar {
      @include flex(row, space-between, center);
      margin-bottom: 10px;
    }
  }
</style>
