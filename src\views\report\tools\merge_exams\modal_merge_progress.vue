<script>
  import { apiGetMergeExamDetail, apiGetExamMergeProgess } from '@/api/emarking'

  import { deepCopy } from '@/utils/object'

  import MergeProjectStatuses from '@/enum/emarking/merge_exam/project_status.js'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      projectStatus: {
        type: String,
        default: '',
      },
      projectMergeProgress: {
        type: Object,
        default: () => ({}),
      },
    },

    emits: ['update:modelValue', 'update-project-status'],

    data() {
      return {
        projectDetail: null,
      }
    },

    computed: {
      projectId() {
        return this.projectMergeProgress?.id || ''
      },
      projectName() {
        return this.projectMergeProgress?.name || ''
      },
      projectProgress() {
        if (this.projectMergeProgress?.progress) {
          const Progress = deepCopy(this.projectMergeProgress.progress)

          ;(Progress.srcExams || []).forEach(e => {
            const TargetExam = (this.projectDetail?.srcExams || []).find(de => de.examId === e.examId)
            if (TargetExam) {
              e.examName = TargetExam.examName
              ;(e.srcSubjects || []).forEach(s => {
                const TargetSubject = (TargetExam.subjects || []).find(ds => ds.subjectId === s.subjectId)
                if (TargetSubject) {
                  s.subjectName = TargetSubject.subjectName
                }
              })
            }
          })

          return Progress
        } else {
          return {}
        }
      },
      projectStatusText() {
        return (this.mergeProjectStatuses.find(s => s.id === this.projectStatus) || { name: '' }).name
      },
      modalTitle() {
        return `项目合并进度${this.projectName ? '（ ' + this.projectName + ' ）' : ''}${this.projectStatusText ? ' —— ' + this.projectStatusText : ''}`
      },
      mergeProjectStatuses() {
        return MergeProjectStatuses.getEntries()
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.fetchMergeExamDetail()
        } else {
          this.onCloseModal()
        }
      },

      fetchMergeExamDetail() {
        if (this.projectId) {
          apiGetMergeExamDetail(this.projectId).then(response => (this.projectDetail = response))
        }
      },

      fetchMergeExamProgress() {
        if (this.projectId) {
          apiGetExamMergeProgess(this.projectId).then(response => {
            this.$emit('update-project-status', response)
          })
        }
      },

      getStatusText(status) {
        return status === 'succeeded' ? '已完成' : status === 'in_progress' ? '进行中' : ''
      },
    },
  }
</script>

<template>
  <Modal :model-value="modelValue" :title="modalTitle" width="720" @on-visible-change="handleModalVisibleChange">
    <div class="modal-merge-progress">
      <div class="step-level1">
        <div class="label">新建考试项目</div>
        <div class="status-and-time">
          <div class="status">
            状态：{{
              getStatusText(projectProgress && projectProgress.createExam && projectProgress.createExam.status) || '-'
            }}
          </div>
          <div class="time">
            耗时：{{
              ((projectProgress && projectProgress.createExam && projectProgress.createExam.costMilliSeconds) || '0') +
              'ms'
            }}
          </div>
        </div>
      </div>

      <div class="step-level1">
        <div class="label">准备目标考试项目数据</div>
        <div class="status-and-time">
          <div class="status">
            状态：{{
              getStatusText(
                projectProgress && projectProgress.prepareDstExamData && projectProgress.prepareDstExamData.status
              ) || '-'
            }}
          </div>
          <div class="time">
            耗时：{{
              ((projectProgress &&
                projectProgress.prepareDstExamData &&
                projectProgress.prepareDstExamData.costMilliSeconds) ||
                '0') + 'ms'
            }}
          </div>
        </div>
      </div>

      <div v-if="projectProgress && projectProgress.srcExams && projectProgress.srcExams.length" class="step-level1">
        <div class="label">源项目数据处理</div>
        <div v-for="sp of projectProgress.srcExams" :key="sp.examId" class="step-level2">
          <div class="exam-name">{{ sp.examName || '-' }}</div>
          <div class="step-level3">
            <div class="label">准备源项目数据</div>
            <div class="status-and-time">
              <div class="status">
                状态：{{ getStatusText(sp.prepareSrcExamData && sp.prepareSrcExamData.status) || '-' }}
              </div>
              <div class="time">
                耗时：{{ ((sp.prepareSrcExamData && sp.prepareSrcExamData.costMilliSeconds) || '0') + 'ms' }}
              </div>
            </div>
          </div>
          <div class="step-level3">
            <div class="label">复制参考学校</div>
            <div class="status-and-time">
              <div class="status">
                状态：{{ getStatusText(sp.copyExamSchools && sp.copyExamSchools.status) || '-' }}
              </div>
              <div class="time">
                耗时：{{ ((sp.copyExamSchools && sp.copyExamSchools.costMilliSeconds) || '0') + 'ms' }}
              </div>
            </div>
          </div>
          <div class="step-level3">
            <div class="label">复制参考学生</div>
            <div class="status-and-time">
              <div class="status">
                状态：{{ getStatusText(sp.copyExamStudents && sp.copyExamStudents.status) || '-' }}
              </div>
              <div class="time">
                耗时：{{ ((sp.copyExamStudents && sp.copyExamStudents.costMilliSeconds) || '0') + 'ms' }}
              </div>
            </div>
          </div>
          <div v-if="sp.srcSubjects && sp.srcSubjects.length" class="step-level3">
            <div class="label">复制考试科目</div>
            <div v-for="s of sp.srcSubjects" :key="sp.examId + s.subjectId" class="step-level4">
              <div class="label">{{ s.subjectName }}</div>
              <div class="step-level5">
                <div class="label">准备科目数据</div>
                <div class="status-and-time">
                  <div class="status">
                    状态：{{ getStatusText(s.prepareSrcExamSubjectData && s.prepareSrcExamSubjectData.status) || '-' }}
                  </div>
                  <div class="time">
                    耗时：{{
                      ((s.prepareSrcExamSubjectData && s.prepareSrcExamSubjectData.costMilliSeconds) || '0') + 'ms'
                    }}
                  </div>
                </div>
              </div>
              <div class="step-level5">
                <div class="label">复制科目考生</div>
                <div class="status-and-time">
                  <div class="status">
                    状态：{{ getStatusText(s.copyExamSubjectStudent && s.copyExamSubjectStudent.status) || '-' }}
                  </div>
                  <div class="time">
                    耗时：{{ ((s.copyExamSubjectStudent && s.copyExamSubjectStudent.costMilliSeconds) || '0') + 'ms' }}
                  </div>
                </div>
              </div>
              <div class="step-level5">
                <div class="label">复制科目成绩</div>
                <div class="status-and-time">
                  <div class="status">状态：{{ getStatusText(s.copyScore && s.copyScore.status) || '-' }}</div>
                  <div class="time">耗时：{{ ((s.copyScore && s.copyScore.costMilliSeconds) || '0') + 'ms' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <Button ghost type="primary" icon="md-refresh" @click="fetchMergeExamProgress">刷新</Button>

        <Button type="primary" @click="onCloseModal">关闭</Button>
      </div>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-merge-progress {
    max-height: calc(70vh);
    overflow-y: auto;

    .step-level1 {
      .label {
        color: $color-primary-light;
        font-weight: bold;
        font-size: $font-size-medium-x;
      }

      &:not(:first-child) {
        margin-top: 12px;
      }

      .step-level2 {
        margin-top: 2px;
        padding-left: 2em;

        .exam-name {
          color: $color-text-btn-blue;
          font-size: $font-size-medium-x;
        }

        .step-level3 {
          margin-top: 2px;
          padding-left: 1.6em;

          .label {
            color: $color-primary-light;
            font-weight: normal;
            font-size: $font-size-medium-xs;
          }

          .step-level4 {
            margin-top: 2px;
            padding-left: 1.4em;

            .label {
              color: $color-text-btn-blue;
              font-size: $font-size-medium-xs;
            }

            .step-level5 {
              margin-top: 2px;
              padding-left: 1.2em;

              .label {
                color: $color-primary;
                font-size: $font-size-medium;
              }
            }
          }
        }
      }
    }

    .status-and-time {
      @include flex(row, flex-start, center);
      width: 60%;
      margin-top: 2px;
      margin-left: 1em;

      .status {
        flex: 1;
      }

      .time {
        flex: 1;
      }
    }
  }

  .modal-footer {
    @include flex(row, space-between, center);
  }
</style>
