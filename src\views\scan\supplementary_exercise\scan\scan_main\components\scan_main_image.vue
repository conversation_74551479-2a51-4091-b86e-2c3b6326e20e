<template>
  <div class="image" :style="imagePaneStyle">
    <div v-if="noImage" class="empty">暂无实时图像</div>
    <template v-else>
      <div class="image-column image-column-left">
        <img v-if="scanImageLeft" :src="scanImageLeft" :style="imageStyle" @click="openImage(scanImageLeft)" />
      </div>
      <div class="image-column image-column-right">
        <img v-if="scanImageRight" :src="scanImageRight" :style="imageStyle" @click="openImage(scanImageRight)" />
      </div>
    </template>
  </div>
</template>

<script>
  import { base64ToBlob } from '@/utils/blob'

  export default {
    props: {
      scanImageLeft: String,
      scanImageRight: String,
      top: Number,
    },
    computed: {
      noImage() {
        return !this.scanImageLeft && !this.scanImageRight
      },
      imagePaneStyle() {
        return {
          height: `calc(100vh - ${this.top}px)`,
        }
      },
      imageStyle() {
        return {
          maxHeight: `calc(100vh - ${this.top}px)`,
        }
      },
    },
    methods: {
      openImage(imageSrc) {
        const blob = base64ToBlob(imageSrc)
        const url = URL.createObjectURL(blob)
        const newWin = window.open(url, '_blank')
        if (!newWin) {
          this.$Message.warning({
            content: '请允许弹窗后重试',
            duration: 3,
          })
          return
        }
        const interval = setInterval(() => {
          if (newWin.closed) {
            URL.revokeObjectURL(url)
            clearInterval(interval)
          }
        }, 1000)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image {
    @include flex(row, flex-start, flex-start);

    .empty {
      width: 100%;
      color: $color-icon;
      line-height: 200px;
      text-align: center;
    }

    .image-column {
      width: 50%;
    }

    .image-column-left {
      @include flex(row, flex-end, flex-start);
    }

    .image-column-right {
      @include flex(row, flex-start, flex-start);
    }

    img {
      max-width: 100%;
      cursor: zoom-in;
    }
  }
</style>
