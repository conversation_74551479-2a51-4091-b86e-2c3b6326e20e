<template>
  <el-col :span="6">
    <!-- <el-date-picker type="date" :placeholder="options.placeholder.value" style="width: 100%"></el-date-picker> -->
    <DatePicker type="date" :placeholder="options.placeholder.value" style="width: 100%" />
  </el-col>
</template>

<script>
  import { ElDatePicker, ElCol } from 'element-plus'
  import 'element-plus/es/components/date-picker/style/css'
  import 'element-plus/es/components/col/style/css'

  export default {
    components: {
      'el-date-picker': ElDatePicker,
      'el-col': ElCol,
    },
    props: {
      options: {
        type: Object,
        default: () => ({
          placeholder: {
            value: '',
          },
          format: {
            value: 'yyyy-MM-dd',
          },
        }),
      },
    },
  }
</script>
