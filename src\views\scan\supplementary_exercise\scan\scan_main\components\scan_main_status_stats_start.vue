<template>
  <div v-show="showFixedStats || showFixedBtnScan" class="fixed-bar" :style="fixedBarStyle">
    <ScanStatus
      class="scan-status"
      :scanner-status-id="scannerStatusId"
      :style="statusStyle"
      @refresh="refresh"
    ></ScanStatus>
    <ScanStats
      v-show="showFixedStats"
      class="scan-stats"
      :stats="stats"
      inline
      @to-monitor="toMonitor"
      @to-exception="toException"
    ></ScanStats>
    <ScanBtn
      v-show="showFixedBtnScan"
      class="scan-btn"
      :current-batch-status="currentBatchStatus"
      :scanner-status-id="scannerStatusId"
      :stats="stats"
      inline
      @start-scan="startScan"
    ></ScanBtn>
  </div>
  <ScanStatus :scanner-status-id="scannerStatusId" :style="statusStyle" @refresh="refresh"></ScanStatus>
  <ScanStats ref="scanStats" :stats="stats" @to-monitor="toMonitor" @to-exception="toException"></ScanStats>
  <ScanBtn
    ref="scanBtn"
    :scanner-status-id="scannerStatusId"
    :current-batch-status="currentBatchStatus"
    :stats="stats"
    @start-scan="startScan"
  ></ScanBtn>
</template>

<script>
  import ScanStatus from './scan_status.vue'
  import ScanStats from './scan_stats.vue'
  import ScanBtn from './scan_btn.vue'

  import { throttle } from '@/utils/function'

  let windowException = null

  export default {
    components: {
      ScanStatus,
      ScanStats,
      ScanBtn,
    },
    props: {
      scannerStatusId: String,
      currentBatchStatus: String,
      stats: Object,
      top: Number,
      fixedHeight: Number,
    },
    emits: ['refresh', 'start-scan', 'to-monitor'],
    data() {
      return {
        showFixedStats: false,
        showFixedBtnScan: false,
      }
    },
    computed: {
      hasExam() {
        return this.$store.getters['scan/hasExam']
      },
      statusStyle() {
        return {
          height: this.fixedHeight + 'px',
        }
      },
      fixedBarStyle() {
        return {
          top: this.top + 'px',
        }
      },
    },
    mounted() {
      this.$parent.$el.addEventListener('scroll', this.handleScroll)
    },
    beforeUnmount() {
      this.$parent.$el.removeEventListener('scroll', this.handleScroll)
    },
    methods: {
      refresh() {
        this.$emit('refresh')
      },
      startScan() {
        this.$emit('start-scan')
      },
      toMonitor(status) {
        if (!this.hasExam) {
          return
        }
        this.$emit('to-monitor', status)
      },
      toException(query) {
        if (!this.hasExam) {
          return
        }
        // 只允许打开一个异常处理窗口
        if (windowException) {
          windowException.close()
        }
        let route = this.$router.resolve({
          name: 'scan-answerSheet-exception',
          params: {
            examId: this.$store.getters['scan/examId'],
            examSubjectId: this.$store.getters['scan/examSubjectId'],
          },
          query,
        })
        windowException = window.open(route.href)
      },
      handleScroll: throttle(function () {
        let elContainer = this.$parent.$el
        let elStats = this.$refs.scanStats.$el
        let elBtnScan = this.$refs.scanBtn.$el
        if (!(elContainer && elStats && elBtnScan)) {
          return
        }
        let containerRect = elContainer.getBoundingClientRect()
        let statsRect = elStats.getBoundingClientRect()
        let btnScanRect = elBtnScan.getBoundingClientRect()
        this.showFixedStats = statsRect.top < containerRect.top
        this.showFixedBtnScan = btnScanRect.top < containerRect.top + this.fixedHeight
      }, 200),
    },
  }
</script>

<style lang="scss" scoped>
  .fixed-bar {
    @include flex(row, flex-start, center);
    position: fixed;
    right: 24px;
    left: 16px;
    z-index: 1;
    background-color: white;

    .scan-status,
    .scan-btn {
      flex-shrink: 0;
    }

    .scan-stats {
      flex-shrink: 1;
      margin-left: 50px;
    }
  }
</style>
