import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiGetChildOrgAndSchool } from '@/api/user/school'

export const useDashboardStore = defineStore('dashboard', () => {
  const currentSemester = ref(null)
  const currentTerm = ref(null)
  const schools = ref([])
  const currentSchool = ref(null)
  const monthExamStat = ref([])
  const examsForSelect = ref([])
  const currentExam = ref(null)
  const mainExamTemplate = ref(null)
  const mainExamSubject = ref(null)

  const examCount = computed(() => {
    return monthExamStat.value.reduce((acc, cur) => {
      return acc + cur.schoolExamCount + cur.unionExamCount + cur.coachExamCount
    }, 0)
  })

  function flattenSchools(arr = [], level = 0) {
    return arr.reduce((acc, val) => {
      acc.push({
        ...val,
        level,
      })
      return Array.isArray(val.schools) ? acc.concat(flattenSchools(val.schools, level + 1)) : acc.concat([val])
    }, [])
  }

  function reset() {
    currentSemester.value = null
    currentTerm.value = null
    currentSchool.value = null
    mainExamTemplate.value = null
    mainExamSubject.value = null
  }

  async function loadSchools(schoolId) {
    const res = await apiGetChildOrgAndSchool({
      eduSchoolId: schoolId,
    })

    schools.value = [
      {
        schoolName: '全体',
        schoolId: '0',
        level: 0,
      },
      ...flattenSchools(res, 1),
    ]
  }

  return {
    currentSemester,
    currentTerm,
    schools,
    currentSchool,
    monthExamStat,
    examCount,
    examsForSelect,
    currentExam,
    mainExamTemplate,
    mainExamSubject,
    reset,
    loadSchools,
  }
})
