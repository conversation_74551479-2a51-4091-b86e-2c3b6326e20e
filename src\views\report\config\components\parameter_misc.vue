<template>
  <div class="container-parameter-misc">
    <div class="misc-item a-line">
      <div class="item-label">计算学校标准分</div>
      <div class="item-content">
        <i-switch v-model="misc.calcSchoolZScore"></i-switch>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      misc: Object,
    },
  }
</script>

<style lang="scss" scoped>
  .misc-item {
    &.a-line {
      @include flex(row, flex-start, center);

      .item-label {
        margin-right: 8px;
      }
    }
  }
</style>
