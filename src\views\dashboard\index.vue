<template>
  <div ref="containerRef" class="container-dashboard">
    <div v-if="containerWidth > 0" class="dashboard-inner" :style="dashboardInnerStyle">
      <PanelDashboardHeader class="dashboard-header" @change-screen-mode="handleScreenMode"></PanelDashboardHeader>
      <div class="dashboard-main">
        <div class="main-left">
          <BorderBox10>
            <div class="main-left-inner">
              <PanelRegionBase class="panel-region-base"></PanelRegionBase>
              <div class="section-divider">
                <Decoration2 style="width: 100%; height: 4px" />
              </div>
              <PanelRegionStat class="panel-region-stat"></PanelRegionStat>
              <div class="section-divider" style="margin-top: 8px">
                <Decoration2 style="width: 100%; height: 4px" />
              </div>
              <PanelRegionActivity class="panel-region-activity"></PanelRegionActivity>
            </div>
          </BorderBox10>
        </div>
        <div class="main-center">
          <BorderBox10>
            <div class="main-center-inner">
              <PanelExamTotal class="panel-exam-total"></PanelExamTotal>
              <PanelExamSubject class="panel-exam-subject"></PanelExamSubject>
            </div>
          </BorderBox10>
        </div>
        <div class="main-right">
          <BorderBox10>
            <div class="mainer-right-inner">
              <PanelDevelopment class="panel-development"></PanelDevelopment>
            </div>
          </BorderBox10>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onBeforeMount, onMounted, onBeforeUnmount, useTemplateRef } from 'vue'
  import fscreen from 'fscreen'
  import store from '@/store'
  import { BorderBox10, Decoration2 } from '@newpanjing/datav-vue3'
  import { useDashboardStore } from '../../store/dashboard'

  // 引入全部panels
  import PanelDashboardHeader from './panels/panel_dashboard_header.vue'
  import PanelRegionBase from './panels/panel_region_base.vue'
  import PanelRegionStat from './panels/panel_region_stat.vue'
  import PanelRegionActivity from './panels/panel_region_activity.vue'
  import PanelExamTotal from './panels/panel_exam_total.vue'
  import PanelExamSubject from './panels/panel_exam_subject.vue'
  import PanelDevelopment from './panels/panel_development.vue'

  import { throttle } from '@/utils/function'

  const dashboardStore = useDashboardStore()
  const containerRef = useTemplateRef('containerRef')

  // 设计稿尺寸
  const designMinWidth = 1600
  const designMaxWidth = 1920
  const designMaxWidthHeightRatio = 16 / 7
  const designMinWidthHeightRatio = 16 / 11

  // 容器尺寸
  const containerWidth = ref(0)
  const containerHeight = ref(0)
  // 监听容器大小变化
  const containerObserver = new ResizeObserver(throttle(setContainerSize, 100))

  const dashboardInnerStyle = computed(() => {
    // 宽高比
    const containerWidthHeightRatio = Math.max(
      designMinWidthHeightRatio,
      Math.min(designMaxWidthHeightRatio, containerWidth.value / containerHeight.value)
    )
    // 内容无缩放宽度，要求保证所有内容完整显示，高度过小则左右留空，高度过大则底部留空
    let innerWidthNoScale = Math.min(containerWidth.value, containerHeight.value * containerWidthHeightRatio)
    // 缩放后的宽度
    let innerWidth = Math.min(designMaxWidth, Math.max(designMinWidth, innerWidthNoScale))
    // 缩放比例
    let scale = innerWidthNoScale / innerWidth
    // 缩放后的高度
    let innerHeight = innerWidth / containerWidthHeightRatio

    return {
      width: innerWidth + 'px',
      height: innerHeight + 'px',
      transform: `scale(${scale}) translateX(-50%)`,
    }
  })

  const userInfo = computed(() => {
    return store.getters['user/info']
  })

  onBeforeMount(() => {
    getSchoolBaseInfo()
  })
  onMounted(() => {
    containerObserver.observe(containerRef.value)
    setContainerSize()
  })
  onBeforeUnmount(() => {
    containerObserver.disconnect()
  })

  function setContainerSize() {
    containerWidth.value = containerRef.value.clientWidth
    containerHeight.value = containerRef.value.clientHeight
  }

  const getSchoolBaseInfo = () => {
    Promise.all([
      store.dispatch('school/getSchoolTerms'),
      store.dispatch('emarking/ensureBasicData'),
      dashboardStore.loadSchools(userInfo.value.schoolId),
    ])
  }

  const handleScreenMode = () => {
    if (fscreen.fullscreenElement) {
      fscreen.exitFullscreen()
    } else {
      fscreen.requestFullscreen(document.documentElement)
    }
  }
</script>

<style lang="scss" scoped>
  .container-dashboard {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-color: #03064f;

    .dashboard-inner {
      @include flex(column, flex-start, stretch);
      position: absolute;
      top: 0;
      left: 50%;
      background: url('../../assets/images/dashboard/background_page.png') no-repeat top center/ 100% 100%;
      transform-origin: 0 0;
    }

    .dashboard-header {
      flex-shrink: 0;
    }

    .dashboard-main {
      @include flex(row, flex-start, stretch);
      flex-grow: 1;
      gap: 1vw;
      box-sizing: border-box;
      padding: 6px 30px 0;
      overflow: auto;

      .main-left,
      .main-right {
        flex-shrink: 0;
        width: 25%;
        overflow: hidden;
      }

      .main-center {
        flex-grow: 1;
        overflow: hidden;
      }

      .main-left-inner,
      .main-center-inner,
      .mainer-right-inner {
        @include flex(column, flex-start, stretch);
        gap: 1vh;
        height: 100%;
      }

      .panel-region-base,
      .panel-region-stat,
      .panel-exam-total {
        flex-shrink: 0;
      }

      .panel-region-activity,
      .panel-exam-subject {
        flex-grow: 1;
      }

      .section-divider {
        margin: 0 15px;
      }
    }

    :deep(.selector-scaled) {
      position: relative;

      .ivu-select-dropdown {
        top: 24px !important;
        left: 0 !important;
      }
    }
  }
</style>
