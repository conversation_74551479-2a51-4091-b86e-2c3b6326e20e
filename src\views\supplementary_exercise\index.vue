<template>
  <RouterView v-if="isServiceNormal"></RouterView>
  <div v-else class="service-unavailable">
    <Alert v-if="alertContent" type="warning" show-icon>{{ alertContent }}</Alert>
    <div class="inner">
      <img class="post" src="@/assets/images/supplementary_exercise/post.svg" />
    </div>
  </div>
</template>

<script>
  import Store from '@/store/index'
  import iView from '@/iview'
  import ModuleEnum from '@/enum/user/module'

  export default {
    beforeRouteEnter(to, from, next) {
      iView.LoadingBar.start()
      // 评审设置需要用到基础数据
      Promise.all([Store.dispatch('emarking/ensureBasicData'), Store.dispatch('qlib/ensureBasicData')])
        .then(() => {
          if (to.name === 'supplementaryExerciseReport') {
            const IsServiceNormal = Store.getters['user/isModuleNormal'](ModuleEnum.CoachHomework.id)
            to.meta.showHeader = !IsServiceNormal
            next()
          } else {
            next()
          }
        })
        .catch(() => {
          iView.LoadingBar.error()
        })
        .finally(() => {
          iView.LoadingBar.finish()
        })
    },

    computed: {
      isServiceNormal() {
        return this.$store.getters['user/isModuleNormal'](ModuleEnum.CoachHomework.id)
      },

      isServiceExpired() {
        return this.$store.getters['user/isModuleExpired'](ModuleEnum.CoachHomework.id)
      },

      alertContent() {
        if (this.isServiceExpired) {
          const ServiceEndDate = this.$store.getters['user/moduleEndDate'](ModuleEnum.CoachHomework.id)
          return `您的${ModuleEnum.CoachHomework.name}已于 ${ServiceEndDate} 到期`
        } else {
          return ''
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .service-unavailable {
    .inner {
      background-color: white;

      .post {
        width: 100%;
      }
    }
  }
</style>
