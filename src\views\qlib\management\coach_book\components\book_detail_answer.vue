<script>
  import TextButton from '@/components/text_button'
  import FileUploader from '@/components/upload'
  import { Switch } from 'view-ui-plus'

  import {
    apiSetCoachBookPaperLocked,
    apiDownloadCoachBookCustomPapersContentPDFZip,
    apiDownloadCoachBookCustomPapersAnswerPDFZip,
    apiDownloadCoachBookCustomPapersListeningAudioZip,
    apiUploadPaperContentPDF,
    apiUploadPaperAnswerPDF,
    apiUploadPaperListeningAudio,
    apiDeletePaperContentPDF,
    apiDeletePaperAnswerPDF,
    apiDeletePaperListeningAudio,
  } from '@/api/qlib/coach_book'

  import { downloadBlob, downloadUrl } from '@/utils/download'
  import { formatDateTime } from '@/utils/date'

  export default {
    components: {
      'file-uploader': FileUploader,
    },

    props: {
      book: {
        type: Object,
        default: () => null,
      },
      paperList: {
        type: Array,
        default: () => [],
      },
    },

    emits: ['refresh'],

    data() {
      return {
        tableColumns: [
          {
            type: 'selection',
            width: 40,
          },
          {
            title: '试卷号',
            width: 100,
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.coachBookCode && params.row.sortCode
                  ? `${params.row.coachBookCode}-${params.row.sortCode.toString().padStart(2, '0')}`
                  : ''
              ),
          },
          {
            title: '试卷名称',
            key: 'name',
            minWidth: 150,
          },
          {
            title: '锁定',
            width: 250,
            align: 'center',
            render: (h, params) => {
              return h(Switch, {
                modelValue: params.row.locked,
                disabled: !(this.isSystemAdministrator || this.isQuestionLibraryAdministrator),
                onOnChange: value => this.changePaperLocked(params.row, value),
              })
            },
          },
          {
            title: '正文PDF',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.contentPDFUrl
                ? h('div', {}, [
                    h(
                      TextButton,
                      {
                        type: 'primary',
                        onClick: () => this.downloadPaperFile('content', params.row),
                      },
                      () => '下载'
                    ),
                    h(
                      TextButton,
                      {
                        type: 'warning',
                        onClick: () => this.deletePaperFile('content', params.row),
                      },
                      () => '删除'
                    ),
                  ])
                : h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.handleBtnUploadFileClick('content', params.row),
                    },
                    () => '上传'
                  ),
          },
          {
            title: '答案PDF',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.answerPDFUrl
                ? h('div', {}, [
                    h(
                      TextButton,
                      {
                        type: 'primary',
                        onClick: () => this.downloadPaperFile('answer', params.row),
                      },
                      () => '下载'
                    ),
                    h(
                      TextButton,
                      {
                        type: 'warning',
                        onClick: () => this.deletePaperFile('answer', params.row),
                      },
                      () => '删除'
                    ),
                  ])
                : h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.handleBtnUploadFileClick('answer', params.row),
                    },
                    () => '上传'
                  ),
          },
          {
            title: '听力音频',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.listeningAudioUrl
                ? h('div', {}, [
                    h(
                      TextButton,
                      {
                        type: 'primary',
                        onClick: () => this.downloadPaperFile('audio', params.row),
                      },
                      () => '下载'
                    ),
                    h(
                      TextButton,
                      {
                        type: 'warning',
                        onClick: () => this.deletePaperFile('audio', params.row),
                      },
                      () => '删除'
                    ),
                  ])
                : h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.handleBtnUploadFileClick('audio', params.row),
                    },
                    () => '上传'
                  ),
          },
        ],

        isModalUploadFileShowed: false,
        isFileUploading: false,
        uploadFile: null,
        uploadType: 'content', // 'content','answer' ro 'audio'

        selectedPaper: null,
        selectedPaperList: [],
      }
    },

    computed: {
      isSystemAdministrator() {
        return this.$store.getters['user/isSystemAdministrator']
      },
      isQuestionLibraryAdministrator() {
        return this.$store.getters['user/isQuestionLibraryAdministrator']
      },
      coachBookId() {
        return this.book && this.book.id
      },
      coachBookName() {
        return (this.book && this.book.bookName) || ''
      },
      coachBookHasPaperContentPDF() {
        return this.paperList.some(paper => paper.contentPDFUrl)
      },
      coachBookHasPaperListeningAudio() {
        return this.paperList.some(paper => paper.listeningAudioUrl)
      },
      cocachBookHasPaperAnswerPDF() {
        return this.paperList.some(paper => paper.answerPDFUrl)
      },
      selectedPapersHasPaperContentPDF() {
        return this.selectedPaperList.some(paper => paper.contentPDFUrl)
      },
      selectedPapersHasPaperAnswerPDF() {
        return this.selectedPaperList.some(paper => paper.answerPDFUrl)
      },
      selectedPapersHasPaperListeningAudio() {
        return this.selectedPaperList.some(paper => paper.listeningAudioUrl)
      },
      uploadFileTypeList() {
        return this.uploadType === 'audio' ? ['.mp3'] : ['.pdf']
      },
      modalUploadFileTitle() {
        return (
          (this.selectedPaper?.name?.trim() || '') +
          ' —— ' +
          (this.uploadType === 'content' ? '上传试卷PDF' : this.uploadType === 'audio' ? '上传音频' : '上传答案PDF')
        )
      },
    },

    methods: {
      changePaperLocked(paper, isLocked) {
        apiSetCoachBookPaperLocked({
          coachBookId: this.coachBookId,
          paperId: paper.id,
          locked: isLocked,
        }).finally(() => this.$emit('refresh'))
      },

      getCurrentTimeText() {
        return formatDateTime(new Date(), 'YYYYMMDDHHmm')
      },

      downloadFileZip(type, isDownloadAllPapers = true) {
        if (type && this.coachBookId) {
          let request
          let fileNameSuffix
          let selectedPaperIdList = []

          if (type === 'content') {
            request = apiDownloadCoachBookCustomPapersContentPDFZip
            fileNameSuffix = '同步卷'
            selectedPaperIdList = this.selectedPaperList.filter(p => p.contentPDFUrl).map(p => p.id)
          } else if (type === 'answer') {
            request = apiDownloadCoachBookCustomPapersAnswerPDFZip
            fileNameSuffix = '同步卷答案'
            selectedPaperIdList = this.selectedPaperList.filter(p => p.answerPDFUrl).map(p => p.id)
          } else if (type === 'audio') {
            request = apiDownloadCoachBookCustomPapersListeningAudioZip
            fileNameSuffix = '同步卷音频'
            selectedPaperIdList = this.selectedPaperList.filter(p => p.listeningAudioUrl).map(p => p.id)
          }

          request(this.coachBookId, isDownloadAllPapers ? [] : selectedPaperIdList).then(responseBlob =>
            downloadBlob(
              responseBlob,
              this.coachBookName + '_' + fileNameSuffix + '_' + this.getCurrentTimeText() + '.zip'
            )
          )
        }
      },

      handleBtnUploadFileClick(type, paper) {
        this.uploadFile = null
        this.uploadType = type
        this.selectedPaper = paper
        this.isModalUploadFileShowed = true
      },

      handleAcceptUploaderFile(file) {
        if (file) {
          this.uploadFile = file
        }
      },

      uploadFileDispatch() {
        if (!this.uploadFile) {
          this.$Message.warning({
            duration: 4,
            content: '请选择文件',
          })
          return
        }
        if (this.isFileUploading) {
          return
        }

        this.isFileUploading = true
        const Request =
          this.uploadType === 'content'
            ? apiUploadPaperContentPDF
            : this.uploadType === 'answer'
              ? apiUploadPaperAnswerPDF
              : apiUploadPaperListeningAudio
        Request({
          coachBookId: this.coachBookId,
          paperId: this.selectedPaper.id,
          file: this.uploadFile,
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '上传成功',
            })
            setTimeout(() => (this.isModalUploadFileShowed = false), 1000)
          })
          .finally(() => {
            this.isFileUploading = false
            this.$emit('refresh')
          })
      },

      downloadPaperFile(type, paper) {
        let fileSuffix
        let fileUrl

        if (type === 'content') {
          fileSuffix = '-正文.pdf'
          fileUrl = paper.contentPDFUrl
        } else if (type === 'answer') {
          fileSuffix = '-答案.pdf'
          fileUrl = paper.answerPDFUrl
        } else if (type === 'audio') {
          fileSuffix = '-音频.mp3'
          fileUrl = paper.listeningAudioUrl
        }

        if (fileUrl) {
          downloadUrl(
            fileUrl,
            `${paper.coachBookCode}-${paper.sortCode.toString().padStart(2, '0')}-${paper.name.trim()}${fileSuffix}`
          )
        } else {
          this.$Message.warning({
            duration: 4,
            content: '获取文件路径失败',
          })
        }
      },

      deletePaperFile(type, paper) {
        let fileTypeName
        let request

        if (type === 'content') {
          fileTypeName = '正文PDF'
          request = apiDeletePaperContentPDF
        } else if (type === 'answer') {
          fileTypeName = '答案PDF'
          request = apiDeletePaperAnswerPDF
        } else if (type === 'audio') {
          fileTypeName = '音频'
          request = apiDeletePaperListeningAudio
        }

        this.$Modal.confirm({
          title: '删除' + fileTypeName,
          content: '确定要删除 ' + paper.name.trim() + ' 的' + fileTypeName + '？',
          onOk: () =>
            request({
              coachBookId: this.coachBookId,
              paperId: paper.id,
            })
              .then(() => {
                this.$Message.success({
                  duration: 3,
                  content: '删除成功',
                })
              })
              .finally(() => this.$emit('refresh')),
        })
      },
    },
  }
</script>

<template>
  <div class="container-coach-book-detail-answers">
    <div class="section-actions">
      <div class="btns">
        <Button
          :disabled="!coachBookHasPaperContentPDF"
          type="primary"
          size="small"
          @click="downloadFileZip('content', true)"
          >下载全部试卷正文pdf压缩包</Button
        >
        <Button
          :disabled="!selectedPapersHasPaperContentPDF"
          type="primary"
          size="small"
          @click="downloadFileZip('content', false)"
          >下载已选试卷正文pdf压缩包</Button
        >
        <Button
          :disabled="!cocachBookHasPaperAnswerPDF"
          type="primary"
          size="small"
          @click="downloadFileZip('answer', true)"
          >下载全部试卷答案pdf压缩包</Button
        >
        <Button
          :disabled="!selectedPapersHasPaperAnswerPDF"
          type="primary"
          size="small"
          @click="downloadFileZip('answer', false)"
          >下载已选试卷答案pdf压缩包</Button
        >
        <Button
          :disabled="!coachBookHasPaperListeningAudio"
          type="primary"
          size="small"
          @click="downloadFileZip('audio', true)"
          >下载全部试卷音频压缩包</Button
        >
        <Button
          :disabled="!selectedPapersHasPaperListeningAudio"
          type="primary"
          size="small"
          @click="downloadFileZip('audio', false)"
          >下载已选试卷音频压缩包</Button
        >
      </div>
    </div>

    <Table :columns="tableColumns" :data="paperList" @on-selection-change="selectedPaperList = $event"></Table>

    <Modal v-model="isModalUploadFileShowed" :title="modalUploadFileTitle" width="700">
      <file-uploader
        :accepts="uploadFileTypeList"
        :file-name="uploadFile && uploadFile.name"
        :max-size="uploadType == 'audio' ? 30 : 10"
        @on-selected="handleAcceptUploaderFile"
      ></file-uploader>

      <template #footer>
        <Button type="default" @click="isModalUploadFileShowed = false">取消</Button>
        <Button :loading="isFileUploading" :disabled="!uploadFile" type="primary" @click="uploadFileDispatch"
          >上传文件</Button
        >
      </template>
    </Modal>
  </div>
</template>

<style lang="scss" scoped>
  .container-coach-book-detail-answers {
    .section-actions {
      margin: 16px 0;

      .btns {
        @include flex(row, flex-start, center);
        flex-wrap: wrap;
        row-gap: 8px;
        column-gap: 10px;
      }
    }
  }
</style>
