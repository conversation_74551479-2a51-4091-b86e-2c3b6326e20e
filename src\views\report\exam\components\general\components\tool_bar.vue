<script>
  import ModalExportReportsBatch from './modal_export_reports_batch.vue'
  import ModalExportSeniorHighReportsBatch from './modal_export_senior_high_reports_batch.vue'

  import {
    apiDownloadAreaPDF,
    apiDownloadTeacherAnalyseExcel,
    apiDownloadUnionClassCompareExcel,
    apiDownloadSchoolPDF,
  } from '@/api/report'

  import { mapGetters } from 'vuex'
  import { formatDateHourMinutesByChinese } from '@/utils/date'
  import { downloadUrl, downloadBlob } from '@/utils/download'
  import { downloadClsRankIntervalExcel } from '@/helpers/report/export_cls_rank_interval_excel'
  import { downloadClsScoreIntervalExcel } from '@/helpers/report/export_cls_score_interval_excel'

  import Role from '@/enum/user/role.js'

  export default {
    components: {
      'modal-export-reports-batch': ModalExportReportsBatch,
      'modal-epxort-senior-high-reports-batch': ModalExportSeniorHighReportsBatch,
    },

    props: {
      disableDownloadExcel: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['export-excel-file', 'change-params-explain-showed-status'],

    data() {
      return {
        isPDFCompleted: true,
        loadPDFTimes: 0,
        isSchoolPDFCompleted: true,
        loadSchoolPDFTimes: 0,
        timeoutID: '',

        modalExportReportsBatchShown: false,
      }
    },

    computed: {
      ...mapGetters('report', [
        'currentLevelName',
        'isCreator',
        'isAdministrator',
        'examCreatedInstitutionId',
        'examId',
        'examName',
        'templateId',
        'currentSchoolId',
        'canExportSchoolPDF',
        'isSeniorHighSchoolSubjectCombinationSelectableProject',
      ]),

      isSchoolAdministrator() {
        return this.$store.getters['user/isSchoolAdministrator']
      },

      isSchoolLeader() {
        return this.$store.getters['user/isSchoolLeader']
      },

      isSystemAdministrator() {
        return this.isCreator || (this.isAdministrator && this.$store.state.user.isSystem)
      },

      userInfo() {
        return this.$store.getters['user/info']
      },

      userRoleList() {
        return (this.userInfo && this.userInfo.roles) || []
      },

      isExamRootSchool() {
        return (
          this.userInfo &&
          this.userInfo.schoolId &&
          this.examCreatedInstitutionId &&
          this.userInfo.schoolId === this.examCreatedInstitutionId
        )
      },

      btnResourcesForLeaderShowed() {
        return (
          this.currentLevelName === 'multipleSchool' &&
          (this.isAdministrator || this.userRoleList.some(roleId => roleId === Role.ResearcherLeader.id))
        )
      },

      btnExportUnitExamPDFShowed() {
        return (
          this.isExamRootSchool &&
          this.isSystemAdministrator &&
          !this.isSeniorHighSchoolSubjectCombinationSelectableProject
        )
      },

      btnExportSchoolExamPDFShowed() {
        return (
          this.currentSchoolId && this.canExportSchoolPDF && !this.isSeniorHighSchoolSubjectCombinationSelectableProject
        )
      },

      btnExportReportsZippedFileShowed() {
        return (
          this.isAdministrator ||
          this.isSchoolLeader ||
          this.isSchoolAdministrator ||
          this.userRoleList.some(roleId => roleId === Role.ResearcherLeader.id)
        )
      },
    },

    beforeUnmount() {
      if (this.timeoutID) {
        clearTimeout(this.timeoutID)
      }
    },

    methods: {
      exportAreaPDF() {
        apiDownloadAreaPDF({
          examId: this.examId,
          templateId: this.templateId,
        }).then(response => {
          const URL = String(response)
          if (URL.endsWith('.pdf')) {
            this.isPDFCompleted = true
            this.loadPDFTimes = 0 // 复原 loadPDFTimes
            downloadUrl(URL, this.examName + '分析报告.pdf')
          } else {
            this.isPDFCompleted = false
            if (this.loadPDFTimes < 6 * 10) {
              this.loadPDFTimes++
              this.timeoutID = setTimeout(this.exportAreaPDF, 10 * 1000)
            } else {
              this.$Message.error({
                content: '加载PDF失败，请稍候重试',
              })
              this.loadPDFTimes = 0
              this.isPDFCompleted = true
            }
          }
        })
      },

      handleUnionTypeReportsDownloadDropdownClick(dropDownItem) {
        let requestMethod
        let requestParams = {
          examId: this.examId,
          templateId: this.templateId,
        }
        let fileName

        if (dropDownItem === 'teacherAnalyse') {
          requestMethod = apiDownloadTeacherAnalyseExcel
          requestParams.isMerged = false
          fileName = '教师教学质量分析报表'
        } else if (dropDownItem === 'teacherAnalyseByMergedClass') {
          requestMethod = apiDownloadTeacherAnalyseExcel
          requestParams.isMerged = true
          fileName = '教师教学质量分析报表(合并任教班级)'
        } else if (dropDownItem === 'unionClassCompare') {
          requestMethod = apiDownloadUnionClassCompareExcel
          fileName = '联考班级对比分析报表'
        } else if (dropDownItem === 'unionClassScoreInterval') {
          requestMethod = downloadClsScoreIntervalExcel
          requestParams = requestParams.templateId
          fileName = '班级分数段统计报告'
        } else if (dropDownItem === 'unionClassRankInterval') {
          requestMethod = downloadClsRankIntervalExcel
          requestParams = requestParams.templateId
          fileName = '班级名次段统计报告'
        }

        if (requestMethod && requestParams && fileName) {
          this.$Spin.show({
            render: h =>
              h(
                'div',
                {
                  style: {
                    fontSize: '24px',
                  },
                },
                '正在导出……'
              ),
          })

          requestMethod(requestParams)
            .then(responseBlob =>
              downloadBlob(
                responseBlob,
                `${this.examName}_${fileName}_${formatDateHourMinutesByChinese(new Date())}.xlsx`
              )
            )
            .finally(() => this.$Spin.hide())
        }
      },

      exportSchoolPDF() {
        if (!this.currentSchoolId) {
          this.$Message.warning({
            duration: 4,
            content: '未有学校信息，导出失败',
          })
          return
        }

        apiDownloadSchoolPDF({
          examId: this.examId,
          templateId: this.templateId,
          schoolId: this.currentSchoolId,
        }).then(response => {
          let url = String(response)
          if (url.endsWith('.pdf')) {
            this.isSchoolPDFCompleted = true
            this.loadSchoolPDFTimes = 0 // 复原 loadSchoolPDFTimes
            downloadUrl(url, this.examName + '分析报告（' + this.currentSchoolName + '）.pdf')
          } else {
            this.isSchoolPDFCompleted = false
            if (this.loadSchoolPDFTimes < 6 * 10) {
              this.loadSchoolPDFTimes++
              this.timeoutID = setTimeout(this.exportSchoolPDF, 10 * 1000)
            } else {
              this.$Message.error({
                content: '加载PDF失败，请稍候重试',
              })
              this.loadSchoolPDFTimes = 0
              this.isSchoolPDFCompleted = true
            }
          }
        })
      },

      exportExcelFile() {
        this.$emit('export-excel-file')
      },

      changeParamsExplainShowedStatus() {
        this.$emit('change-params-explain-showed-status')
      },
    },
  }
</script>

<template>
  <div class="component-tool-bar">
    <div class="btns">
      <div v-if="btnResourcesForLeaderShowed">
        <Button
          v-if="btnExportUnitExamPDFShowed"
          type="primary"
          size="small"
          style="width: 130px"
          :loading="!isPDFCompleted"
          @click="exportAreaPDF"
        >
          <span v-if="isPDFCompleted">导出联考PDF报告</span>
          <span v-else>报告生成中</span>
        </Button>

        <Dropdown v-if="isExamRootSchool" transfer @on-click="handleUnionTypeReportsDownloadDropdownClick">
          <Button type="primary" size="small">
            导出联考班级教师相关汇总表
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <template #list>
            <DropdownMenu>
              <DropdownItem name="teacherAnalyse">联考教师教学质量分析表</DropdownItem>
              <DropdownItem name="teacherAnalyseByMergedClass">联考教师教学质量分析表(合并任教班级)</DropdownItem>
              <DropdownItem name="unionClassCompare">联考班级对比分析表</DropdownItem>
              <DropdownItem v-if="isSystemAdministrator" name="unionClassScoreInterval"
                >联考班级分数段统计表</DropdownItem
              >
              <DropdownItem v-if="isSystemAdministrator" name="unionClassRankInterval"
                >联考班级名次段统计表</DropdownItem
              >
            </DropdownMenu>
          </template>
        </Dropdown>

        <Button v-if="isSystemAdministrator" type="primary" size="small" @click="modalExportReportsBatchShown = true"
          >批量导出报表</Button
        >
      </div>

      <div v-if="currentLevelName === 'school'">
        <Button
          v-if="btnExportSchoolExamPDFShowed"
          type="primary"
          size="small"
          style="width: 130px"
          :loading="!isSchoolPDFCompleted"
          @click="exportSchoolPDF"
        >
          <span v-if="isSchoolPDFCompleted">导出学校PDF报告</span>
          <span v-else>报告生成中</span>
        </Button>

        <Button
          v-if="btnExportReportsZippedFileShowed"
          type="primary"
          size="small"
          ghost
          @click="modalExportReportsBatchShown = true"
          >批量导出报表</Button
        >
      </div>
    </div>

    <div class="text-btns">
      <TextButton icon="md-download" :disabled="disableDownloadExcel" @click="exportExcelFile">导出Excel</TextButton>
      <Divider type="vertical" />
      <TextButton icon="md-help-circle" @click="changeParamsExplainShowedStatus">说明</TextButton>
    </div>

    <modal-epxort-senior-high-reports-batch
      v-if="isSeniorHighSchoolSubjectCombinationSelectableProject"
      v-model="modalExportReportsBatchShown"
    ></modal-epxort-senior-high-reports-batch>
    <modal-export-reports-batch v-else v-model="modalExportReportsBatchShown"></modal-export-reports-batch>
  </div>
</template>

<style lang="scss" scoped>
  .component-tool-bar {
    @include flex(row, space-between, center);
    margin-bottom: 8px;
  }
</style>
