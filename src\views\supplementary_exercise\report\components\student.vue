<template>
  <div class="container-coach-report-student">
    <div class="table-student-score">
      <Table
        :columns="tableColumns"
        :data="classDetail.studentScoreList"
        :max-height="tableMaxHeight"
        :row-class-name="tableRowClassName"
        @on-row-click="onTableItemChange"
      />
    </div>
    <div class="paper-student">
      <div class="box-paper">
        <template v-if="paperImages.length">
          <div v-for="p in paperImages" :key="p.pageIndex" class="paper-item">
            <img :src="p.src" alt="" />
            <template v-if="isAIPractise">
              <div
                v-for="item in subjectives[p.pageIndex]"
                :key="item.blockId"
                class="btn-ai"
                :style="getBtnRect(p, item)"
                @click="onShowAnalysis(item.blockId)"
              >
                <!-- <span class="icon-ai">AI</span> -->
                <img src="@/assets/images/supplementary_exercise/icon_ai_analysis.svg" alt="" />
              </div>
            </template>
          </div>
        </template>
        <template v-else-if="!loading">
          <div class="box-no-data">
            <NoData :show-image="false">无答卷图像</NoData>
          </div>
        </template>
      </div>
    </div>
    <div class="stat-student-answer">
      <div class="stat-summary">
        <div class="stat-summary-item stat-item-score-rate">
          <div class="stat-item-num">
            <span class="text-num">{{ myScoreRate }}</span>
          </div>
          <div class="stat-item-label">正答率</div>
        </div>
        <div class="stat-summary-item stat-item-score-correct">
          <div class="stat-item-num">
            <span class="text-num">{{ correctQuestions.length }}</span>
            <span> 道</span>
          </div>
          <div class="stat-item-label">答对</div>
        </div>
        <div class="stat-summary-item stat-item-score-wrong">
          <div class="stat-item-num">
            <span class="text-num">{{ wrongQuestions.length }}</span>
            <span> 道</span>
          </div>
          <div class="stat-item-label">答错</div>
        </div>
      </div>
      <div class="stat-detail">
        <div
          v-for="item in questionStat"
          :key="item.code"
          class="question-item"
          :class="item.fullScore === item.score ? 'question-item-correct' : 'question-item-wrong'"
        >
          <span>{{ item.code }}</span>
        </div>
      </div>
    </div>

    <Modal
      v-model="modalAnalysisVisible"
      class="modal-analysis"
      :title="modalTitle"
      :width="modalWidth"
      footer-hide
      mask-closable
      draggable
      :scrollable="!isFullscreen"
      :fullscreen="isFullscreen"
      :mask="false"
      style="top: 50px"
      @on-visible-change="onVisibleChange"
    >
      <template #header>
        <div class="modal-header">
          <span>{{ modalTitle }}</span>
          <span class="btn" type="default" title="全屏切换" @click="handleBtnFullScreenClick">
            <Icon type="md-resize" />
          </span>
        </div>
      </template>
      <div class="container-modal-analysis">
        <Table ref="resultTable" :data="currentAIItem" :columns="tableAnalysisColumns">
          <template #solution="{ row }">
            <div style="padding-top: 8px; padding-bottom: 8px" v-html="row.solution"></div>
          </template>
          <template #studentAnswer="{ row }">
            <div style="padding-top: 8px; padding-bottom: 8px" v-html="row.studentAnswer"></div>
          </template>
          <template #scoreReason="{ row }">
            <div style="padding-top: 8px; padding-bottom: 8px" v-html="row.scoreReason"></div>
          </template>
        </Table>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { Icon } from 'view-ui-plus'
  import fscreen from 'fscreen'
  import NoData from '@/components/no_data'

  import { apiGetStuPaperAndMark } from '@/api/emarking/practise'
  import { apiGetCoachHomeworkScanTemplate } from '@/api/emarking/coach_homework'
  import { apiGetSubjectiveQuestions } from '@/api/emarking/subject'

  import StudentPaperDrawHelper from '@/helpers/report/student_paper_draw_helper'
  import { roundPercent } from '@/utils/math'
  import MathJaxUtil from '@/utils/mathjax'
  import { canvasToBlob, loadImage } from '@/utils/promise'
  import ExamType from '@/enum/emarking/exam_type'

  export default {
    components: {
      NoData,
    },
    props: {
      classDetail: {
        type: Object,
        default: null,
      },
      examInfo: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        // 扫描模板
        paperTemplate: null,
        // 当前学生Id
        currentStudentId: '',
        // 当前学生数据
        paperDetail: null,
        // 当前学生答卷图
        paperImages: [],
        // 加载中
        loading: false,

        studentPaperDrawHelper: null,
        tableMaxHeight: window.innerHeight - 180,

        // 主观题题块信息
        subjectives: [],
        currentAIItem: [],
        modalAnalysisVisible: false,
        currentBlock: null,
        isFullscreen: false,
      }
    },
    computed: {
      paper() {
        return this.$store.getters['practise/paperContent']
      },
      tableColumns() {
        let columns = [
          {
            title: '姓名',
            key: 'studentName',
            align: 'center',
          },
          {
            title: '得分',
            key: 'score',
            align: 'center',
          },
          {
            title: '班排名',
            key: 'rank',
            align: 'center',
          },
          {
            title: '升降',
            key: 'rankChange',
            align: 'center',
            render: (h, params) => {
              let rankChange = params.row.rankChange
              if (rankChange == null) {
                return null
              }
              let children = [h('span', Math.abs(rankChange))]
              if (rankChange != 0) {
                let iconType = 'ios-arrow-round-up'
                let iconColor = '#3ECBC4'
                if (rankChange < 0) {
                  iconType = 'ios-arrow-round-down'
                  iconColor = '#FFC43F'
                }
                children.unshift(
                  h(Icon, {
                    type: iconType,
                    size: 20,
                    style: {
                      color: iconColor,
                      fontWeight: 'bold',
                    },
                  })
                )
              }
              return h(
                'div',
                {
                  style: {
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  },
                },
                children
              )
            },
          },
        ]

        return columns
      },
      questionStat() {
        let currentStudent = this.classDetail.studentScoreList.find(stu => stu.studentId == this.currentStudentId)
        if (!currentStudent) {
          return []
        }
        return this.classDetail.quesScores.map(item => {
          return {
            ...item,
            score: currentStudent.codeScoreMap[item.code],
          }
        })
      },
      correctQuestions() {
        return this.questionStat.filter(item => item.score === item.fullScore)
      },
      wrongQuestions() {
        return this.questionStat.filter(item => item.score !== item.fullScore)
      },
      myScoreRate() {
        return roundPercent(this.correctQuestions.length / this.questionStat.length)
      },
      hasWritingComment() {
        return this.currentAIItem.some(q => q.writingComment)
      },
      modalWidth() {
        return this.isBindingPaper || this.hasWritingComment ? 720 : 520
      },
      modalTitle() {
        return this.currentBlock ? `${this.currentBlock.blockName} —— AI评卷结果` : 'AI评卷结果'
      },
      isBindingPaper() {
        return this.paper && this.paper.paperInfo?.id && this.paper.paperInfo?.id != '0'
      },
      tableAnalysisColumns() {
        let columns = [
          {
            title: '小题',
            align: 'center',
            width: 60,
            key: 'questionCode',
          },
          {
            title: '评分',
            align: 'center',
            width: 60,
            key: 'studentScore',
          },
          {
            title: '识别作答',
            align: 'center',
            slot: 'studentAnswer',
            // render: (h, params) => {
            //   return h(
            //     'div',
            //     {
            //       style: {
            //         paddingTop: '8px',
            //         paddingBottom: '8px',
            //       },
            //     },
            //     params.row.studentAnswer
            //   )
            // },
          },
          {
            title: '评分理由',
            align: 'center',
            slot: 'scoreReason',
            // render: (h, params) => {
            //   return h(
            //     'div',
            //     {
            //       style: {
            //         paddingTop: '8px',
            //         paddingBottom: '8px',
            //       },
            //     },
            //     params.row.scoreReason
            //   )
            // },
          },
        ]

        if (this.isBindingPaper) {
          columns.splice(2, 0, {
            title: '参考答案',
            align: 'center',
            slot: 'solution',
          })
        }
        if (this.hasWritingComment) {
          columns.push({
            title: '作文评语',
            align: 'center',
            key: 'writingComment',
          })
        }
        return columns
      },
      isAIPractise() {
        return [ExamType.ClassPractise.id, 8].includes(this.examInfo.examType)
      },
    },
    watch: {
      classDetail: {
        handler() {
          this.changeStudent(this.currentStudentId)
        },
        immediate: true,
      },
    },
    methods: {
      async fetchData() {
        if (this.loading) {
          return
        }
        try {
          this.loading = true
          this.$TransparentSpin.show()
          await this.getSubjectives()
          await this.fetchPaperTemplate()
          let paperDetail = await this.fetchPaperMark()
          this.paperDetail = paperDetail
          let paperImages = await this.drawCanvas(paperDetail)
          this.paperImages = paperImages
        } catch {
          this.paperDetail = null
          this.paperImages = []
        } finally {
          this.loading = false
          this.$TransparentSpin.hide()
        }
      },
      getSubjectives() {
        return apiGetSubjectiveQuestions({
          examId: this.examInfo?.examId,
          examSubjectId: this.examInfo?.examSubjectId,
        }).then(res => {
          this.subjectiveInfo = res
        })
      },
      fetchPaperTemplate() {
        if (this.paperTemplate) {
          return Promise.resolve()
        }
        return apiGetCoachHomeworkScanTemplate(this.examInfo.examSubjectId)
          .then(paperTemplate => {
            this.paperTemplate = paperTemplate
          })
          .catch(ex => {
            this.paperTemplate = null
            throw ex
          })
      },
      fetchPaperMark() {
        return apiGetStuPaperAndMark({
          examSubjectId: this.examInfo.examSubjectId,
          studentId: this.currentStudentId,
        })
      },
      async drawCanvas(paperDetail) {
        if (!this.paperTemplate || !this.paperTemplate.templateJson) {
          return paperDetail.paperUrls.map((src, pageIndex) => ({ src, pageIndex }))
        }
        if (!this.studentPaperDrawHelper) {
          this.studentPaperDrawHelper = new StudentPaperDrawHelper(this.paperTemplate.templateJson)
        }

        let template = null
        try {
          template = JSON.parse(this.paperTemplate.templateJson)
        } catch {}

        let result = []
        let canvas = document.createElement('canvas')
        let subjectiveAreaInfos = template.questions.subjectives
        let pages = template.pages || template.tpl.tplFile.pages

        for (let pageIndex = 0; pageIndex < paperDetail.paperUrls.length; pageIndex++) {
          try {
            await this.studentPaperDrawHelper.drawPaperCanvas(
              canvas,
              paperDetail.paperUrls[pageIndex],
              pageIndex,
              paperDetail
            )

            // 提取主观题位置
            this.subjectives[pageIndex] = []
            ;(pages[pageIndex].subjectiveAreas || []).forEach(area => {
              let info = subjectiveAreaInfos.find(x => x.areaIds.some(id => id == area.id))
              if (!info) {
                return
              }
              let part = info.areaIds.findIndex(id => id == area.id)
              this.subjectives[pageIndex].push({
                blockId: info.subjectiveId,
                part,
                partOffsetY: 0,
                rect: area.area,
              })
            })

            let blob = await canvasToBlob(canvas)
            let src = window.URL.createObjectURL(blob)
            let canvasImageElement = await loadImage(src)

            result.push({
              pageIndex,
              src,
              naturalWidth: canvasImageElement.width,
              naturalHeight: canvasImageElement.height,
            })

            if (this.modalAnalysisVisible && this.currentBlock) {
              this.currentAIItem = []
              this.onShowAnalysis(this.currentBlock.blockId)
            }
          } catch (err) {
            this.$Message.error(typeof err == 'string' ? err : '绘制答卷图失败')
            throw err
          }
        }

        return result
      },
      getBtnRect(paper, item) {
        let boxPaperElement = document.querySelector('.box-paper')
        let actualWidth = boxPaperElement.offsetWidth
        let ratio = actualWidth / paper.naturalWidth

        return `top: ${(item.rect.y - 40) * ratio}px; left: ${(item.rect.x - 45) * ratio}px`
      },
      onShowAnalysis(blockId) {
        const stuSubjectiveVos = this.paperDetail?.stuSubjectiveVos || []
        const theItem = stuSubjectiveVos.find(s => s.blockId === blockId)
        let theItemContent = []

        try {
          theItemContent = theItem.aiScoreResult ? JSON.parse(theItem.aiScoreResult) : []
        } catch {
          try {
            let aiScoreResult = theItem.aiScoreResult.replace(/\n/g, '\\n')
            theItemContent = JSON.parse(aiScoreResult)
          } catch {
            theItemContent = []
          }
        }

        const questionList = this.paper?.questionList || []

        theItemContent.forEach(item => {
          const theBranch = this.subjectiveInfo.find(s => s.fullCode == item.questionCode)
          const theQuestion = questionList.find(q => q.id === theBranch?.originalQuestionId)
          const theOriginalBranch = (theQuestion?.branches || []).find(b => b.id === theBranch?.originalBranchId)
          let solution = ''

          try {
            solution = JSON.parse(theOriginalBranch.solution)
          } catch {
            solution = theOriginalBranch.solution
          }

          item.solution = solution
        })

        this.currentBlock = theItem
        this.currentAIItem = theItemContent
        this.modalAnalysisVisible = true

        if (theItemContent.length > 0) {
          setTimeout(() => {
            this.renderMath()
          }, 0)
        }
      },
      tableRowClassName(row) {
        return row.studentId == this.currentStudentId ? 'active' : null
      },
      onTableItemChange(row) {
        this.changeStudent(row.studentId)
      },
      changeStudent(studentId) {
        if (this.classDetail.studentScoreList.every(stu => stu.studentId != studentId)) {
          studentId = this.classDetail.studentScoreList[0].studentId
        }
        this.currentStudentId = studentId
        this.fetchData()
      },
      renderMath() {
        let el = this.$refs.resultTable?.$el
        if (el) {
          MathJaxUtil.clearRenderQueue()
          MathJaxUtil.render(el)
        }
      },
      handleBtnFullScreenClick() {
        // if (fscreen.fullscreenElement) {
        //   fscreen.exitFullscreen()
        // } else {
        //   fscreen.requestFullscreen(document.documentElement)
        // }
        this.isFullscreen = !this.isFullscreen
      },
      onVisibleChange(visible) {
        if (!visible) {
          this.isFullscreen = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-coach-report-student {
    @include flex(row, flex-start, flex-start);

    .table-student-score {
      position: sticky;
      top: 76px;
      width: 310px;

      :deep(.ivu-table-row.active td) {
        background-color: $color-iview-table-active-row;
      }
    }

    .box-no-data {
      @include flex(row, center, center);
      height: 100px;
    }

    .paper-student {
      flex: 1;
      margin: 0 15px;
    }

    .box-paper {
      min-height: 100px;

      .paper-item {
        position: relative;

        img {
          width: 100%;
        }
      }

      .btn-ai {
        position: absolute;
        //left: -2px;
        box-sizing: content-box;
        width: 25px;
        height: 25px;
        color: $color-primary;
        // font-size: 14px;
        // line-height: 20px;
        text-align: center;
        // background-color: #f0f0f0;
        cursor: pointer;
        user-select: none;
      }
    }

    .stat-student-answer {
      position: sticky;
      top: 76px;
      width: 300px;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);

      .stat-summary {
        @include flex(row, space-between, center);
      }

      .stat-summary-item {
        @include flex(column, center, center);
        width: 80px;
        height: 80px;
        border: 4px solid #dfdfdf;
        border-radius: 50%;
      }

      .stat-item-score-rate {
        border-color: #5cadff;
      }

      .stat-item-score-correct {
        border-color: #19be6b;
      }

      .stat-item-score-wrong {
        border-color: #f56c6c;
      }

      .stat-item-label {
        font-size: 14px;
      }

      .stat-item-num {
        font-size: 14px;
      }

      .text-num {
        font-size: 18px;
      }

      .stat-detail {
        @include flex(row, flex-start, center);
        flex-wrap: wrap;
        margin-top: 20px;
      }

      .question-item {
        position: relative;
        min-width: 30px;
        height: 30px;
        margin-right: 12px;
        margin-bottom: 12px;
        padding-right: 6px;
        padding-left: 6px;
        border: 1px solid #05c1ae;
        border-radius: 3px;
        line-height: 28px;
        text-align: center;
        user-select: none;
      }

      .question-item-correct {
        border-color: #19be6b;
        color: #19be6b;
      }

      .question-item-wrong {
        border-color: #f56c6c;
        color: #f56c6c;
      }
    }
  }

  .modal-header {
    @include flex(row, space-between, center);
    padding: 4px 40px 4px 0;

    .btn {
      cursor: pointer;
    }
  }
</style>
