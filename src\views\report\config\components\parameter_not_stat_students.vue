<script>
  import TextButton from '@/components/text_button'
  import ModalAddNotStatStudents from './modal_add_not_stat_students.vue'

  import { apiGetReportTemplateNonCountingStudents, apiRemoveReportTemplateNonCountingStudents } from '@/api/report'

  export default {
    components: {
      'component-modal-add-not-stat-students': ModalAddNotStatStudents,
    },

    props: {
      isNewTemplate: {
        type: Boolean,
        default: false,
      },
      examId: {
        type: String,
        default: '',
      },
      templateId: {
        type: String,
        default: '',
      },
    },

    data() {
      return {
        total: 0,
        currentPage: 1,
        pageSize: 10,

        tableColumns: [
          {
            type: 'selection',
            width: 40,
          },
          {
            title: '姓名',
            key: 'studentName',
          },
          {
            title: '学校',
            key: 'schoolName',
          },
          {
            title: '班级',
            key: 'className',
          },
          {
            title: '准考号',
            key: 'admissionNum',
          },
          {
            title: '操作',
            width: 100,
            align: 'center',
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'error',
                  onClick: () => this.ensureRecoverNotStatStudent(params.row),
                },
                () => '删除'
              ),
          },
        ],
        tableData: [],
        tableSelectedList: [],

        isModalAddNotStatStudentsShowed: false,
      }
    },

    created() {
      this.fetchPageNotStatStudents()
    },

    methods: {
      fetchPageNotStatStudents() {
        if (!this.isNewTemplate && this.templateId) {
          return apiGetReportTemplateNonCountingStudents({
            templateId: this.templateId,
            currentPage: this.currentPage,
            pageSize: this.pageSize,
          })
            .then(response => {
              this.total = (response && response.total) || 0
              this.tableData = (response && response.records) || []
            })
            .catch(() => {
              this.total = 0
              this.tableData = []
            })
        } else {
          return Promise.resolve()
        }
      },

      onTableSelectionChange(rows) {
        this.tableSelectedList = rows || []
      },
      onPageChange(page = 1) {
        this.currentPage = page
        this.fetchPageNotStatStudents()
      },
      ensureRecoverNotStatStudent(student) {
        this.$Modal.confirm({
          title: '删除剔除学生',
          content: `您确定要将 ${student.studentName} 从剔除学生列表中删除吗？`,
          onOk: () => {
            apiRemoveReportTemplateNonCountingStudents({
              templateId: this.templateId,
              studentIds: [student.studentId],
            })
              .then(() => {
                this.$Message.success({
                  duration: 4,
                  content: '删除剔除学生成功',
                })
              })
              .finally(() => this.onPageChange())
          },
        })
      },
      ensureRecoverNotStatStudentBatch() {
        this.$Modal.confirm({
          title: '删除剔除学生',
          content: `您确定要将已选择的 ${this.tableSelectedList.length} 名学生 从剔除学生列表中删除吗？`,
          onOk: () => {
            apiRemoveReportTemplateNonCountingStudents({
              templateId: this.templateId,
              studentIds: this.tableSelectedList.map(stu => stu.studentId),
            })
              .then(() => {
                this.$Message.success({
                  duration: 4,
                  content: '删除剔除学生成功',
                })
                this.tableSelectedList = []
              })
              .finally(() => this.onPageChange())
          },
        })
      },

      handleTextButtonAddNotStatStudentClick() {
        this.isModalAddNotStatStudentsShowed = true
      },
    },
  }
</script>

<template>
  <div class="container-report-config-parameter-not-stat-students">
    <div class="section-action">
      <div class="student-count">
        共剔除 <span class="color-primary">{{ total }}</span> 名学生
      </div>

      <div>
        <TextButton
          v-show="tableSelectedList.length"
          type="error"
          icon="md-trash"
          @click="ensureRecoverNotStatStudentBatch"
          >删除已选剔除学生</TextButton
        >
        <TextButton type="primary" icon="md-add" @click="handleTextButtonAddNotStatStudentClick"
          >添加剔除学生</TextButton
        >
      </div>
    </div>

    <Table :columns="tableColumns" :data="tableData" @on-selection-change="onTableSelectionChange"></Table>

    <div style="text-align: right">
      <Page
        show-elevator
        :model-value="currentPage"
        :total="total"
        :page-size="10"
        size="small"
        style="margin-top: 10px"
        @on-change="onPageChange"
      ></Page>
    </div>

    <component-modal-add-not-stat-students
      v-model="isModalAddNotStatStudentsShowed"
      :exam-id="examId"
      :template-id="templateId"
      @refresh-not-stat-students="onPageChange"
    ></component-modal-add-not-stat-students>
  </div>
</template>

<style lang="scss" scoped>
  .container-report-config-parameter-not-stat-students {
    .section-action {
      @include flex(row, space-between, center);
      margin-bottom: 10px;

      .student-count {
        .color-primary {
          color: $color-primary;
        }
      }
    }
  }
</style>
