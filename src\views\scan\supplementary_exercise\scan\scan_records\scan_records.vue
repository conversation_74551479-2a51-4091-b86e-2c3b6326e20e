<template>
  <div class="scan-records-container">
    <PanelBatch
      class="panel-batch"
      :active-batch="activeBatch"
      :table-max-height="tableMaxHeight"
      @change-active-batch="activeBatch = $event"
    ></PanelBatch>
    <PanelPaper class="panel-paper" :active-batch="activeBatch" :table-max-height="tableMaxHeight"></PanelPaper>
  </div>
</template>

<script>
  import PanelBatch from './components/panel_batch.vue'
  import PanelPaper from './components/panel_paper.vue'

  // 各部分高度
  const PanelHeaderHeight = 60
  const PanelFooterHeight = 60

  export default {
    components: {
      PanelBatch,
      PanelPaper,
    },
    props: {
      top: Number,
    },
    data() {
      return {
        //  选定批次
        activeBatch: null,
      }
    },
    computed: {
      // 表格高度
      tableMaxHeight() {
        return window.innerHeight - this.top - PanelHeaderHeight - PanelFooterHeight
      },
    },
  }
</script>

<style lang="scss" scoped>
  .scan-records-container {
    @include flex(row, flex-start, stretch);
    overflow: hidden;

    .panel-batch {
      position: relative;
      flex: 0 0 500px;
      height: 100%;
    }

    .panel-paper {
      position: relative;
      flex-grow: 1;
      height: 100%;
    }

    :deep(.panel-header) {
      @include flex(row, flex-start, center);
      height: 60px;
    }

    :deep(.panel-footer) {
      @include flex(row, flex-end, center);
      position: absolute;
      bottom: 0;
      left: 0;
      flex-wrap: wrap;
      width: 100%;
      height: 60px;
      padding-right: 16px;

      .summary {
        margin-right: 10px;
      }
    }
  }
</style>
