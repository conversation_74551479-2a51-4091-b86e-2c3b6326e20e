/**
 * 题组
 * 对于客观题
 *   1. 不显示题目内容(QuestionGroupTypeEnum.Objective)
 *     1.1 若统一显示在顶部，则整个答题卡只有一个客观题题组
 *     1.2 否则每个存在客观题的大题均有一个客观题题组
 *   2. 显示题目内容，每道客观题皆为一个包含题目内容的客观题题组(QuestionGroupTypeEnum.ContentObjective)
 * 对于主观题，一般题组与评卷题块一一对应。
 * 例外1：由试卷结构创建答题卡时，若评卷题块跨大题或题号不连续或（跨题且只包含部分小题），则该评卷题块对应多个题组，如: 题块X = 2.3,3.1
 * 例外2：选做题题组对应多个评卷题块
 * 例外3：显示题目内容时，材料题组不对应题块
 */

import ScoreType from './score_type'
import PageBlock from './page_block'

import { splitBlankScores } from '@/helpers/qlib/miscellaneous'
import { randomId } from '@/utils/string'
import { numberToChinese } from '@/utils/number'
import { uniq, chunk } from '@/utils/array'
import { getHTMLWidth, createTextWidthMeasure } from '@/utils/dom'

import ModeEnum from '@/enum/answer_sheet/mode'
import QuestionGroupTypeEnum from '@/enum/answer_sheet/question_group_type'
import BranchTypeEnum from '@/enum/qlib/branch_type'
import TrueFalseTypeEnum from '@/enum/answer_sheet/true_false_type'
import ScoreTypeEnum from '@/enum/answer_sheet/score_type'
import AiScoreTypeEnum from '@/enum/emarking/ai_score_type'
import { sortQuestionFunction } from '../emarking/miscellaneous'
import {
  PixelsPerMillimeter,
  SubjectiveBlockExpandMillimeters,
  TemplateResolutionScale,
  TrueFalseBoxCssClass,
} from '@/const/answer_sheet'

export default class QuestionGroup {
  constructor(g = {}, structureQuestions = [], subjectName) {
    // 题组Id
    this.id = g.id || ''
    // 题组类型（客观题、填空题、解答题、语文作文、英语作文、含内容的客观题、材料）
    this.type = QuestionGroupTypeEnum.Objective.id
    if (g.type) {
      if (QuestionGroupTypeEnum.hasId(g.type)) {
        this.type = g.type
      } else {
        throw '题组类型错误'
      }
    }
    // 题组先阅后扫时的打分类型
    this.scoreType = new ScoreType(g.scoreType)
    // 题目
    this.questions = (g.questions || []).map(q => {
      let target = structureQuestions.find(x => x.questionCode == q.questionCode && x.branchCode == q.branchCode)
      if (!target) {
        let questionName = q.branchCode == 0 ? `${q.questionCode}` : `${q.questionCode}.${q.branchCode}`
        throw `找不到题目：第${questionName}题`
      }
      return target
    })
    // 内容
    this.content = g.content || ''
    // 各选项内容
    this.options = g.options || []
    // 高度，指其下所有块高度之和
    this.height = g.height || 0
    // 客观题每组题数
    this.groupLength = g.groupLength || 5
    // 判断题显示类型
    this.trueOrFalseType =
      g.trueOrFalseType || (subjectName == '英语' ? TrueFalseTypeEnum.TF.id : TrueFalseTypeEnum.TC.id)
    // 填空题每行空数
    this.blanksPerRow = g.blanksPerRow || 3
    // 填空题是否显示材料（英语短文填空等题型）
    this.trunkInContent = false
    // 解答题、英语作文题内容行数
    this.rows = g.rows || 5
    // 解答题是否显示答题线
    this.showLine = g.showLine == false ? false : true
    // 语文作文说明内容高度
    this.contentHeight = g.contentHeight || 0
    // 语文作文字数
    this.words = g.words || 0
    // 选做题组
    this.selectGroupName = g.selectGroupName || ''
    // 选做个数
    this.selectCount = g.selectCount || 0
    // 选做题说明
    this.selectDescription = g.selectDescription || ''
    // 智能评分类型
    this.aiScoreTypeId = g.aiScoreTypeId || 0
    // 客观题括号内容
    this.objectiveQuestionBracketContent =
      g.objectiveQuestionBracketContent || `（${String.fromCharCode(160).repeat(8)}）`
    // 客观题选项行高
    this.optionLineHeight = g.optionLineHeight || 1.5
    // 客观题选项左边距
    this.optionPaddingLeft = g.optionPaddingLeft || 31
    // 块（跨页时一个题组有多块）
    this.blocks = (g.blocks || []).map(b => new PageBlock(b))
    // 新块，更新页面时使用
    this.newBlocks = []

    // 删除原对错框
    let deleteClass = 'igrade-answer-sheet-true-false-box'
    if (this.type == QuestionGroupTypeEnum.FillBlank.id && this.content.includes(deleteClass)) {
      this.content = removeContentClass({ content: this.content, deleteClass })
    }
  }

  export() {
    return {
      id: this.id,
      type: this.type,
      scoreType: this.scoreType,
      questions: this.questions.map(q => ({
        questionCode: q.questionCode,
        branchCode: q.branchCode,
      })),
      content: this.content,
      options: this.options.map(opt => ({
        label: opt.label,
        content: opt.content,
      })),
      height: this.height,
      groupLength: this.groupLength,
      trueOrFalseType: this.trueOrFalseType,
      blanksPerRow: this.blanksPerRow,
      rows: this.rows,
      showLine: this.showLine,
      contentHeight: this.contentHeight,
      words: this.words,
      selectGroupName: this.selectGroupName,
      selectCount: this.selectCount,
      selectDescription: this.selectDescription,
      aiScoreTypeId: this.aiScoreTypeId,
      objectiveQuestionBracketContent: this.objectiveQuestionBracketContent,
      optionLineHeight: this.optionLineHeight,
      optionPaddingLeft: this.optionPaddingLeft,
      blocks: [],
      newBlocks: [],
    }
  }

  // 导出智能评分题块
  exportAiScoreBlock() {
    if (this.aiScoreTypeId != AiScoreTypeEnum.KouSuan.id) {
      return null
    }
    let kouSuanQuestionRects = []
    let top = 0
    let subjectiveBlockExpandPixels = Math.round(
      PixelsPerMillimeter * SubjectiveBlockExpandMillimeters * TemplateResolutionScale
    )
    this.blocks.forEach(block => {
      block.instance.getKouSuanQuestionRects().forEach(rect => {
        rect.x += subjectiveBlockExpandPixels
        rect.y += subjectiveBlockExpandPixels + top
        kouSuanQuestionRects.push(rect)
      })
      top += Math.round(block.height * TemplateResolutionScale) + subjectiveBlockExpandPixels * 2
    })
    if (this.questions.length > 1 && this.questions.length != kouSuanQuestionRects.length) {
      let first = this.questions[0]
      let last = this.questions[this.questions.length - 1]
      throw `智能评分口算题区域数量与小题数量不一致，请重新设置：${first.questionCode}.${first.branchCode}-${last.questionCode}.${last.branchCode}`
    }
    return {
      questions: this.questions.map(q => ({
        questionCode: q.questionCode,
        branchCode: q.branchCode,
      })),
      aiScoreTypeId: this.aiScoreTypeId,
      aiScoreExtraJson: kouSuanQuestionRects.length > 0 ? JSON.stringify(kouSuanQuestionRects) : null,
    }
  }

  // 设置随机Id
  setRandomId() {
    let prefix = ''
    if (this.type == QuestionGroupTypeEnum.Objective.id) {
      prefix = 'objective'
    } else if (this.type == QuestionGroupTypeEnum.ContentObjective.id) {
      prefix = 'contentobjective'
    } else if (this.type == QuestionGroupTypeEnum.Trunk.id) {
      prefix = 'trunk'
    } else {
      prefix = 'subjective'
    }
    this.id = `${prefix}_${randomId()}`
  }

  // 生成内容
  generateContent({ containerWidth, baseStyle, mode, showQuestionContent }) {
    if (this.type == QuestionGroupTypeEnum.Trunk.id) {
      this.generateTrunkContent()
    } else if (this.type == QuestionGroupTypeEnum.ContentObjective.id) {
      this.generateContentObjectiveContent()
    } else if (this.type == QuestionGroupTypeEnum.ContentObjectiveNoOption.id) {
      this.generateContentObjectiveContent()
    } else if (this.type == QuestionGroupTypeEnum.FillBlank.id) {
      this.generateFillBlankContent({ containerWidth, baseStyle, mode, showQuestionContent })
    } else if (this.type == QuestionGroupTypeEnum.Essay.id) {
      this.generateEssayContent({ containerWidth, baseStyle, showQuestionContent })
    } else if (this.type == QuestionGroupTypeEnum.ChineseWriting.id) {
      this.generateChineseWritingContent({ showQuestionContent, mode })
    } else if (this.type == QuestionGroupTypeEnum.EnglishWriting.id) {
      this.generateEnglishWritingContent({ containerWidth, baseStyle, showQuestionContent })
    }
  }

  generateTrunkContent() {
    this.content = getTrunkContent(this.questions[0])
  }

  generateContentObjectiveContent() {
    let content = ''
    let options = []
    let firstQuestion = this.questions[0]
    let originalQuestion = firstQuestion.originalQuestion
    let originalBranch = firstQuestion.originalBranch
    if (originalQuestion && originalBranch) {
      if (originalBranch.stem) {
        let codeText = getBranchCodeText(firstQuestion)
        content = prefixCodeTextToTrunkStem(originalBranch.stem, codeText)
      }
      if (originalBranch.branchType.id == BranchTypeEnum.TrueOrFalse.id) {
        if (originalQuestion.subject.name == '英语') {
          options = [
            { label: 'T', content: '' },
            { label: 'F', content: '' },
          ]
        } else {
          options = [
            { label: '√', content: '' },
            { label: '×', content: '' },
          ]
        }
      } else {
        options = originalBranch.options.map(opt => ({
          label: opt.label,
          content: opt.content,
        }))
      }
    }
    this.content = content
    this.options = options
  }

  generateFillBlankContent({ containerWidth, baseStyle, mode, showQuestionContent }) {
    let content = ''
    let addScoreBox = mode == ModeEnum.PostScan.id
    if (showQuestionContent) {
      let branchContents = this.questions
        .map(q => {
          let branchContent = getBranchContent(q)
          if (branchContent && addScoreBox) {
            branchContent = setContentScoreBox(branchContent, [q])
          }
          return branchContent
        })
        .filter(Boolean)
      if (this.aiScoreTypeId == AiScoreTypeEnum.KouSuan.id) {
        content = this.getKouSuanContent(branchContents, containerWidth, baseStyle)
      } else if (branchContents.length > 0) {
        content = branchContents.join('<br>')
      } else if (this.trunkInContent) {
        content = getTrunkContent(this.questions[0])
        if (addScoreBox) {
          content = setContentScoreBox(content, this.questions)
        }
      } else {
        content = this.getBlanksContent({ containerWidth, baseStyle, addScoreBox })
      }
    } else {
      content = this.getBlanksContent({ containerWidth, baseStyle, addScoreBox })
    }
    this.content = content
  }

  // 口算智能评分内容
  getKouSuanContent(branchContents, containerWidth, baseStyle) {
    // 是否只有一个小题
    let onlyOneBranch = branchContents.length <= 1
    // 行高需较大，多个小题还需设置内联
    branchContents = branchContents.map(branchContent => this.setKouSuanQuestionContent(branchContent, onlyOneBranch))
    if (onlyOneBranch) {
      return branchContents[0] || ''
    }
    // 多个小题需按宽度分行
    let branchContentWidthList = branchContents.map(branchContent =>
      getHTMLWidth({ width: containerWidth + 'px' }, branchContent)
    )
    // 答案至少留15mm
    let minAnswerWidth = PixelsPerMillimeter * 15
    let maxBranchWidth = Math.max(...branchContentWidthList) + minAnswerWidth
    let branchesPerRow = Math.floor(containerWidth / maxBranchWidth)
    // 宽度不足的补空格
    let fontSize = Number.parseFloat(baseStyle.fontSize)
    branchContents = branchContents.map((branchContent, idx) => {
      let branchWidth = branchContentWidthList[idx]
      let addBlank = Math.floor(((maxBranchWidth - branchWidth) / fontSize) * 4)
      return branchContent.replace(/<\/span>$/, match => `${'&nbsp;'.repeat(addBlank)}${match}`)
    })
    let rows = chunk(branchContents, branchesPerRow)
    return rows.map(row => row.join('')).join('<br>')
  }

  setKouSuanQuestionContent(content, onlyOneBranch) {
    let div = document.createElement('div')
    div.innerHTML = content
    let children = Array.from(div.childNodes)
    for (let child of children) {
      if (child.nodeType != 1 && child.nodeType != 3) {
        child.remove()
        continue
      }
      if (child.nodeType == 3) {
        let span = document.createElement('span')
        span.innerHTML = child.nodeValue
        child.replaceWith(span)
        child = span
      } else if (child.nodeType == 1 && child.tagName == 'P' && !onlyOneBranch) {
        let span = document.createElement('span')
        Array.from(child.childNodes).forEach(node => span.appendChild(node))
        child.replaceWith(span)
        child = span
      }
      child.style.lineHeight = '3'
    }
    if (!onlyOneBranch) {
      if (div.childElementCount > 1) {
        let span = document.createElement('span')
        span.style.display = 'inline-block'
        Array.from(div.childNodes).forEach(node => span.appendChild(node))
        div.innerHTML = ''
        div.appendChild(span)
      }
      if (div.childElementCount == 1) {
        div.firstElementChild.classList.add('kousuan-branch')
        div.firstElementChild.style.display = 'inline-block'
      }
      // 补等号
      let textContent = div.textContent.trim()
      if (!textContent.endsWith('=') && !textContent.endsWith('＝')) {
        let textNode = document.createTextNode(' =')
        div.firstElementChild.appendChild(textNode)
      }
    }
    return div.innerHTML
  }

  // 填空内容（不包含题干）
  getBlanksContent({ containerWidth, baseStyle, addScoreBox = false, hideQuestionCode = false }) {
    // 填空之间的间距
    let gapCharCount = 4
    let gapText = '&nbsp;'.repeat(gapCharCount)
    // 各行
    let rows = []
    let row = ''
    let rowBlankCount = 0
    // 创建测量文本宽度实例
    let measure = createAnswerSheetTextWidthMeasure(baseStyle.fontSize)
    // 添加各空
    this.questions.forEach(q => {
      for (let i = 0; i < q.blankScores.length; i++) {
        let codeText = ''
        if (i == 0) {
          if (hideQuestionCode) {
            codeText = getBranchCodeTextNoQuestionCode(q)
          } else {
            codeText = getBranchCodeTextAddQuestionCode(q)
          }
        }
        let blankText = getBlankContent({
          measure,
          containerWidth,
          blanksPerRow: this.blanksPerRow,
          codeText,
          gapCharCount,
        })
        row += blankText
        rowBlankCount++

        if (rowBlankCount % this.blanksPerRow == 0) {
          rows.push(row)
          row = ''
        } else {
          row += gapText
        }
      }
    })
    if (row) {
      rows.push(row)
      row = ''
    }
    measure.destroy()
    let content = rows.map(row => `<p style="line-height: 3">${row}</p>`).join('')
    if (addScoreBox) {
      content = setContentScoreBox(content, this.questions)
    }
    return content
  }

  setFillBlankScoreBox({ showScoreBox }) {
    if (this.type != QuestionGroupTypeEnum.FillBlank.id) {
      return
    }

    const getNewContent = content => {
      if (showScoreBox) {
        return setContentScoreBox(content, this.questions)
      } else {
        return removeContentClass({
          content,
          deleteClass: TrueFalseBoxCssClass,
          keepChildren: true,
        })
      }
    }

    if (this.blocks.length > 1) {
      let separator = '@@igrade-fill-blank-separator@@'
      let content = this.blocks.map(block => block.content).join(separator)
      let newContent = getNewContent(content)
      let split = newContent.split(separator)
      this.blocks.forEach((block, idx) => {
        block.content = split[idx] || ''
      })
      this.content = split.join('')
    } else {
      this.content = getNewContent(this.content)
      if (this.blocks.length == 1) {
        this.blocks[0].content = this.content
      }
    }
  }

  generateEssayContent({ containerWidth, baseStyle, showQuestionContent }) {
    let content = ''
    let measure = createAnswerSheetTextWidthMeasure(baseStyle.fontSize)
    this.questions.forEach((q, qIdx) => {
      if (qIdx > 0) {
        content += '<br>'
      }
      // 加题干
      let questionContent = ''
      if (showQuestionContent) {
        questionContent = getBranchContent(q)
      }
      content += questionContent
      // 若为填空则加空，否则加空行或答题线
      let blankCount = q.blankScores.length
      if (blankCount > 0 && showQuestionContent) {
        return
      }
      // 题号
      let codeText = ''
      if (!questionContent) {
        codeText = getBranchCodeTextAddQuestionCode(q)
      }

      if (blankCount > 0) {
        let gapCharCount = 4
        let gapText = '&nbsp;'.repeat(gapCharCount)
        let rows = []
        let row = ''
        let rowBlankCount = 0
        let blanksPerRow = 3
        for (let i = 0; i < blankCount; i++) {
          row += getBlankContent({
            measure,
            containerWidth,
            blanksPerRow,
            codeText: i == 0 ? codeText : '',
            gapCharCount: 4,
          })
          rowBlankCount++
          if (rowBlankCount % blanksPerRow == 0) {
            rows.push(row)
            row = ''
          } else {
            row += gapText
          }
        }
        if (row) {
          rows.push(row)
          row = ''
        }
        content += rows.map(row => `<p style="line-height: 3">${row}</p>`).join('')
      } else {
        if (this.rows == 0) {
          if (!showQuestionContent) {
            content += `<p style="line-height: 3">${codeText}</p>`
          }
        } else {
          for (let i = 0; i < this.rows; i++) {
            let lineContent = ''
            if (this.showLine) {
              lineContent = getLineContent({ measure, containerWidth, codeText: i == 0 ? codeText : '' })
            } else {
              lineContent = i == 0 ? codeText : '&nbsp;'
            }
            content += `<p style="line-height: 3">${lineContent}</p>`
          }
        }
      }
    })
    this.content = content
    measure.destroy()
  }

  generateChineseWritingContent({ showQuestionContent, mode }) {
    let content = ''
    let firstQuestion = this.questions[0]
    if (showQuestionContent) {
      content = getBranchContent(firstQuestion)
    } else {
      content = `${firstQuestion.questionCode}. `
      // 避免手写打分框超出内容框
      if (mode == ModeEnum.PostScan.id && this.scoreType.name == ScoreTypeEnum.Number.id) {
        content += '<br><br>'
      }
    }
    this.content = content
  }

  generateEnglishWritingContent({ containerWidth, baseStyle, showQuestionContent }) {
    let content = ''
    let firstQuestion = this.questions[0]
    if (showQuestionContent) {
      content = getBranchContent(firstQuestion)
    }
    // 题号
    let codeText = ''
    if (!content) {
      codeText = getBranchCodeTextAddQuestionCode(firstQuestion)
    }
    let measure = createAnswerSheetTextWidthMeasure(baseStyle.fontSize)
    for (let i = 0; i < this.rows; i++) {
      let lineContent = getLineContent({ measure, containerWidth, codeText: i == 0 ? codeText : '' })
      content += `<p style="line-height: 3">${lineContent}</p>`
    }
    this.content = content
    measure.destroy()
  }

  // 改题组类型
  changeType({ type, groupLength, trueOrFalseType, words, rows, showLine, blanksPerRow }) {
    if (!QuestionGroupTypeEnum.hasId(type)) {
      return
    }

    let needResetContent = false
    // 类型有变化时，重置高度
    if (this.type != type) {
      this.type = type
      this.contentHeight = 0
      this.height = 0
      needResetContent = true
    }
    // 改填空题填空个数、解答题行数及答题线、英语作文行数，均重新生成内容
    else if (
      [
        QuestionGroupTypeEnum.FillBlank.id,
        QuestionGroupTypeEnum.Essay.id,
        QuestionGroupTypeEnum.EnglishWriting.id,
      ].includes(type)
    ) {
      needResetContent = true
    }

    // 分别就各种类型设置参数
    if (type == QuestionGroupTypeEnum.Objective.id) {
      this.groupLength = groupLength
      this.trueOrFalseType = trueOrFalseType
    } else if (type == QuestionGroupTypeEnum.ChineseWriting.id) {
      this.words = words
    } else if (type == QuestionGroupTypeEnum.EnglishWriting.id) {
      this.rows = rows
    } else if (type == QuestionGroupTypeEnum.Essay.id) {
      this.rows = rows
      this.showLine = showLine
    } else if (type == QuestionGroupTypeEnum.FillBlank.id) {
      this.blanksPerRow = blanksPerRow
    }

    // 设置填空个数
    this.questions.forEach(q => {
      if (type == QuestionGroupTypeEnum.FillBlank.id) {
        if (q.blankScores.length == 0) {
          let length = q.originalBranch?.answer?.fillBlank?.length || 1
          q.blankScores = splitBlankScores(q.fullScore, length)
        }
      } else {
        q.blankScores = []
      }
    })

    this.aiScoreTypeId = 0

    return needResetContent
  }

  // 改打分类型
  changeScoreType(scoreType) {
    // 填空题打分类型始终是对错框
    if (QuestionGroupTypeEnum.FillBlank.id == this.type) {
      this.scoreType.change({
        name: ScoreTypeEnum.TrueFalse.id,
      })
    } else if (
      [
        QuestionGroupTypeEnum.Essay.id,
        QuestionGroupTypeEnum.ChineseWriting.id,
        QuestionGroupTypeEnum.EnglishWriting.id,
      ].includes(this.type)
    ) {
      this.scoreType.change(scoreType)
    }
  }

  // 改是否支持0.5分或半对错
  changeScoreTypeHalfScore(halfScore) {
    this.scoreType.changeHalfScore(halfScore)
  }

  // 向客观题题组中添加新客观题
  addObjectiveQuestions(objectiveQuestions) {
    if (this.type != QuestionGroupTypeEnum.Objective.id) {
      return
    }
    this.questions.push(...objectiveQuestions)
    this.questions.sort(sortQuestionFunction)
  }

  // 复制为没有题目的新题组
  cloneAsNoQuestion() {
    let newGroup = new QuestionGroup()
    newGroup.type = this.type
    newGroup.scoreType = new ScoreType(this.scoreType)
    newGroup.questions = []
    newGroup.content = ''
    newGroup.options = []
    newGroup.height = 0
    newGroup.contentHeight = 0
    newGroup.groupLength = this.groupLength
    newGroup.trueOrFalseType = this.trueOrFalseType
    newGroup.blanksPerRow = this.blanksPerRow
    newGroup.rows = this.rows
    newGroup.showLine = this.showLine
    newGroup.words = this.words
    newGroup.selectGroupName = this.selectGroupName
    newGroup.selectCount = this.selectCount
    newGroup.selectDescription = this.selectDescription
    newGroup.objectiveQuestionBracketContent = this.objectiveQuestionBracketContent
    newGroup.optionLineHeight = this.optionLineHeight
    newGroup.optionPaddingLeft = this.optionPaddingLeft
    newGroup.blocks = []
    newGroup.newBlocks = []
    newGroup.setRandomId()
    return newGroup
  }

  // 设为选做
  setSelect(selectGroupName, selectCount) {
    this.selectGroupName = selectGroupName
    this.selectCount = selectCount
    let questionCodes = uniq(this.questions.map(q => q.questionCode)).sort((a, b) => a - b)
    this.selectDescription = `请从${questionCodes.join('、')}题中任选${
      selectCount == 1 ? '一' : selectCount == 2 ? '两' : numberToChinese(selectCount)
    }题作答，并用2B铅笔将所选题目对应的标记涂黑，如果多做，则按所做的${
      selectCount == 1 ? '第一' : selectCount == 2 ? '前两' : '前' + numberToChinese(selectCount)
    }题计分。`
  }
  // 取消选做
  unsetSelect() {
    this.selectGroupName = ''
    this.selectCount = 0
    this.selectDescription = ''
  }

  // 设置智能评分
  changeAiScoreType(aiScoreTypeId) {
    this.contentHeight = 0
    this.height = 0
    if (!AiScoreTypeEnum.hasId(aiScoreTypeId)) {
      this.aiScoreTypeId = 0
    } else if (aiScoreTypeId == AiScoreTypeEnum.KouSuan.id) {
      if (this.type != QuestionGroupTypeEnum.FillBlank.id) {
        return
      }
      this.aiScoreTypeId = aiScoreTypeId
    }
  }

  // 添加或移除选择题括号中三角形
  addOrRemoveContentObjectiveTriangle(action) {
    if (
      ![QuestionGroupTypeEnum.ContentObjective.id, QuestionGroupTypeEnum.ContentObjectiveNoOption.id].includes(
        this.type
      )
    ) {
      return
    }
    let changed = false
    this.blocks.forEach(block => {
      let newContent = block.content
      if (action == 'add') {
        newContent = addBracketTriangle(newContent)
      } else if (action == 'remove') {
        newContent = removeBracketTriangle(newContent)
      }
      if (newContent != block.content) {
        changed = true
        block.content = newContent
      }
    })
    if (changed) {
      this.content = this.blocks.map(b => b.content).join('')
    }

    if (action == 'add') {
      this.objectiveQuestionBracketContent = `（${String.fromCharCode(160).repeat(2)}▲${String.fromCharCode(160).repeat(2)}）`
    } else if (action == 'remove') {
      this.objectiveQuestionBracketContent = `（${String.fromCharCode(160).repeat(8)}）`
    }
  }

  // 创建客观题题组
  static createObjectiveQuestionGroup({ objectiveQuestions, trueOrFalseType, subjectName, groupLength }) {
    let firstOriginalQuestion = objectiveQuestions[0].originalQuestion
    if (firstOriginalQuestion) {
      if (!subjectName) {
        subjectName = firstOriginalQuestion.subject.name
      }
    }

    let g = new QuestionGroup()
    g.type = QuestionGroupTypeEnum.Objective.id
    g.questions = objectiveQuestions

    // 设置判断题类型
    if (!trueOrFalseType) {
      trueOrFalseType = subjectName == '英语' ? TrueFalseTypeEnum.TF.id : TrueFalseTypeEnum.TC.id
    }
    g.trueOrFalseType = trueOrFalseType

    if (groupLength) {
      g.groupLength = groupLength
    }

    g.setRandomId()
    return g
  }

  // 创建带题干的客观题题组
  static createContentObjectiveQuestionGroup(objectiveQuestion) {
    let g = new QuestionGroup()
    g.type = QuestionGroupTypeEnum.ContentObjective.id
    g.questions = [objectiveQuestion]
    g.setRandomId()
    return g
  }

  // 创建主观题题组
  static createSubjectiveQuestionGroup({
    subjectiveQuestions,
    questionGroupType,
    blanksPerRow,
    rows,
    showLine,
    words,
    subjectName,
    stageName,
  }) {
    let firstOriginalQuestion = subjectiveQuestions[0].originalQuestion
    if (firstOriginalQuestion) {
      if (!subjectName) {
        subjectName = firstOriginalQuestion.subject.name
      }
      if (!stageName) {
        stageName = firstOriginalQuestion.stage.name
      }
    }

    let g = new QuestionGroup()
    g.questions = subjectiveQuestions

    // 题组类型
    if (!questionGroupType) {
      // 小学数学口算题设为填空题
      if (stageName == '小学' && subjectName == '数学') {
        fixKouSuan(subjectiveQuestions)
      }
      // 所有小题都是填空题
      if (subjectiveQuestions.every(q => q.blankScores.length > 0)) {
        questionGroupType = QuestionGroupTypeEnum.FillBlank.id
      }
      // 只有一个小题，且属于语文科，且这个小题是语文作文题
      else if (
        subjectName == '语文' &&
        subjectiveQuestions.length == 1 &&
        subjectiveQuestions[0].originalBranch &&
        subjectiveQuestions[0].originalBranch.branchType.id == BranchTypeEnum.ChineseWriting.id
      ) {
        questionGroupType = QuestionGroupTypeEnum.ChineseWriting.id
      }
      // 只有一个小题，且属于英语科，且这个小题是英语作文题
      else if (
        subjectName == '英语' &&
        subjectiveQuestions.length == 1 &&
        subjectiveQuestions[0].originalBranch &&
        subjectiveQuestions[0].originalBranch.branchType.id == BranchTypeEnum.EnglishWriting.id
      ) {
        questionGroupType = QuestionGroupTypeEnum.EnglishWriting.id
      }
      // 其他情况为解答题
      else {
        questionGroupType = QuestionGroupTypeEnum.Essay.id
      }
    }
    g.type = questionGroupType

    // 各类型参数
    if (g.type == QuestionGroupTypeEnum.FillBlank.id) {
      if (blanksPerRow) {
        g.blanksPerRow = blanksPerRow
      }
    } else if (g.type == QuestionGroupTypeEnum.ChineseWriting.id) {
      if (!words) {
        words = stageName == '高中' ? 1200 : stageName == '小学' ? 600 : 900
      }
      g.words = words
    } else if (g.type == QuestionGroupTypeEnum.EnglishWriting.id) {
      if (!rows) {
        rows = stageName == '高中' ? 12 : stageName == '小学' ? 6 : 9
      }
      g.rows = rows
    } else if (g.type == QuestionGroupTypeEnum.Essay.id) {
      if (!rows) {
        if (['数学', '物理', '化学'].includes(subjectName)) {
          rows = 5
        } else if (['语文', '英语'].includes(subjectName)) {
          rows = 1
        } else {
          rows = 3
        }
      }
      g.rows = rows

      if (showLine == null) {
        showLine = !['数学', '物理', '化学'].includes(subjectName)
      }
      g.showLine = showLine
    }

    g.setRandomId()
    return g
  }

  // 创建材料题组
  static createTrunkQuestionGroup(questions) {
    let g = new QuestionGroup()
    g.questions = questions
    g.type = QuestionGroupTypeEnum.Trunk.id
    g.setRandomId()
    return g
  }
}

function getTrunkContent(q) {
  let originalQuestion = q.originalQuestion
  if (originalQuestion && originalQuestion.trunk) {
    let codeText = originalQuestion.code ? `${q.questionCode}. ` : ''
    return prefixCodeTextToTrunkStem(originalQuestion.getTrunkContent(), codeText)
  } else {
    return ''
  }
}

// 小题号
function getBranchCodeText(q) {
  return q.branchCode == 0 ? `${q.questionCode}. ` : `（${q.branchCode}）`
}

// 小题号不加题号
function getBranchCodeTextNoQuestionCode(q) {
  return q.branchCode == 0 ? '' : `（${q.branchCode}）`
}

// 小题号第一个小题加题号
function getBranchCodeTextAddQuestionCode(q) {
  return q.branchCode == 0
    ? `${q.questionCode}. `
    : q.branchCode == 1
      ? `${q.questionCode}.（${q.branchCode}）`
      : `（${q.branchCode}）`
}

function getBranchContent(q) {
  if (!q.originalQuestion || !q.originalBranch) {
    return ''
  }
  let { stem, branchType, options } = q.originalBranch
  let hasOptions = branchType.isChoice && options.length > 0
  if (!stem && !hasOptions) {
    return ''
  }
  let codeText = getBranchCodeText(q)
  let content = prefixCodeTextToTrunkStem(stem, codeText)
  if (hasOptions) {
    if (stem) {
      content += '<br>'
    }
    content += options.map(opt => `${opt.label}. ${opt.content}`).join('<br>')
  }
  return content
}

// 给材料、题干添加题号
function prefixCodeTextToTrunkStem(content, codeText) {
  if (!codeText) {
    return content
  }
  let match = content.match(/^<p[^>]*>/)
  // 插入到p标签内
  if (match) {
    let idx = match[0].length
    return content.substring(0, idx) + codeText + content.substring(idx)
  } else {
    return codeText + content
  }
}

// 获取添加空行后的文本
function getLineContent({ measure, containerWidth, codeText }) {
  return getUnderline({ measure, maxWidth: containerWidth, codeText })
}

// 获取填空题下划线文本
function getBlankContent({ measure, containerWidth, codeText, blanksPerRow, gapCharCount }) {
  let gapWidth = measure.nbspWidth * gapCharCount
  let maxWidth = Math.floor((containerWidth - gapWidth * (blanksPerRow - 1)) / blanksPerRow)
  return getUnderline({ measure, maxWidth, codeText })
}

// 获取指定长度的下划线
function getUnderline({ measure, maxWidth, codeText }) {
  let codeTextWidth = codeText ? measure.measureWidth(codeText) : 0
  // 先估计下划线长度
  let repeatCount = Math.floor((maxWidth - codeTextWidth) / measure.underlineWidth)
  let getContent = () => codeText + '_'.repeat(repeatCount)
  return getContent()
}

// 测量文本宽度
function createAnswerSheetTextWidthMeasure(fontSize) {
  let instance = createTextWidthMeasure(fontSize, 'times new roman, 宋体')
  let underlineWidth = instance.measureWidth('_'.repeat(100)) / 100
  let nbspWidth = instance.measureWidth(' '.repeat(100)) / 100
  return {
    instance,
    underlineWidth,
    nbspWidth,
    measureWidth(text) {
      return instance.measureWidth(text)
    },
    destroy() {
      instance.destroy()
    },
  }
}

function fixKouSuan(subjectiveQuestions) {
  // 已经是填空题不再处理
  if (subjectiveQuestions.every(q => q.blankScores.length > 0)) {
    return
  }
  // 无题目内容的不处理
  let originalQuestion = subjectiveQuestions[0].originalQuestion
  if (!originalQuestion || subjectiveQuestions.some(q => q.originalQuestion != originalQuestion)) {
    return
  }
  // 找题目起始文本内容
  let questionContent = ''
  if (subjectiveQuestions.length == 1) {
    questionContent = originalQuestion.branches[0].stem
  } else {
    questionContent = originalQuestion.trunk
  }
  let div = document.createElement('div')
  div.innerHTML = questionContent
  let text = div.innerText.trim()
  // 若以特定字符串开头，则认为是口算题
  if (text.startsWith('直接写出得数') || text.startsWith('口算')) {
    subjectiveQuestions.forEach(q => {
      if (q.blankScores.length == 0) {
        q.blankScores.push(q.fullScore)
      }
    })
  }
}

function setContentScoreBox(content, questions) {
  // 题目填空列表
  let blankCodes = []
  for (let q of questions) {
    let fullCode = q.fullCode
    if (q.blankScores.length == 0) {
      q.blankScores = [q.fullScore]
    }
    for (let i = 0; i < q.blankScores.length; i++) {
      blankCodes.push({
        questionCode: q.questionCode,
        branchCode: q.branchCode,
        blankIndex: i,
        infoText: q.blankScores.length > 1 ? `${fullCode}_${i + 1}` : fullCode,
      })
    }
  }

  // 清除原有填空
  content = removeContentClass({ content, deleteClass: TrueFalseBoxCssClass, keepChildren: true })

  // 尝试找小括号、下划线两种填空，采用匹配多的
  let blankRegExList = [/[(（](\s|&nbsp;|_){4,}[）)]/g, /_{4,}/g]
  let blankCountList = blankRegExList.map(reg => {
    let match = content.match(reg)
    return {
      reg,
      count: match?.length || 0,
    }
  })
  blankCountList.sort((a, b) => b.count - a.count)
  let reg = blankCountList[0].reg

  // 替换
  let idx = 0
  let blankText = '________________'
  const replaceBlankTextWithNode = (blank, text) => {
    return `<span class="${TrueFalseBoxCssClass}" data-question-code=${blank.questionCode} data-branch-code=${blank.branchCode} data-blank-index=${blank.blankIndex} data-blank-info=${blank.infoText}>${text}</span>`
  }
  content = content.replace(reg, text => {
    blankText = text
    let blank = blankCodes[idx]
    if (!blank) {
      return text
    }
    idx++
    return replaceBlankTextWithNode(blank, text)
  })
  // 空不够的补充在后面
  for (; idx < blankCodes.length; idx++) {
    let blank = blankCodes[idx]
    content += '<p>' + replaceBlankTextWithNode(blank, blankText) + '</p>'
  }
  return content
}

function removeContentClass({ content, deleteClass, keepChildren }) {
  let div = document.createElement('div')
  div.innerHTML = content
  div.querySelectorAll('.' + deleteClass).forEach(box => {
    if (keepChildren) {
      box.replaceWith(...box.childNodes)
    } else {
      box.remove()
    }
  })
  content = div.innerHTML
  div.remove()
  return content
}

function addBracketTriangle(content) {
  if (!content) {
    return content
  }
  let reg = /[(（](\s|&nbsp;){4,}[）)]/g
  let matches = Array.from(content.matchAll(reg))
  if (!matches || matches.length == 0) {
    return content
  }
  let match = matches[matches.length - 1]
  let targetStr = match[0]
  let startIndex = match.index
  let endIndex = startIndex + targetStr.length
  targetStr = targetStr.replace(/&nbsp;/g, ' ')
  // 一个▲的宽度大约为4格空格的宽度
  let blankCharCount = targetStr.length - 2
  let halfBlankCharCount = blankCharCount % 2 == 1 ? (blankCharCount - 3) / 2 : (blankCharCount - 4) / 2
  halfBlankCharCount = Math.max(2, halfBlankCharCount)
  let halfBlank = '&nbsp;'.repeat(halfBlankCharCount)
  let withTriangle = `（${halfBlank}▲${halfBlank}）`
  return content.substring(0, startIndex) + withTriangle + content.substring(endIndex)
}

function removeBracketTriangle(content) {
  if (!content) {
    return content
  }
  let reg = /[(（](\s|&nbsp;)+▲(\s|&nbsp;)+[）)]/g
  let matches = Array.from(content.matchAll(reg))
  if (!matches || matches.length == 0) {
    return content
  }
  let match = matches[matches.length - 1]
  let targetStr = match[0]
  let startIndex = match.index
  let endIndex = startIndex + targetStr.length
  let withoutTriangle = targetStr.replace('▲', '&nbsp;'.repeat(4))
  return content.substring(0, startIndex) + withoutTriangle + content.substring(endIndex)
}
