<script>
  import ModalStudentPaper from '@/views/emarking/monitor/components/modal_student_paper.vue'

  import { apiGetStudentPaperByStudentPaperInfo } from '@/api/emarking'
  import { apiGetAINormalMarkHistory, apiCorrectAIMark } from '@/api/emarking/ai'

  import { debounce } from '@/utils/function'
  import { deepCopy } from '@/utils/object'
  import { sumByDefaultZero, roundScore } from '@/utils/math'
  import MathJaxUtil from '@/utils/mathjax'

  const PageSize = 10

  export default {
    components: {
      'modal-student-paper': ModalStudentPaper,
    },

    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      blocks: {
        type: Array,
        default: () => [],
      },
      targetBlockId: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        aiMarkHistoryList: [],
        total: 0,
        currentPage: 1,

        activeBlockId: '',
        minScore: null,
        maxScore: null,
        corrected: 0,
        correctedStatus: [
          {
            id: 0,
            name: '全部',
          },
          {
            id: 1,
            name: '已纠正',
          },
          {
            id: 2,
            name: '无纠正',
          },
        ],

        studentInfo: null,
        modalStudentPaperShowed: false,

        isEditingActivePaperQuestions: false,
      }
    },

    computed: {
      activeBlock() {
        return this.blocks.find(b => b.blockId === this.activeBlockId)
      },
      activeBlockFullScore() {
        return this.activeBlock ? this.activeBlock.fullScore : null
      },

      inputMinScorePlaceholder() {
        return !this.activeBlock ? '' : '最低0分'
      },
      inputMaxScorePlaceholder() {
        return !this.activeBlock ? '' : `满分${this.activeBlock.fullScore}分`
      },

      activePaper() {
        return this.aiMarkHistoryList.find(x => x.active)
      },
      activePaperIndex() {
        return this.aiMarkHistoryList.findIndex(x => x.active)
      },

      hasPreviousPaper() {
        return this.activePaperIndex > 0 || this.currentPage > 1
      },
      hasNextPaper() {
        return (
          (this.activePaperIndex >= 0 && this.activePaperIndex < this.aiMarkHistoryList.length - 1) ||
          this.currentPage * PageSize < this.total
        )
      },

      tableColumns() {
        return [
          {
            title: '序号',
            key: 'No',
            align: 'center',
            width: 80,
          },
          {
            title: 'AI 评分',
            key: 'score',
            render: (h, params) => h('span', {}, params.row.type == 1 ? '问题卷' : params.row.score),
            align: 'center',
          },
          {
            title: '纠正评分',
            align: 'center',
            render: (h, params) => h('span', {}, params.row.correctScore || '-'),
          },
        ]
      },

      activePaperQuestions() {
        return this.activePaper?.panelQuestions || []
      },
      activePaperTotalScore() {
        return this.activePaper?.totalScore || 0
      },
    },

    watch: {
      activePaperQuestions() {
        if (this.activePaperQuestions?.length) {
          this.renderMathJax()
        }
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(isVisible) {
        if (isVisible) {
          this.resetData()
          this.changeActiveBlock(this.targetBlockId)
        } else {
          this.onCloseModal()
        }
      },
      resetData() {
        this.aiMarkHistoryList = []
        this.activeBlockId = ''
        this.minScore = null
        this.maxScore = null
        this.corrected = 0
        this.total = 0
        this.currentPage = 1
      },
      changeAIMarkRequestParams: debounce(function () {
        this.onPageChange()
      }, 600),

      /**
       * fetch data
       */
      fetchAIMarkHistory() {
        if (this.activeBlockId) {
          this.isEditingActivePaperQuestions = false
          return apiGetAINormalMarkHistory({
            blockId: this.activeBlockId,
            minScore: this.minScore === null ? undefined : this.minScore,
            maxScore: this.maxScore === null ? undefined : this.maxScore,
            corrected: this.corrected ? this.corrected === 1 : undefined,
            currentPage: this.currentPage,
            pageSize: PageSize,
          })
            .then(response => {
              const ActiveBlockQuestions = this.activeBlock?.questions || []
              this.aiMarkHistoryList = (response?.list || []).map((item, idx) => {
                const SubScoreSplitList = (item.subScore || '')
                  .split(';')
                  .filter(String)
                  .map(q => {
                    const QuestionMap = q.split('=')
                    const QuestionCodes = (QuestionMap[0] || '').split('.')

                    return {
                      questionName: QuestionMap[0],
                      score: Number(QuestionMap[1]),
                      questionCode: QuestionCodes[0],
                      branchCode: QuestionCodes[1] || 0,
                    }
                  })
                  .filter(q => q.questionName)
                const CorrectSubScoreSplitList = (item.correctSubScore || '')
                  .split(';')
                  .filter(String)
                  .map(q => {
                    const QuestionMap = q.split('=')
                    const QuestionCodes = (QuestionMap[0] || '').split('.')

                    return {
                      questionName: QuestionMap[0],
                      score: Number(QuestionMap[1]),
                      questionCode: QuestionCodes[0],
                      branchCode: QuestionCodes[1] || 0,
                    }
                  })
                  .filter(q => q.questionName)

                const SubScoreArray = []
                const CorrectSubScoreArray = []
                const Questions = []

                let questionResults = []
                if (item.resultJson) {
                  try {
                    questionResults = JSON.parse(item.resultJson)
                  } catch {
                    try {
                      questionResults = JSON.parse(item.resultJson.replace(/\n/g, '\\n'))
                    } catch {
                      questionResults = []
                    }
                  }
                }

                ActiveBlockQuestions.forEach(abq => {
                  const TargetSubScoreItem = (SubScoreSplitList || []).find(
                    q => q.questionCode == abq.questionCode && q.branchCode == abq.branchCode
                  )
                  SubScoreArray.push({
                    questionName:
                      TargetSubScoreItem?.questionName ||
                      `${abq.branchCode ? abq.questionCode + '.' + abq.branchCode : abq.questionCode}` ||
                      '',
                    score: TargetSubScoreItem?.score,
                    questionCode: TargetSubScoreItem?.questionCode || abq.questionCode,
                    branchCode: TargetSubScoreItem?.branchCode || abq.branchCode,
                    fullScore: abq.fullScore || 0,
                  })

                  const TargetCorrectSubScoreItem = (CorrectSubScoreSplitList || []).find(
                    q => q.questionCode == abq.questionCode && q.branchCode == abq.branchCode
                  )
                  CorrectSubScoreArray.push({
                    questionName:
                      TargetCorrectSubScoreItem?.questionName ||
                      `${abq.branchCode ? abq.questionCode + '.' + abq.branchCode : abq.questionCode}` ||
                      '',
                    score: TargetCorrectSubScoreItem?.score,
                    questionCode: TargetCorrectSubScoreItem?.questionCode || abq.questionCode,
                    branchCode: TargetCorrectSubScoreItem?.branchCode || abq.branchCode,
                    fullScore: abq.fullScore || 0,
                  })

                  let answer
                  let answerWithoutHTML
                  if (abq.answer) {
                    if (Array.isArray(abq.answer)) {
                      answer = abq.answer.join('、')
                      answerWithoutHTML = answer
                    } else {
                      answer = abq.answer
                      answerWithoutHTML = this.getTextWithoutHTMLTag(abq.answer)
                    }
                  } else {
                    answer = ''
                    answerWithoutHTML = ''
                  }

                  Questions.push({
                    questionName:
                      TargetCorrectSubScoreItem?.questionName ||
                      TargetSubScoreItem?.questionName ||
                      `${abq.branchCode ? abq.questionCode + '.' + abq.branchCode : abq.questionCode}` ||
                      '',
                    score:
                      TargetCorrectSubScoreItem?.score || TargetCorrectSubScoreItem?.score === 0
                        ? TargetCorrectSubScoreItem.score
                        : TargetSubScoreItem?.score,
                    questionCode: abq.questionCode,
                    branchCode: abq.branchCode,
                    fullScore: abq.fullScore || 0,
                    questionResult: questionResults.find(
                      qr =>
                        qr.questionCode ==
                        (qr.questionCode.includes('.') ? abq.questionCode + '.' + abq.branchCode : abq.questionCode)
                    ),
                    answer: answer,
                    answerWithoutHTML: answerWithoutHTML,
                  })
                })

                return {
                  ...item,
                  No: idx + 1 + (this.currentPage - 1) * PageSize,
                  subScoreText: SubScoreArray.map(
                    q => q.questionName + '=' + (q.score || q.score === 0 ? q.score : '')
                  ).join(';'),
                  correctSubScoreText: CorrectSubScoreArray.map(
                    q => q.questionName + '=' + (q.score || q.score === 0 ? q.score : '')
                  ).join(';'),
                  questions: Questions,
                  panelQuestions: deepCopy(Questions),
                }
              })
              this.total = response?.total || 0
            })
            .catch(() => {
              this.aiMarkHistoryList = []
              this.total = 0
            })
        } else {
          return Promise.resolve()
        }
      },
      async renderMathJax() {
        MathJaxUtil.clearRenderQueue()
        await this.$nextTick()
        const RenderComponents = this.$refs['math-render'] || []
        if (RenderComponents.length) {
          MathJaxUtil.render(RenderComponents)
        }
      },

      /**
       * handle action
       */
      onPageChange(page = 1, nextActivePaperIndex = 0) {
        this.currentPage = page
        this.fetchAIMarkHistory().then(() => this.changeActivePaper(this.aiMarkHistoryList[nextActivePaperIndex]))
      },
      changeActiveBlock(blockId) {
        this.activeBlockId = blockId
        this.minScore = null
        this.maxScore = null
        this.corrected = 0

        this.onPageChange()
      },
      changeActivePaper(paper) {
        if (paper) {
          this.aiMarkHistoryList.forEach(item => (item.active = item.subjectiveItemId === paper.subjectiveItemId))
          this.calcActivePaperTotalScore()
        }
      },
      goPreviousPaper() {
        if (this.activePaperIndex > 0) {
          this.changeActivePaper(this.aiMarkHistoryList[this.activePaperIndex - 1])
        } else if (this.activePaperIndex === 0 && this.currentPage > 1) {
          this.onPageChange(this.currentPage - 1, PageSize - 1)
        }
      },
      goNextPaper() {
        if (this.activePaperIndex < this.aiMarkHistoryList.length - 1) {
          this.changeActivePaper(this.aiMarkHistoryList[this.activePaperIndex + 1])
        } else if (
          this.activePaperIndex === this.aiMarkHistoryList.length - 1 &&
          this.currentPage < Math.ceil(this.total / PageSize)
        ) {
          this.onPageChange(this.currentPage + 1, 0)
        }
      },
      handleBtnCheckStudentPaperClick() {
        if (!this.activePaper || !this.activePaper.subjectiveId || !this.activePaper.subjectiveItemId) {
          this.$Message.warning({
            duration: 4,
            content: '未有目标考生信息，请刷新后重试',
          })
          return
        }

        this.studentInfo = null
        this.$Spin.show({
          render: h =>
            h(
              'span',
              {
                style: {
                  fontSize: '26px',
                },
              },
              '正在加载考生原卷，请稍候'
            ),
        })
        apiGetStudentPaperByStudentPaperInfo({
          subjectiveId: this.activePaper.subjectiveId,
          subjectiveItemId: this.activePaper.subjectiveItemId,
        })
          .then(response => {
            this.studentInfo = response || null
            this.modalStudentPaperShowed = true
          })
          .finally(() => this.$Spin.hide())
      },
      changeEditingActivePaperQuestionsStatus() {
        this.isEditingActivePaperQuestions = !this.isEditingActivePaperQuestions
        if (!this.isEditingActivePaperQuestions) {
          this.activePaper.panelQuestions = deepCopy(this.activePaper.questions)
          this.calcActivePaperTotalScore()
        }
      },

      /**
       * for show
       */
      getTableRowClassName(row) {
        if (row && row.active) {
          return 'row-active'
        } else {
          return ''
        }
      },
      getTextWithoutHTMLTag(text) {
        const TempDiv = document.createElement('div')
        TempDiv.innerHTML = text
        return (TempDiv.textContent || TempDiv.innerText || '').replace(/\s+/g, ' ').trim()
      },

      /**
       * score panel
       */
      calcActivePaperTotalScore() {
        if (!this.activePaper) {
          return
        }
        this.activePaper.totalScore = sumByDefaultZero(this.activePaperQuestions, q => Number(q.score))
      },

      setQuestionFullScore(questionCode) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (target) {
          target.score = target.fullScore
        }
        this.calcActivePaperTotalScore()
      },

      setQuestionZeroScore(questionCode) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (target) {
          target.score = 0
        }
        this.calcActivePaperTotalScore()
      },

      setQuestionScore(questionCode, score) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (!target) {
          return
        }
        target.score = score
        this.calcActivePaperTotalScore()
      },

      changeQuestionScore(question) {
        if (question.score == null || question.score === '') {
          question.score = null
        } else {
          question.score = Number(question.score)
          if (isNaN(question.score) || question.score < 0 || question.score > question.fullScore) {
            question.score = null
          } else {
            question.score = roundScore(question.score)
          }
        }
        this.calcActivePaperTotalScore()
      },

      focusInput(questionCode) {
        let elementInput = this.$refs['inputScore_' + questionCode]
        if (!elementInput) {
          return
        }

        if (elementInput.focus) {
          elementInput.focus()
        } else if (elementInput[0] && elementInput[0].focus) {
          elementInput[0].focus()
        }
      },

      focusNextQuestion(questionCode) {
        let index = this.activePaperQuestions.findIndex(x => x.questionCode === questionCode)
        if (index < 0) {
          return
        }

        let nextQuestion = this.activePaperQuestions[index + 1]
        if (!nextQuestion) {
          return
        }

        this.focusInput(nextQuestion.questionCode)
      },

      handleInputEnter(question) {
        this.changeQuestionScore(question)
        this.focusNextQuestion(question.questionCode)
      },
      handleBtnSubmitScoreClick() {
        if (!this.activePaper) {
          return
        }

        this.calcActivePaperTotalScore()
        let isScoreValid =
          typeof this.activePaperTotalScore == 'number' &&
          this.activePaperQuestions.every(x => {
            if (x.score == null || x.score === '') {
              return false
            }
            let transScore = Number(x.score)
            return !Number.isNaN(transScore) && transScore >= 0 && transScore <= x.fullScore
          })

        if (!isScoreValid) {
          this.$Message.warning({
            content: '请输入合理的分数值',
          })
          return
        }

        apiCorrectAIMark({
          score: this.activePaperTotalScore,
          subScore: this.activePaperQuestions.map(x => x.questionName + '=' + x.score).join(';'),
          subjectiveId: this.activePaper.subjectiveId,
          subjectiveItemId: this.activePaper.subjectiveItemId,
        }).then(() => {
          this.$Message.success({
            content: '已纠正评分',
          })
          const LastActivePaperIndex = this.activePaperIndex
          this.fetchAIMarkHistory().then(() => this.changeActivePaper(this.aiMarkHistoryList[LastActivePaperIndex]))
        })
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :mask="false"
    :closable="false"
    :mask-closable="false"
    fullscreen
    footer-hide
    class="modal-ai-score-history"
    @on-visible-change="handleModalVisibleChange"
  >
    <template #header>
      <div class="modal-header">
        <div class="header-left">
          <div class="modal-title">查看AI智能评卷记录</div>
          <div class="form">
            <div class="form-item">
              <span class="form-item-label">题块</span>
              <Select :model-value="activeBlockId" class="selector-block" @on-change="changeActiveBlock">
                <Option v-for="block of blocks" :key="block.blockId" :value="block.blockId">{{
                  block.blockName
                }}</Option>
              </Select>
            </div>
            <div class="form-item">
              <span class="form-item-label">分数范围</span>
              <span>
                <InputNumber
                  v-model="minScore"
                  :disabled="!activeBlock"
                  :min="0"
                  :max="activeBlockFullScore"
                  :placeholder="inputMinScorePlaceholder"
                  @on-change="changeAIMarkRequestParams"
                ></InputNumber>
                <span>分至</span>
                <InputNumber
                  v-model="maxScore"
                  :disabled="!activeBlock"
                  :min="0"
                  :max="activeBlockFullScore"
                  :placeholder="inputMaxScorePlaceholder"
                  @on-change="changeAIMarkRequestParams"
                ></InputNumber>
                <span>分</span>
              </span>
            </div>
            <div class="form-item">
              <RadioGroup
                v-model="corrected"
                type="button"
                button-style="solid"
                size="small"
                @on-change="changeAIMarkRequestParams"
              >
                <Radio v-for="s of correctedStatus" :key="s.id" :label="s.id">{{ s.name }}</Radio>
              </RadioGroup>
            </div>
          </div>
        </div>

        <div class="header-right">
          <ButtonGroup size="small">
            <Button size="small" type="default" :disabled="!hasPreviousPaper" @click="goPreviousPaper">
              <Icon type="md-arrow-back"></Icon>
              上一份
            </Button>
            <Button size="small" type="default" :disabled="!hasNextPaper" @click="goNextPaper">
              <Icon type="md-arrow-forward"></Icon>
              下一份
            </Button>
          </ButtonGroup>
          <span class="btn-close" @click="onCloseModal"><Icon type="ios-close" /></span>
        </div>
      </div>
    </template>

    <div class="modal-content">
      <div class="section-list">
        <div>
          <Table
            :columns="tableColumns"
            :data="aiMarkHistoryList"
            :row-class-name="getTableRowClassName"
            border
            @on-row-click="changeActivePaper"
          ></Table>
        </div>
        <div class="pager">
          <span class="total-info">共 {{ total }} 条</span>
          <Page :model-value="currentPage" :total="total" simple @on-change="onPageChange" />
        </div>
      </div>

      <div class="section-paper">
        <div v-if="activePaper && activePaper.imgUrl" class="mark">
          <div class="box-paper-top">
            <div>
              <span class="number">{{ activePaper.score + ' 分' }}</span>
              <span
                >（第 {{ activePaper.No }} 份<span v-if="activePaper.type != 1">，{{ activePaper.subScoreText }}</span
                >）</span
              >
            </div>
            <div class="box-action">
              <Button type="warning" ghost @click="handleBtnCheckStudentPaperClick">查看原卷</Button>
            </div>
          </div>
          <template v-if="activePaper && activePaper.correctScore">
            <span class="spot-check-point">
              <span class="title">【 纠正评分 】</span>
              <span class="score">{{ activePaper.correctScore }}分</span>
              （{{ activePaper.correctByName }}，{{ activePaper.correctSubScoreText }}，纠正时间：{{
                activePaper.correctTime
              }}）
            </span>
          </template>
        </div>
        <img v-if="activePaper && activePaper.imgUrl" :src="activePaper.imgUrl" />
      </div>

      <div class="section-mark-panel">
        <div class="header">
          <div class="row">
            <span class="title">评分详情</span>
            <Button
              v-show="activePaperQuestions && activePaperQuestions.length"
              type="primary"
              size="small"
              @click="changeEditingActivePaperQuestionsStatus"
              >{{ isEditingActivePaperQuestions ? '取消' : '纠正' }}</Button
            >
          </div>
        </div>

        <div class="content">
          <template v-if="activeBlock">
            <div v-for="question of activePaperQuestions" :key="question.questionCode" class="question-marking">
              <div class="row row-1">
                <div class="question-name">{{ question.questionName }}</div>
                <div class="score">
                  <div class="item">
                    <span class="item-label">AI 评分：</span>
                    <template v-if="isEditingActivePaperQuestions">
                      <Input
                        :ref="'inputScore_' + question.questionCode"
                        v-model="question.score"
                        class="input-score"
                        :placeholder="'满分' + question.fullScore + '分'"
                        @on-blur="changeQuestionScore(question)"
                        @on-enter="handleInputEnter(question)"
                      />
                      <Button
                        icon="md-checkmark"
                        class="btn-score btn-score-full"
                        @click="setQuestionFullScore(question.questionCode)"
                      ></Button>
                      <Button
                        icon="md-close"
                        class="btn-score btn-score-zero"
                        @click="setQuestionZeroScore(question.questionCode)"
                      ></Button>
                    </template>
                    <span v-else>{{ question.score }}</span>
                  </div>
                  <template v-if="question.questionResult">
                    <Tooltip :max-width="330" transfer>
                      <template #content>
                        <div ref="math-render" v-html="question.answerWithoutHTML"></div>
                      </template>
                      <div class="item">
                        <span class="item-label">参考答案：</span>
                        <div ref="math-render" class="one-line-content" v-html="question.answerWithoutHTML"></div>
                      </div>
                    </Tooltip>
                    <div class="item">
                      <span class="item-label">识别作答：</span>
                      <div ref="math-render" v-html="question.questionResult.studentAnswer"></div>
                    </div>
                    <div class="item">
                      <span class="item-label">评分理由：</span>
                      <div ref="math-render" v-html="question.questionResult.scoreReason"></div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>

        <div class="footer">
          <div class="total-score">
            <span class="title">总分</span>
            <span class="score-value">{{ activePaperTotalScore }}</span>
          </div>
          <Button v-if="isEditingActivePaperQuestions" type="success" @click="handleBtnSubmitScoreClick"
            >提交分数</Button
          >
        </div>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary">确定</Button>
    </template>

    <modal-student-paper v-model="modalStudentPaperShowed" :student-info="studentInfo"></modal-student-paper>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-ai-score-history {
    .modal-header {
      @include flex(row, space-between, center);

      .header-left {
        @include flex(row, flex-start, center);

        .modal-title {
          flex: none;
          margin-right: 30px;
          font-size: $font-size-medium-x;
        }

        .form {
          @include flex(row, flex-start, center);
          flex-shrink: 0;

          .form-item {
            @include flex(row, flex-start, center);
            margin-left: 20px;

            .form-item-label {
              flex-shrink: 0;
              margin-right: 5px;
            }

            .selector-block {
              flex-shrink: 0;
              width: 140px;
            }
          }
        }
      }

      .header-right {
        @include flex(row, flex-end, center);

        .btn-close {
          margin-left: 30px;
          color: #999;
          font-size: 31px;
          transition: color ease 0.2s;
        }

        .btn-close:hover {
          color: #444;
          cursor: pointer;
        }
      }
    }

    .modal-content {
      @include flex(row, flex-start, flex-start);
      position: absolute;
      top: 10px;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0 10px;
      overflow: auto;

      .section-list {
        position: sticky;
        top: 10px;
        flex-grow: 0;
        flex-shrink: 0;
        width: 260px;
        margin-top: 10px;

        .pager {
          @include flex(row, flex-end, center);
          height: 30px;
          margin-top: 10px;

          .total-info {
            margin-right: 6px;
          }

          :deep(.ivu-page-simple .ivu-page-simple-pager input) {
            width: 46px;
          }
        }

        :deep(.row-active td) {
          background-color: #ebf7ff;
        }
      }

      .section-paper {
        @include flex(column, flex-start, stretch);
        flex-grow: 1;
        width: 0;
        min-height: 100%;
        margin-top: 10px;
        margin-left: 10px;

        img {
          width: 100%;
        }

        .box-paper-top {
          @include flex(row, space-between, center);
          margin-bottom: 10px;

          .box-action {
            width: 300px;
            text-align: right;
          }
        }

        .mark {
          position: sticky;
          top: 0;
          margin-bottom: 4px;
          padding-top: 10px;
          padding-left: 20px;
          color: red;
          font-size: $font-size-medium-x;
          background-color: white;

          .number {
            font-weight: bold;
            font-size: $font-size-large;
          }

          .spot-check-point {
            color: $color-primary;

            .title {
              margin-right: 5px;
            }

            .score {
              font-weight: bold;
              font-size: $font-size-large;
            }
          }
        }

        .ai-score-content-wrapper {
          position: sticky;
          bottom: 0;
          width: 100%;
          margin-top: 16px;
          padding-bottom: 10px;
          background-color: white;

          .alert-ai-score-content {
            margin-bottom: 0;
            padding: 8px 16px;

            .ai-score-content-header {
              @include flex(row, space-between, center);
              margin-bottom: 8px;

              .ai-score-btn-more {
                font-size: $font-size-medium;
              }
            }

            .ai-score-content {
              height: 80px;
              padding-right: 4px;
              overflow-y: auto;
              white-space: pre-wrap;
            }
          }
        }
      }

      .section-mark-panel {
        @include flex(column, flex-start, stretch);
        position: sticky;
        top: 10px;
        flex-grow: 0;
        flex-shrink: 0;
        width: 400px;
        height: calc(100vh - 100px);
        margin-left: 10px;
        padding: 15px;
        border: 1px solid $color-border;

        .header {
          flex-grow: 0;
          height: 45px;
          padding-bottom: 10px;
          border-bottom: 1px solid $color-border;

          .row {
            @include flex(row, space-between, center);

            .title {
              color: $color-primary;
              font-size: $font-size-medium-xs;
            }
          }
        }

        .content {
          flex-grow: 1;
          height: 0;
          padding: 25px 0;
          overflow-y: auto;

          .question-marking {
            &:not(:last-child) {
              margin-bottom: 15px;
            }

            .row-1 {
              @include flex(row, flex-start, stretch);
              min-height: 32px;
              line-height: 2;

              .question-name {
                min-width: 1.5em;
                margin-right: 0.5em;
                font-weight: bold;
              }

              .score {
                .item {
                  @include flex(row, flex-start, stretch);

                  flex-grow: 0;
                  flex-shrink: 0;

                  .item-label {
                    width: 70px;
                    font-weight: bold;
                    white-space: nowrap;
                  }

                  .input-score {
                    width: 80px;
                  }

                  .btn-score {
                    margin-left: 5px;
                  }

                  .btn-score-full {
                    color: $color-success;
                  }

                  .btn-score-zero {
                    color: $color-error;
                  }

                  .one-line-content {
                    width: 244px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                }
              }
            }
          }
        }

        .footer {
          @include flex(row, space-between, center);
          flex-grow: 0;
          height: 43px;

          padding-top: 10px;
          border-top: 1px solid $color-border;
          user-select: none;

          .total-score {
            .title {
              display: inline-block;
              width: 3em;
              font-weight: bold;
              line-height: 20px;
            }

            .score-value {
              color: #3bf1df;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
</style>
