<template>
  <Modal
    title="AI评卷结果"
    :model-value="modelValue"
    footer-hide
    draggable
    :width="modalWidth"
    @on-visible-change="handleVisibleChange"
  >
    <div v-if="invalid" class="result-invalid">
      <div class="title">AI评卷结果格式有误，以下是原始结果：</div>
      <div class="result-content" v-html="aiScoreResult"></div>
    </div>
    <Table v-else ref="resultTable" class="table-question" :columns="tableColumns" :data="questions"></Table>
  </Modal>
</template>

<script>
  import MathJaxUtil from '@/utils/mathjax'

  export default {
    props: {
      modelValue: Boolean,
      aiScoreResult: String,
    },
    emits: ['update:modelValue'],
    data() {
      return {
        questions: [],
        invalid: false,
      }
    },
    computed: {
      hasWritingComment() {
        return this.questions.some(q => q.writingComment)
      },
      modalWidth() {
        return this.hasWritingComment ? 720 : 520
      },
      tableColumns() {
        let columns = [
          {
            title: '小题',
            align: 'center',
            width: 60,
            key: 'questionCode',
          },
          {
            title: '评分',
            align: 'center',
            width: 60,
            key: 'studentScore',
          },
          {
            title: '识别作答',
            align: 'center',
            key: 'studentAnswer',
          },
          {
            title: '评分原因',
            align: 'center',
            key: 'scoreReason',
          },
        ]
        if (this.hasWritingComment) {
          columns.push({
            title: '作文评语',
            align: 'center',
            key: 'writingComment',
          })
        }
        return columns
      },
    },
    watch: {
      aiScoreResult() {
        this.parseAiScoreResult()
      },
      modelValue() {
        this.parseAiScoreResult()
      },
    },
    methods: {
      parseAiScoreResult() {
        if (!this.modelValue) {
          this.questions = []
          this.invalid = false
          return
        }

        if (!this.aiScoreResult) {
          this.questions = []
          this.invalid = false
          return
        }

        try {
          this.questions = JSON.parse(this.aiScoreResult)
          this.invalid = false
        } catch {
          try {
            let aiScoreResult = this.aiScoreResult.replace(/\n/g, '\\n')
            this.questions = JSON.parse(aiScoreResult)
            this.invalid = false
          } catch {
            this.questions = []
            this.invalid = true
          }
        }
        if (this.questions.length > 0) {
          setTimeout(() => {
            this.renderMath()
          }, 0)
        }
      },
      renderMath() {
        let el = this.$refs.resultTable?.$el
        if (el) {
          MathJaxUtil.clearRenderQueue()
          MathJaxUtil.render(el)
        }
      },
      handleVisibleChange(visibility) {
        this.$emit('update:modelValue', visibility)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .table-question {
    :deep(.ivu-table-cell) {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }

  .result-content {
    margin-top: 8px;
    padding: 8px;
    border-radius: 4px;
    background-color: #fff9e6;
  }
</style>
