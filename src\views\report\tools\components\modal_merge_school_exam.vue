<script>
  import { Checkbox } from 'view-ui-plus'

  import { apiGetExamsAdmin, apiGetCoachHomeworkList, apiMergeSchoolExams } from '@/api/emarking'

  import PageCache from '@/utils/page_cache'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    data() {
      return {
        examName: '',
        examSubjects: [],
        activeTab: 'exercise',
        mergeLoading: false,

        // filter
        term: '',
        semesterId: '',
        gradeId: '',

        tableColumns: [
          {
            title: '项目名称',
            key: 'examName',
            width: 360,
          },
          {
            title: '科目',
            align: 'center',
            render: (h, params) => {
              return h(
                'div',
                {
                  style: {
                    'white-space': 'normal',
                    'padding-top': '10px',
                  },
                },
                params.row.subjects.map(s =>
                  h(
                    Checkbox,
                    {
                      modelValue: Boolean(
                        this.examSubjects &&
                          this.examSubjects.length &&
                          this.examSubjects.some(es => es.examSubjectId === s.examSubjectId)
                      ),
                      style: {
                        'line-height': 1.5,
                        'margin-bottom': '10px',
                      },
                      onOnChange: () => this.handleCheckboxClick(s),
                    },
                    () => s.subjectName
                  )
                )
              )
            },
          },
        ],
        examCount: 0,
        examList: [],
        exerciseCount: 0,
        exerciseList: [],

        // pager
        currentPageExam: 1,
        pageSize: 10,
        currentPageExercise: 1,

        tableMaxHeight: window.innerHeight - 310,
      }
    },

    computed: {
      terms() {
        return this.$store.getters['emarking/terms']() || []
      },

      selectedTerm() {
        return this.terms.find(t => t.semesterId === this.semesterId && t.term === this.term)
      },

      termName() {
        return this.selectedTerm ? this.selectedTerm.termName : ''
      },

      grades() {
        return this.$store.getters['emarking/gradeSubjects']() || []
      },

      allGradeSubjects() {
        const Subjects = []

        if (this.grades.length) {
          this.grades.forEach(g => {
            ;(g.subjects || []).forEach(s => {
              if (!Subjects.some(sub => sub.id === s.id)) {
                Subjects.push(s)
              }
            })
          })
          Subjects.sort((a, b) => a.id - b.id)
        }

        return Subjects
      },

      filtedGradeExerciseList() {
        const TargetGrade = this.grades.find(g => g.id === this.gradeId)
        return TargetGrade && TargetGrade.name
          ? this.exerciseList.filter(item => item.gradeName === TargetGrade.name)
          : []
      },

      filtedExerciseList() {
        const StartIndex = (this.currentPageExercise - 1) * this.pageSize
        const EndIndex = StartIndex + this.pageSize
        return this.filtedGradeExerciseList.slice(StartIndex, EndIndex)
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleStatusChanged(isVisible) {
        if (isVisible) {
          this.initSemester()
          this.initGrade()
          this.resetModalData()
          this.fetchList()
        } else {
          this.closeModal()
        }
      },
      initSemester() {
        if (!this.selectedTerm && this.terms.length) {
          this.semesterId = this.terms[0].semesterId
          this.term = this.terms[0].term
        }
      },
      initGrade() {
        if (!this.gradeId && this.grades.length) {
          this.gradeId = this.grades[0].id
        }
      },
      resetModalData() {
        this.examName = ''
        this.examSubjects = []
        this.examCount = 0
        this.examList = []
        this.exerciseCount = 0
        this.exerciseList = []
        this.currentPageExam = 1
        this.currentPageExercise = 1
        this.pageSize = 10
      },

      /**
       * filter
       */
      changeTerm(termName) {
        const TargetTerm = this.terms.find(t => t.termName === termName)
        if (TargetTerm) {
          this.semesterId = TargetTerm.semesterId
          this.term = TargetTerm.term

          this.currentPageExam = 1
          this.currentPageExercise = 1
          this.examSubjects = []

          this.fetchList()
        }
      },
      changeGrade(gradeId) {
        this.gradeId = gradeId

        this.currentPageExam = 1
        this.currentPageExercise = 1
        this.examSubjects = []

        this.$TransparentSpin.show()
        this.fetchExamList().finally(() => this.$TransparentSpin.hide())
      },
      changePageExam(page = 1) {
        this.currentPageExam = page
        this.$TransparentSpin.show()
        this.fetchExamList().finally(() => this.$TransparentSpin.hide())
      },
      changePageExercise(page = 1) {
        this.currentPageExercise = page
      },

      /**
       * fetch list
       */
      fetchList() {
        this.$TransparentSpin.show()
        Promise.all([this.fetchExamList(), this.fetchExerciseList()]).finally(() => this.$TransparentSpin.hide())
      },
      fetchExamList() {
        this.examList = []
        if (!this.semesterId || !this.term || !this.gradeId) {
          return Promise.resolve()
        }

        return apiGetExamsAdmin({
          semesterId: this.semesterId,
          term: this.term,
          gradeId: this.gradeId,
          currentPage: this.currentPageExam,
          pageSize: this.pageSize,
        }).then(response => {
          this.examCount = (response && response.total) || 0
          this.examList = ((response && response.exams) || []).map(e => {
            e.subjects = e.subjects.map(es => ({
              examId: e.examId,
              examName: e.examName,
              subjectId: es.subjectId,
              subjectName: es.subjectName,
              examSubjectId: es.examSubjectId,
            }))
            return e
          })
        })
      },
      fetchExerciseList() {
        this.exerciseList = []
        if (!this.semesterId || !this.term) {
          return Promise.resolve()
        }

        return apiGetCoachHomeworkList({
          semesterId: this.semesterId,
          termId: this.term,
          currentPage: this.currentPageExercise,
          pageSize: 500,
        }).then(response => {
          this.exerciseCount = (response && response.total) || 0
          this.exerciseList = ((response && response.list) || []).map(item => {
            item.subjects = [
              {
                examId: item.examId,
                examName: item.examName,
                subjectId: (this.allGradeSubjects.find(ags => ags.name === item.subjectName) || { id: 0 }).id,
                subjectName: item.subjectName,
                examSubjectId: item.examSubjectId,
              },
            ]
            return item
          })
        })
      },

      /**
       * operation
       */
      handleCheckboxClick(subject) {
        if (this.examSubjects.some(es => es.examSubjectId === subject.examSubjectId)) {
          this.examSubjects = this.examSubjects.filter(es => es.examSubjectId !== subject.examSubjectId)
        } else {
          this.examSubjects = this.examSubjects.filter(es => es.subjectId !== subject.subjectId)
          this.examSubjects.push(subject)
          this.examSubjects.sort((a, b) => a.subjectId - b.subjectId)
        }
      },

      mergeProject() {
        if (!this.examName) {
          this.$Message.warning({
            duration: 4,
            content: '请输入合并后生成新项目的考试名称',
          })
          return
        }

        if (!this.examSubjects.some(es => es.examId !== this.examSubjects[0].examId)) {
          this.$Message.warning({
            duration: 4,
            content: '请选择多于一个考试项目的科目进行合并',
          })
          return
        }

        this.mergeLoading = true
        apiMergeSchoolExams({
          examName: this.examName,
          examSubjectIds: this.examSubjects.map(es => es.examSubjectId),
        })
          .then(() => {
            this.$Message.success({
              duration: 3,
              content: '合并成功',
            })

            PageCache.save('report-home', {
              semesterId: this.semesterId,
              term: this.term,
              gradeId: '',
              examType: '',
              examScope: '',
              beginTime: '',
              endTime: '',
              keyword: this.examName,
              pageSize: 10,
              currentPage: 1,
            })
            this.$router.replace({
              name: 'report',
            })
          })
          .finally(() => (this.mergeLoading = false))
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    title="合并校内考试项目"
    fullscreen
    @on-visible-change="handleModalVisibleStatusChanged"
  >
    <div class="modal-merge-school-exam-panel">
      <div class="merge-project-info">
        <Form :label-width="90" label-posotion="left">
          <FormItem label="考试名称">
            <Input
              v-model="examName"
              style="width: 510px"
              placeholder="请输入合并项目的考试名称"
              clearable
              maxlength="38"
            />
          </FormItem>

          <FormItem label="考试科目">
            <div v-if="examSubjects && examSubjects.length">
              <Tag
                v-for="es of examSubjects"
                :key="es.examSubjectId"
                :title="es.examName"
                closable
                @on-close="handleCheckboxClick(es)"
                >{{ es.subjectName }}</Tag
              >
            </div>
            <div v-else class="text-color-warning">未选择科目</div>
          </FormItem>
        </Form>
      </div>

      <div class="exam-subjects-selector">
        <div class="selector-title-bar">
          <div class="selector-title">选择考试科目</div>
        </div>

        <div class="selector-content">
          <div class="selector-filter-bar">
            <div class="selector-filter-bar-left">
              <div class="filter-item">
                <span class="filter-item-label">学期</span>
                <Select
                  :model-value="termName"
                  class="filter-item-body"
                  style="width: 200px"
                  transfer
                  @on-change="changeTerm"
                >
                  <Option v-for="t of terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
                </Select>
              </div>

              <div class="filter-item">
                <span class="filter-item-label">年级</span>
                <Select
                  :model-value="gradeId"
                  class="filter-item-body"
                  style="width: 100px"
                  transfer
                  @on-change="changeGrade"
                >
                  <Option v-for="g of grades" :key="g.id" :value="g.id">{{ g.name }}</Option>
                </Select>
              </div>
            </div>
            <Button type="primary" icon="md-refresh" @click="fetchList">刷新</Button>
          </div>
        </div>

        <Tabs v-model="activeTab" :animated="false">
          <TabPane label="数智教辅" name="exercise">
            <div class="exercise-list-panel">
              <Table
                :columns="tableColumns"
                :data="filtedExerciseList"
                :max-height="tableMaxHeight"
                style="margin-bottom: 10px"
              ></Table>
              <div class="pager">
                <div class="table-item-total-count">共 {{ filtedGradeExerciseList.length }} 条</div>
                <Page
                  :model-value="currentPageExercise"
                  :total="filtedGradeExerciseList.length"
                  :page-size="pageSize"
                  @on-change="changePageExercise"
                ></Page>
              </div>
            </div>
          </TabPane>
          <TabPane label="校内考试" name="exam">
            <div class="school-exam-list-panel">
              <Table
                :columns="tableColumns"
                :data="examList"
                :max-height="tableMaxHeight"
                style="margin-bottom: 10px"
              ></Table>
              <div class="pager">
                <div class="table-item-total-count">共 {{ examCount }} 条</div>
                <Page
                  :model-value="currentPageExam"
                  :total="examCount"
                  :page-size="pageSize"
                  class="pager"
                  @on-change="changePageExam"
                ></Page>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>

    <template #footer>
      <Button type="text" @click="closeModal">取消</Button>
      <Button type="primary" :loading="mergeLoading" @click="mergeProject">开始合并</Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-merge-school-exam-panel {
    @include flex(row, flex-start, stretch);
    height: 100%;

    .merge-project-info {
      flex-grow: 0;
      flex-shrink: 0;
      width: 600px;

      .text-color-warning {
        color: $color-warning;
        user-select: none;
      }
    }

    .exam-subjects-selector {
      flex-grow: 1;
      flex-shrink: 1;
      height: 100%;
      margin-left: 20px;
      padding-left: 20px;
      border-left: 1px dashed gray;

      .selector-title-bar {
        .selector-title {
          color: $color-primary-dark;
          font-size: $font-size-medium-x;
        }
      }

      .selector-content {
        margin-top: 10px;

        .selector-filter-bar {
          @include flex(row, space-between, center);

          .selector-filter-bar-left {
            @include flex(row, flex-start, center);
            flex-grow: 1;
            flex-shrink: 1;
            margin-bottom: 6px;

            .filter-item {
              @include flex(row, flex-start, center);

              &:not(:last-child) {
                margin-right: 20px;
              }

              .filter-item-label {
                margin-right: 5px;
              }
            }
          }
        }
      }

      .pager {
        @include flex(row, flex-end, center);

        .table-item-total-count {
          margin-right: 20px;
        }
      }
    }
  }
</style>
