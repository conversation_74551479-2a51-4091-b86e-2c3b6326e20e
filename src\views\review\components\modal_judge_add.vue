<template>
  <Modal
    class="modal-judge-add"
    :model-value="modelValue"
    width="900"
    title="添加评委"
    :mask-closable="false"
    :closable="false"
    :styles="{ top: '50px' }"
  >
    <div class="container-content">
      <div class="category-bar">
        <span class="label">评审类别：</span>
        <RadioGroup v-model="currentCategory" type="button" button-style="solid">
          <Radio v-for="c of categories" :key="c.id" :label="c.id">{{ c.name }}</Radio>
        </RadioGroup>
      </div>
      <div class="section-search">
        <div class="search-left">
          <Select
            v-model="selectedSchoolId"
            class="selector-school"
            placeholder="选择学校"
            filterable
            style="width: 300px"
            @on-change="onSchoolChange"
          >
            <Option v-for="s in schoolListForSelect" :key="s.schoolId" :value="s.schoolId">{{ s.schoolName }}</Option>
          </Select>
        </div>
        <!-- <div class="search-right">
          <Select
            v-model="selectedJudge"
            :max-tag-count="1"
            filterable
            multiple
            placeholder="请选择添加的评委"
            style="width: 300px"
            @on-change="onJudgeChange"
          >
            <Option v-for="item in teacherList" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </div> -->
      </div>
      <div class="section-list">
        <p v-if="teacherList.length === 0" class="empty">当前查询条件下无老师</p>
        <div v-else>
          <Checkbox
            class="check-teacher-all"
            :indeterminate="isCheckboxAllIndeterminate"
            :model-value="isCheckboxAllChecked"
            @click.prevent="toggleCheckAllTeachers"
            >全选</Checkbox
          >
          <div class="check-teacher-group">
            <Checkbox
              v-for="t in teacherList"
              :key="t.id"
              class="check-teacher-item"
              :model-value="t.checked"
              @on-change="changeCheckAddTeacher(t, $event)"
              >{{ t.name }}</Checkbox
            >
          </div>
        </div>
      </div>
      <div class="section-group-list">
        <div v-for="g in groupSelectedTeachers" :key="g.schoolId" class="group-list-item">
          <div class="school-name">{{ g.schoolName }}</div>
          <div class="teacher-list">
            <div v-for="t in g.teachers" :key="t.id" class="teacher-item">
              <span class="name">{{ t.realName }}</span>
              <Icon class="icon-remove" type="md-close-circle" @click="removeCheckedTeacher(t.id)"></Icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="container-footer">
        <Button @click="handleModalCancel">取消</Button>
        <Button type="primary" @click="handleAdd">添加</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiGetTeacherList, apiGetTeachers } from '@/api/user'
  import { apiAddJudges } from '@/api/review/judge'

  import { mapGetters } from 'vuex'
  import { groupArray } from '@/utils/array'

  export default {
    props: {
      modelValue: Boolean,
      activeCategory: String,
    },
    emits: ['update:modelValue', 'on-refresh'],
    data() {
      return {
        selectedSchoolId: [],
        selectedJudge: [],
        teacherList: [],
        addTeachers: [],
        currentCategory: '',
      }
    },
    computed: {
      ...mapGetters('review', ['currentActivity', 'flattenSchoolList', 'categories']),
      schoolListForSelect() {
        return this.flattenSchoolList.map(item => ({
          ...item,
        }))
      },
      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },
      groupSelectedTeachers() {
        let result = groupArray(this.addTeachers, t => t.schoolId).map(item => {
          let first = (item.group && item.group.length && item.group[0]) || {}

          return {
            schoolId: first.schoolId,
            schoolName: first.schoolName,
            teachers: item.group,
          }
        })

        return result
      },
      // 全部选中
      isCheckboxAllChecked() {
        return this.teacherList.length > 0 && this.teacherList.every(t => t.checked)
      },
      // 部分选中
      isCheckboxAllIndeterminate() {
        return this.teacherList.some(t => t.checked) && !this.isCheckboxAllChecked
      },
    },
    watch: {
      modelValue(val) {
        if (val) {
          this.teacherList = []
          this.addTeachers = []
          this.selectedSchoolId = ''
          this.selectedJudge = ''

          if (this.activeCategory && this.activeCategory !== '0') {
            this.currentCategory = this.activeCategory
          }
        }
      },
      categories: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            this.currentCategory = val[0].id
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      handleModalCancel() {
        this.$emit('update:modelValue', false)
      },
      handleAdd() {
        if (!this.addTeachers || !this.addTeachers.length) {
          this.$Message.info('请选择要添加的评委')
          return
        }

        apiAddJudges({
          activityId: this.currentActivity.id,
          categoryId: this.currentCategory,
          teacherIds: this.addTeachers.map(t => t.id),
        }).then(() => {
          this.handleModalCancel()
          this.$emit('on-refresh')
        })
      },
      onSchoolChange() {
        this.getTeachers()
      },
      getTeachers() {
        if (!this.selectedSchoolId) return
        let params = {
          currentPage: 1,
          pageSize: 1000,
          current: 1,
          size: 1000,
          schoolId: this.selectedSchoolId,
          keyword: '',
        }

        const theSchool = this.schoolListForSelect.find(s => s.schoolId === this.selectedSchoolId)

        if (this.isBureauInstitution) {
          apiGetTeacherList(params).then(response => {
            this.teacherList = (response?.teachers || []).map(u => ({
              ...u,
              id: u.userId,
              name: `${u.realName} ( ${u.mobile || '-'} )`,
              schoolId: theSchool && theSchool.schoolId,
              schoolName: theSchool && theSchool.schoolName,
            }))
          })
        } else {
          return apiGetTeachers(params).then(response => {
            this.teacherList = (response?.teachers || []).map(u => ({
              ...u,
              id: u.userId,
              name: `${u.realName} ( ${u.mobile || '-'} )`,
              schoolId: theSchool && theSchool.schoolId,
              schoolName: theSchool && theSchool.schoolName,
            }))
          })
        }
      },
      // 全选或取消全选
      toggleCheckAllTeachers() {
        if (this.isCheckboxAllChecked) {
          this.teacherList.forEach(t => (t.checked = false))
        } else {
          this.teacherList.forEach(t => (t.checked = true))
        }

        this.teacherList.forEach(t => {
          let tIndex = this.addTeachers.findIndex(x => x.id === t.id)
          if (t.checked && tIndex < 0) {
            this.addTeachers.push(t)
          } else if (!t.checked && tIndex >= 0) {
            this.addTeachers.splice(tIndex, 1)
          }
        })
      },
      // 勾选教师或取消勾选
      changeCheckAddTeacher(teacher, checked) {
        teacher.checked = checked

        let tIndex = this.addTeachers.findIndex(x => x.id === teacher.id)
        if (checked && tIndex < 0) {
          this.addTeachers.push(teacher)
        } else if (!checked && tIndex >= 0) {
          this.addTeachers.splice(tIndex, 1)
        }
      },
      removeCheckedTeacher(teacherId) {
        let teacherIndex = this.addTeachers.findIndex(x => x.id === teacherId)
        if (teacherIndex >= 0) {
          this.addTeachers.splice(teacherIndex, 1)
        }
        let teacherInSelectedSchool = this.teacherList.find(t => t.id === teacherId)
        if (teacherInSelectedSchool) {
          teacherInSelectedSchool.checked = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-judge-add {
    .container-footer {
      @include flex(row, flex-end, center);
    }

    .category-bar {
      margin-bottom: 15px;
    }

    .section-search {
      @include flex(row, flex-start, center);
      margin-bottom: 20px;

      .search-left {
        margin-right: 20px;
      }
    }

    .section-list {
      .empty {
        line-height: 100px;
        text-align: center;
      }

      .check-teacher-all {
        margin-bottom: 10px;
      }

      .check-teacher-group {
        margin-bottom: 20px;

        .check-teacher-item {
          min-width: 5em;
        }
      }
    }

    .group-list-item {
      margin-bottom: 15px;

      .school-name {
        margin-bottom: 4px;
        font-weight: 600;
      }

      .teacher-list {
        line-height: 24px;
      }

      .teacher-item {
        display: inline-block;
        min-width: 5em;
        margin-right: 10px;

        .icon-remove:hover {
          color: $color-warning;
          cursor: pointer;
        }
      }
    }
  }
</style>
