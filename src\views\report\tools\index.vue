<script>
  import ModalMergeSchoolExam from './components/modal_merge_school_exam.vue'
  import ModalExportMultiTemplateSchoolCompare from './components/modal_export_multi_template_school_compare.vue'

  export default {
    components: {
      'modal-merge-school-exam': ModalMergeSchoolExam,
      'modal-export-multi-template-school-compare': ModalExportMultiTemplateSchoolCompare,
    },

    data() {
      return {
        isModalMergeSchoolExamShowed: false,
        isModalExportMultiTemplateSchoolCompareShowed: false,
      }
    },

    computed: {
      isSystemUser() {
        return this.$store.state.user.isSystem || false
      },
      isBureauInstitution() {
        return this.$store.getters['user/isBureauInstitution']
      },
    },

    methods: {
      routeBackToPageList() {
        this.$router.replace({
          name: 'report-home',
        })
      },

      goToPageMergeExam() {
        this.$router.push({
          name: 'report-tool-merge-exam',
        })
      },
    },
  }
</script>

<template>
  <div class="report-tools">
    <div class="header">
      <TextButton type="primary" icon="ios-arrow-back" title="分析报告列表" @click="routeBackToPageList"></TextButton>
      <span>报表工具</span>
    </div>

    <div class="tools-panel">
      <div class="tool-item" @click="isModalMergeSchoolExamShowed = true">
        <span class="tool-item-name">合并校内考试项目</span>
        <Icon type="ios-arrow-forward" />
      </div>
      <div v-show="isSystemUser" class="tool-item" @click="isModalExportMultiTemplateSchoolCompareShowed = true">
        <span class="tool-item-name">多次考试【学校对比】合并导出</span>
        <Icon type="ios-arrow-forward" />
      </div>
      <div v-show="isSystemUser && isBureauInstitution" class="tool-item" @click="goToPageMergeExam">
        <span class="tool-item-name">合并考试项目</span>
        <Icon type="ios-arrow-forward" />
      </div>
    </div>

    <modal-merge-school-exam v-model="isModalMergeSchoolExamShowed"></modal-merge-school-exam>

    <modal-export-multi-template-school-compare
      v-model="isModalExportMultiTemplateSchoolCompareShowed"
    ></modal-export-multi-template-school-compare>
  </div>
</template>

<style lang="scss" scoped>
  .report-tools {
    min-height: 500px;
    padding: 20px;
    background-color: white;

    .header {
      font-size: $font-size-large;
    }

    .tools-panel {
      display: grid;
      grid-auto-rows: 110px;
      grid-template-columns: 1fr 1fr 1fr;
      min-height: 500px;
      margin-top: 20px;
      column-gap: 20px;

      .tool-item {
        @include flex(row, space-between, center);
        height: 80px;
        padding: 0 1em;
        border-radius: 10px;
        font-size: $font-size-medium-x;
        background-color: #eeeeee;
        user-select: none;

        &:hover {
          border: 1px solid $color-primary;
          transform: scale(1.01);
          cursor: pointer;
        }
      }
    }
  }
</style>
