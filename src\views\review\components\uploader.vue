<script>
  export default {
    props: {
      accepts: {
        type: Array,
        default: () => [],
      },
      maxSize: {
        type: Number, // MB
        default: 5,
      },
      uploadedFiles: {
        type: Array,
        default: () => [],
      },
      maxFileCount: {
        type: Number,
        default: 1,
      },
      isModeCheck: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['on-selected', 'delete-file', 'reset-files', 'rename-file'],

    data() {
      return {
        // 是否预览图片
        showImagePreview: false,
        // 预览图片链接
        previewImageUrlList: [],
        // 预览图片初始序号
        previewImageInitialIndex: 0,
        // 是否预览pdf
        showPdfPreview: false,
        // 预览pdf链接
        previewPdfUrl: '',
        // 预览pdf文件名
        previewPdfName: '',

        fileBeingRenamed: {
          index: -1,
          oldName: '',
          name: '',
          calcName: '',
          nameSuffix: '',
        },
      }
    },

    computed: {
      multiple() {
        return this.maxFileCount > this.uploadedFiles.length
      },
      acceptString() {
        return this.accepts.join(',')
      },
    },

    methods: {
      selectFile() {
        this.$refs.originHTMLInput.click()
      },

      grabFile() {
        let files = this.$refs.originHTMLInput.files
        if (!this.multiple) {
          files = [files[0]]
        } else {
          files = Array.from(files).slice(0, this.maxFileCount - this.uploadedFiles.length)
        }

        for (let file of files) {
          if (!this.checkFileType(file)) {
            this.$Message.error({
              content: `请选择以下格式的文件：${this.accepts.join('，')}`,
              duration: 5,
            })
            return
          }

          if (!this.checkFileSize(file)) {
            this.$Message.error({
              content: `请选择不大于${
                this.maxSize >= 1 ? '' + this.maxSize + 'MB' : '' + this.maxSize * 1000 + 'KB'
              }的文件`,
              duration: 5,
            })
            return
          }
        }
        this.$refs.originHTMLInput.value = null

        this.$emit('on-selected', files)
      },

      checkFileType(file) {
        let dotIndex = file.name.lastIndexOf('.')
        if (dotIndex <= 0) {
          return false
        }

        let extension = file.name.substring(dotIndex)
        return this.accepts.includes(extension)
      },

      checkFileSize(file) {
        return this.maxSize * 1024 * 1024 >= file.size
      },

      handleDeleteFile(fileName) {
        this.$emit('delete-file', fileName)
      },

      resetUploadFiles() {
        this.$emit('reset-files')
      },

      viewFile(file) {
        let isImage = x => ['.jpg', 'jpeg', '.png', '.gif'].some(extension => x.name.endsWith(extension))
        if (isImage(file)) {
          let images = this.uploadedFiles.filter(isImage)
          let idx = images.indexOf(file)
          if (idx < 0) {
            return
          }
          this.previewImageUrlList = images.map(x => {
            if (x.isUploaded) {
              return x.path
            } else {
              return URL.createObjectURL(x.file)
            }
          })
          this.previewImageInitialIndex = idx
          this.showImagePreview = true
        } else if (file.name.endsWith('.pdf')) {
          this.previewPdfName = file.name
          this.previewPdfUrl = file.path || URL.createObjectURL(file.file)
          this.showPdfPreview = true
        }
      },
      closePreview() {
        this.showImagePreview = false
        this.previewImageInitialIndex = 0
        this.previewImageUrlList.forEach(url => {
          if (url.startsWith('blob')) {
            URL.revokeObjectURL(url)
          }
        })
        this.previewImageUrlList = []
      },

      setFileBeingRenamed(index = -1, name = '', calcName = '', nameSuffix = '') {
        this.fileBeingRenamed = {
          index,
          name,
          calcName: calcName || name,
          oldName: name,
          nameSuffix,
        }
      },
      handleChangeFileNameClick(index = -1, name = '') {
        const NameLastDotIndex = name.lastIndexOf('.')
        const NameWithoutFileType = NameLastDotIndex === -1 ? name : name.substring(0, NameLastDotIndex)
        this.setFileBeingRenamed(
          index,
          NameWithoutFileType,
          NameWithoutFileType.length > 40 ? NameWithoutFileType.slice(0, 40) : NameWithoutFileType,
          NameLastDotIndex === -1 ? '' : name.substring(NameLastDotIndex)
        )
      },
      changeFileName() {
        const TrimedFileName = (this.fileBeingRenamed?.name || '').trim()
        if (TrimedFileName) {
          this.$emit('rename-file', {
            oldName: this.fileBeingRenamed.oldName + this.fileBeingRenamed.nameSuffix,
            newName: this.fileBeingRenamed.name + this.fileBeingRenamed.nameSuffix,
          })
          this.setFileBeingRenamed()
        } else {
          this.$Message.warning({
            duration: 4,
            content: '文件名不可为空',
          })
        }
      },
    },
  }
</script>

<template>
  <div class="hand-make-uploader">
    <div class="uploaded-files">
      <div
        v-for="(f, fdx) of uploadedFiles"
        :key="f.name + fdx"
        :class="[f.isUploaded ? 'uploaded-file-item' : 'unupload-file-item']"
        class="file-item"
      >
        <template v-if="fileBeingRenamed.index !== fdx">
          <div class="normal-file-name" :title="f.name" @click="viewFile(f)">
            {{ f.name.length > 40 ? f.name.slice(0, 40) + '...' : f.name }}
          </div>
          <Icon type="md-search" class="file-item-icon" title="查看" @click="viewFile(f)" />
          <template v-if="!isModeCheck">
            <Icon
              type="md-create"
              class="file-item-icon"
              title="修改文件名"
              @click="handleChangeFileNameClick(fdx, f.name)"
            />
            <Icon type="md-close" class="file-item-icon" title="删除" @click="handleDeleteFile(f.name)" />
          </template>
        </template>
        <template v-else>
          <Input
            v-model="fileBeingRenamed.name"
            :border="false"
            :style="{ width: 'calc(' + fileBeingRenamed.calcName.length * 1.3 + 'ch + 20px)' }"
            size="small"
            class="file-name-input"
            type="text"
            @on-enter="changeFileName"
          />
          <Icon type="md-checkmark" class="file-item-icon" @click="changeFileName" />
          <Icon type="md-close" class="file-item-icon" title="取消" @click="setFileBeingRenamed" />
        </template>
      </div>
    </div>
    <div class="file-graber">
      <input
        v-show="false"
        ref="originHTMLInput"
        type="file"
        :accept="acceptString"
        :multiple="multiple"
        @change="grabFile"
      />
      <template v-if="!isModeCheck">
        <div v-if="uploadedFiles.length < maxFileCount" class="upload-btn">
          <Button type="primary" size="small" icon="ios-cloud-upload-outline" @click="selectFile">选择文件</Button>
          <span class="upload-file-condition"
            >（最多上传 {{ maxFileCount }} 个文件类型为 {{ acceptString }} 的文件，单个文件大小不超过
            {{ Math.floor(maxSize) }} MB）</span
          >
        </div>
        <Button v-else type="error" size="small" icon="md-trash" @click="resetUploadFiles">清空文件</Button>
      </template>
    </div>

    <ImagePreview
      v-model="showImagePreview"
      :preview-list="previewImageUrlList"
      :initial-index="previewImageInitialIndex"
      @on-close="closePreview"
    ></ImagePreview>

    <Modal v-model="showPdfPreview" :width="1000" :title="previewPdfName" footer-hide :styles="{ top: 0 }">
      <object
        v-if="showPdfPreview"
        class="pdf-viewer-object"
        :data="previewPdfUrl"
        type="application/pdf"
        width="100%"
        height="600px"
      >
        <p>您的浏览器不支持查看PDF文件，请<a :href="previewPdfUrl">下载PDF文件</a>查看。</p>
      </object>
    </Modal>
  </div>
</template>

<style lang="scss" scoped>
  .hand-make-uploader {
    .uploaded-files {
      .file-item {
        @include flex(row, flex-start, center);
        width: fit-content;
        margin: 3px 4px 3px 0;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: $font-size-medium;
        line-height: $font-size-medium;

        .normal-file-name {
          margin-right: 2px;

          &:hover {
            cursor: pointer;
          }
        }

        .file-name-modifier {
          margin-right: 2px;
        }

        .file-item-icon {
          margin-left: 4px;

          &:hover {
            cursor: pointer;
          }
        }

        .file-name-input {
          :deep(.ivu-input-small) {
            height: 18px;
          }
        }
      }

      .uploaded-file-item {
        border: 1px solid $color-primary;
        color: white;
        background-color: $color-primary;
      }

      .unupload-file-item {
        border: 1px solid #e8eaec;
        color: $color-content;
        background-color: #f7f7f7;
      }
    }

    .file-graber {
      .upload-btn {
        @include flex(row, flex-start, center);
        height: 35px;
        line-height: 35px;

        .upload-file-condition {
          color: $color-second-title;
          font-size: $font-size-small;
        }
      }
    }
  }

  .pdf-viewer-object {
    height: calc(100vh - 90px);
  }
</style>
