<template>
  <Modal :model-value="modelValue" :width="600" :title="title" @on-visible-change="handleVisibleChange">
    <Form :label-width="80">
      <FormItem label="扫描员">
        <Select
          v-model="teacherId"
          class="select-teacher"
          filterable
          clearable
          :remote-method="deboucedSearchTeacher"
          :loading="searchingTeacher"
          :disabled="isModeEdit"
          placeholder="教师姓名 / 手机号码"
        >
          <Option v-for="t in teachers" :key="t.userId" :value="t.userId" :label="t.label"></Option>
        </Select>
      </FormItem>
      <FormItem label="扫描范围">
        <div v-for="config in configItems" :key="config.id" class="config-item">
          <Select
            class="select-grade"
            :model-value="config.gradeId"
            placeholder="选择年级"
            transfer
            @on-change="changeGradeId(config, $event)"
          >
            <Option v-for="g in grades" :key="g.gradeId" :value="g.gradeId">{{ g.gradeName }}</Option>
          </Select>
          <Select
            class="select-subject"
            :model-value="config.subjectIds"
            multiple
            clearable
            transfer
            placeholder="选择科目"
            @on-change="changeSubjectIds(config, $event)"
          >
            <Option v-for="s of getGradeSubjects(config.gradeId)" :key="s.subjectId" :value="s.subjectId">{{
              s.subjectName
            }}</Option>
          </Select>
          <div class="btn-group">
            <TextButton type="error" icon="md-close" @click="removeConfig(config)"></TextButton>
            <TextButton v-if="config.id == maxConfigId" type="success" icon="md-add" @click="addConfig"></TextButton>
          </div>
        </div>
      </FormItem>
    </Form>
    <template #footer>
      <div class="footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOK">确定</Button>
      </div>
    </template>
  </Modal>
</template>

<script>
  import { apiGetTeachers } from '@/api/user'

  import { deepCopy } from '@/utils/object'
  import { debounce } from '@/utils/function'
  import { duplicateBy } from '@/utils/array'
  import ScannerRangeTypeEnum from '@/enum/school/scanner_range_type'

  const GradeAll = {
    id: -1,
    name: '全部年级',
  }
  const SubjectAll = {
    subjectId: 0,
    subjectName: '全部科目',
  }

  export default {
    props: {
      modelValue: Boolean,
      teacher: Object,
      mode: String,
    },
    emits: ['update:modelValue', 'ok'],
    data() {
      return {
        // 选中教师
        teacherId: '',

        // 扫描范围配置，每个年级一条
        configItems: [],

        // 教师查询
        searchingTeacher: false,
        teachers: [],
        deboucedSearchTeacher: () => {},
      }
    },
    computed: {
      isModeAdd() {
        return this.mode == 'add'
      },
      isModeEdit() {
        return this.mode == 'edit'
      },
      title() {
        if (this.isModeAdd) {
          return '添加扫描员'
        } else {
          return '修改扫描范围'
        }
      },
      // 学校年级班级
      schoolGradeClassSubjects() {
        return this.$store.getters['school/schoolGradeClassSubjects']
      },
      // 年级
      grades() {
        let list = this.schoolGradeClassSubjects.map(g => {
          return {
            gradeId: g.gradeId,
            gradeName: g.gradeName,
            subjects: [SubjectAll].concat(g.subjects),
          }
        })
        list.unshift({
          gradeId: GradeAll.id,
          gradeName: GradeAll.name,
          subjects: [SubjectAll].concat(this.$store.getters['school/schoolAllSubjects'] || []),
        })
        return list
      },
      maxConfigId() {
        if (this.configItems.length == 0) {
          return 0
        }
        return Math.max(...this.configItems.map(item => item.id))
      },
    },
    created() {
      this.deboucedSearchTeacher = debounce(this.searchTeacher, 500)
    },
    methods: {
      async init() {
        this.teacherId = ''
        this.configItems = []
        this.searchingTeacher = false
        this.teachers = []
        if (this.isModeAdd) {
          this.addConfig()
        } else {
          // show teacher info
          await this.searchTeacher(this.teacher.teacherName)
          this.teacherId = this.teacher.teacherId

          // transer config
          let configItems = []
          let teacherGrades = this.teacher.teacherGrades.find(tg => !tg.gradeId)
          if (teacherGrades && teacherGrades.config.some(tgc => !tgc.subjectId)) {
            configItems.push({
              gradeId: GradeAll.id,
              subjectIds: [SubjectAll.subjectId],
            })
          } else {
            teacherGrades = this.teacher.teacherGrades
            teacherGrades.sort((a, b) => a.gradeId - b.gradeId)
            teacherGrades.forEach(tg => {
              configItems.push({
                gradeId: tg.gradeId || GradeAll.id,
                subjectIds: tg.config.some(tgc => !tgc.subjectId)
                  ? [SubjectAll.subjectId]
                  : tg.config.map(tgc => tgc.subjectId),
              })
            })
          }

          if (configItems.length) {
            configItems.forEach((item, idx) => (item.id = idx + 1))
            this.configItems = configItems
          } else {
            this.addConfig()
          }
        }
      },

      /**
       * 选择教师
       */
      searchTeacher(keyword) {
        if (this.teachers.some(t => t.label == keyword)) {
          return
        }
        this.searchingTeacher = true
        return apiGetTeachers({
          keyword,
          pageSize: 100,
          currentPage: 1,
        })
          .then(data => {
            data.teachers.forEach(t => {
              t.label = t.realName + (t.mobile ? '（' + t.mobile + '）' : '')
            })
            this.teachers = data.teachers
          })
          .finally(() => {
            this.searchingTeacher = false
          })
      },

      /**
       * 选择年级班级
       */
      changeGradeId(config, gradeId) {
        config.gradeId = gradeId
      },
      getGradeSubjects(gradeId) {
        const Grade = this.grades.find(grade => grade.gradeId === gradeId)
        return Grade ? Grade.subjects : []
      },
      changeSubjectIds(config, subjectIds) {
        if (subjectIds.includes(SubjectAll.subjectId)) {
          if (config.subjectIds.includes(SubjectAll.subjectId) && subjectIds.length > 1) {
            config.subjectIds = subjectIds.filter(id => id !== SubjectAll.subjectId)
          } else {
            config.subjectIds = [SubjectAll.subjectId]
          }
        } else {
          if (subjectIds.length === this.getGradeSubjects(config.gradeId).length - 1) {
            config.subjectIds = [SubjectAll.subjectId]
          } else {
            config.subjectIds = subjectIds.sort((a, b) => a.subjectId - b.subjectId)
          }
        }
      },

      /**
       * 增删配置
       */
      removeConfig(config) {
        let index = this.configItems.findIndex(x => x.id == config.id)
        if (index >= 0) {
          this.configItems.splice(index, 1)
        }
        if (this.configItems.length == 0) {
          this.addConfig()
        }
      },
      addConfig() {
        let config = {
          id: this.maxConfigId + 1,
          gradeId: null,
          subjectIds: [],
        }
        this.configItems.push(config)
      },

      /**
       * 确认、取消
       */
      handleVisibleChange(visible) {
        if (visible) {
          this.init()
        } else {
          this.handleCancel()
        }
      },
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleOK() {
        let message = this.check()
        if (message) {
          this.$Message.info({
            content: message,
            duration: 5,
            closable: true,
          })
          return
        }

        // 配置拉平
        let configs = []
        this.configItems.forEach(item => {
          const IsAllGradeItem = item.gradeId === GradeAll.id
          let config = {
            teacherId: this.teacherId,
            rangeType: IsAllGradeItem ? ScannerRangeTypeEnum.School.id : ScannerRangeTypeEnum.Grade.id,
            gradeId: IsAllGradeItem ? '' : item.gradeId,
            classId: '',
            subjectId: '',
          }

          if (item.subjectIds.includes(SubjectAll.subjectId)) {
            configs.push(config)
          } else {
            item.subjectIds.forEach(subjectId => {
              let subjectConfig = deepCopy(config)
              subjectConfig.subjectId = subjectId
              configs.push(subjectConfig)
            })
          }
        })
        this.$emit('ok', configs)
      },
      check() {
        if (!this.teacherId) {
          return '请选择教师'
        }
        if (this.configItems.length == 0) {
          return '请设置扫描范围'
        }
        if (this.configItems.some(item => !item.gradeId)) {
          return '请选择年级'
        }
        let duplicates = duplicateBy(this.configItems, item => item.gradeId)
        if (duplicates.length > 0) {
          return '年级重复'
        }
        if (this.configItems.some(item => !item.subjectIds || item.subjectIds.length === 0)) {
          return '请选择科目'
        }
        return ''
      },
    },
  }
</script>

<style lang="scss" scoped>
  .select-teacher {
    width: 400px;
  }

  .config-item {
    @include flex(row, flex-start, flex-start);

    &:not(:first-child) {
      margin-top: 10px;
    }

    .select-grade {
      width: 120px;
    }

    .select-subject {
      width: 260px;
      margin-right: 20px;
      margin-left: 20px;
    }

    .btn-group {
      @include flex(row, flex-start, center);
      height: 32px;
    }
  }
</style>
