<template>
  <div class="pane-monitor-stats">
    <div class="panel-header">
      <RadioGroup
        v-if="viewRadios.length > 1"
        class="radio"
        :model-value="view"
        type="button"
        button-style="solid"
        @on-change="handleViewChange"
      >
        <Radio v-for="v in viewRadios" :key="v.id" :label="v.id">{{ v.radio }}</Radio>
      </RadioGroup>
      <Input
        class="search"
        :model-value="keyword"
        :placeholder="searchPlaceholder"
        clearable
        search
        @on-change="handleKeywordChange"
      />
      <TextButton class="btn-refresh" type="primary" icon="md-refresh" @click="refresh">刷新</TextButton>
    </div>
    <div class="panel-body">
      <Table
        :max-height="tableMaxHeight"
        :columns="tableColumns"
        :data="tableData"
        :row-class-name="rowClassName"
        @on-filter-change="handleFilterChange"
        @on-sort-change="handleSortChange"
        @on-row-click="handleRowClick"
      ></Table>
    </div>
    <Page
      class="panel-footer"
      :model-value="currentPage"
      :page-size="pageSize"
      :total="fitleredData.length"
      show-total
      show-sizer
      :page-size-opts="[10, 20, 50, 100]"
      @on-change="handlePageChange"
      @on-page-size-change="handlePageSizeChange"
    ></Page>
  </div>
</template>

<script>
  import { debounce } from '@/utils/function'

  // 查看统计类别
  const ViewEnum = {
    Room: {
      id: 'roomNo',
      name: '考场',
      radio: '按考场查看',
    },
    School: {
      id: 'schoolId',
      name: '学校',
      radio: '按学校查看',
    },
    Station: {
      id: 'scanStationId',
      name: '扫描点',
      radio: '按扫描点查看',
    },
  }

  export default {
    props: {
      stats: Object,
      studentParams: Object,
      tableMaxHeight: Number,
    },
    emits: ['change-active-row', 'refresh'],
    data() {
      return {
        // 当前查看类别
        view: ViewEnum.Room.id,
        // 搜索关键字
        keyword: '',
        // 筛选，限制只能筛选一列
        filter: {
          key: '',
          value: [],
        },
        // 排序
        sortType: {
          key: '',
          order: 'normal',
        },
        // 分页
        currentPage: 1,
        pageSize: 10,
      }
    },
    computed: {
      isSystem() {
        return this.$store.getters['user/isSystem']
      },
      isInSchoolExam() {
        return this.$store.getters['scan/isInSchoolExam']
      },

      /**
       * 头部表单
       */
      viewRadios() {
        let list = [ViewEnum.Room]
        if (!this.isInSchoolExam) {
          list.push(ViewEnum.School)
          if (this.stations.length > 0) {
            list.push(ViewEnum.Station)
          }
        }
        return list.map(item => ({
          id: item.id,
          radio: item.radio,
        }))
      },
      searchPlaceholder() {
        let key = Object.keys(ViewEnum).find(key => ViewEnum[key].id == this.view)
        return ViewEnum[key].name
      },

      /**
       * 各类别数据
       */
      total() {
        if (this.stats && this.stats.total) {
          return this.stats.total
        }
        return null
      },
      rooms() {
        if (!this.stats) {
          return []
        }
        return this.stats.rooms
          .map(room => {
            let schools = room.schools.map(sch => ({
              schoolId: sch.schoolId,
              schoolName: sch.schoolName,
            }))
            let schoolName = schools.map(sch => sch.schoolName || '').join('、')
            let stations = []
            room.schools.forEach(sch => {
              if (sch.scanStationId && stations.every(x => x.scanStationId != sch.scanStationId)) {
                stations.push({
                  scanStationId: sch.scanStationId,
                  scanStationName: sch.scanStationName,
                })
              }
            })
            let scanStationName = stations.map(station => station.scanStationName || '').join('、')
            return {
              roomNo: room.roomNo,
              schools,
              schoolName,
              stations,
              scanStationName,
              ...room.total,
            }
          })
          .sort((a, b) => a.roomNo - b.roomNo)
      },
      schools() {
        if (!this.stats) {
          return []
        }
        return this.stats.schools.map(sch => {
          return {
            schoolId: sch.schoolId,
            schoolName: sch.schoolName,
            scanStationId: sch.scanStationId,
            scanStationName: sch.scanStationName,
            ...sch.total,
          }
        })
      },
      stations() {
        if (!this.stats) {
          return []
        }
        // 无扫描点
        if (this.stats.scanStations.length == 1 && this.stats.scanStations[0].scanStationId == 0) {
          return []
        }
        return this.stats.scanStations.map(s => {
          return {
            scanStationId: s.scanStationId,
            scanStationName: s.scanStationName,
            ...s.total,
          }
        })
      },

      /**
       * 表格
       */
      tableColumns() {
        // 数据列
        let columns = [
          {
            title: '考生',
            key: 'total',
            align: 'center',
            sortable: 'custom',
            width: 68,
            render: (h, params) =>
              h(
                'div',
                {
                  class: 'table-clickable-item',
                  onClick: e => {
                    this.handleCellClick(params.row, 'total')
                    e.stopPropagation()
                  },
                },
                params.row.total
              ),
          },
          {
            title: '已扫',
            key: 'scanned',
            align: 'center',
            sortable: 'custom',
            width: 68,
            render: (h, params) =>
              h(
                'div',
                {
                  class: 'table-clickable-item',
                  onClick: e => {
                    this.handleCellClick(params.row, 'scanned')
                    e.stopPropagation()
                  },
                },
                params.row.scanned
              ),
          },
          {
            title: '扫描缺考',
            key: 'scanAbsent',
            align: 'center',
            sortable: 'custom',
            width: 68,
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                    verticalAlign: 'middle',
                  },
                },
                '扫描\n缺考'
              ),
            render: (h, params) =>
              h(
                'div',
                {
                  class: 'table-clickable-item',
                  onClick: e => {
                    this.handleCellClick(params.row, 'scanAbsent')
                    e.stopPropagation()
                  },
                },
                params.row.scanAbsent
              ),
          },
          {
            title: '标记缺考',
            key: 'markAbsent',
            align: 'center',
            sortable: 'custom',
            width: 68,
            renderHeader: h =>
              h(
                'span',
                {
                  style: {
                    display: 'inline-block',
                    whiteSpace: 'pre-line',
                    verticalAlign: 'middle',
                  },
                },
                '标记\n缺考'
              ),
            render: (h, params) =>
              h(
                'div',
                {
                  class: 'table-clickable-item',
                  onClick: e => {
                    this.handleCellClick(params.row, 'markAbsent')
                    e.stopPropagation()
                  },
                },
                params.row.markAbsent
              ),
          },
          {
            title: '未扫',
            key: 'notScan',
            align: 'center',
            sortable: 'custom',
            width: 68,
            render: (h, params) =>
              h(
                'div',
                {
                  class: 'table-clickable-item',
                  onClick: e => {
                    this.handleCellClick(params.row, 'notScan')
                    e.stopPropagation()
                  },
                },
                params.row.notScan
              ),
          },
        ]
        // 系统用户显示入库统计
        if (this.isSystem) {
          columns.push(
            {
              title: '客观题待入库',
              key: 'objectiveUnUploadNum',
              align: 'center',
              sortable: 'custom',
              width: 68,
              renderHeader: h =>
                h(
                  'span',
                  {
                    style: {
                      display: 'inline-block',
                      whiteSpace: 'pre-line',
                    },
                  },
                  '客观题\n待入库'
                ),
            },
            {
              title: '主观题待入库',
              key: 'subjectiveUnUploadNum',
              align: 'center',
              sortable: 'custom',
              width: 68,
              renderHeader: h =>
                h(
                  'span',
                  {
                    style: {
                      display: 'inline-block',
                      whiteSpace: 'pre-line',
                    },
                  },
                  '主观题\n待入库'
                ),
            }
          )
        }
        // 排序
        if (this.sortType.key && this.sortType.order != 'normal') {
          let column = columns.find(col => col.key == this.sortType.key)
          if (column) {
            column.sortType = this.sortType.order
          }
        }
        // 名称列
        if (this.view == ViewEnum.Room.id) {
          if (!this.isInSchoolExam && this.stations.length > 0) {
            let stationsColumn = {
              title: '扫描点',
              key: 'scanStationName',
              align: 'left',
              minWidth: 120,
              filters: this.stations.map(station => ({
                label: station.scanStationName,
                value: station.scanStationId,
              })),
              filterRemote: () => {},
            }
            if (this.filter.key == 'scanStationName') {
              stationsColumn.filteredValue = this.filter.value
            }
            columns.unshift(stationsColumn)
          }
          if (!this.isInSchoolExam) {
            let schoolsColumn = {
              title: '学校',
              key: 'schoolName',
              align: 'left',
              minWidth: 120,
              filters: this.schools.map(sch => ({
                label: sch.schoolName,
                value: sch.schoolId,
              })),
              filterRemote: () => {},
            }
            if (this.filter.key == 'schoolName') {
              schoolsColumn.filteredValue = this.filter.value
            }
            columns.unshift(schoolsColumn)
          }
          columns.unshift({
            title: '考场',
            key: 'roomNo',
            align: 'left',
            minWidth: 80,
            render: (h, params) => h('span', {}, params.row.roomNo == 0 ? '合计' : params.row.roomNo),
          })
        } else if (this.view == ViewEnum.School.id) {
          if (!this.isInSchoolExam && this.stations.length > 0) {
            let stationColumn = {
              title: '扫描点',
              key: 'scanStationName',
              align: 'left',
              minWidth: 120,
              filters: this.stations.map(station => ({
                label: station.scanStationName,
                value: station.scanStationId,
              })),
              filterRemote: () => {},
            }
            if (this.filter.key == 'scanStationName') {
              stationColumn.filteredValue = this.filter.value
            }
            columns.unshift(stationColumn)
          }
          columns.unshift({
            title: '学校',
            key: 'schoolName',
            align: 'left',
            minWidth: 120,
          })
        } else if (this.view == ViewEnum.Station.id) {
          columns.unshift({
            title: '扫描点',
            key: 'scanStationName',
            align: 'left',
            minWidth: 120,
          })
        }
        return columns
      },
      fitleredData() {
        // 每种统计先过滤搜索，再过滤筛选
        let records = []
        if (this.view == ViewEnum.Room.id) {
          records = this.rooms
          if (this.keyword) {
            records = records.filter(room => String(room.roomNo).includes(this.keyword))
          }
          if (this.filter.key == 'schoolName' && this.filter.value.length > 0) {
            records = records.filter(room => room.schools.some(sch => this.filter.value.includes(sch.schoolId)))
          } else if (this.filter.key == 'scanStationName' && this.filter.value.length > 0) {
            records = records.filter(room =>
              room.stations.some(station => this.filter.value.includes(station.scanStationId))
            )
          }
        } else if (this.view == ViewEnum.School.id) {
          records = this.schools
          if (this.keyword) {
            records = records.filter(sch => sch.schoolName.includes(this.keyword))
          }
          if (this.filter.key == 'scanStationName' && this.filter.value.length > 0) {
            records = records.filter(sch => this.filter.value.includes(sch.scanStationId))
          }
        } else if (this.view == ViewEnum.Station.id) {
          records = this.stations
          if (this.keyword) {
            records = records.filter(station => station.scanStationName.includes(this.keyword))
          }
        }
        return records
      },
      tableData() {
        let records = this.fitleredData.slice()
        // 排序
        if (this.sortType.order == 'asc') {
          records.sort((a, b) => a[this.sortType.key] - b[this.sortType.key])
        } else {
          records.sort((a, b) => b[this.sortType.key] - a[this.sortType.key])
        }
        // 分页
        let page = records.slice(this.pageSize * (this.currentPage - 1), this.pageSize * this.currentPage)
        // 无搜索、无筛选且在第一页，则添加全体
        if (this.currentPage == 1 && this.total && !this.keyword && (!this.filter.key || !this.filter.value.length)) {
          if (this.view == ViewEnum.Room.id) {
            page.unshift({
              roomNo: '合计',
              schools: [],
              schoolName: '',
              stations: [],
              scanStationName: '',
              ...this.total,
            })
          } else if (this.view == ViewEnum.School.id) {
            page.unshift({
              schoolId: '合计',
              schoolName: '合计',
              scanStationId: '',
              scanStationName: '',
              ...this.total,
            })
          } else if (this.view == ViewEnum.Station.id) {
            page.unshift({
              scanStationId: '合计',
              scanStationName: '合计',
              ...this.total,
            })
          }
        }

        return page
      },
    },
    watch: {
      tableData() {
        if (this.tableData.every(row => row[this.view] != this.studentParams[this.view])) {
          this.handleRowClick(this.tableData[0])
        }
      },
    },
    methods: {
      handleViewChange(view) {
        this.view = view
        this.keyword = ''
        this.filter = {
          key: '',
          value: [],
        }
        this.currentPage = 1
      },
      handleKeywordChange: debounce(function (e) {
        this.keyword = e.target.value.trim()
        this.filter = {
          key: '',
          value: [],
        }
        this.currentPage = 1
      }, 500),
      handleFilterChange(params) {
        this.filter.key = params.key
        this.filter.value = params._filterChecked.slice()
        if (this.filter.value.length == 0) {
          this.filter.key = ''
        }
        this.currentPage = 1
      },
      handleSortChange(params) {
        this.sortType.key = params.key
        this.sortType.order = params.order
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      handlePageSizeChange(pageSize) {
        this.pageSize = pageSize
        this.currentPage = 1
      },
      rowClassName(row) {
        if (row[this.view] == this.studentParams[this.view]) {
          return 'stats-row-active'
        }
      },
      handleRowClick(row) {
        this.handleCellClick(row, 'total')
      },
      handleCellClick(row, status) {
        let params = {
          roomNo: 0,
          schoolId: '',
          scanStationId: 0,
          status: status || 'total',
        }
        if (row) {
          params[this.view] = row[this.view]
        }
        this.$emit('change-active-row', params)
      },
      refresh() {
        this.$emit('refresh')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .pane-monitor-stats {
    padding-right: 16px;
    padding-left: 16px;
    border-right: 1px solid $color-border;
  }

  .panel-header {
    .radio {
      margin-right: 10px;
    }

    .search {
      width: 200px;
      margin-right: 20px;
    }

    .btn-refresh {
      margin-left: auto;
    }
  }

  .panel-body {
    :deep(.stats-row-active td) {
      background-color: $color-iview-table-active-row;
    }

    :deep(.table-clickable-item) {
      text-align: center;
      text-decoration: underline;

      &:hover {
        cursor: pointer;
      }
    }
  }
</style>
