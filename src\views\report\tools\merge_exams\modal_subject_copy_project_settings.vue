<script>
  import { Tooltip } from 'view-ui-plus'

  import BranchType from '@/enum/qlib/branch_type'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      structConfirmed: {
        type: Boolean,
        default: true,
      },
      projectSubjectDetails: {
        type: Array,
        default: () => [],
      },
    },

    emits: ['update:modelValue', 'copy-project-setting'],

    data() {
      return {
        selectedProjectId: '',
        objectiveQuestionColumns: [
          {
            title: '大题',
            key: 'topicName',
            align: 'center',
          },
          {
            title: '题号',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '题型',
            align: 'center',
            render: (h, params) =>
              h('span', {}, this.optionalBranchType.find(x => x.id === params.row.questionType)?.name),
          },
          {
            title: '选项个数',
            key: 'optionCount',
            align: 'center',
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '答案',
            key: 'answer',
            align: 'center',
          },
          {
            title: '其它答案分数',
            align: 'center',
            render: (h, params) => {
              if (params.row.branchTypeId === BranchType.MultipleChoice.id && params.row.answerScoreList) {
                return h(
                  Tooltip,
                  {
                    content: params.row.answerScoreList,
                    'max-width': '800',
                    transfer: true,
                  },
                  [
                    h('span', {}, () =>
                      params.row.answerScoreList.length > 21
                        ? params.row.answerScoreList.slice(0, 21) + '……'
                        : params.row.answerScoreList
                    ),
                  ]
                )
              } else {
                return h('span', {}, '-')
              }
            },
          },
          {
            title: '附加题',
            align: 'center',
            render: (h, params) => h('span', {}, params.row.isAdditional ? '附加题' : '-'),
          },
        ],
        subjectiveQuestionColumns: [
          {
            title: '大题',
            key: 'topicName',
            align: 'center',
          },
          {
            title: '题号',
            key: 'questionName',
            align: 'center',
          },
          {
            title: '小题号',
            key: 'branchName',
            align: 'center',
          },
          {
            title: '系统题号',
            align: 'center',
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.branchCode ? `${params.row.questionCode}.${params.row.branchCode}` : params.row.questionCode
              ),
          },
          {
            title: '满分',
            key: 'fullScore',
            align: 'center',
          },
          {
            title: '附加题',
            align: 'center',
            render: (h, params) => h('span', {}, params.row.isAdditional ? '附加题' : '-'),
          },
        ],
      }
    },

    computed: {
      selectedProjectSubjectConfig() {
        return this.projectSubjectDetails.find(d => d.id === this.selectedProjectId)?.subjectConfig
      },
      optionalBranchType() {
        return BranchType.getIdNames().filter(x => x.id > 10 && x.id <= 13)
      },
    },

    methods: {
      onCloseModal() {
        this.$emit('update:modelValue', false)
      },
      handleModalVisibleChange(visible) {
        if (visible) {
          if (
            this.projectSubjectDetails.length &&
            (!this.selectedProjectId || !this.projectSubjectDetails.some(d => d.id === this.selectedProjectId))
          ) {
            this.selectedProjectId = this.projectSubjectDetails[0].id
          }
        } else {
          this.onCloseModal()
        }
      },

      copyCurrentProjectSettings() {
        this.$emit('copy-project-setting', this.selectedProjectSubjectConfig)
        this.selectedProjectId = ''
        this.onCloseModal()
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :footer-hide="structConfirmed"
    title="复制源项目设置"
    width="86"
    @on-visible-change="handleModalVisibleChange"
  >
    <div class="modal-subject-copy-project-settings">
      <Form :label-width="120" label-position="left" class="form">
        <FormItem label="考试项目">
          <Select v-model="selectedProjectId" transfer class="project-selector">
            <Option v-for="item of projectSubjectDetails" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <div v-if="selectedProjectId" class="overflow-panel">
          <FormItem label="笔试满分">
            <span>{{ selectedProjectSubjectConfig.writtenFullScore }}</span>
          </FormItem>
          <FormItem label="其它满分">
            <span>{{ selectedProjectSubjectConfig.otherFullScore }}</span>
          </FormItem>
          <FormItem label="客观题">
            <Table :columns="objectiveQuestionColumns" :data="selectedProjectSubjectConfig.objectives" border></Table>
          </FormItem>
          <FormItem label="主观题">
            <Table :columns="subjectiveQuestionColumns" :data="selectedProjectSubjectConfig.subjectives" border></Table>
          </FormItem>
        </div>
      </Form>
    </div>

    <template #footer>
      <Button type="text" @click="onCloseModal">取消</Button>
      <Button type="primary" :disabled="!selectedProjectId" @click="copyCurrentProjectSettings"
        >复制当前项目配置</Button
      >
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-subject-copy-project-settings {
    .btn-copy-project-settings {
      position: fixed;
      top: 20px;
      right: 20px;
    }

    .form {
      .overflow-panel {
        max-height: 66vh;
        padding-right: 20px;
        overflow-y: auto;
      }

      .project-selector {
        width: 480px;
      }
    }
  }
</style>
