/**
 * 答题卡页面尺寸
 */

import Enum from '@/enum/enum'

export default new Enum({
  A4_1: {
    id: 'A4_1',
    name: 'A4单栏',
    width: 210,
    height: 297,
    pages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 12,
      },
    ],
  },
  K16_1: {
    id: 'K16_1',
    name: '16开单栏',
    width: 185,
    height: 260,
    pages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 12,
      },
    ],
  },
  A3_2: {
    id: 'A3_2',
    name: 'A3两栏',
    width: 420,
    height: 297,
    pages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 5,
      },
      {
        top: 15,
        bottom: 12,
        left: 5,
        right: 12,
      },
    ],
  },
  K8_2: {
    id: 'K8_2',
    name: '8开两栏',
    width: 370,
    height: 260,
    pages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 5,
      },
      {
        top: 15,
        bottom: 12,
        left: 5,
        right: 12,
      },
    ],
  },
  A3_3: {
    id: 'A3_3',
    name: 'A3三栏',
    width: 420,
    height: 297,
    pages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 2,
      },
      {
        top: 15,
        bottom: 12,
        left: 7,
        right: 7,
      },
      {
        top: 15,
        bottom: 12,
        left: 2,
        right: 12,
      },
    ],
  },
  A3_3_2: {
    id: 'A3_3_2',
    name: 'A3正三栏反两栏',
    width: 420,
    height: 297,
    frontPages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 2,
      },
      {
        top: 15,
        bottom: 12,
        left: 7,
        right: 7,
      },
      {
        top: 15,
        bottom: 12,
        left: 2,
        right: 12,
      },
    ],
    backPages: [
      {
        top: 15,
        bottom: 12,
        left: 12,
        right: 5,
      },
      {
        top: 15,
        bottom: 12,
        left: 5,
        right: 12,
      },
    ],
  },
})
