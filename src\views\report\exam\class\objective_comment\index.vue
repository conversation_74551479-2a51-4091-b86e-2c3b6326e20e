<template>
  <div class="page-objective-comment">
    <LayoutHeader sticky background class="container-report-exam-header">
      <TextButton type="primary" icon="ios-arrow-back" @click="backHome"></TextButton>
      <div class="exam-info">
        <div class="exam-title">{{ title }}</div>
        <div class="exam-description">{{ description }}</div>
      </div>
    </LayoutHeader>
    <div v-show="visibleSubjects.length || visibleClasses.length" class="section-selector">
      <div class="selectors">
        <RadioGroup
          v-show="visibleSubjects.length"
          v-model="currentExamSubjectId"
          class="selector-subject"
          type="button"
        >
          <Radio v-for="s in visibleSubjects" :key="s.examSubjectId" :label="s.examSubjectId">{{
            s.subjectName
          }}</Radio>
        </RadioGroup>
        <Select
          v-show="visibleClasses.length"
          v-model="currentClassId"
          class="selector-class"
          placeholder="选择班级"
          filterable
        >
          <Option v-for="c in visibleClasses" :key="c.classId" :value="c.classId">{{ c.className }}</Option>
        </Select>
      </div>
    </div>
    <div class="section-stat">
      <div class="section-left">
        <span class="class-name">{{ currentClassName }}</span>
        <span>{{ classStudents.length }} 名考生</span>
        <span v-if="scannedStudents.length">，扫描正常 {{ scannedStudents.length }} 人</span>
        <span v-if="absentStudents.length">，扫描缺考 {{ absentStudents.length }} 人</span>
        <span v-if="markAbsentStudents.length">，标记缺考 {{ markAbsentStudents.length }} 人</span>
        <span v-if="notScannedStudents.length">，未扫 {{ notScannedStudents.length }} 人</span>
        <TextButton type="primary" @click="setModalStudentVisible">查看名单</TextButton>
      </div>
      <div class="section-right">
        <TextButton icon="md-download" @click="onShowModalExport">导出客观题统计</TextButton>
      </div>
    </div>
    <component
      :is="currentComponent"
      :questions="questions"
      :show-grade-avg-score="false"
      :show-all-branch="false"
      score-detail-visible
      @on-change-mode="onChangeMode"
    />
    <Modal v-model="modalStudentVisible" class="modal-student-list" title="查看学生名单" footer-hide>
      <div class="content-modal">
        <div v-if="scannedStudents.length" class="box-student">
          <div class="header">扫描正常 {{ scannedStudents.length }} 人</div>
          <div class="body">
            <div v-for="s in scannedStudents" :key="s.studentId" class="student-item">{{ s.realName }}</div>
          </div>
        </div>
        <div v-if="absentStudents.length" class="box-student">
          <div class="header">扫描缺考 {{ absentStudents.length }} 人</div>
          <div class="body">
            <div v-for="s in absentStudents" :key="s.studentId" class="student-item">{{ s.realName }}</div>
          </div>
        </div>
        <div v-if="markAbsentStudents.length" class="box-student">
          <div class="header">标记缺考 {{ markAbsentStudents.length }} 人</div>
          <div class="body">
            <div v-for="s in markAbsentStudents" :key="s.studentId" class="student-item">{{ s.realName }}</div>
          </div>
        </div>
        <div v-if="notScannedStudents.length" class="box-student">
          <div class="header">未扫 {{ notScannedStudents.length }} 人</div>
          <div class="body">
            <div v-for="s in notScannedStudents" :key="s.studentId" class="student-item">{{ s.realName }}</div>
          </div>
        </div>
      </div>
    </Modal>

    <Modal v-model="modalExportVisible" :width="500" title="导出客观题统计">
      <div class="container-export-stat">
        <Checkbox
          :indeterminate="indeterminate"
          :model-value="checkAll"
          style="margin-bottom: 10px"
          @click.prevent="handleCheckAll"
          >全选</Checkbox
        >
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <Checkbox v-for="cls in visibleClasses" :key="cls.classId" :label="cls.classId" style="margin-bottom: 8px">{{
            cls.className
          }}</Checkbox>
        </CheckboxGroup>
      </div>
      <template #footer>
        <div class="box-footer-btn">
          <Button style="vertical-align: middle" @click="modalExportVisible = false">取消</Button>
          <Button type="primary" :loading="isLoading" style="vertical-align: middle" @click="onExportConfirm"
            >导出</Button
          >
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
  import LayoutHeader from '@/components/layout_header'
  import NormalMode from '../../components/lecture/normal_mode.vue'
  import FullScreenMode from '../../components/lecture/full_screen_mode.vue'

  import { apiGetPaperObjectiveComment, apiGetSubjectClassList, apiDownloadObjectiveStatistics } from '@/api/report'
  import { apiGetBindPaperContent, apiGetClassExamSubjectStudents, apiGetExamById } from '@/api/emarking'

  import { initializePaperQuestions } from '@/helpers/report/paper_review'
  import { formatMonthDay } from '@/utils/date'
  import ScanStatus from '@/enum/emarking/scan_status'
  import PageCache from '@/utils/page_cache'
  import { downloadBlob } from '@/utils/download'

  const uniquePageName = 'report-objective-comment'

  export default {
    components: {
      LayoutHeader,
      NormalMode,
      FullScreenMode,
    },
    data() {
      return {
        screenMode: 'normal',
        questions: [],
        currentExamSubjectId: '',
        visibleClasses: [],
        currentClassId: '',
        classStudents: [],
        modalStudentVisible: false,
        objectiveCommentCurrentExam: null,
        modalExportVisible: false,

        indeterminate: true,
        checkAll: false,
        checkAllGroup: [],
        isLoading: false,
      }
    },
    computed: {
      currentComponent() {
        return this.screenMode === 'full-screen' ? FullScreenMode : NormalMode
      },
      visibleSubjects() {
        const { currentClassId, visibleClasses } = this
        if (!currentClassId || !visibleClasses.length) return []

        const currentClass = visibleClasses.find(item => item.classId === currentClassId)
        if (currentClass) return currentClass.subjects
        return []
      },
      currentClassName() {
        const { currentClassId, visibleClasses } = this
        if (!currentClassId || !visibleClasses.length) return ''

        const currentClass = visibleClasses.find(item => item.classId === currentClassId)
        if (currentClass) return currentClass.className
        return ''
      },
      currentParams() {
        return `${this.currentExamSubjectId}_${this.currentClassId}`
      },
      isMultipleSchool() {
        return this.objectiveCommentCurrentExam && this.objectiveCommentCurrentExam.examScope.id > 1
      },
      title() {
        return `客观题讲评 — ${this.objectiveCommentCurrentExam && this.objectiveCommentCurrentExam.examName}`
      },
      description() {
        const { objectiveCommentCurrentExam } = this
        if (!objectiveCommentCurrentExam) return ''
        let str = objectiveCommentCurrentExam.grade.name
        if (objectiveCommentCurrentExam.examType.name) {
          str += '，' + objectiveCommentCurrentExam.examType.name
        }
        if (this.isMultipleSchool) {
          str += '，' + objectiveCommentCurrentExam.examScope.name
        }
        str +=
          '，' +
          formatMonthDay(objectiveCommentCurrentExam.startDate) +
          ' 至 ' +
          formatMonthDay(objectiveCommentCurrentExam.endDate)
        return str
      },
      scannedStudents() {
        return this.classStudents.filter(s => s.sign === ScanStatus.Normal.id)
      },
      absentStudents() {
        return this.classStudents.filter(s => s.sign === ScanStatus.Absent.id)
      },
      markAbsentStudents() {
        return this.classStudents.filter(s => s.sign === ScanStatus.MarkAbsent.id)
      },
      notScannedStudents() {
        return this.classStudents.filter(s => s.sign === ScanStatus.NotScanned.id)
      },
    },
    watch: {
      currentParams() {
        if (this.currentExamSubjectId && this.currentClassId) {
          this.getExamSubjectClassStudents()
          this.fetchData()
        }
      },
      visibleSubjects: {
        handler: function (newVal) {
          let lastActiveInfo = PageCache.fetch(uniquePageName)

          if (newVal && newVal.length) {
            if (!this.currentExamSubjectId) {
              if (
                lastActiveInfo &&
                lastActiveInfo.currentExamSubjectId &&
                newVal.some(s => s.examSubjectId === lastActiveInfo.currentExamSubjectId)
              ) {
                this.currentExamSubjectId = lastActiveInfo.currentExamSubjectId
              } else {
                this.currentExamSubjectId = newVal[0].examSubjectId
              }
            } else {
              if (!newVal.some(s => s.examSubjectId === this.currentExamSubjectId)) {
                this.currentExamSubjectId = newVal[0].examSubjectId
              }
            }
          }
        },
        immediate: true,
      },
    },
    created() {
      this.getExamDetail()
      this.getSubjectClasses()
    },
    beforeUnmount() {
      PageCache.save(uniquePageName, {
        currentExamSubjectId: this.currentExamSubjectId,
        currentClassId: this.currentClassId,
      })
    },
    methods: {
      getExamDetail() {
        const { examId } = this.$route.params
        apiGetExamById({
          examId,
        }).then(exam => {
          this.objectiveCommentCurrentExam = exam
        })
      },
      getSubjectClasses() {
        let lastActiveInfo = PageCache.fetch(uniquePageName)
        const { examId } = this.$route.params

        apiGetSubjectClassList({
          examId,
        }).then(res => {
          this.visibleClasses = res || []
          if (
            lastActiveInfo &&
            lastActiveInfo.currentClassId &&
            res.some(cls => cls.classId === lastActiveInfo.currentClassId)
          ) {
            this.currentClassId = lastActiveInfo.currentClassId
          } else if (
            (!this.currentClassId || !res.some(cls => cls.classId === this.currentClassId)) &&
            res &&
            res.length
          ) {
            this.currentClassId = res[0].classId
          }
        })
      },
      setModalStudentVisible() {
        this.modalStudentVisible = true
      },
      backHome() {
        this.$router.back()
      },
      async fetchData() {
        this.$TransparentSpin.show()
        try {
          let [{ questions, studentAnswers }, paper] = await Promise.all([
            apiGetPaperObjectiveComment({
              examSubjectId: this.currentExamSubjectId,
              classId: this.currentClassId,
            }),
            apiGetBindPaperContent({
              examSubjectId: this.currentExamSubjectId,
              examId: this.$route.params.examId,
            }),
          ])
          this.questions = initializePaperQuestions(questions, paper, studentAnswers)
          this.checkQuestionScoreAnswer()
        } finally {
          this.$TransparentSpin.hide()
        }
      },
      checkQuestionScoreAnswer() {
        let noAnswerQuestionNames = []
        let noScoreQuestionNames = []
        this.questions.forEach(q => {
          q.branches.forEach(b => {
            if (b.isObjective) {
              let branchName = b.branchName
              if (!branchName) {
                branchName = `${b.questionName || b.questionCode}`
                if (b.branchCode > 0) {
                  branchName += `.${b.branchCode}`
                }
              }
              if (!b.standardAnswer) {
                noAnswerQuestionNames.push(branchName)
              }
              if (!b.fullScore) {
                noScoreQuestionNames.push(branchName)
              }
            }
          })
        })
        if (noScoreQuestionNames.length > 0 || noAnswerQuestionNames.length > 0) {
          let content = ''
          if (noAnswerQuestionNames.length > 0) {
            content += `以下题目未设置答案：<br>${noAnswerQuestionNames.join('、')}`
          }
          if (noScoreQuestionNames.length > 0) {
            if (content) {
              content += '<br>'
            }
            content += `以下题目未设置分数：<br>${noScoreQuestionNames.join('、')}`
          }
          this.$Modal.error({
            title: '客观题信息不完整',
            content,
          })
        }
      },
      getExamSubjectClassStudents() {
        apiGetClassExamSubjectStudents({
          examSubjectId: this.currentExamSubjectId,
          classId: this.currentClassId,
        }).then(res => {
          this.classStudents = (res && res.students) || []
        })
      },
      onChangeMode(val) {
        this.screenMode = val
      },
      onShowModalExport() {
        this.modalExportVisible = true
        this.checkAllGroup = [this.currentClassId]
      },
      handleCheckAll() {
        if (this.indeterminate) {
          this.checkAll = false
        } else {
          this.checkAll = !this.checkAll
        }
        this.indeterminate = false

        if (this.checkAll) {
          this.checkAllGroup = this.visibleClasses.map(c => c.classId)
        } else {
          this.checkAllGroup = []
        }
      },
      checkAllGroupChange(data) {
        if (data.length === this.visibleClasses.length) {
          this.indeterminate = false
          this.checkAll = true
        } else if (data.length > 0) {
          this.indeterminate = true
          this.checkAll = false
        } else {
          this.indeterminate = false
          this.checkAll = false
        }
      },
      onExportConfirm() {
        if (this.checkAllGroup.length === 0) {
          this.$Message.info('请至少选择一个班级')
          return
        }

        this.isLoading = true
        apiDownloadObjectiveStatistics({
          classIds: this.checkAllGroup,
          examSubjectId: this.currentExamSubjectId,
        })
          .then(res => {
            downloadBlob(
              res,
              `${this.objectiveCommentCurrentExam && this.objectiveCommentCurrentExam.examName}客观题统计.xlsx`
            )
          })
          .finally(() => {
            this.isLoading = false
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-report-exam-header {
    @include flex(row, flex-start, center);
    z-index: 10000;
    min-height: 50px;
    font-size: $font-size-large;

    :deep(> .background) {
      background-color: white;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    }

    .icon-home {
      cursor: pointer;
    }

    .exam-info {
      flex-grow: 1;
      flex-shrink: 1;
      margin-left: 10px;
      line-height: 1.2;

      .exam-title {
        display: inline;
        margin-right: 20px;
      }

      .exam-description {
        display: inline-block;
        font-size: $font-size-small;
      }
    }

    .report-level {
      flex-grow: 0;
      flex-shrink: 0;
      margin-left: 20px;

      .report-level-current {
        color: $color-primary;
        font-size: $font-size-medium-x;
        cursor: pointer;

        .arrow {
          transition: transform 0.5s ease;
        }
      }
    }
  }

  .section-selector {
    margin-top: 10px;
    padding: 15px;
    background-color: white;

    .selectors {
      @include flex(row, flex-end, center);
      flex-wrap: wrap;
      // padding-bottom: 10px;
      // border-bottom: 1px solid $color-border;
      user-select: none;

      .selector-subject {
        flex-grow: 1;

        .ivu-radio-wrapper.ivu-radio-group-item.ivu-radio-wrapper-checked.ivu-radio-default {
          border-color: $color-primary;
          color: white;
          font-weight: bold;
          background-color: $color-primary;
        }
      }

      .selector-class {
        flex-grow: 0;
        width: 180px;
        margin-left: 10px;
      }
    }
  }

  .section-stat {
    @include flex(row, space-between, center);
    margin-bottom: 10px;
    padding: 0 15px 15px;
    background-color: #fff;

    .class-name {
      margin-right: 10px;
      font-size: 20px;
    }
  }

  .modal-student-list {
    .box-student {
      &:not(:last-child) {
        margin-bottom: 10px;
      }
    }

    .header {
      margin-bottom: 10px;
      font-size: 18px;
    }

    .body {
      @include flex(row, flex-start, center);
      flex-wrap: wrap;
    }

    .student-item {
      margin-right: 15px;
      margin-bottom: 10px;
    }
  }
</style>
