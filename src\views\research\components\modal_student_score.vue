<template>
  <Modal
    :model-value="modelValue"
    :title="modalTitle"
    :width="800"
    footer-hide
    mask-closable
    @on-visible-change="handleVisibilityChange"
  >
    <div ref="chart" class="chart"></div>
    <div class="exam-names">考试：{{ examNames }}</div>
  </Modal>
</template>

<script>
  import { nextTick } from 'vue'
  import echarts from '@/utils/echarts'

  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      modalTitle: {
        type: String,
        default: '',
      },
      modalType: {
        type: String,
        default: '',
      },
      studentInModal: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['update:modelValue'],
    data() {
      return {}
    },
    computed: {
      examNames() {
        if (!this.studentInModal || !this.studentInModal.scores) return ''
        return this.studentInModal.scores.map((x, idx) => `(${idx + 1}) ${x.templateName}`).join('；')
      },
    },
    watch: {
      modelValue: {
        handler: function (val) {
          if (!val) return
          nextTick(() => {
            this.drawChart()
          })
        },
      },
    },
    methods: {
      handleCancel() {
        this.$emit('update:modelValue', false)
      },
      handleVisibilityChange(visibility) {
        if (!visibility) {
          this.handleCancel()
        }
      },
      drawChart() {
        if (!this.chart) {
          let elChart = this.$refs['chart']
          if (this.chart) {
            this.chart.dispose()
          }
          this.chart = echarts.init(elChart)
        }
        let chart = this.chart

        let chartOption = {
          legend: {
            data: ['联考排名', '校排名', '班排名'],
          },
          grid: {
            left: 50,
            right: 50,
            top: 50,
            bottom: 50,
          },
          tooltip: {
            show: true,
            trigger: 'axis',
          },
          xAxis: {
            type: 'category',
            data: this.studentInModal.scores.map((x, idx) => `(${idx + 1})`),
            axisTick: {
              show: false,
            },
            axisLine: {
              onZero: false,
            },
            axisLabel: {
              interval: 0,
              fontSize: 11,
              formatter: function (value) {
                var result = ''
                var maxRowWidth = 20
                var rowWidth = 0
                ;(value || '').split('').forEach(c => {
                  var width = c.charCodeAt(0) < 128 ? 1 : 2
                  if (rowWidth + width > maxRowWidth) {
                    result += '\n'
                    rowWidth = width
                  } else {
                    rowWidth += width
                  }
                  result += c
                })
                return result
              },
            },
          },
          yAxis: {
            name: '排名',
            type: 'value',
            splitLine: {
              lineStyle: {
                color: ['#eee'],
              },
            },
            inverse: true,
            nameLocation: 'start',
            min: 1,
            minInterval: 1,
          },
          series: [
            {
              name: '联考排名',
              type: 'line',
              symbol: 'triangle',
              symbolSize: 6,
              data: this.studentInModal.scores.map(x => x.areaRank),
            },
            {
              name: '校排名',
              type: 'line',
              symbol: 'triangle',
              symbolSize: 6,
              data: this.studentInModal.scores.map(x => x.schoolRank),
            },
            {
              name: '班排名',
              type: 'line',
              symbol: 'triangle',
              symbolSize: 6,
              data: this.studentInModal.scores.map(x => x.classRank),
            },
          ],
        }

        chart.setOption(chartOption)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart {
    height: 400px;
  }
</style>
