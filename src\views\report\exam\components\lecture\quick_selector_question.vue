<template>
  <div class="container-quick-selector-question">
    <div class="color-description">
      <span class="color-item">
        <span class="spot spot-easy"></span>
        <span>容易</span>
        <span id="color-item-explain">(1.0~0.7)</span>
      </span>
      <span class="color-item">
        <span class="spot spot-normal"></span>
        <span>一般</span>
        <span id="color-item-explain">(0.7~0.4)</span>
      </span>
      <span class="color-item">
        <span class="spot spot-hard"></span>
        <span>困难</span>
        <span id="color-item-explain">(0.4~0)</span>
      </span>
    </div>
    <div class="list">
      <div
        v-for="q in questions"
        :key="q.questionName"
        class="question-name-item"
        :data-difficulty="q.difficulty"
        @click="onItemClick(q)"
      >
        {{ q.questionName }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      questions: {
        type: Array,
        default: () => [],
      },
    },
    emits: ['on-change'],
    methods: {
      onItemClick(q) {
        this.$emit('on-change', q)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container-quick-selector-question {
    .color-description {
      margin-top: 4px;
      margin-bottom: 10px;

      .color-item {
        display: block;
        height: 20px;
        margin-right: 10px;
        line-height: 20px;

        .spot {
          display: inline-block;
          width: 10px;
          height: 10px;
          vertical-align: middle;
        }

        .spot-easy {
          background-color: $color-success;
        }

        .spot-normal {
          background-color: #2db7f5;
        }

        .spot-hard {
          background-color: $color-error;
        }
      }
    }

    .question-name-item {
      display: inline-block;
      min-width: 30px;
      height: 30px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding-right: 6px;
      padding-left: 6px;
      border: 1px solid $color-primary;
      border-radius: 3px;
      line-height: 28px;
      text-align: center;
      cursor: pointer;
      user-select: none;

      &[data-difficulty='easy'] {
        border: 1px solid $color-success;
        color: $color-success;
      }

      &[data-difficulty='normal'] {
        border: 1px solid #2db7f5;
        color: #2db7f5;
      }

      &[data-difficulty='hard'] {
        border: 1px solid $color-error;
        color: $color-error;
      }
    }

    #color-item-explain {
      color: $color-second-title;
      font-size: $font-size-small;
    }
  }
</style>
