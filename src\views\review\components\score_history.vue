<script>
  import { mapGetters } from 'vuex'
  import ScoreResult from '../components/mark/score_result.vue'
  import { apiGetReviewedHistory, apiGetScoreSettings, apiGetJudgeProgress } from '@/api/review/review'

  import { sumByDefaultZero, roundScore } from '@/utils/math'
  import { debounce } from '@/utils/function'

  export default {
    components: {
      ScoreResult,
    },
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
      categoryId: {
        type: String,
        default: '',
      },
    },

    emits: ['update:modelValue'],
    data() {
      return {
        // 表格尺寸
        modalWidth: window.innerWidth,
        tableMaxHeight: window.innerHeight - 130,

        // 评审记录查询
        judgeUsers: [],
        activeCategory: '',
        activeUserId: '',
        minScore: null,
        maxScore: null,
        beginTime: null,
        beginTimeOption: {
          disabledDate: date => {
            return date && this.endTime && date.getTime() > this.endTime.getTime()
          },
        },
        endTime: null,
        endTimeOption: {
          disabledDate: date => {
            return date && this.beginTime && date.getTime() < this.beginTime.getTime()
          },
        },
        admissionNum: '',
        total: 0,
        pageSize: 10,
        currentPage: 1,
        markHistoryList: [],
        fetchingList: false,
        changeListParams: () => {},

        // 加载试卷，加载期间禁止打分
        loadingPaper: false,
        scoreItemsMap: {},
        fullScore: null,
      }
    },

    computed: {
      ...mapGetters('review', ['currentActivity', 'activityId', 'categories']),
      categoriesForSelect() {
        return [
          {
            id: '0',
            name: '不限',
          },
          ...this.categories,
        ]
      },
      tableWidth() {
        return Math.min(600, (this.modalWidth - 50) * 0.4) + 'px'
      },

      inputMinScorePlaceholder() {
        return '最低0分'
      },

      inputMaxScorePlaceholder() {
        return this.fullScore == null ? '' : `满分${this.fullScore}分`
      },

      tableColumns() {
        let columns = [
          {
            title: '评审员',
            key: 'realName',
            align: 'center',
            width: 80,
          },
          {
            title: '评审序号',
            key: 'judgeAssignOrder',
            align: 'center',
            width: 60,
          },
          {
            title: '评审给分',
            key: 'score',
            renderHeader: h => h('div', {}, [h('div', {}, '评审'), h('div', {}, '给分')]),
            render: (h, params) => h('span', {}, params.row.type == 1 ? '问题卷' : params.row.score),
            align: 'center',
            width: 70,
          },
          {
            title: '给分时间',
            key: 'submitTime',
            align: 'center',
            width: 170,
          },
        ]

        return columns
      },

      activePaper() {
        return this.markHistoryList.find(x => x.active)
      },

      isMultipleScore() {
        const theCategory = this.categories.find(c => c.id === this.activePaper.categoryId)

        return theCategory && theCategory.scoreMode === 'multiple'
      },

      activePaperIndex() {
        return this.markHistoryList.findIndex(x => x.active)
      },

      hasPreviousPaper() {
        return this.activePaperIndex > 0 || this.currentPage > 1
      },

      hasNextPaper() {
        return (
          (this.activePaperIndex >= 0 && this.activePaperIndex < this.markHistoryList.length - 1) ||
          this.currentPage * this.pageSize < this.total
        )
      },

      activePaperQuestions() {
        return (this.activePaper && this.activePaper.questions) || []
      },

      activePaperSpotCheckInfo() {
        return (this.activePaper && this.activePaper.spotCheckInfo) || {}
      },

      activePaperTotalScore() {
        return this.activePaper ? this.activePaper.totalScore : null
      },

      paperSpotScoreShowed() {
        return this.activePaperSpotCheckInfo.score != null
      },
    },

    created() {
      this.changeListParams = debounce(() => {
        this.currentPage = 1
        this.getMarkHistoryList().then(() => {
          if (this.markHistoryList && this.markHistoryList.length) {
            this.changeActivePaper(this.markHistoryList[0])
          }
        })
      }, 500)
    },

    methods: {
      resetData() {
        this.modalWidth = window.innerWidth
        this.tableMaxHeight = window.innerHeight - 130
        this.activeCategory = ''
        this.activeUserId = ''
        this.minScore = null
        this.maxScore = null
        this.admissionNum = ''
        this.total = 0
        this.pageSize = 10
        this.currentPage = 1
        this.markHistoryList = []
        this.fetchingList = false
      },

      /* modal */
      closeModal() {
        this.$emit('update:modelValue', false)
      },
      async handleModalVisibleChanged(visibility) {
        if (visibility) {
          this.resetData()
          this.activeCategory = this.categoryId
          this.getJudgeUsers()
          await this.getMarkHistoryList()
          if (this.markHistoryList && this.markHistoryList.length) {
            this.changeActivePaper(this.markHistoryList[0])
          }
        } else {
          this.closeModal()
        }
      },

      /* function */

      async onCategoryChange(categoryId) {
        this.activeCategory = categoryId
        this.activeUserId = ''
        this.minScore = null
        this.maxScore = null
        this.currentPage = 1
        this.getJudgeUsers()
        await this.getMarkHistoryList()
        this.changeActivePaper(this.markHistoryList[0])
      },
      async changeActiveUser(userId) {
        this.activeUserId = userId
        this.currentPage = 1
        await this.getMarkHistoryList()
        this.changeActivePaper(this.markHistoryList[0])
      },
      async changePage(page, nextActivePaperIndex = 0) {
        this.currentPage = page
        await this.getMarkHistoryList()
        this.changeActivePaper(this.markHistoryList[nextActivePaperIndex])
      },
      changeActivePaper(paper) {
        this.markHistoryList.forEach(x => {
          x.active = x === paper
        })

        if (!paper) return
        if (!this.scoreItemsMap[paper.categoryId]) {
          this.getScoreConfig(paper.categoryId)
        }
      },
      getJudgeUsers() {
        return apiGetJudgeProgress({
          activityId: this.activityId,
          categoryId: this.activeCategory != '0' ? this.activeCategory : undefined,
        }).then(res => {
          this.judgeUsers = res
        })
      },
      getMarkHistoryList() {
        this.fetchingList = true
        return apiGetReviewedHistory({
          activityId: this.activityId,
          categoryId: this.activeCategory != '0' ? this.activeCategory : undefined,
          judgeId: this.activeUserId,
          minScore: this.minScore,
          maxScore: this.maxScore,
          beginTime: (this.beginTime && this.getDateString(this.beginTime)) || undefined,
          endTime: (this.endTime && this.getDateString(this.endTime)) || undefined,
          size: this.pageSize,
          page: this.currentPage,
        })
          .then(data => {
            this.total = Number(data.total)
            this.markHistoryList = data.list.map(x => {
              return {
                ...x,
              }
            })
          })
          .catch(err => {
            this.markHistoryList = []
            this.total = 0
            throw err
          })
          .finally(() => {
            this.fetchingList = false
          })
      },
      getDateString(date) {
        return `${date.getFullYear()}-${
          date.getMonth() + 1
        }-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`
      },

      /* header tool bar */
      goPreviousPaper() {
        if (this.activePaperIndex > 0) {
          this.changeActivePaper(this.markHistoryList[this.activePaperIndex - 1])
        } else if (this.activePaperIndex === 0 && this.currentPage > 1) {
          this.changePage(this.currentPage - 1, this.pageSize - 1)
        }
      },
      goNextPaper() {
        if (this.activePaperIndex < this.markHistoryList.length - 1) {
          this.changeActivePaper(this.markHistoryList[this.activePaperIndex + 1])
        } else if (
          this.activePaperIndex === this.markHistoryList.length - 1 &&
          this.currentPage < Math.ceil(this.total / this.pageSize)
        ) {
          this.changePage(this.currentPage + 1, 0)
        }
      },

      /* table */
      getTableRowClassName(row) {
        if (row && row.active) {
          return 'row-active'
        } else {
          return ''
        }
      },
      handleTableRowClick(row, rowIndex) {
        this.changeActivePaper(this.markHistoryList[rowIndex])
      },

      /* 打分板 */
      calcActivePaperTotalScore() {
        if (!this.activePaper) {
          return
        }
        this.activePaper.totalScore = sumByDefaultZero(this.activePaperQuestions, q => Number(q.score))
      },
      setAllQuestionsFullScore() {
        this.activePaperQuestions.forEach(q => {
          q.score = q.fullScore
        })
        this.calcActivePaperTotalScore()
      },
      setAllQuestionsZero() {
        this.activePaperQuestions.forEach(q => {
          q.score = q.minScore
        })
        this.calcActivePaperTotalScore()
      },
      setQuestionFullScore(questionCode) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (target) {
          target.score = target.fullScore
        }
        this.calcActivePaperTotalScore()
      },
      setQuestionZero(questionCode) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (target) {
          target.score = target.minScore
        }
        this.calcActivePaperTotalScore()
      },
      setQuestionScore(questionCode, score) {
        let target = this.activePaperQuestions.find(x => x.questionCode === questionCode)
        if (!target) {
          return
        }
        target.score = score
        this.calcActivePaperTotalScore()
      },
      changeQuestionScore(question) {
        if (question.score == null || question.score === '') {
          question.score = null
        } else {
          question.score = Number(question.score)
          if (isNaN(question.score) || question.score < question.minScore || question.score > question.fullScore) {
            question.score = null
          } else {
            question.score = roundScore(question.score)
          }
        }
        this.calcActivePaperTotalScore()
      },
      focusInput(questionCode) {
        let elementInput = this.$refs['inputScore_' + questionCode]
        if (!elementInput) {
          return
        }

        if (elementInput.focus) {
          elementInput.focus()
        } else if (elementInput[0] && elementInput[0].focus) {
          elementInput[0].focus()
        }
      },
      focusNextQuestion(questionCode) {
        let index = this.activePaperQuestions.findIndex(x => x.questionCode === questionCode)
        if (index < 0) {
          return
        }

        let nextQuestion = this.activePaperQuestions[index + 1]
        if (!nextQuestion) {
          return
        }

        this.focusInput(nextQuestion.questionCode)
      },
      handleInputEnter(question) {
        this.changeQuestionScore(question)
        this.focusNextQuestion(question.questionCode)
      },
      getScoreConfig(categoryId) {
        apiGetScoreSettings({
          categoryId,
        }).then(res => {
          this.scoreItemsMap[categoryId] = res?.scoreItems || []
          this.fullScore = res?.fullScore
        })
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    :mask-closable="false"
    :mask="false"
    :closable="false"
    class="modal-monitor-score-history"
    fullscreen
    footer-hide
    @on-visible-change="handleModalVisibleChanged"
  >
    <template #header>
      <div class="modal-header">
        <div class="header-left">
          <div class="title">查看评审记录</div>
          <div class="form">
            <div class="form-item">
              <span class="form-item-label">类别</span>
              <Select
                v-model="activeCategory"
                class="form-item-content selector-block"
                size="small"
                @on-change="onCategoryChange"
              >
                <Option v-for="c of categoriesForSelect" :key="c.id" :value="c.id">{{ c.name }}</Option>
              </Select>
            </div>
            <div class="form-item">
              <span class="form-item-label">评审员</span>
              <Select
                class="form-item-content selector-user"
                size="small"
                :model-value="activeUserId"
                clearable
                filterable
                @on-change="changeActiveUser"
              >
                <Option v-for="user of judgeUsers" :key="user.judgeId" :value="user.judgeId">{{
                  user.realName
                }}</Option>
              </Select>
            </div>
            <div class="form-item">
              <span class="form-item-label">分数范围</span>
              <span class="form-item-content">
                <InputNumber
                  v-model="minScore"
                  class="input-number"
                  size="small"
                  :min="0"
                  :max="fullScore"
                  :placeholder="inputMinScorePlaceholder"
                  @on-change="changeListParams"
                ></InputNumber>
                <span class="text">分至</span>
                <InputNumber
                  v-model="maxScore"
                  class="input-number"
                  size="small"
                  :min="0"
                  :max="fullScore"
                  :placeholder="inputMaxScorePlaceholder"
                  @on-change="changeListParams"
                ></InputNumber>
                <span class="text">分</span>
              </span>
            </div>
            <div class="form-item">
              <span class="form-item-label">给分时间</span>
              <span class="form-item-content">
                <DatePicker
                  v-model="beginTime"
                  size="small"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  placeholder="起始时间"
                  style="width: 152px"
                  :options="beginTimeOption"
                  @on-change="changeListParams"
                />
                <span>至</span>
                <DatePicker
                  v-model="endTime"
                  size="small"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  placeholder="终止时间"
                  style="width: 152px"
                  :options="endTimeOption"
                  @on-change="changeListParams"
                />
              </span>
            </div>
          </div>
        </div>

        <div class="header-right">
          <ButtonGroup size="small">
            <Button size="small" type="default" :disabled="!hasPreviousPaper" @click="goPreviousPaper">
              <Icon type="md-arrow-back"></Icon>
              上一份
            </Button>
            <Button size="small" type="default" :disabled="!hasNextPaper" @click="goNextPaper">
              <Icon type="md-arrow-forward"></Icon>
              下一份
            </Button>
          </ButtonGroup>
          <span class="btn-close" @click="closeModal"><Icon type="ios-close" /></span>
        </div>
      </div>
    </template>

    <div class="modal-body">
      <div class="section-list section-list-cannot-spotmark">
        <div class="table">
          <Table
            :columns="tableColumns"
            :data="markHistoryList"
            :row-class-name="getTableRowClassName"
            :max-height="tableMaxHeight"
            border
            @on-row-click="handleTableRowClick"
          />
        </div>
        <div class="pager">
          <span class="total-info">共 {{ total }} 条</span>
          <Page :model-value="currentPage" :total="total" simple @on-change="changePage" />
        </div>
      </div>

      <ScoreResult
        v-if="activePaper"
        :task-detail="currentActivity"
        :judge-review-detail="activePaper"
        :score-items="scoreItemsMap[activePaper.categoryId]"
        :is-multiple-score="isMultipleScore"
        :full-score="fullScore"
      />
    </div>
  </Modal>
</template>

<style lang="scss">
  .modal-monitor-score-history {
    .ivu-modal-header {
      @include flex(row, flex-start, center);
      height: 61px;
      padding-top: 4px;
      padding-bottom: 4px;
    }

    .ivu-modal-body {
      top: 61px;
    }
  }
</style>

<style lang="scss" scoped>
  .modal-header {
    @include flex(row, space-between, center);
    width: 100%;

    .header-left {
      @include flex(row, flex-start, center);

      .title {
        flex-shrink: 0;
        margin-right: 30px;
        font-size: $font-size-medium-x;
      }

      .form {
        @include flex(row, flex-start, center);
        flex-wrap: wrap;

        .form-item {
          @include flex(row, flex-start, center);
          margin-top: 2px;
          margin-bottom: 2px;
          margin-left: 20px;

          .form-item-label {
            flex-shrink: 0;
            margin-right: 5px;
          }

          .selector-block {
            flex-shrink: 0;
            width: 140px;
          }

          .selector-user {
            flex-shrink: 0;
            width: 100px;
          }
        }
      }
    }

    .header-right {
      @include flex(row, flex-end, center);
      flex-shrink: 0;

      .btn-close {
        margin-left: 30px;
        color: #999;
        font-size: 31px;
        transition: color ease 0.2s;
      }

      .btn-close:hover {
        color: #444;
        cursor: pointer;
      }
    }
  }

  .modal-body {
    @include flex(row, flex-start, flex-start);

    .section-list {
      position: sticky;
      top: 0;
      flex-grow: 0;
      flex-shrink: 0;

      .pager {
        @include flex(row, flex-end, center);
        height: 30px;
        margin-top: 10px;

        .total-info {
          margin-right: 6px;
        }

        :deep(.ivu-page-simple .ivu-page-simple-pager input) {
          width: 46px;
        }
      }

      :deep(.row-active td) {
        background-color: #ebf7ff;
      }
    }

    .section-list-cannot-spotmark {
      width: 382px;
    }

    .section-list-can-spotmark {
      width: 400px;
    }

    .section-paper {
      position: relative;
      flex-grow: 1;
      width: 0;
      margin-left: 10px;
      overflow-y: auto;

      img {
        width: 100%;
      }

      .box-paper-top {
        @include flex(row, space-between, center);
        margin-bottom: 10px;

        .box-action {
          width: 300px;
          white-space: nowrap;
          text-align: right;
        }
      }

      .mark {
        margin-bottom: 4px;
        padding-left: 20px;
        color: red;
        font-size: $font-size-medium-x;

        .number {
          font-weight: bold;
          font-size: $font-size-large;
        }

        .spot-check-point {
          color: $color-primary;

          .title {
            margin-right: 5px;
          }

          .score {
            font-weight: bold;
            font-size: $font-size-large;
          }
        }
      }
    }

    .section-mark-panel {
      @include flex(column, flex-start, stretch);
      position: relative;
      flex-grow: 0;
      flex-shrink: 0;
      width: 300px;
      height: calc(100vh - 100px);
      margin-left: 10px;
      padding: 15px;
      border: 1px solid $color-border;

      .mask {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
      }

      .header {
        flex-grow: 0;
        height: 45px;
        padding-bottom: 10px;
        border-bottom: 1px solid $color-border;

        .row {
          @include flex(row, space-between, center);

          .title {
            color: $color-primary;
            font-size: $font-size-medium-xs;
          }
        }
      }

      .content {
        flex-grow: 1;
        height: 0;
        padding: 10px 0;
        overflow-y: auto;

        .question-marking {
          margin: 15px 0;

          .row-1 {
            @include flex(row, flex-start, center);

            .question-name {
              min-width: 2.5em;
              margin-right: 0.5em;
              font-weight: bold;
            }

            .score {
              @include flex(row, flex-start, center);
              flex-grow: 0;
              flex-shrink: 0;

              .input-score {
                width: 80px;
              }

              .btn-score {
                margin-left: 5px;
              }

              .btn-score-full {
                color: $color-success;
              }

              .btn-score-zero {
                color: $color-error;
              }
            }
          }

          .row-2 {
            padding-top: 0.3em;
            padding-left: 3em;
          }

          .key {
            position: relative;
            top: 0;
            display: inline-block;
            width: 40px;
            height: 40px;
            margin-top: 8px;
            margin-right: 8px;

            /*
              width: 30px;
              height: 30px;
              margin-top: 5px;
              margin-right: 5px;
              line-height: 30px;
            */
            border-radius: 3px;
            color: white;
            font-weight: bold;
            line-height: 40px;
            text-align: center;
            background-color: $color-primary;
            cursor: pointer;
            transition: top 0.2s ease;
            user-select: none;

            &:hover {
              background-color: red;
            }

            &:active {
              top: 5px;
            }
          }
        }
      }

      .footer {
        @include flex(row, space-between, center);
        flex-grow: 0;
        height: 43px;

        padding-top: 10px;
        border-top: 1px solid $color-border;
        user-select: none;

        .total-score {
          .title {
            display: inline-block;
            width: 3em;
            font-weight: bold;
            line-height: 20px;
          }

          .score-value {
            color: #3bf1df;
            font-weight: bold;
          }
        }
      }
    }
  }

  .typical-paper-sign {
    position: absolute;
    top: calc(80px + 1em);
    right: 1em;
    z-index: 3;

    max-width: 130px;
    border: 2px solid red;
    color: red;
    opacity: 0.6;

    .typical-sign-bar {
      padding: 2px 6px;
      font-weight: bold;
      font-size: $font-size-medium-x;
      user-select: none;
    }

    .typical-comment {
      padding: 0 6px 2px 6px;
      font-size: $font-size-medium;
      text-indent: 2em;
      word-wrap: break-word;
      word-break: break-all;
    }

    .rect-remove {
      position: absolute;
      top: -15px;
      right: -15px;
      z-index: 2;
      display: none;
      color: red;
      cursor: pointer;

      .rect-remove-background {
        position: absolute;
        top: 7px;
        left: 7px;
        z-index: -1;
        width: 16px;
        height: 16px;
        background: white;
      }
    }

    &:hover .rect-remove {
      display: block;
    }

    &:hover {
      background-color: white;
      opacity: 1;
    }
  }
</style>
