<script>
  export default {
    props: {
      modelValue: {
        type: Boolean,
        default: false,
      },
    },

    emits: ['update:modelValue'],

    computed: {
      userInfo() {
        return this.$store.getters['user/info']
      },
    },

    methods: {
      closeModal() {
        this.$emit('update:modelValue', false)
      },

      handleModalVisibilityStatusChange(isVisible) {
        if (!isVisible) {
          this.closeModal()
        }
      },
    },
  }
</script>

<template>
  <Modal
    :model-value="modelValue"
    width="350"
    title="个人信息"
    footer-hide
    mask-closable
    @on-visible-change="handleModalVisibilityStatusChange"
  >
    <div v-if="userInfo" class="modal-content-panel">
      <div class="info-item">
        <div class="item-label">账号</div>
        <div class="item-content">{{ userInfo.userName || '' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">姓名</div>
        <div class="item-content">{{ userInfo.realName || '' }}</div>
      </div>
      <div class="info-item">
        <div class="item-label">学校</div>
        <div class="item-content">{{ userInfo.schoolName || '' }}</div>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped>
  .modal-content-panel {
    padding: 0 10px;

    .info-item {
      @include flex(row, flex-start, center);

      &:not(:first-child) {
        margin-top: 12px;
      }

      .item-label {
        user-select: none;
      }

      .item-content {
        padding-left: 2em;
        line-height: 2;
      }
    }
  }
</style>
