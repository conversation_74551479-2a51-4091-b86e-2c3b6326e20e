<template>
  <div class="ingestion-step-upload">
    <div class="upload-info">
      <div class="file-type">
        <span>上传文件类型：</span>
        <RadioGroup v-if="canChangeFileType" :model-value="currentFileType" @on-change="changeFileType">
          <Radio v-for="t in fileTypes" :key="t.id" :label="t.id">{{ t.name }}</Radio>
        </RadioGroup>
        <span v-else>{{ currentFileTypeName }}</span>
      </div>
    </div>
    <div class="upload-area"></div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useCoachBookIngestionStore } from '@/store/qlib/coach_book_ingestion'

  import { apiChangeCoachBookIngestionFileType } from '@/api/qlib/coach_book_ingestion'

  const ingestionStore = useCoachBookIngestionStore()

  const fileTypes = [
    {
      id: 'pdf',
      name: 'PDF',
    },
    {
      id: 'image',
      name: '图片',
    },
  ]

  const currentFileType = computed(() => {
    return ingestionStore.ingestionInfo?.fileType
  })

  const currentFileTypeName = computed(() => {
    return fileTypes.find(t => t.id == currentFileType.value)?.name || ''
  })

  const canChangeFileType = computed(() => {
    let filePath = ingestionStore.ingestionInfo?.filePath
    return !filePath || filePath.length == 0
  })

  async function changeFileType(fileType) {
    ingestionStore.ingestionInfo.fileType = fileType
    await apiChangeCoachBookIngestionFileType({
      ingestionId: ingestionStore.ingestionInfo.id,
      fileType,
    })
    await ingestionStore.refreshIngestionInfo()
  }
</script>

<style lang="scss" scoped>
  .upload-info {
    @include flex(row, flex-start, center);
    height: 32px;
  }
</style>
