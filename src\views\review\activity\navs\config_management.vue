<template>
  <div class="page-review-config">
    <div class="section-header">
      <div class="box-tab">
        <RadioGroup v-model="activeTab" type="button" button-style="solid" @on-change="onTabChange">
          <Radio v-for="tab of tabList" :key="tab.name" :label="tab.name">{{ tab.label }}</Radio>
        </RadioGroup>
      </div>
    </div>
    <div v-if="categories.length > 1 && activeTab === 'score-config'" class="box-category">
      <span class="label">评审类别：</span>
      <RadioGroup v-model="currentCategory" type="button" button-style="solid" @on-change="onTabChange">
        <Radio v-for="c of categories" :key="c.id" :label="c.id">{{ c.name }}</Radio>
      </RadioGroup>
    </div>
    <div class="section-content">
      <ReviewConfig v-show="activeTab === 'review-config'" />
      <ScoreConfig v-show="activeTab === 'score-config'" :current-category="currentCategory" />
    </div>
  </div>
</template>

<script>
  import ReviewConfig from '../../components/review_config.vue'
  import ScoreConfig from '../../components/score_config.vue'

  export default {
    components: {
      ReviewConfig,
      ScoreConfig,
    },
    data() {
      return {
        tabList: [
          {
            label: '评审设置',
            name: 'review-config',
          },
          {
            label: '评分设置',
            name: 'score-config',
          },
        ],
        activeTab: 'review-config',
        currentCategory: '',
      }
    },
    computed: {
      categories() {
        return this.$store.getters['review/categories'] || []
      },
    },
    watch: {
      categories: {
        handler: function (val) {
          if (Array.isArray(val) && val.length) {
            this.currentCategory = val[0].id
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.fetchReviewConfig()
    },
    methods: {
      onTabChange() {},
      fetchReviewConfig() {
        this.$store.dispatch('review/fetchReviewConfig')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .page-review-config {
    padding: 20px;
    background-color: #fff;

    .section-header {
      @include flex(row, space-between, center);
      margin-bottom: 16px;
    }

    .box-category {
      @include flex(row, flex-start, center);
      margin-bottom: 15px;

      .label {
        margin-right: 4px;
      }
    }
  }
</style>
