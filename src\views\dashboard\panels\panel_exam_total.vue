<template>
  <div class="panel-exam-total">
    <div class="header">
      <div class="title">学校对比</div>
      <div class="box-selector">
        <Select
          :model-value="currentExam?.examId"
          filterable
          size="small"
          style="width: 500px"
          @on-change="onChangeExam"
        >
          <Option v-for="item in examList" :key="item.examId" :value="item.examId">{{ item.examName }}</Option>
        </Select>
      </div>
    </div>
    <div class="section-chart">
      <div class="box-chart-left">
        <div v-if="schoolCompareData.length" id="chart-bar" class="chart-bar"></div>
        <div v-else class="no-data">暂无数据</div>
      </div>
      <div class="box-chart-right">
        <div v-if="subjectCompareData.length" id="chart-radar" class="chart-radar"></div>
        <div v-else class="no-data">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import echarts from '@/utils/echarts'
  import { useDashboardStore } from '../../../store/dashboard'
  import { roundNumber } from '@/utils/math'
  import { UUID_ZERO } from '@/const/string'
  import { apiGetReportExams, apiGetSchoolCompare, apiGetSubjectCompare } from '@/api/report'

  const dashboardStore = useDashboardStore()
  const ReportStatus = {
    UnGenerate: 0,
    Generating: 1,
    GenerateSucceeded: 2,
    GenerateFailed: 3,
    WaitForGenerate: 4,
    WaitForSubject: 5,
  }
  const targets = [
    {
      value: 'avg',
      name: '平均分',
    },
  ]
  let chartBar = null
  let chartRadar = null
  const activeTarget = ref('avg')
  const examList = ref([])
  const schoolCompareData = ref([])
  const subjectCompareData = ref([])

  const currentSemester = computed(() => {
    return dashboardStore.currentSemester
  })
  const currentTerm = computed(() => {
    return dashboardStore.currentTerm
  })
  const currentSchool = computed(() => {
    return dashboardStore.currentSchool
  })
  const currentExam = computed(() => {
    return dashboardStore.currentExam
  })
  const barOptions = computed(() => {
    if (!schoolCompareData.value.length) return null
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '0%',
        right: '12%',
        bottom: '4%',
        top: '4%',
        containLabel: true,
      },
      dataZoom: [
        // 滑动条
        {
          type: 'slider',
          show: true,
          yAxisIndex: 0,
          width: 16,
          right: '2%',
          startValue: 0,
          endValue: 9,
          // 滑动时坐标轴范围不变
          filterMode: 'empty',
          showDetail: false,
        },
        // 内置，支持滚轮缩放
        {
          type: 'inside',
          yAxisIndex: 0,
          startValue: 0,
          endValue: 9,
          filterMode: 'empty',
          zoomOnMouseWheel: false,
          moveOnMouseWheel: true,
          moveOnMouseMove: true,
        },
      ],
      yAxis: {
        type: 'category',
        axisLabel: { color: '#FFFFFF', fontSize: 12 },
        axisLine: { show: false },
        axisTick: { show: false },
        data: schoolCompareData.value.map(item => {
          if (item.schoolId === UUID_ZERO) {
            return `全体学校 (${schoolCompareData.value.length}所)`
          }
          return item.schoolName
        }),
        inverse: true,
      },
      xAxis: {
        name: '平均分',
        type: 'value',
        axisLabel: {
          color: () => {
            return '#fff'
          },
        },
        splitLine: { show: true, lineStyle: { color: '#3C4061' } },
        position: 'top',
        nameTextStyle: {
          verticalAlign: 'bottom',
          color: '#7db7db',
          lineHeight: 28,
        },
      },
      series: [
        {
          data: schoolCompareData.value.map(item => item.avgScore),
          type: 'bar',
          barWidth: '40%',
          barMaxWidth: 20,
          itemStyle: {
            borderRadius: [0, 20, 20, 0],
            color: '#05d1bc',
          },
        },
      ],
    }
  })
  const radarOptions = computed(() => {
    if (!subjectCompareData.value.length) return null
    let compareData = subjectCompareData.value.filter(item => item.examSubjId !== UUID_ZERO)

    return {
      // legend: {
      //   data: ['平均分'],
      // },
      tooltip: {
        show: true,
        confine: true,
      },
      radar: {
        radius: '50%',
        indicator: compareData.map(item => ({
          name: item.subjName,
          max: item.totalFullScore,
          min: 0,
        })),
      },
      series: [
        {
          type: 'radar',
          symbolSize: 5,
          itemStyle: {
            color: '#05d1bc',
          },
          areaStyle: {
            color: '#05d1bc',
          },
          lineStyle: {
            color: '#05d1bc',
          },
          data: [
            {
              value: compareData.map(item => item.avgScore),
              name: '平均分',
            },
          ],
        },
      ],
    }
  })

  watch([currentSemester, currentTerm, currentSchool], () => {
    if (currentSemester.value && currentSchool.value) {
      loadExams()
    }
  })

  watch(currentExam, newVal => {
    schoolCompareData.value = []
    subjectCompareData.value = []

    if (newVal) {
      loadCompare()
    }
  })

  function onChangeExam(val) {
    const theExam = examList.value.find(item => item.examId === val)
    dashboardStore.currentExam = theExam
  }

  function onChangeTarget(val) {
    activeTarget.value = val
  }

  async function loadExams() {
    const res = await apiGetReportExams({
      semesterId: currentSemester.value,
      term: currentTerm.value,
      pageSize: 1000,
      currentPage: 1,
    })

    let list = (res?.exams || []).filter(item => {
      return item.templates.some(
        t => t.sign === ReportStatus.GenerateSucceeded && t.hasViewReportPermission && t.isShow
      )
    })
    examList.value = list
    dashboardStore.examsForSelect = list
    dashboardStore.currentExam = list.length ? list[0] : null
  }

  async function loadCompare() {
    const templates = (currentExam.value?.templates || []).filter(
      t => t.hasViewReportPermission && t.sign === ReportStatus.GenerateSucceeded && t.isShow
    )
    const defaultTemplate = templates.find(t => t.isDefault) || (templates.length && templates[0])
    const templateId = defaultTemplate ? defaultTemplate.templateId : undefined

    if (!templateId) {
      schoolCompareData.value = []
      subjectCompareData.value = []
      return
    }

    const res = await Promise.all([
      apiGetSchoolCompare({
        examId: currentExam.value?.examId,
        reportName: 'union_schCompare',
        templateId,
      }),
      apiGetSubjectCompare({
        examId: currentExam.value?.examId,
        reportName: 'union_overview',
        templateId,
      }),
    ])

    if (res && res.length) {
      schoolCompareData.value = res[0].map(item => ({
        ...item,
        avgScore: roundNumber(item.avgScore, 2),
      }))
      subjectCompareData.value = res[1].map(item => ({
        ...item,
        avgScore: roundNumber(item.avgScore, 2),
      }))

      nextTick(() => {
        drawBar()
        drawRadar()
      })
    }
  }

  const drawBar = () => {
    if (barOptions.value) {
      if (chartBar) {
        chartBar.dispose()
      }
      chartBar = echarts.init(document.getElementById('chart-bar'))
      chartBar.setOption(barOptions.value)
    }
  }

  const drawRadar = () => {
    if (radarOptions.value) {
      if (chartRadar) {
        chartRadar.dispose()
      }
      chartRadar = echarts.init(document.getElementById('chart-radar'))
      chartRadar.setOption(radarOptions.value)
    }
  }
</script>

<style lang="scss" scoped>
  .panel-exam-total {
    @include flex(column, flex-start, flex-start);
    padding: 0 10px;

    :deep(.ivu-select-selection) {
      color: white;
      background-color: transparent;

      .ivu-select-arrow {
        color: #fff;
      }
    }

    :deep(.ivu-select-input) {
      color: #fff;
    }

    :deep(.ivu-select-placeholder) {
      color: #fff;
    }

    .header {
      @include flex(row, center, center);
      position: relative;
      flex-shrink: 0;
      width: 100%;
      padding: 10px 0;

      .title {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 10px;
        height: 18px;
        margin: auto;
        color: #fff;
        font-size: 16px;
        line-height: 18px;
      }
    }

    .active-item {
      color: #fff;
      font-size: 14px;
    }

    .btn-change {
      color: #fff;
      font-size: 14px;
    }

    .section-chart {
      @include flex(row, flex-start, stretch);
      flex: 1 1 180px;
      width: 100%;

      .box-chart-left {
        width: 70%;
      }

      .box-chart-right {
        width: 30%;
      }

      .chart-bar,
      .chart-radar {
        width: 100%;
        height: 100%;
      }

      .no-data {
        @include flex(row, center, center);
        width: 100%;
        height: 180px;
        color: #fff;
        font-size: 14px;
      }
    }
  }
</style>
