<template>
  <div class="container-coach-report-knowledge">
    <template v-if="tableData.length">
      <Alert
        ><div class="alert">
          <ul>
            <li>
              本次作业共考核 <span class="bolder-text" style="color: #5cadff">{{ tableData.length }}</span> 个知识点
            </li>
            <li v-if="highRateKnowledges.length">
              得分率 <span class="bolder-text" style="color: #2cd58e">80%以上</span> 的知识点有
              <span class="bolder-text" style="color: #2cd58e">{{ highRateKnowledges.length }}</span> 个, 分别是：
              <span class="bolder-text">{{ highRateKnowledges.map(item => item.knowName).join('、') }}</span
              >。
            </li>
            <li v-if="lowRateKnowledges.length">
              得分率 <span class="bolder-text text-color-warning">30%以下</span> 的知识点有
              <span class="bolder-text text-color-warning">{{ lowRateKnowledges.length }}</span> 个, 分别是：
              <span class="bolder-text">{{ lowRateKnowledges.map(item => item.knowName).join('、') }}</span
              >。
            </li>
          </ul>
        </div></Alert
      >
      <Table :columns="tableColumns" :data="tableData"></Table>
    </template>
    <NoData v-else></NoData>
  </div>
</template>

<script>
  import NoData from '@/components/no_data'
  import { ElProgress } from 'element-plus'
  import 'element-plus/es/components/progress/style/css'
  import { Tooltip } from 'view-ui-plus'

  import { deepCopy } from '@/utils/object'
  import { roundNumber } from '@/utils/math'

  export default {
    components: {
      NoData,
    },

    props: {
      classDetail: {
        type: Object,
        default: null,
      },
    },

    data() {
      return {
        tableColumns: [
          {
            title: '知识点',
            key: 'knowName',
          },
          {
            title: '对应题目',
            render: (h, params) =>
              h('span', {}, params.row.quesList && params.row.quesList.length ? params.row.quesList.join('、') : '-'),
          },
          {
            title: '班级得分率',
            render: (h, params) => {
              const Percentage = Math.min(100, roundNumber(params.row.scoreRate * 100, 1))
              const ClassPaperScoreRate =
                this.classDetail && this.classDetail.fullScore
                  ? roundNumber(((this.classDetail.avgScore || 0) / this.classDetail.fullScore) * 100, 1)
                  : 100

              return h(ElProgress, {
                'stroke-width': 10,
                percentage: Percentage,
                color: Percentage >= ClassPaperScoreRate ? '#2cd58e' : '#ffe96b',
              })
            },
          },
          {
            title: '低分前5名',
            render: (h, params) => {
              const SortedQuestionStudents = (
                params.row.students && params.row.students.length ? deepCopy(params.row.students) : []
              ).sort((a, b) => (a.score || 0) - (b.score || 0))
              let lowestScoreStudents = []

              for (let i = 0; i < SortedQuestionStudents.length; i++) {
                if (
                  lowestScoreStudents.length < 5 ||
                  (SortedQuestionStudents[i].score || 0) ===
                    (lowestScoreStudents[lowestScoreStudents.length - 1].score || 0)
                ) {
                  lowestScoreStudents.push(SortedQuestionStudents[i])
                } else {
                  break
                }
              }

              return h(
                Tooltip,
                {
                  'max-width': 800,
                  content: lowestScoreStudents
                    .map(item => item.studentName + '：' + (item.score || 0) + '分\n')
                    .join(''),
                  transfer: true,
                },
                () =>
                  lowestScoreStudents.length > 5
                    ? lowestScoreStudents
                        .slice(0, 5)
                        .map(student => student.studentName)
                        .join('、') + '...'
                    : lowestScoreStudents.map(student => student.studentName).join('、')
              )
            },
          },
        ],
        tableMaxHeight: window.innerHeight - 280,
      }
    },

    computed: {
      tableData() {
        return (this.classDetail && this.classDetail.knowScoreList) || []
      },

      highRateKnowledges() {
        return this.tableData.filter(item => item.scoreRate > 0.8)
      },

      lowRateKnowledges() {
        return this.tableData.filter(item => item.scoreRate < 0.3)
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep(.ivu-alert) {
    margin-bottom: 16px;
    padding-right: 20px;
    padding-left: 34px;
  }

  .alert {
    margin: 16px 0;
    line-height: 1.6;

    .bolder-text {
      font-weight: bolder;
    }

    .text-color-warning {
      color: $color-warning;
    }
  }
</style>
