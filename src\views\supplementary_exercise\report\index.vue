<template>
  <div class="container-supplementary-exercise-report">
    <LayoutHeader class="section-header" sticky background border-bottom-shadow>
      <div class="header-left">
        <TextButton type="primary" icon="ios-arrow-back" title="返回" @click="back"></TextButton>
        <div class="title">{{ title }}</div>
      </div>
      <div class="header-right">
        <TextButton v-if="!firstLoading" type="primary" icon="md-refresh" class="btn-refresh" @click="refresh"
          >刷新</TextButton
        >
        <Divider v-if="showBtnFinish || showBtnReStart || showTextDoing" type="vertical" />
        <TextButton v-if="showBtnFinish" type="success" icon="md-checkmark-circle" @click="handleTestFinishClick"
          >完成测练</TextButton
        >
        <TextButton v-if="showBtnReStart" type="warning" icon="md-refresh" @click="handleTestRestartClick"
          >重启测练</TextButton
        >
        <span v-if="showTextDoing">成绩生成中...</span>
      </div>
    </LayoutHeader>

    <div v-if="!firstLoading" class="section-neck">
      <Tabs v-model="activeTabName" class="tabs" :animated="false">
        <TabPane v-for="t in tabs" :key="t.name" :label="t.name" :name="t.name"></TabPane>
      </Tabs>
      <div class="float-right">
        <div v-if="classDetail && !classDetail.existsExceptionOrUnUpload" class="scan-stats">
          共<span class="number">{{ classDetail.total }}</span
          >人，已扫<span class="number" :class="{ warning: classDetail.scanned < classDetail.total }">{{
            classDetail.scanned
          }}</span
          >人
        </div>
        <Select class="class-selector" :model-value="activeClassId" @on-change="changeClass">
          <Option v-for="cls in classList" :key="cls.classId" :value="cls.classId">{{ cls.className }}</Option>
        </Select>
      </div>
    </div>

    <template v-if="classDetail">
      <div v-if="classDetail.existsExceptionOrUnUpload" class="error">
        <div>{{ classDetail.className }}存在扫描异常未处理</div>
        <Button class="btn-to-scan-exception" type="primary" @click="toScanExceptionPage">立即处理</Button>
      </div>
      <div v-else-if="classDetail.existsMarkUnFinish" class="error">
        <div>{{ classDetail.className }}尚有答卷未批改</div>
        <Button class="btn-to-scan-exception" type="primary" @click="toMarkPage">前往批改</Button>
      </div>
      <template v-else-if="classDetail.scanned > 0">
        <KeepAlive>
          <Component
            :is="activeTab.component"
            :exam-info="examInfo"
            :class-detail="classDetail"
            :tab-question-initial-question="tabPaperInitialQuestion"
            class="section-content"
            @to-question="toQuestion"
          ></Component
        ></KeepAlive>
      </template>

      <div v-else class="empty">{{ classDetail.className }}尚未扫描本教辅作业</div>
    </template>
    <div v-else-if="!loading" class="empty">暂无数据</div>
  </div>
</template>

<script>
  import LayoutHeader from '@/components/layout_header.vue'
  import TabGeneral from './components/general.vue'
  import TabStudent from './components/student.vue'
  import TabPaper from './components/paper.vue'
  import TabKnowledge from './components/knowledge.vue'
  import TabScore from './components/score.vue'

  import { apiGetCoachHomeworkInfo } from '@/api/emarking/coach_homework'
  import { apiGetCoachHomeworkClassDetail } from '@/api/report/coach_homework'
  import { apiGetStudentGroup } from '@/api/user/student'
  import { apiFinishMarking, apiReStartMarking } from '@/api/emarking'

  import { backOrDefault } from '@/utils/router'
  import Role from '@/enum/user/role'
  import SubjectStatus from '@/enum/emarking/subject_status'
  import ExamType from '@/enum/emarking/exam_type'

  export default {
    components: {
      LayoutHeader,
    },
    data() {
      return {
        // 加载中
        loading: false,
        // 加载次数
        loadTimes: 0,
        // 项目信息
        examInfo: null,
        // 当前标签页名称
        activeTabName: '作业概览',
        // 选中班级
        activeClassId: '',
        // 选中班级数据
        classDetail: null,
        // 试卷讲评初始题目
        tabPaperInitialQuestion: null,
        btnViewText: '',
      }
    },
    computed: {
      examId() {
        return this.$route.params.examId
      },
      examSubjectId() {
        return (this.examInfo && this.examInfo.examSubjectId) || ''
      },
      title() {
        if (!this.examInfo) {
          return ''
        }

        let examType = this.examInfo.examType === ExamType.ClassPractise.id ? 'AI测练分析' : '教辅作业分析'
        return `${examType}——${this.examInfo.examName}`
      },
      isAdministrator() {
        return (this.$store.getters['user/info'].roles || []).some(role =>
          [Role.SystemAdministrator.id, Role.SchoolAdministrator.id, Role.Agent.id].includes(role)
        )
      },
      showBtnFinish() {
        let enableStatus = [
          SubjectStatus.Started.id,
          SubjectStatus.Suspended.id,
          SubjectStatus.Finished.id,
          SubjectStatus.ReportFailed.id,
        ]

        if (!this.examInfo?.needMark) {
          enableStatus.unshift(SubjectStatus.UnDefined.id)
        }

        return enableStatus.includes(this.examInfo?.subjectStatus) && this.isAdministrator
      },
      showBtnReStart() {
        return this.examInfo?.subjectStatus === SubjectStatus.ReportSucceeded.id && this.isAdministrator
      },
      showTextDoing() {
        return [SubjectStatus.ReportWaiting.id, SubjectStatus.Reporting.id].includes(this.examInfo?.subjectStatus)
      },
      // 首次进入页面并加载中
      firstLoading() {
        return this.loadTimes <= 1 && this.loading
      },
      // 所有标签页
      tabs() {
        return [
          {
            name: '作业概览',
            component: TabGeneral,
          },
          {
            name: '学生分析',
            component: TabStudent,
          },
          {
            name: '作业讲评',
            component: TabPaper,
          },
          {
            name: '知识点分析',
            component: TabKnowledge,
          },
          {
            name: '成绩单',
            component: TabScore,
          },
        ]
      },
      // 当前标签页
      activeTab() {
        return this.tabs.find(t => t.name == this.activeTabName)
      },
      // 可选班级
      classList() {
        if (this.examInfo == null) {
          return []
        }
        return this.examInfo.classList || []
      },
    },
    created() {
      this.loadData()
    },
    methods: {
      async loadData() {
        let examId = this.$route.params.examId
        if (!examId) {
          this.$Message.error('参数错误')
          return
        }
        if (this.loading) {
          return
        }
        try {
          this.loading = true
          this.loadTimes++
          if (this.firstLoading) {
            this.$Loading.start()
          } else {
            this.$TransparentSpin.show()
          }

          this.examInfo = await apiGetCoachHomeworkInfo(examId)
          await this.$store.dispatch('practise/getPaperContent', {
            examId: this.examInfo.examId,
            examSubjectId: this.examInfo.examSubjectId,
          })
          await this.changeClass(this.activeClassId)

          if (this.firstLoading) {
            this.$Loading.finish()
          }
          return true
        } catch (ex) {
          if (this.firstLoading) {
            this.$Loading.error()
          }
          this.examInfo = null
          throw ex
        } finally {
          this.loading = false
          if (!this.firstLoading) {
            this.$TransparentSpin.hide()
          }
        }
      },
      async getClassDetail() {
        if (!this.activeClassId) {
          this.classDetail = null
          return
        }
        let classId = this.activeClassId
        return Promise.all([
          apiGetCoachHomeworkClassDetail({
            examSubjectId: this.examSubjectId,
            classId: this.activeClassId,
          }),
          apiGetStudentGroup({
            classId: this.activeClassId,
            subjectId: this.examInfo.subjectId,
          }),
        ])
          .then(([classDetail, stuGroup]) => {
            if (classId != this.activeClassId) {
              this.classDetail = null
              return
            }
            let activeClass = this.classList.find(cls => cls.classId === classId)
            classDetail.classId = (activeClass && activeClass.classId) || ''
            classDetail.className = (activeClass && activeClass.className) || ''
            classDetail.studentGroups = stuGroup
            this.classDetail = classDetail
          })
          .catch(ex => {
            this.classDetail = null
            throw ex
          })
      },
      async changeClass(classId) {
        if (this.classList.length == 0) {
          classId = ''
        } else if (this.classList.every(cls => cls.classId != classId)) {
          classId = this.classList[0].classId
        }
        this.activeClassId = classId
        await this.getClassDetail()
      },
      async refresh() {
        let loadSuccessfully = await this.loadData()
        if (loadSuccessfully) {
          this.$Message.success('已刷新')
        }
      },
      toQuestion(q) {
        this.tabPaperInitialQuestion = q
        this.activeTabName = '作业讲评'
        this.$nextTick().then(() => {
          this.tabPaperInitialQuestion = null
        })
      },
      toScanExceptionPage() {
        let route = this.$router.resolve({
          name: 'scan-answerSheet-exception',
          params: {
            examId: this.examId,
            examSubjectId: this.examSubjectId,
          },
        })
        window.open(route.href)
      },
      toMarkPage() {
        this.$router.push({
          name: 'emarking-subject-index',
          params: {
            examSubjectId: this.examSubjectId,
            isFromHomePage: 0,
          },
        })
      },
      back() {
        backOrDefault(this.$router, {
          name: 'supplementaryExerciseHome',
        })
      },
      handleTestFinishClick() {
        this.$Modal.confirm({
          title: '注意',
          content: '确定要完成测练 ' + this.examInfo?.examName + ' ？',
          onOk: () => {
            this.$Spin.show({
              render: h => {
                return h(
                  'div',
                  {
                    style: {
                      fontSize: '24px',
                    },
                  },
                  () => '正在完成评卷'
                )
              },
            })

            apiFinishMarking({
              examSubjectId: this.examInfo?.examSubjectId,
              examId: this.examInfo?.examId,
            })
              .then(() => {
                setTimeout(() => {
                  this.$Message.success({
                    duration: 2,
                    content: '已完成测练',
                  })
                }, 600)
              })
              .catch(err => {
                setTimeout(() => {
                  this.$Modal.error({
                    title: '完成测练失败',
                    content: (err && err.msg) || '',
                    width: 800,
                  })
                }, 600)
              })
              .finally(() => {
                this.$Spin.hide()
                this.loadData()
              })
          },
        })
      },
      handleTestRestartClick() {
        this.$Modal.confirm({
          title: '注意',
          content: '确定要重启测练 ' + this.examInfo?.examName + ' ？',
          onOk: () => {
            apiReStartMarking({
              examSubjectId: this.examInfo?.examSubjectId,
              examId: this.examInfo?.examId,
            })
              .then(() => {
                this.$Message.success({
                  duration: 2,
                  content: '已重启测练',
                })
              })
              .finally(() => {
                this.loadData()
              })
          },
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .section-header {
    @include flex(row, space-between, center);
    z-index: 10;
    height: 60px;

    .header-left {
      @include flex(row, flex-start, center);
    }

    .header-right {
      font-size: 16px;
    }

    .title {
      margin-left: 8px;
      font-size: $font-size-large;
    }

    .btn-refresh {
      margin-left: auto;
    }
  }

  .section-neck {
    position: relative;
    margin-top: 10px;
    padding: 16px 24px 0 24px;
    background-color: white;

    :deep(.ivu-tabs-tab) {
      padding-bottom: 16px;
    }

    .float-right {
      @include flex(row, flex-end, center);
      position: absolute;
      top: 18px;
      right: 24px;

      .number {
        margin-right: 4px;
        margin-left: 4px;
        color: $color-primary;
        font-weight: bold;

        &.warning {
          color: $color-warning;
        }
      }

      .class-selector {
        width: 120px;
        margin-left: 32px;
      }
    }
  }

  .section-content {
    min-height: 500px;
    padding: 0 24px 24px 24px;
    background-color: white;
  }

  .error {
    min-height: 500px;
    padding-top: 150px;
    color: $color-error;
    font-size: 24px;
    text-align: center;
    background-color: white;

    .btn-to-scan-exception {
      margin-top: 20px;
    }
  }

  .empty {
    min-height: 500px;
    padding-top: 150px;
    color: $color-disabled;
    font-size: 24px;
    text-align: center;
    background-color: white;
  }
</style>
