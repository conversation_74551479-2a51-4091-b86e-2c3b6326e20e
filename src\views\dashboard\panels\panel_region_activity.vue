<template>
  <div class="panel-activity">
    <div class="header">
      <div class="header-left">
        <span class="title">最新动态</span>
        <div class="tabs">
          <span
            v-for="tab in tabList"
            :key="tab.value"
            :class="['tab-item', tab.value === activeTab ? 'active' : '']"
            @click="handleTabClick(tab.value)"
            >{{ tab.name }}</span
          >
        </div>
      </div>
      <div class="btn-more" @click="handleShowMore">更多>></div>
    </div>
    <div class="section-list">
      <template v-if="showNormalAndUnionExam">
        <div class="box-list-header">
          <div class="item-school">
            {{ activeTab === 'normal' ? '学校' : activeTab === 'union' ? '机构' : '学校/机构' }}
          </div>
          <div class="item-grade">年级</div>
          <div class="item-name">考试</div>
          <div class="item-time">创建时间</div>
        </div>
        <swiper
          v-if="examList.length"
          direction="vertical"
          :autoplay="{
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }"
          loop
          class="my-swiper"
          :speed="1000"
          slides-per-view="auto"
          :space-between="0"
          free-mode
          mousewheel-control
          keyboard-control
        >
          <swiper-slide v-for="item in examList" :key="item.examId" class="my-swiper-slide">
            <div class="list-item" :class="{ 'list-item-odd': item.isOdd }">
              <div class="item-school" :title="item.schoolName">{{ item.schoolName }}</div>
              <div class="item-grade" :title="item.grade?.name">{{ item.grade?.name }}</div>
              <div class="item-name" :title="item.examName">{{ item.examName }}</div>
              <div class="item-time" :title="item.createTimeTitle">{{ item.createTimeStr }}</div>
            </div>
          </swiper-slide>
        </swiper>
        <div v-else class="no-data">暂无数据</div>
      </template>
      <template v-else>
        <div class="box-list-header">
          <div class="item-school">学校/机构</div>
          <div class="item-name">考试</div>
          <div class="item-grade">在评人数</div>
          <div class="item-time">最近评卷</div>
        </div>
        <swiper
          v-if="examList.length"
          direction="vertical"
          :autoplay="{
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }"
          loop
          class="my-swiper"
          :speed="1000"
          slides-per-view="auto"
          :space-between="0"
          free-mode
          mousewheel-control
          keyboard-control
        >
          <swiper-slide v-for="item in examList" :key="item" class="my-swiper-slide">
            <div class="list-item" :class="{ 'list-item-odd': item.isOdd }">
              <div class="item-school" :title="item.schoolName">{{ item.schoolName }}</div>
              <div class="item-name" :title="item.examName">{{ item.examName }}</div>
              <div class="item-grade" :title="item.userCount">{{ item.userCount }}</div>
              <div class="item-time" :title="item.recentMarkingTimeTitle">{{ item.recentMarkingTimeStr }}</div>
            </div>
          </swiper-slide>
        </swiper>
        <div v-else class="no-data">暂无数据</div>
      </template>
    </div>

    <modal-recent-exam
      v-model="visibleModalExam"
      :tab-list="tabList"
      :active-tab="activeTab"
      :list="examList"
      @on-change-tab="handleTabClick"
    />
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue'
  import { Swiper, SwiperSlide } from 'swiper/vue'
  import SwiperCore from 'swiper/core'
  import { Autoplay } from 'swiper/modules'
  import 'swiper/css'
  import 'swiper/css/autoplay'
  import ModalRecentExam from '../components/modal_recent_exam.vue'
  import { useDashboardStore } from '../../../store/dashboard'
  import { formatDateTime } from '@/utils/date'
  import { apiGetRecentExamList, apiGetRecentMarkingExamList } from '@/api/emarking/exam'

  SwiperCore.use([Autoplay])

  const dashboardStore = useDashboardStore()
  const examScopes = {
    normal: 1,
    union: 2,
    scan: 3,
    mark: 4,
  }
  const topNum = 50
  const tabList = ref([
    {
      value: 'normal',
      name: '校考',
    },
    {
      value: 'union',
      name: '联考',
    },
    {
      value: 'mark',
      name: '最近24小时评卷',
    },
  ])
  const activeTab = ref('normal')
  const examList = ref([])
  const visibleModalExam = ref(false)

  const currentSchool = computed(() => {
    return dashboardStore.currentSchool
  })
  const showNormalAndUnionExam = computed(() => {
    return ['normal', 'union'].includes(activeTab.value)
  })

  watch(currentSchool, newVal => {
    examList.value = []

    if (newVal) {
      loadRecentExams()
    }
  })

  function handleTabClick(val) {
    activeTab.value = val
    loadRecentExams()
  }

  function handleShowMore() {
    visibleModalExam.value = true
  }

  async function loadRecentExams() {
    const res =
      activeTab.value !== 'mark'
        ? await apiGetRecentExamList({
            childSchoolId: currentSchool.value.schoolId !== '0' ? currentSchool.value.schoolId : undefined,
            examScope: examScopes[activeTab.value],
            topNum,
          })
        : await apiGetRecentMarkingExamList({
            childSchoolId: currentSchool.value.schoolId !== '0' ? currentSchool.value.schoolId : undefined,
          })

    examList.value = (res || []).map((item, idx) => ({
      ...item,
      isOdd: idx % 2 === 1,
      createTimeStr: formatDateTime(new Date(item.createTime), 'MM-DD HH:mm'),
      createTimeTitle: formatDateTime(new Date(item.createTime), 'YYYY-MM-DD HH:mm'),
      beginTimeStr: formatDateTime(new Date(item.beginTime), 'YYYY-MM-DD'),
      endTimeStr: formatDateTime(new Date(item.endTime), 'YYYY-MM-DD'),
      recentMarkingTimeStr: item.recentMarkingTime
        ? formatDateTime(new Date(item.recentMarkingTime), 'MM-DD HH:mm')
        : '',
      recentMarkingTimeTitle: item.recentMarkingTime
        ? formatDateTime(new Date(item.recentMarkingTime), 'YYYY-MM-DD HH:mm')
        : '',
    }))
  }
</script>

<style lang="scss" scoped>
  .panel-activity {
    @include flex(column, flex-start, flex-start);

    .no-data {
      @include flex(row, center, center);
      width: 100%;
      height: calc(100vh - 535px);
      color: #fff;
      font-size: 14px;
    }

    .header {
      @include flex(row, space-between, center);
      width: 100%;
      padding-top: 10px;
      padding-right: 10px;

      .header-left {
        @include flex(row, flex-start, center);
      }

      .btn-more {
        color: #fff;
        font-size: 12px;
        cursor: pointer;
      }

      .title {
        width: 90px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        background-color: rgba(49, 72, 128, 0.8); // #314880
      }

      .tab-item {
        display: inline-block;
        min-width: 50px;
        height: 26px;
        margin-left: 6px;
        padding: 0 10px;
        border: 1px solid #999;
        border-bottom: none;
        color: #fff;
        font-size: 13px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;

        &.active {
          border-color: #0069ea;
          background-color: #0069ea;
        }
      }
    }

    .section-list {
      @include flex(column, flex-start, flex-start);
      flex-grow: 1;
      width: 100%;
      padding: 0 10px 10px;
      overflow: hidden;

      .box-list-header {
        @include flex(row, flex-start, center);
        flex-shrink: 0;
        width: 100%;
        height: 30px;
        color: #fff;
        font-size: 14px;
        background-color: #0069ea;
      }

      .my-swiper {
        flex-grow: 1;
        width: 100%;
        height: calc(100vh - 545px);
      }

      .my-swiper-slide {
        height: 28px;
      }

      .list-item {
        @include flex(row, flex-start, center);
        height: 28px;
        color: #fff;
        font-size: 12px;
        background-color: transparent;
        user-select: none;
      }

      .list-item-odd {
        background-color: #0b5481;
      }

      .item-school {
        width: 100px;
        padding-right: 6px;
        padding-left: 10px;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
      }

      .item-name {
        flex: 1;
        padding: 0 6px;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
      }

      .item-grade {
        width: 60px;
        text-align: center;
      }

      .item-time {
        width: 90px;
        text-align: center;
      }
    }
  }
</style>
