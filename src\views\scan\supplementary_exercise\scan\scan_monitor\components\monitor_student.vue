<template>
  <div class="pane-monitor-student">
    <div class="panel-header">
      <RadioGroup
        class="radio"
        :model-value="params.status"
        type="button"
        button-style="solid"
        @on-change="handleChangeStatus"
      >
        <Radio v-for="s in statusList" :key="s.id" :label="s.id">{{ s.name }}</Radio>
      </RadioGroup>
      <Button
        class="btn-download"
        size="small"
        type="primary"
        icon="md-download"
        :loading="downloading"
        :disabled="isBtnDownloadDisabled"
        :title="isBtnDownloadDisabled ? '选中合计行时可导出' : ''"
        @click="handleBtnDownloadClick"
        >{{ downloading ? '导出中' : '导出' }}</Button
      >
      <Input
        class="search"
        :model-value="params.keyword"
        placeholder="准考号 / 姓名"
        clearable
        search
        @on-change="handleKeywordChange"
      />
    </div>
    <div class="panel-body">
      <Table :max-height="tableMaxHeight" :columns="tableColumns" :data="students"></Table>
    </div>
    <Page
      class="panel-footer"
      :model-value="params.currentPage"
      :page-size="params.pageSize"
      :total="total"
      show-total
      show-sizer
      :page-size-opts="[10, 20, 50, 100]"
      @on-change="handleChangePage"
      @on-page-size-change="handleChangePageSize"
    ></Page>

    <ModalRoomStudents
      v-model="showModalRoomStudents"
      :room-no="modalRoomNo"
      :student-id="modalStudentId"
    ></ModalRoomStudents>

    <ModalDownloadStudentScan
      v-model="showModalDownloadStudentScan"
      :init-sign-text="initSignText"
      :downloading="downloading"
      @ok="handleModalDownloadOK"
    ></ModalDownloadStudentScan>
  </div>
</template>

<script>
  import ModalRoomStudents from '../../scan_student/modal_room_students.vue'
  import ModalDownloadStudentScan from './modal_download_student_scan.vue'
  import TextButton from '@/components/text_button'

  import { apiSetStudentsMarkAbsent, apiUnsetStudentMarkAbsent } from '@/api/scan/scan_batch'
  import { apiDownloadStudentSignExcel } from '@/api/scan/scan_stats'

  import { mapGetters } from 'vuex'

  import { debounce } from '@/utils/function'
  import { downloadBlob } from '@/utils/download'
  import { formatDateTime } from '@/utils/date'

  export default {
    components: {
      ModalRoomStudents,
      ModalDownloadStudentScan,
    },
    props: {
      students: Array,
      total: Number,
      params: Object,
      statusList: Array,
      tableMaxHeight: Number,
    },
    emits: ['change-status', 'change-keyword', 'change-page', 'change-page-size', 'refresh'],
    data() {
      return {
        // 查看考生答卷弹窗
        showModalRoomStudents: false,
        modalRoomNo: 0,
        modalStudentId: '',
        // 下载考生
        showModalDownloadStudentScan: false,
        initSignText: '',
        // 下载中
        downloading: false,
      }
    },
    computed: {
      ...mapGetters('scan', [
        'isInSchoolExam',
        'hasPermissionScan',
        'examSubjectId',
        'viewMode',
        'examId',
        'examName',
        'subjectName',
      ]),
      isBtnDownloadDisabled() {
        let { scanStationId, schoolId, roomNo } = this.params
        return ![scanStationId, schoolId, roomNo].includes('合计')
      },
      tableColumns() {
        let columns = [
          {
            title: '准考号',
            key: 'admissionNum',
            minWidth: 80,
          },
          {
            title: '姓名',
            key: 'studentName',
            align: 'center',
            minWidth: 60,
          },
          {
            title: '班级',
            key: 'className',
            align: 'center',
            minWidth: 90,
          },
          {
            title: '考场',
            key: 'roomNo',
            align: 'center',
            minWidth: 60,
          },
          {
            title: '座位',
            key: 'seatNum',
            align: 'center',
            minWidth: 60,
          },
          {
            title: '状态',
            key: 'statusText',
            align: 'center',
            minWidth: 60,
            render: (h, params) =>
              h(
                'span',
                {
                  class: params.row.statusClass,
                },
                params.row.statusText
              ),
          },
          {
            title: '操作',
            key: 'actions',
            minWidth: this.hasPermissionScan ? 140 : 60,
            render: (h, params) => {
              let btns = [
                h(
                  TextButton,
                  {
                    type: 'primary',
                    onClick: () => {
                      this.handleStudentClick(params.row)
                    },
                  },
                  () => '查看'
                ),
              ]
              if (this.hasPermissionScan && !params.row.scanned) {
                if (params.row.markAbsent) {
                  btns.push(
                    h(
                      TextButton,
                      {
                        type: 'default',
                        icon: 'ios-bookmark-outline',
                        style: {
                          marginLeft: '10px',
                          display: 'inline-block',
                        },
                        onClick: () => {
                          this.unMarkStudentAbsent(params.row)
                        },
                      },
                      () => '取消标记'
                    )
                  )
                } else {
                  btns.push(
                    h(
                      TextButton,
                      {
                        type: 'default',
                        icon: 'ios-bookmark',
                        style: {
                          marginLeft: '10px',
                          display: 'inline-block',
                        },
                        onClick: () => {
                          this.markStudentAbsent(params.row)
                        },
                      },
                      () => '标记缺考'
                    )
                  )
                }
              }
              return h(
                'div',
                {
                  class: {
                    'student-btns': true,
                  },
                },
                btns
              )
            },
            align: 'center',
          },
        ]
        if (!this.isInSchoolExam) {
          columns.unshift({
            title: '学校',
            key: 'schoolName',
            minWidth: 120,
          })
        }
        return columns
      },
    },
    methods: {
      handleChangeStatus(status) {
        this.$emit('change-status', status)
      },
      handleKeywordChange: debounce(function (e) {
        this.$emit('change-keyword', e.target.value.trim())
      }, 500),
      handleChangePage(page) {
        this.$emit('change-page', page)
      },
      handleChangePageSize(pageSize) {
        this.$emit('change-page-size', pageSize)
      },
      handleStudentClick(row) {
        this.modalRoomNo = row.roomNo
        this.modalStudentId = row.studentId
        this.showModalRoomStudents = true
      },
      markStudentAbsent(stu) {
        if (stu.scanned || stu.markAbsent) {
          return
        }
        this.$Modal.confirm({
          title: '标记缺考',
          content: `是否标记缺考<br><strong>${stu.studentName} (${stu.admissionNum})</strong>？`,
          onOk: () => {
            apiSetStudentsMarkAbsent({
              examSubjectId: this.examSubjectId,
              studentIds: [stu.studentId],
            })
              .then(() => {
                this.$Message.success({
                  content: '操作成功',
                })
              })
              .finally(() => {
                this.$emit('refresh')
              })
          },
        })
      },
      unMarkStudentAbsent(stu) {
        if (stu.scanned || !stu.markAbsent) {
          return
        }
        this.$Modal.confirm({
          title: '取消标记缺考',
          content: `是否取消标记缺考<br><strong>${stu.studentName} (${stu.admissionNum})</strong>？`,
          onOk: () => {
            apiUnsetStudentMarkAbsent({
              examSubjectId: this.examSubjectId,
              studentId: stu.studentId,
            })
              .then(() => {
                this.$Message.success({
                  content: '操作成功',
                })
              })
              .finally(() => {
                this.$emit('refresh')
              })
          },
        })
      },
      handleBtnDownloadClick() {
        if (this.isBtnDownloadDisabled) {
          return
        }
        let status = this.statusList.find(s => s.id == this.params.status)
        this.initSignText = status ? status.name : ''
        this.showModalDownloadStudentScan = true
      },
      handleModalDownloadOK({ signs, signText }) {
        let scanStationId = this.viewMode.scanStationId
        let fileName = `${this.examName}${this.subjectName}${signText}考生名单`
        if (scanStationId) {
          let scanStationName = this.$store.getters['scan/scanStationName'](scanStationId) || 'XX扫描点'
          fileName += '-' + scanStationName
        }

        this.downloading = true
        apiDownloadStudentSignExcel({
          examId: this.examId,
          examSubjectIds: [this.examSubjectId],
          scanStationId,
          signs,
        })
          .then(blob => {
            let createTimeText = formatDateTime(new Date(), 'YYYY-MM-DD HH_mm_ss')
            fileName += `-截至${createTimeText}.xlsx`
            return downloadBlob(blob, fileName)
          })
          .finally(() => {
            this.downloading = false
          })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .pane-monitor-student {
    padding-right: 16px;
    padding-left: 16px;
  }

  .panel-header {
    .btn-download {
      margin-left: 20px;
    }

    .search {
      width: 200px;
      margin-left: auto;
    }
  }

  .panel-body {
    :deep(.ivu-table-cell .warning) {
      color: $color-warning;
    }
  }
</style>
