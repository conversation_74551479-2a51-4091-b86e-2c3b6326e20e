<template>
  <div class="container-paper-upload-batch-content">
    <div v-if="!paperHTML" class="empty">暂无试卷内容</div>
    <template v-else>
      <div class="header">
        <template v-if="handlingPaperContent">正在处理试卷内容... </template>
        <template v-else>
          <div v-if="!questionsSaved" class="switcher">
            <span class="switcher-label">编辑试卷</span>
            <i-switch v-model="contentEditEnabled" size="small"></i-switch>
          </div>
          <div class="switcher">
            <span class="switcher-label">显示背景</span>
            <i-switch v-model="showBackground" size="small"></i-switch>
          </div>
          <div class="switcher">
            <span class="switcher-label">自动划题</span>
            <i-switch v-model="autoDivide" size="small"></i-switch>
          </div>
          <Button v-if="!questionsSaved" class="btn-divide" size="small" type="primary" @click="startDivide"
            >划题</Button
          >
        </template>
      </div>
      <div class="content" :class="{ background: showBackground }">
        <div ref="paperContentRef" class="paper-content" :contenteditable="contentEditEnabled" v-html="paperHTML"></div>
      </div>
    </template>

    <ModalDivideResult
      ref="modalDivideResultRef"
      v-model="showModalDivideResult"
      :selection="htmlSelection"
      :paper-info="paper?.paperInfo"
      @add="handleModalDivideResultOK"
    ></ModalDivideResult>
  </div>
</template>

<script setup>
  import { computed, ref, watch, markRaw, useTemplateRef } from 'vue'
  import iView from '@/iview'
  import ModalDivideResult from './modal_divide_result.vue'
  import { useUploadBatchStore } from '@/store/qlib/upload_batch'

  import MathJaxUtil from '@/utils/mathjax'
  import ColorDetector from '@/utils/color'
  import { sleep } from '@/utils/promise'
  import StepStatusEnum from '@/enum/qlib/upload_batch/step_status'

  const uploadBatchStore = useUploadBatchStore()

  const paperContentRef = useTemplateRef('paperContentRef')
  const modalDivideResultRef = useTemplateRef('modalDivideResultRef')

  // 编辑试卷内容
  const contentEditEnabled = ref(false)
  // 显示背景
  const showBackground = ref(false)
  // 自动确认划题结果
  const autoDivide = ref(false)
  // 显示划题结果弹窗
  const showModalDivideResult = ref(false)
  // 划题选中的内容
  let htmlSelection = ref(null)

  // 试卷内容HTML
  const paperHTML = computed(() => {
    return uploadBatchStore.currentFile?.paperHTML
  })
  // 是否正在处理试卷内容
  const handlingPaperContent = computed(() => {
    return uploadBatchStore.currentFile?.handlingPaperHTML
  })
  // 试卷
  const paper = computed(() => {
    return uploadBatchStore.currentFile?.paper
  })
  // 题目是否已保存
  const questionsSaved = computed(() => {
    return uploadBatchStore.currentFile?.saveQuestionsStatus == StepStatusEnum.Succeeded.id
  })

  watch(paperHTML, process, { immediate: true })

  // 执行
  async function process() {
    if (!paperHTML.value) {
      return
    }
    await handlePaperContent()
    if (autoDivide.value && paper.value?.questionList.length == 0) {
      autoDivideAndExtractQuestions()
    }
  }

  // 处理试卷内容
  async function handlePaperContent() {
    uploadBatchStore.setCurrentFileHandlingPaperHTML(true)
    uploadBatchStore.addCurrentFileMessage('处理word内容中...')
    await sleep(0)
    try {
      let result = await Promise.all([
        removeSmallImages(),
        removeWhiteElements(),
        MathJaxUtil.render(paperContentRef.value),
      ])
      result = result.filter(Boolean)
      let message = '处理word内容完成'
      if (result.length > 0) {
        message += `: ${result.join(', ')}`
      }
      uploadBatchStore.addCurrentFileMessage(message)
    } catch (err) {
      iView.Message.error({
        content: 'word内容处理失败',
        duration: 0,
        closable: true,
      })
      uploadBatchStore.addCurrentFileMessage(`处理word内容失败: ${err.message}`, 'error')
    } finally {
      uploadBatchStore.setCurrentFileHandlingPaperHTML(false)
    }
  }
  // 删除与内容无关的小图片
  async function removeSmallImages() {
    let waitImageLoad = img =>
      new Promise(resolve => {
        if (img.complete) {
          resolve()
        } else {
          img.onload = resolve
          img.onerror = resolve
        }
      })
    let imgs = Array.from(paperContentRef.value.querySelectorAll('img'))
    await Promise.all(imgs.map(waitImageLoad))
    let count = 0
    for (let img of imgs) {
      if (img.naturalWidth > 0) {
        let rect = img.getBoundingClientRect()
        // 显示宽高均小于8像素
        if (rect.width < 8 && rect.height < 8) {
          img.remove()
          count++
        }
      }
    }
    if (count > 0) {
      return `移除小图片${count}处`
    }
  }
  // 删除与内容无关的白色文字
  async function removeWhiteElements() {
    let count = 0
    let elements = paperContentRef.value.querySelectorAll('*[style*=color]')
    let detector = new ColorDetector()
    for (let el of elements) {
      if (el.style.color) {
        let { r, g, b } = detector.getColorRGBAValue(el.style.color)
        // 接近白色
        if (r >= 250 && g >= 250 && b >= 250) {
          el.remove()
          count += el.textContent.length
        }
      }
    }
    if (count > 0) {
      return `移除白色文字${count}处`
    }
  }

  // 自动划题和解析题目内容
  async function autoDivideAndExtractQuestions() {
    uploadBatchStore.addCurrentFileMessage('开始划题')
    await sleep(0)
    htmlSelection.value = markRaw(paperContentRef.value.cloneNode(1))
    await sleep(0)

    let newQuestions
    try {
      newQuestions = await modalDivideResultRef.value.divideAndExtractQuestions()
    } catch (err) {
      uploadBatchStore.addCurrentFileMessage(`划题失败: ${err.message}`, 'error')
      throw err
    }

    if (newQuestions.length > 0) {
      addQuestions(newQuestions)
    } else {
      startDivide()
    }
  }

  // 点击划题
  async function startDivide() {
    htmlSelection.value = markRaw(paperContentRef.value.cloneNode(1))
    showModalDivideResult.value = true
  }

  // 划题结果确认
  async function handleModalDivideResultOK({ newQuestions }) {
    const doConfirmResult = async () => {
      showModalDivideResult.value = false
      await sleep(0)
      addQuestions(newQuestions)
    }
    if (paper.value?.questionList.length > 0) {
      iView.Modal.confirm({
        title: '重新划题',
        content: '将替换已有题目，是否继续？',
        onOk: doConfirmResult,
      })
    } else {
      doConfirmResult()
    }
  }

  // 添加题目
  function addQuestions(newQuestions) {
    if (!paper.value) {
      return
    }
    let existingQuestionIds = paper.value.questionList.map(q => q.id)
    if (existingQuestionIds.length > 0) {
      paper.value.remove(existingQuestionIds)
    }
    paper.value.add(newQuestions)
    uploadBatchStore.addCurrentFileMessage(`添加题目：${newQuestions.length}道`)
  }
</script>

<style lang="scss" scoped>
  .container-paper-upload-batch-content {
    @include flex(column, flex-start, stretch);

    .empty {
      @include flex(column, center, center);
      width: 100%;
      height: 100%;
      color: $color-icon;
      font-size: $font-size-large;
    }

    .header {
      @include flex(row, flex-start, center);
      flex-shrink: 0;
      gap: 16px;
      height: 40px;
      padding: 0 16px;
      border-bottom: 1px solid $color-border;

      .switcher {
        @include flex(row, flex-start, center);

        .switcher-label {
          margin-right: 5px;
        }
      }

      .btn-divide {
        margin-left: auto;
      }
    }

    .content {
      flex-grow: 1;
      flex-shrink: 1;
      overflow: auto;

      &.background {
        background-color: $color-background;
      }

      .paper-content {
        position: relative;
        padding: 16px;
        border: 1px solid transparent;

        &[contenteditable='true'] {
          border-color: $color-primary;
        }

        :deep(a) {
          color: inherit;
        }
      }
    }
  }
</style>
