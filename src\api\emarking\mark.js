import ajax from '@/api/ajax'
import store from '@/store/index'
import { addImageUrlParam } from '@/utils/url'
import { formatDate } from '@/utils/date'
import { getBlockQuestionsDefine } from '@/helpers/emarking'
import { transformUser } from './exam'
import ExamModeEnum from '@/enum/emarking/exam_mode'
import ExamScopeEnum from '@/enum/emarking/exam_scope'
import ExamTypeEnum from '@/enum/emarking/exam_type'
import SubjectStatusEnum from '@/enum/emarking/subject_status'
import MarkModeEnum from '@/enum/emarking/mark_mode'
import MarkingWayEnum from '@/enum/emarking/marking_way'
import AssignModeEnum from '@/enum/emarking/assign_mode'

/**
 * 任务分配
 */
export function apiGetBlockTasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/task/list',
      params: {
        examSubjectId,
      },
      requestName: '获取任务分配',
    })
    .then(data => {
      return (data || [])
        .map(item => {
          let questions = getBlockQuestionsDefine(item.branchs)
          let fullScore = questions.reduce((a, c) => a + c.fullScore, 0)
          let markMode = MarkModeEnum.getEntryById(Number(item.evaluateWay)) || {
            id: Number(item.evaluateWay),
            key: '',
            name: '',
          }
          let assignMode = AssignModeEnum.getEntryById(Number(item.assignWay)) || {
            id: Number(item.assignWay),
            key: '',
            name: '',
          }
          let markingWay = MarkingWayEnum.getEntryById(Number(item.markingWay)) || {
            id: Number(item.markingWay),
            key: '',
            name: '',
          }
          let groups = (item.evaluationGroups || []).map(g => ({
            groupId: g.groupId,
            groupName: g.groupName,
            studentNum: g.studentNum,
            amount: g.taskNum || 0,
          }))
          let teachers = (item.evaluateUsers || []).map(t => ({
            ...transformUser(t),
            assignedNumber: t.maxCount,
            groupId: t.groupId,
            groupName: (groups.find(g => g.groupId === t.groupId) || { groupName: '' }).groupName,
          }))
          let leaders = (item.blockLeaders || []).map(t => ({
            ...transformUser(t),
            groupId: t.groupId,
            groupName: (groups.find(g => g.groupId === t.groupId) || { groupName: '' }).groupName,
          }))
          let arbitrators = (item.arbitrateUsers || []).map(t => ({
            ...transformUser(t),
            groupId: t.groupId,
            groupName: (groups.find(g => g.groupId === t.groupId) || { groupName: '' }).groupName,
          }))
          return {
            blockId: item.subjectiveId,
            blockName: item.subjectiveName,
            questions,
            fullScore,
            markMode,
            errorScore: item.errorValue,
            doubleRate: item.doubleRate,
            assignMode,
            markingWay,
            groups,
            teachers,
            leaders,
            arbitrators,
            batchScoreSize: item.batchScoreSize,
            aiScoreType: item.aiscoreType,
            aiScoreUseType: item.aiscoreUseType,
          }
        })
        .sort(
          (a, b) =>
            Number(a.questions[0] && a.questions[0].questionName) -
            Number(b.questions[0] && b.questions[0].questionName)
        )
    })
}

export function apiChangeBlockMarkMode(data) {
  return ajax.post({
    url: 'mark/mark/task/setMarkWay',
    data: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
      markWay: data.markModeId || data.markMode.id,
      errorValue: data.errorScore,
      valueWay: 0,
      doubleRate: data.doubleRate,
      aiScoreType: data.aiScoreType,
      aiScoreUseType: data.aiScoreUseType,
    },
  })
}

export function apiChangeSubjectMarkingWay(params) {
  return ajax.put({
    url: 'mark/mark/task/subjMarkingWay',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      markingWay: params.markingWayId,
    },
    requestName: '设置发卷方式',
  })
}

export function apiChangeBlockMarkingWay(params) {
  return ajax.put({
    url: 'mark/mark/task/block/setMarkingWay',
    params: {
      blockId: params.blockId,
      markingWay: params.markingWayId,
    },
    requestName: '设置发卷方式',
  })
}

export function apiSaveSchoolAmount(data) {
  return ajax.put({
    url: 'mark/mark/task/schoolAmount/taskNum/save',
    params: {
      examSubjectId: data.examSubjectId,
    },
    data: data.schools.map(s => ({
      schoolId: s.schoolId,
      taskNum: s.amount,
    })),
    requestName: '设置学校任务量',
  })
}

export function apiSetSubjectAssignMode(params) {
  return ajax.put({
    url: 'mark/mark/task/subjectAssign',
    params,
    requestName: '设置科目所有题块分配方式',
  })
}

export function apiChangeBlockAssignMode(data) {
  return ajax.put({
    url: 'mark/mark/task/assignSetting',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
      assignWay: data.assignModeId || data.assignMode.id,
    },
    data: data.teachers.map(t => ({
      userId: t.userId,
      num: t.assignedNumber,
    })),
    requestName: '设置分配方式与任务量',
  })
}

export function apiGetBlockTeachers(params) {
  return ajax
    .get({
      url: 'mark/mark/task/taskNum',
      params: {
        examSubjectId: params.examSubjectId,
        subjectiveId: params.blockId,
      },
      requestName: '获取阅卷老师',
    })
    .then(data =>
      (data || []).map(t => ({
        ...transformUser(t),
        assignedNumber: t.maxNum,
        markedNumber: t.markedNum,
        isMarker: t.isMarker,
        isLeader: t.isSubjectiveLeader,
        isArbitrator: t.isArbitrator,
        groupId: t.groupId,
        groupName: t.groupName,
      }))
    )
}

export function apiAddBlockTeachers(data) {
  return ajax.post({
    url: 'mark/mark/task/setMarkTeacherFromBaseMsg',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加阅卷老师',
  })
}

export function apiAddBlockTeachersFromGroup(data) {
  return ajax.post({
    url: 'mark/mark/task/addMarkerFromGroup',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
      groupId: data.groupId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加阅卷老师',
  })
}

export function apiRemoveBlockTeachers(data) {
  return ajax.delete({
    url: 'mark/mark/task/markTeacher',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '删除阅卷老师',
  })
}

export function apiGetBlockLeaders(params) {
  return ajax.get({
    url: 'mark/mark/task/subjectiveLeader',
    params: {
      examSubjectId: params.examSubjectId,
      subjectiveId: params.subjectiveId,
      groupId: params.groupId,
      schoolId: params.schoolId,
    },
    requestName: '获取题组长',
  })
}

// 评卷员获取题组长，分组或分校评卷时，只返回其所在评卷组或所在学校的组长
export function apiGetMyBlockLeaders(params) {
  return ajax.get({
    url: 'mark/mark/task/myBlockLeaders',
    params: {
      examSubjectId: params.examSubjectId,
      subjectiveId: params.subjectiveId,
    },
    requestName: '获取题组长',
  })
}

export function apiAddBlockLeaders(data) {
  return ajax.post({
    url: 'mark/mark/task/setBlockMasterFromBaseMsg',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加题组长',
  })
}

export function apiAddBlockLeadersFromGroup(data) {
  return ajax.post({
    url: 'mark/mark/task/addLeaderFromGroup',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
      groupId: data.groupId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加题组长',
  })
}

export function apiRemoveBlockLeaders(data) {
  return ajax.delete({
    url: 'mark/mark/task/subjectiveLeader',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '删除题组长',
  })
}

export function apiAddBlockArbitrators(data) {
  return ajax.post({
    url: 'mark/mark/task/setArbitrateTeacherFromBaseMsg',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加仲裁员',
  })
}

export function apiAddBlockArbitratorsFromGroup(data) {
  return ajax.post({
    url: 'mark/mark/task/addArbitratorFromGroup',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
      groupId: data.groupId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '添加仲裁员',
  })
}

export function apiRemoveBlockArbitrators(data) {
  return ajax.delete({
    url: 'mark/mark/task/arbitrateTeacher',
    params: {
      examSubjectId: data.examSubjectId,
      subjectiveId: data.blockId,
    },
    data: data.teacherIds || (data.teachers || []).map(t => t.userId) || [],
    requestName: '删除仲裁员',
  })
}

export function apiImportBlockTeachers(data) {
  return ajax.upload({
    url: 'mark/mark/task/import',
    data: {
      examId: data.examId,
      examSubjectId: data.examSubjectId,
      file: data.file,
    },
  })
}

// 复制评卷员
export function apiCopyMarkers(data) {
  return ajax.post({
    url: 'mark/mark/task/batchCopyMarker',
    params: {
      blockId: data.srcBlockId,
    },
    data: {
      blockIds: data.destBlockIds,
      userIds: data.userIds,
    },
  })
}

// 复制题组长
export function apiCopyLeaders(data) {
  return ajax.post({
    url: 'mark/mark/task/batchCopyLeader',
    params: {
      blockId: data.srcBlockId,
    },
    data: {
      blockIds: data.destBlockIds,
      userIds: data.userIds,
    },
  })
}

export function apiSyncTeacherWhenMarkingByClass(params) {
  return ajax.put({
    url: 'mark/mark/task/fenbanTeaSync',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '同步任课教师',
  })
}

export function apiGetMarkersByClass(examSubjectId) {
  return ajax.get({
    url: 'mark/mark/task/fenbanClassMarkers',
    params: {
      examSubjectId,
    },
    requestName: '获取分班阅卷各班评卷老师',
  })
}

export function apiDownloadMarkingTaskTemplate(params) {
  return ajax.download({
    url: 'mark/mark/task/temp',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取任务分配模板',
  })
}

export function apiDownloadMarkingTaskExcel(params) {
  return ajax.download({
    url: 'mark/mark/task/export',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '获取已分配评卷任务表',
  })
}

export function apiSaveBlockGroups(data) {
  return ajax.post({
    url: 'mark/mark/task/block/evaluationGroup/save',
    params: {
      blockId: data.blockId,
    },
    data: data.groups,
    requestName: '设置题块评卷组',
  })
}

export function apiDeleteBlockGroups(data) {
  return ajax.delete({
    url: 'mark/mark/task/block/evaluationGroup/delete',
    params: {
      blockId: data.blockId,
    },
    data: data.groupIds,
    requestName: '题块删除评卷组',
  })
}

// 获取评卷监控可见题块
export function apiGetMonitorVisibleBlocks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/task/blockInfoList',
      params: {
        examSubjectId,
      },
      requestName: '获取题块信息',
    })
    .then(data => {
      return (data || []).map(item => {
        let markMode = MarkModeEnum.getEntryById(Number(item.evaluateWay)) || {
          id: Number(item.evaluateWay),
          key: '',
          name: '',
        }
        let assignMode = AssignModeEnum.getEntryById(Number(item.assignWay)) || {
          id: Number(item.assignWay),
          key: '',
          name: '',
        }
        let markingWay = MarkingWayEnum.getEntryById(Number(item.markingWay)) || {
          id: Number(item.markingWay),
          key: '',
          name: '',
        }
        return {
          blockId: item.subjectiveId,
          blockName: item.subjectiveName,
          questions: getBlockQuestionsDefine(item.branchs),
          strictMark: item.strictMark || false,
          markMode,
          errorScore: item.errorValue,
          doubleRate: item.doubleRate,
          assignMode,
          markingWay,
          groups: item.evaluationGroups || [],
        }
      })
    })
}

/**
 * 评卷任务
 */
export function apiGetMarkTasks({
  semesterId,
  term,
  gradeId,
  status,
  beginTime,
  endTime,
  keyword,
  currentPage,
  pageSize,
}) {
  return ajax
    .get({
      url: 'mark/mark/task2',
      params: {
        semesterId,
        term,
        gradeId,
        status,
        beginTime,
        endTime,
        examName: keyword,
        page: currentPage,
        size: pageSize,
      },
      requestName: '获取阅卷任务',
    })
    .then(data => {
      let grades = store.getters['emarking/grades']()
      let subjects = store.getters['emarking/subjects']()

      return {
        total: data.total,
        tasks: (data.records || []).map(t => ({
          examId: t.examId,
          examName: t.examName,
          examMode: ExamModeEnum.getEntryById(t.examMode) || { id: t.examMode, name: '' },
          examScope: ExamScopeEnum.getEntryById(t.examScope) || { id: t.examScope, name: '' },
          examType: ExamTypeEnum.getEntryById(t.examType) || { id: t.examType, name: '' },
          grade: grades.find(x => x.id == t.gradeId) || { id: t.gradeId, name: '' },
          startDateText: formatDate(new Date(t.beginTime)),
          endDateText: formatDate(new Date(t.endTime)),
          examSubjectId: t.examSubjectId,
          subjectId: t.subjectId,
          subjectName: (subjects.find(x => x.id == t.subjectId) || { name: '' }).name,
          status: SubjectStatusEnum.getEntryById(t.status) || { id: t.status, name: '' },
        })),
      }
    })
}

export function apiGetSchoolMarkTasks(topNum) {
  return ajax
    .get({
      url: 'mark/mark/schoolMarkTasks',
      params: {
        topNum: topNum || undefined,
      },
      requestName: '获取阅卷任务',
    })
    .then(data => {
      return (data || []).map(t => ({
        examId: t.examId,
        examName: t.examName,
        examScope: ExamScopeEnum.getEntryById(t.examScope) || { id: t.examScope, name: '' },
        gradeId: t.gradeId,
        gradeName: t.gradeName,
        examSubjectId: t.examSubjectId,
        subjectId: t.subjectId,
        subjectName: t.subjectName,
        schoolId: t.schoolId,
        schoolName: t.schoolName,
      }))
    })
}

export function apiGetSubjectMarkTasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/task/blocks',
      params: {
        examSubjectId,
      },
      requestName: '获取评卷任务',
    })
    .then(data => {
      return (data || [])
        .map(x => ({
          blockId: x.subjectiveId,
          blockName: x.subjectiveName,
          sortCode: Number(x.sortCode),
          questions: getBlockQuestionsDefine(x.branchs),
          fullScore: x.fullScore,
          strictMark: x.strictMark || false,
          optional: x.optional,
          markMode: MarkModeEnum.getEntryById(x.evaluationWay) || { id: x.evaluationWay, name: '' },
          assignMode: AssignModeEnum.getEntryById(x.assignWay) || { id: x.assignWay, name: '' },
          markingWay: MarkingWayEnum.getEntryById(x.markingWay) || { id: x.markingWay, name: '' },
          errorScore: x.errorValue,
          doubleRate: x.doubleRate,
          totalNum: x.totalNum,
          markedNum: x.markedNum,
          groupId: x.groupId,
          groupName: x.groupName,
          groupTotalNum: x.groupMaxNum,
          groupMarkedNum: x.groupMarkedNum,
          myTotalNum: x.myMaxNum,
          myMarkedNum: x.myMarkedNum,
          imgPixelType: x.imgPixelType,
          batchScoreSize: x.batchScoreSize,
          aiScoreType: x.aiscoreType,
          aiScoreUseType: x.aiscoreUseType,
          aiScoreGroupStat: x.aiscoreGroupStat,
        }))
        .sort((a, b) => a.sortCode - b.sortCode)
    })
}

export function apiGetSubjectAbnormalTasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/problem/task',
      params: {
        examSubjectId,
      },
      requestName: '获取问题卷处理任务',
    })
    .then(data => {
      return (data || [])
        .map(x => ({
          blockId: x.subjectiveId,
          blockName: x.subjectiveName,
          sortCode: Number(x.sortCode),
          questions: getBlockQuestionsDefine(x.branchs),
          fullScore: x.fullScore,
          strictMark: x.strictMark || false,
          optional: x.optional,
          markMode: MarkModeEnum.getEntryById(x.evaluationWay) || { id: x.evaluationWay, name: '' },
          assignMode: AssignModeEnum.getEntryById(x.assignWay) || { id: x.assignWay, name: '' },
          markingWay: MarkingWayEnum.getEntryById(x.markingWay) || { id: x.markingWay, name: '' },
          errorScore: x.errorValue,
          doubleRate: x.doubleRate,
          totalNum: x.totalNum,
          markedNum: x.markedNum,
          groupId: x.groupId,
          groupName: x.groupName,
          groupTotalNum: x.groupMaxNum,
          groupMarkedNum: x.groupMarkedNum,
          myMarkedNum: x.myMarkedNum,
          imgPixelType: x.imgPixelType,
        }))
        .sort((a, b) => a.sortCode - b.sortCode)
    })
}

export function apiGetSubjectArbitrateTasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/arbitrate/task',
      params: {
        examSubjectId,
      },
      requestName: '获取仲裁任务',
    })
    .then(data => {
      return (data || [])
        .map(x => ({
          blockId: x.subjectiveId,
          blockName: x.subjectiveName,
          sortCode: Number(x.sortCode),
          fullScore: x.fullScore,
          questions: getBlockQuestionsDefine(x.branchs),
          strictMark: x.strictMark || false,
          optional: x.optional,
          markMode: MarkModeEnum.getEntryById(x.evaluationWay) || { id: x.evaluationWay, name: '' },
          assignMode: AssignModeEnum.getEntryById(x.assignWay) || { id: x.assignWay, name: '' },
          markingWay: MarkingWayEnum.getEntryById(x.markingWay) || { id: x.markingWay, name: '' },
          errorScore: x.errorValue,
          doubleRate: x.doubleRate,
          totalNum: x.totalNum,
          markedNum: x.markedNum,
          groupId: x.groupId,
          groupName: x.groupName,
          groupTotalNum: x.groupMaxNum,
          groupMarkedNum: x.groupMarkedNum,
          myMarkedNum: x.myMarkedNum,
          imgPixelType: x.imgPixelType,
        }))
        .sort((a, b) => a.sortCode - b.sortCode)
    })
}

export function apiGetSubjectRepeatTasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/redo/task',
      params: {
        examSubjectId,
      },
      requestName: '获取重评任务',
    })
    .then(data => {
      return (data || [])
        .map(x => ({
          blockId: x.subjectiveId,
          blockName: x.subjectiveName,
          sortCode: Number(x.sortCode),
          questions: getBlockQuestionsDefine(x.branchs),
          fullScore: x.fullScore,
          strictMark: x.strictMark || false,
          optional: x.optional,
          markMode: MarkModeEnum.getEntryById(x.evaluationWay) || { id: x.evaluationWay, name: '' },
          assignMode: AssignModeEnum.getEntryById(x.assignWay) || { id: x.assignWay, name: '' },
          markingWay: MarkingWayEnum.getEntryById(x.markingWay) || { id: x.markingWay, name: '' },
          errorScore: x.errorValue,
          doubleRate: x.doubleRate,
          totalNum: x.totalNum,
          markedNum: x.markedNum,
          groupId: x.groupId,
          groupName: x.groupName,
          groupTotalNum: x.groupMaxNum,
          groupMarkedNum: x.groupMarkedNum,
          myTotalNum: x.myMaxNum,
          myMarkedNum: x.myMarkedNum,
          imgPixelType: x.imgPixelType,
        }))
        .sort((a, b) => a.sortCode - b.sortCode)
    })
}

export function apiGetSubjectAITasks(examSubjectId) {
  return ajax
    .get({
      url: 'mark/mark/task/aiScoreBlocks',
      params: {
        examSubjectId,
      },
    })
    .then(data => {
      let result = (data || []).map(x => {
        return {
          blockId: x.subjectiveId,
          blockName: x.subjectiveName,
          sortCode: Number(x.sortCode),
          questions: getBlockQuestionsDefine(x.branchs),
          fullScore: x.fullScore,
          strictMark: x.strictMark,
          optional: x.optional,
          markMode: MarkModeEnum.getEntryById(x.evaluationWay) || { id: x.evaluationWay, name: '' },
          assignMode: AssignModeEnum.getEntryById(x.assignWay) || { id: x.assignWay, name: '' },
          markingWay: MarkingWayEnum.getEntryById(x.markingWay) || { id: x.markingWay, name: '' },
          errorScore: x.errorValue,
          totalNum: x.totalNum,
          groupId: x.groupId,
          groupName: x.groupName,
          groupTotalNum: x.groupMaxNum,
          groupMarkedNum: x.groupMarkedNum,
          myTotalNum: x.myMaxNum,
          myMarkedNum: x.myMarkedNum,
          abnormalMarkedNum: x.abnormalMarkedNum,
          aiScoreType: x.aiScoreType, // TODO enum
          aiSuccessNum: x.aiSuccessNum,
          aiFailedNum: x.aiFailedNum,
          myAiSuccessNum: x.myAiSuccessNum,
          myAiFailedNum: x.myAiFailedNum,
          myAbnormalMarkedNum: x.myAbnormalMarkedNum,
          aiScoreExtraJson: x.aiScoreExtraJson,
        }
      })
      result.sort((a, b) => a.sortCode - b.sortCode)
      return result
    })
}

export function apiGetExamInfo(examId) {
  return ajax.get({
    url: `mark/mark/examNav/${examId}`,
    requestName: '获取考试信息',
  })
}

export function apiGetExamSubjectInfo(examSubjectId) {
  return ajax
    .get({
      url: `mark/mark/subjectNav`,
      params: {
        examSubjectId,
      },
      requestName: '获取科目信息',
    })
    .then(data => {
      let subjects = store.getters['emarking/subjects']()

      return {
        examId: data.examId,
        examName: data.examName,
        examSubjectId: examSubjectId,
        subjectId: data.subjectId,
        isClassPractise: data.examType && data.examType === 7,
        subjectName: (subjects.find(x => x.id == data.subjectId) || { name: '' }).name,
        enableIncreaseMarkTaskNum: data.enableIncreaseMarkTaskNum || false,
      }
    })
}

// 获取评卷定义，评卷页面定期调用此接口更新
export function apiGetSubjectiveDefine(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/task/block',
      params: {
        subjectiveId,
      },
    })
    .then(data => ({
      blockId: data.subjectiveId,
      questions: getBlockQuestionsDefine(data.branchs),
      strictMark: data.strictMark || false,
    }))
}

/**
 * 评卷
 */
// 正评取卷
export function apiGetMarkStudentBlock(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark',
      params: {
        subjectiveId,
      },
    })
    .then(data => ({
      imgUrl: addImageUrlParam(data.imgUrl),
      // markStatus: data.markStatus,
      No: data.no,
      // readTime: new Date(data.readTime),
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      type: data.type,
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
    }))
}

// 正评（批量）取卷
export function apiGetMarkBatchStudentBlock(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/markBatch',
      params: {
        subjectiveId,
      },
    })
    .then(response =>
      (response || []).map(data => ({
        imgUrl: addImageUrlParam(data.imgUrl),
        // markStatus: data.markStatus,
        No: data.no,
        // readTime: new Date(data.readTime),
        secretNum: data.secretNum,
        blockId: data.subjectiveId,
        studentBlockId: data.subjectiveItemId,
        type: data.type,
        isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
      }))
    )
}

// 正评提交
export function apiSubmitMarkStudentBlock(params) {
  let sendParams = {
    subjectiveId: params.blockId,
    subjectiveItemId: params.studentBlockId,
    score: params.score,
    subScore: params.subScore,
    commentJson: params.comments,
    typicalType: params.typicalType, // Number (1-优秀卷；2-错误卷)
    typicalComment: params.typicalComment, // String
  }

  return ajax
    .post({
      url: `mark/mark/submit`,
      data: sendParams,
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
        myTotalNum: data.myMax,
      }
    })
}

export function apiSubmitMarkBatchStudentBlocks(params) {
  return ajax
    .post({
      url: 'mark/mark/markBatch/submit',
      data: params, // Array* [previos interface sendParams]
    })
    .then(response => ({
      myMarkedNum: response.myMarked,
      myTotalNum: response.myMax,
    }))
  // .then(response =>
  //   (response || []).map(data => ({
  //     myMarkedNum: data.myMarked,
  //     myTotalNum: data.myMax,
  //   }))
  // )
}

// 提交问题卷（正评时）
export function apiTagStudentBlockAbnormalWhenMark(params) {
  return ajax
    .post({
      url: 'mark/mark/problem/submit',
      data: {
        subjectiveId: params.blockId,
        subjectiveItemId: params.studentBlockId,
      },
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
        myTotalNum: data.myMax,
      }
    })
}

// 提交问题卷（回评时）
export function apiTagStudentBlockAbnormalWhenRemark(params) {
  return ajax
    .post({
      url: 'mark/mark/problem/remark/submit',
      data: {
        subjectiveId: params.blockId,
        subjectiveItemId: params.studentBlockId,
        readTime: params.readTime,
      },
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
        myTotalNum: data.myMax,
      }
    })
}

// 回评取卷
export function apiGetReMarkStudentBlock(params) {
  return ajax
    .get({
      url: 'mark/mark/remark',
      params: {
        subjectiveId: params.blockId,
        no: params.No,
      },
    })
    .then(data => ({
      subScore: data.subScore,
      imgUrl: addImageUrlParam(data.imgUrl),
      // markStatus: data.markStatus,
      No: data.no,
      // submitTime: new Date(data.submitTime),
      readTime: data.readTime,
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      type: data.type,
      comments: data.teacherComment,
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
      typicalList: data.typicalList || [],
    }))
}

// 回评提交
export function apiSubmitRemarkStudentBlock(params) {
  let sendParams = {
    subjectiveId: params.blockId,
    subjectiveItemId: params.studentBlockId,
    score: params.score,
    subScore: params.subScore,
    readTime: params.readTime,
    commentJson: params.comments,
    typicalType: params.typicalType, // Number (1-优秀卷；2-错误卷)
    typicalComment: params.typicalComment, // String
  }

  return ajax
    .post({
      url: `mark/mark/remark/submit`,
      data: sendParams,
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
        myTotalNum: data.myMax,
      }
    })
}

// 阅卷历史
export function apiGetMarkHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/remarkList',
      params: {
        subjectiveId: params.blockId,
        current: params.currentPage,
        size: params.pageSize,
        minScore: params.minScore, // [Number .2]
        maxScore: params.maxScore, // [Number .2]
        beginTime: params.beginTime || undefined, // [String]
        endTime: params.endTime || undefined, // [String]
      },
      requestName: '获取阅卷历史',
    })
    .then(data => {
      return {
        total: data.total,
        records: (data.records || []).map(x => ({
          No: x.no,
          score: x.score,
          type: x.type,
          submitTime: new Date(x.submitTime),
          hasTypical: x.hasTypical || false,
        })),
      }
    })
}

// 个人评卷统计
export function apiGetMarkerStatistics(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/personal/progress',
      params: {
        subjectiveId,
      },
      requestName: '获取评卷统计',
    })
    .then(data => {
      return {
        markedNum: data.marked,
        totalNum: data.total,
        avgScore: data.avgScore || 0,
        maxScore: data.maxScore || 0,
        minScore: data.minScore || 0,
        groupId: data.group && data.group.id,
        groupName: data.group && data.group.name,
        groupMarkedNum: data.group && data.group.marked,
        groupTotalNum: data.group && data.group.taskNum,
        groupAvgScore: (data.group && data.group.avgScore) || 0,
        groupMaxScore: (data.group && data.group.maxScore) || 0,
        groupMinScore: (data.group && data.group.minScore) || 0,
        myMarkedNum: data.myMarked,
        myTotalNum: data.myMax,
        myAvgScore: data.myAvgScore || 0,
        myMaxScore: data.myMaxScore || 0,
        myMinScore: data.myMinScore || 0,
      }
    })
}

// 回收评卷员试卷
export function apiReleaseOccupiedPaper(params) {
  return ajax.put({
    url: 'mark/mark/releaseOccupy',
    params: {
      subjectiveId: params.blockId,
      userId: params.userId,
    },
    requestName: '回收试卷',
  })
}

/**
 * 问题卷处理
 */
// 问题卷取卷
export function apiGetAbnormalStudentBlock(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/problem/new',
      params: {
        subjectiveId,
      },
    })
    .then(data => ({
      imgUrl: addImageUrlParam(data.imgUrl),
      No: data.no,
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      type: data.type,
      submitterUserId: data.marker.userId,
      submitterRealName: data.marker.realName,
      submitterMobile: data.marker.mobile,
      submitTime: new Date(data.marker.submitTime),
      readTime: data.readTime,
      // submitReason: data.submitReason,
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
    }))
}

// 问题卷回评取卷
export function apiGetReAbnormalStudentBlock(params) {
  return ajax
    .get({
      url: 'mark/mark/problem/view',
      params: {
        subjectiveId: params.blockId,
        no: params.No,
      },
    })
    .then(data => ({
      subScore: data.subScore,
      imgUrl: addImageUrlParam(data.imgUrl),
      No: data.no,
      readTime: data.readTime,
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      type: data.type,
      submitterUserId: data.marker.userId,
      submitterRealName: data.marker.realName,
      submitterMobile: data.marker.mobile,
      submitTime: new Date(data.marker.submitTime),
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
    }))
}

// 提交问题卷处理分数
export function apiSubmitAbnormalStudentBlock(params) {
  let sendParams = {
    subjectiveId: params.blockId,
    subjectiveItemId: params.studentBlockId,
    score: params.score,
    subScore: params.subScore,
    readTime: params.readTime,
  }

  return ajax
    .post({
      url: `mark/mark/problem/mark`,
      data: sendParams,
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
      }
    })
}

// 获取问题卷原卷图片
export function apiGetStudentOriginalPaperImages(params) {
  return ajax
    .get({
      url: 'mark/mark/problem/paper',
      params: {
        subjectiveId: params.blockId,
        subjectiveItemId: params.studentBlockId,
      },
    })
    .then(data => {
      return (data || []).map(imgUrl => addImageUrlParam(imgUrl))
    })
}

// 问题卷处理历史
export function apiGetAbnormalHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/problem/markList',
      params: {
        subjectiveId: params.blockId,
        current: params.currentPage,
        size: params.pageSize,
      },
      requestName: '获取问题卷处理历史',
    })
    .then(data => {
      return {
        total: data.total,
        records: (data.records || []).map(x => ({
          No: x.no,
          score: x.score,
          type: x.type,
          submitTime: new Date(x.submitTime),
        })),
      }
    })
}

// 个人问题卷处理统计
export function apiGetMarkerAbnormalStatistics(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/problem/personal/progress',
      params: {
        subjectiveId,
      },
      requestName: '获取评卷统计',
    })
    .then(data => {
      return {
        markedNum: data.marked,
        totalNum: data.total,
        groupId: data.group && data.group.id,
        groupName: data.group && data.group.name,
        groupMarkedNum: data.group && data.group.marked,
        groupTotalNum: data.group && data.group.taskNum,
        myMarkedNum: data.myMarked,
      }
    })
}

// 驳回重评
export function apiRejectSubmit(params) {
  const ReasonString = (params.reason || '').trim()
  return ajax.post({
    url: 'mark/mark/undo/submit',
    params: {
      subjectiveId: params.blockId,
      subjectiveItemId: params.studentBlockId,
      markUserId: params.submitterUserId,
      reason: ReasonString,
    },
    data: {
      reason: ReasonString,
    },
  })
}

// 个人驳回记录
export function apiGetMyRejectHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/undo/page',
      params: {
        subjectiveId: params.blockId,
        markType: params.markType,
        isFinish: params.isFinish,
        page: params.currentPage,
        size: params.pageSize,
      },
      requestName: '获取驳回记录',
    })
    .then(data => {
      data.records.forEach(item => {
        item.imgUrl = addImageUrlParam(item.imgUrl)
      })
      return data
    })
}

/**
 * 仲裁
 */
// 仲裁取卷
export function apiGetArbitrateStudentBlock(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/arbitrate/new',
      params: {
        subjectiveId,
      },
    })
    .then(data => ({
      imgUrl: addImageUrlParam(data.imgUrl),
      No: data.no,
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      readTime: data.readTime,
      markers: (data.markers || []).map(u => ({
        userId: u.userId,
        realName: u.realName,
        mobile: u.mobile,
        score: u.score,
      })),
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
    }))
}

// 仲裁提交
export function apiSubmitArbitrateStudentBlock(params) {
  let sendParams = {
    subjectiveId: params.blockId,
    subjectiveItemId: params.studentBlockId,
    score: params.score,
    subScore: params.subScore,
    readTime: params.readTime,
  }

  return ajax
    .post({
      url: `mark/mark/arbitrate/mark`,
      data: sendParams,
    })
    .then(data => {
      return {
        myMarkedNum: data.myMarked,
      }
    })
}

// 仲裁回评取卷
export function apiGetReArbitrateStudentBlock(params) {
  return ajax
    .get({
      url: 'mark/mark/arbitrate/view',
      params: {
        subjectiveId: params.blockId,
        no: params.No,
      },
    })
    .then(data => ({
      imgUrl: addImageUrlParam(data.imgUrl),
      No: data.no,
      secretNum: data.secretNum,
      blockId: data.subjectiveId,
      studentBlockId: data.subjectiveItemId,
      readTime: data.readTime,
      markers: (data.markers || []).map(u => ({
        userId: u.userId,
        realName: u.realName,
        mobile: u.mobile,
        score: u.score,
      })),
      isRehearsal: data.isTrailMarking || data.isTrialMarking || false,
    }))
}

// 仲裁历史
export function apiGetArbitrateHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/arbitrate/markList',
      params: {
        subjectiveId: params.blockId,
        current: params.currentPage,
        size: params.pageSize,
      },
      requestName: '获取仲裁历史',
    })
    .then(data => {
      return {
        total: data.total,
        records: (data.records || []).map(x => ({
          No: x.no,
          score: x.score,
          type: x.type,
          submitTime: new Date(x.submitTime),
        })),
      }
    })
}

// 个人仲裁统计
export function apiGetMarkerArbitrateStatistics(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/arbitrate/personal/progress',
      params: {
        subjectiveId,
      },
      requestName: '获取评卷统计',
    })
    .then(data => {
      return {
        markedNum: data.marked,
        totalNum: data.total,
        groupId: data.group && data.group.id,
        groupName: data.group && data.group.name,
        groupMarkedNum: data.group && data.group.marked,
        groupTotalNum: data.group && data.group.taskNum,
        myMarkedNum: data.myMarked,
      }
    })
}

// 获取仲裁卷相关评卷员信息
export function apiGetArbitratePaperMarkerInfomations(requestParams) {
  return ajax.get({
    url: 'mark/mark/arbitrate/markerList',
    params: {
      subjectiveId: requestParams.subjectiveId,
      subjectiveItemId: requestParams.subjectiveItemId,
    },
    requestName: '获取仲裁卷相关评卷信息',
  })
}

/**
 * 重评
 */
//重评取卷
export function apiGetRepeatMarkStudentBlock(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/redo/onePending',
      params: {
        subjectiveId,
      },
    })
    .then(data => ({
      id: data.id,
      No: data.no,
      imgUrl: addImageUrlParam(data.imgUrl),
      studentBlockId: data.subjectiveItemId,
      secretNum: data.secretNum,
      comments: data.teacherComment,
      readTime: data.readTime,
      markNo: data.markNo,
      markScore: data.markScore,
      markSubScore: data.markSubScore,
      markType: data.markType,
      markTime: data.markTime,
      remarkScore: data.remarkScore,
      remarkSubScore: data.remarkSubScore,
      remarkType: null,
      remarkTime: data.remarkTime,
      undoTime: data.undoTime,
      reason: data.reason || '',
    }))
}

//重评提交
export function apiSubmitRepeatMarkStudentBlock(data) {
  return ajax
    .post({
      url: `mark/mark/redo/submit`,
      data: {
        id: data.id,
        score: data.score,
        subScore: data.subScore,
        readTime: data.readTime,
        commentJson: data.comments,
        typicalType: data.typicalType, // Number (1-优秀卷；2-错误卷)
        typicalComment: data.typicalComment, // String
      },
    })
    .then(data => {
      return {
        myMarkedNum: data.done,
        myTotalNum: data.total,
      }
    })
}

//重评卷回评取卷
export function apiGetRepeatReMarkStudentBlockById(id) {
  return ajax
    .get({
      url: 'mark/mark/redo/oneDoneById',
      params: {
        id,
      },
    })
    .then(data => ({
      id: data.id,
      No: data.no,
      imgUrl: addImageUrlParam(data.imgUrl),
      studentBlockId: data.subjectiveItemId,
      secretNum: data.secretNum,
      comments: data.teacherComment,
      readTime: data.readTime,
      markNo: data.markNo,
      markScore: data.markScore,
      markSubScore: data.markSubScore,
      markType: data.markType,
      markTime: data.markTime,
      remarkScore: data.remarkScore,
      remarkSubScore: data.remarkSubScore,
      remarkType: data.remarkScore == null ? 1 : 0,
      remarkTime: data.remarkTime,
      undoTime: data.undoTime,
      typicalList: data.typicalList || [],
      reason: data.reason || '',
    }))
}

// 重评阅卷历史
export function apiGetRepeatMarkHistory(params) {
  return ajax
    .get({
      url: 'mark/mark/redo/donePage',
      params: {
        subjectiveId: params.blockId,
        page: params.currentPage,
        size: params.pageSize,
      },
      requestName: '获取重评阅卷历史',
    })
    .then(data => {
      return {
        total: data.total,
        records: data.records.map(item => ({
          id: item.id,
          No: item.no,
          studentBlockId: item.subjectiveItemId,
          markScore: item.markScore,
          markSubScore: item.markSubScore,
          markType: item.markType,
          markTime: item.markTime,
          remarkScore: item.remarkScore,
          remarkSubScore: item.remarkSubScore,
          remarkType: item.remarkScore == null ? 1 : 0,
          remarkTime: item.remarkTime,
          undoTime: item.undoTime,
          hasTypical: item.hasTypical,
        })),
      }
    })
}

// 个人重评评卷统计
export function apiGetRepeatMarkerStatistics(subjectiveId) {
  return ajax
    .get({
      url: 'mark/mark/redo/stat',
      params: {
        subjectiveId,
      },
      requestName: '获取重评评卷统计',
    })
    .then(data => {
      return {
        myMarkedNum: data.done,
        myTotalNum: data.total,
      }
    })
}

/**
 * 评卷开启、暂停、完成等
 */
export function apiStartMarking(params) {
  return ajax.post({
    url: 'mark/mark/start',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
      skipWarn: params.skipWarn || false,
    },
    // requestName: '开启评卷',
  })
}

export function apiSuspendMarking(params) {
  return ajax.post({
    url: 'mark/mark/stop',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '暂停评卷',
  })
}

export function apiFinishMarking(params) {
  return ajax.put({
    url: 'mark/mark/finish',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
  })
}

export function apiReStartMarking(params) {
  return ajax.put({
    url: 'mark/mark/restart',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '重启评卷',
  })
}

export function apiResetSubjectMarking(params) {
  return ajax.put({
    url: 'mark/mark/resetSubject',
    params: {
      examId: params.examId,
      examSubjectId: params.examSubjectId,
    },
    requestName: '重置评卷',
  })
}

export function apiStartRehearsalMarking(requestParams) {
  return ajax.post({
    url: 'mark/mark/startTrialMarking',
    params: {
      examSubjectId: requestParams.examSubjectId,
      skipWarn: requestParams.skipWarn || false,
    },
    // requestName: '开启试评'
  })
}

export function apiFinishRehearsalMarking(examSubjectId) {
  return ajax.post({
    url: 'mark/mark/finishTrialMarking',
    params: {
      examSubjectId: examSubjectId,
    },
    requestName: '结束试评',
  })
}

// 弃用
// export function apiGetExamSubjectMarkingRehearsalState(examSubjectId) {
//   return ajax.get({
//     url: 'mark/exam/isTrialMarking',
//     params: {
//       examSubjectId: examSubjectId,
//     },
//     requestName: '查询考试科目试评状态',
//   })
// }

export function apiGetExamRehearsalSubjects(examId) {
  return ajax.get({
    url: 'mark/exam/getTrialMarkingExamSubjectIds',
    params: {
      examId: examId, // *String
    },
    requestName: '查询考试试评科目Id列表',
  })
}

export function apiResetBlockMarking(params) {
  return ajax.put({
    url: 'mark/mark/resetSubjective',
    params: {
      subjectiveId: params.blockId,
    },
    requestName: '重置题块评卷',
  })
}

export function apiResetMarkerBlockMarking(requestParams) {
  return ajax.put({
    url: 'mark/mark/resetMarkerSubjective',
    params: {
      markerId: requestParams.markerId,
      subjectiveId: requestParams.subjectiveId,
    },
    requestName: '重置评卷员题块评卷',
  })
}

export function apiAddClassPrepareHeadAsLeader(examSubjectId) {
  return ajax.post({
    url: 'mark/mark/task/addClassPrepareHeadAsLeader',
    params: {
      examSubjectId: examSubjectId,
    },
    requestName: '一键将备课组长设置为题块组长（单校）',
  })
}

export function apiMarkerGetMorePaperAfterTaskFinished(requestParams) {
  return ajax.put({
    url: 'mark/mark/task/increaseMarkTaskNum',
    params: {
      subjectiveId: requestParams.subjectiveId, // *String
      increaseNum: requestParams.increaseNum, // *Number
    },
    requestName: '评卷员完成任务后申请继续评卷',
  })
}

export function apiUpdateScanStudentStatus(requestParams) {
  return ajax.put({
    url: 'mark/mark/updateExamSubjectStudentSign',
    params: {
      examSubjectId: requestParams.examSubjectId, // *[String]
      studentId: requestParams.studentId, // *[String]
      sign: requestParams.sign, // *[Integer]
    },
    requestName: '更新学生科目答卷扫描状态',
  })
}

export function apiBatchStartTrialMarking({ examSubjectIds, skipWarn }) {
  return ajax.post({
    url: 'mark/mark/batchStartTrialMarking',
    data: examSubjectIds,
    params: {
      skipWarn,
    },
    // requestName: '批量开启试评',
  })
}

export function apiBatchFinishTrialMarking({ examSubjectIds }) {
  return ajax.post({
    url: 'mark/mark/batchFinishTrialMarking',
    data: examSubjectIds,
    requestName: '批量结束试评',
  })
}

export function apiBatchStartFormalMarking({ examSubjectIds, skipWarn }) {
  return ajax.post({
    url: 'mark/mark/batchStartMark',
    data: examSubjectIds,
    params: {
      skipWarn,
    },
    // requestName: '批量开启正评',
  })
}

export function apiBatchPauseTrialMarking({ examSubjectIds }) {
  return ajax.post({
    url: 'mark/mark/batchStopMark',
    data: examSubjectIds,
    requestName: '批量暂停正评',
  })
}

export function apiGetTypicalPaperList(requestParams) {
  // role: 题组长、抽查员、科目负责人、考试管理员
  return ajax.get({
    url: 'mark/markTypical/listTypical',
    params: {
      examSubjectId: requestParams.examSubjectId, // *String
      subjectiveId: requestParams.subjectiveId, // String (题块ID)
      pageNum: requestParams.currentPage || 1, // Number
      pageSize: requestParams.pageSize || 10, // Number
    },
    requestName: '获取科目标记典型卷列表',
  })
}

export function apiLeaderSetTypicalPaper(requestParams) {
  // role: 题组长、抽查员、科目负责人、考试管理员
  return ajax.post({
    url: 'mark/markTypical/submit',
    data: {
      subjectiveId: requestParams.subjectiveId, // String
      subjectiveItemId: requestParams.subjectiveItemId, // String
      typicalComment: requestParams.typicalComment, //String
      typicalType: requestParams.typicalType, // Number (典型卷类型：1-优秀卷；2-错误卷)
    },
    requestName: '标记科目典型卷',
  })
}

export function apiDeleteTypicalPaper(subjectiveItemId) {
  // role: everybody
  return ajax.delete({
    url: 'mark/markTypical/delete',
    params: {
      subjectiveItemId: subjectiveItemId, // String*
    },
    requestName: '删除科目典型卷',
  })
}

export function apiSubmitTimeSchedule(data) {
  return ajax.post({
    url: 'mark/timeSchedule/submitTasks',
    data,
    // requestName: '提交定时计划任务',
  })
}
