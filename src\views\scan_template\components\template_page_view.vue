<template>
  <div class="image-wrapper">
    <img class="page-image" :src="page.imageUrl" @load="handleImageLoaded" />
    <div v-if="naturalWidth > 0 && showRects" class="page-mask" :class="{ 'no-label': !showLabels }">
      <div v-for="r in page.elements" :key="r.id" class="rect rect-element" :style="getRectStyle(r)">
        <div v-if="r.label" class="label">{{ r.label }}</div>
      </div>
      <div v-for="r in page.admissionOmrCells" :key="r.id" class="rect rect-admission-omr" :style="getRectStyle(r)">
        <div v-if="r.content" class="content" :style="getContentStyle(r)">{{ r.content }}</div>
      </div>
      <div v-for="r in page.objectiveOmrCells" :key="r.id" class="rect rect-objective-omr" :style="getRectStyle(r)">
        <div v-if="r.questionName" class="title" :style="getOmrTitleStyle(r)">{{ r.questionName }}</div>
        <div v-if="r.content" class="content" :style="getContentStyle(r)">{{ r.content }}</div>
      </div>
      <div v-for="r in page.scoreAreas" :key="r.id" class="rect rect-score-area" :style="getRectStyle(r)">
        <div v-if="r.label" class="label">{{ r.label }}</div>
      </div>
      <div v-for="r in page.subjectiveAreas" :key="r.id" class="rect rect-subjective-area" :style="getRectStyle(r)">
        <div class="block-info">
          <div v-if="r.blockName" class="block-name">
            <span class="block-name-inner">{{ r.blockName + r.partInfo }}</span>
          </div>
          <div v-if="r.questionNameScoresText" class="questions">
            <span class="questions-inner">（{{ r.questionNameScoresText }}）</span>
          </div>
        </div>
      </div>
      <div v-for="r in page.selectMarkAreas" :key="r.id" class="rect rect-select-mark-area" :style="getRectStyle(r)">
        <div v-if="r.content" class="content" :style="getContentStyle(r)">{{ r.content }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { markRaw } from 'vue'
  import OmrDirectionEnum from '@/enum/scan_template/omr_direction'

  export default {
    props: {
      page: Object,
      showRects: Boolean,
      showLabels: Boolean,
      rectBorderWidth: {
        type: Number,
        default: 2,
      },
    },
    emits: ['image-loaded'],
    data() {
      return {
        naturalWidth: 0,
        naturalHeight: 0,
        maskWidth: 0,
        observer: null,
      }
    },
    mounted() {
      this.maskWidth = this.$el.offsetWidth
      this.observer = markRaw(
        new ResizeObserver(entries => {
          for (let entry of entries) {
            this.maskWidth = entry.contentRect.width
          }
        })
      )
      this.observer.observe(this.$el)
    },
    beforeUnmount() {
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }
    },
    methods: {
      handleImageLoaded(e) {
        this.naturalWidth = e.target.naturalWidth
        this.naturalHeight = e.target.naturalHeight
        this.$emit('image-loaded', {
          naturalWidth: this.naturalWidth,
          naturalHeight: this.naturalHeight,
        })
      },
      getRectStyle({ rect }) {
        if (!this.naturalWidth || !this.naturalHeight) {
          return null
        }
        return {
          left: `${(rect.x / this.naturalWidth) * 100}%`,
          top: `${(rect.y / this.naturalHeight) * 100}%`,
          width: `${(rect.width / this.naturalWidth) * 100}%`,
          height: `${(rect.height / this.naturalHeight) * 100}%`,
          borderWidth: this.rectBorderWidth + 'px',
        }
      },
      getContentStyle({ rect }) {
        return {
          fontSize: `${(rect.height * this.maskWidth) / this.naturalWidth}px`,
        }
      },
      getOmrTitleStyle({ questionName, rect, direction }) {
        if (!questionName) {
          return null
        }
        let titleStyle = {
          fontSize: `${((rect.height * this.maskWidth) / this.naturalWidth) * 1.2}px`,
        }
        if (direction == OmrDirectionEnum.Vertical.id) {
          titleStyle.top = '-250%'
          titleStyle.textAlign = 'center'
        } else {
          titleStyle.right = '170%'
          titleStyle.top = '-20%'
          titleStyle.textAlign = 'right'
        }
        return titleStyle
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-wrapper {
    position: relative;

    .page-image {
      display: block;
      width: 100%;
    }

    .page-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      &.no-label {
        .label,
        .content,
        .title,
        .block-info {
          display: none;
        }
      }

      .rect {
        position: absolute;
        border: 2px solid red;
        color: red;

        .label {
          position: absolute;
          top: -16px;
          left: -2px;
          padding-right: 2px;
          padding-left: 2px;
          color: white;
          font-size: $font-size-small;
          line-height: 14px;
          white-space: pre;
          background-color: red;
        }

        .content {
          @include flex(row, center, center);
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
        }

        .title {
          position: absolute;
          width: 100%;
          height: 100%;
          line-height: 1;
        }

        &.rect-subjective-area {
          @include flex(row, center, center);
          background-color: rgba(255, 0, 0, 0.05);
        }

        .block-info {
          text-align: center;
        }

        .block-name-inner {
          font-weight: bold;
          font-size: 20px;
          background-color: rgba(255, 255, 255, 0.6);
        }

        .questions-inner {
          font-size: 14px;
          background-color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
</style>
