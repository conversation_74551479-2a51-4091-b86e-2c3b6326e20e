<script>
  import NoData from '@/components/no_data'
  import TextButton from '@/components/text_button'
  import { Avatar } from 'view-ui-plus'

  import { apiGetSchoolReferenceBookByPage, apiGetCoachBookPapers } from '@/api/qlib'

  import { debounce } from '@/utils/function'
  import { downloadUrl } from '@/utils/download'

  export default {
    components: {
      'no-data': NoData,
    },

    data() {
      return {
        // 分页
        currentPage: 1,
        pageSize: 10,
        total: 0,

        // book
        currentTermValue: '',
        gradeId: null,
        subjectId: null,
        bookName: '',
        pageCoachBookList: [],
        bookColumns: [
          {
            title: '征订代码',
            align: 'center',
            width: 180,
            render: (h, params) => h('span', {}, params.row.bookCode || '-'),
          },
          {
            title: '封面',
            key: 'cover',
            align: 'center',
            width: 80,
            render: (h, params) =>
              h(
                Avatar,
                {
                  shape: 'square',
                  src: params.row.cover || undefined,
                  style: {
                    width: '50px',
                    height: '64px',
                    margin: '8px 0',
                  },
                },
                () => 'Cover'
              ),
          },
          {
            title: '书名',
            key: 'bookName',
            align: 'center',
          },
          {
            title: '教材版本',
            key: 'pressName',
            align: 'center',
            width: 150,
          },
          {
            title: '学年学期',
            align: 'center',
            width: 120,
            render: (h, params) => h('span', params.row.termName || '-'),
          },
          {
            title: '年级',
            key: 'gradeName',
            align: 'center',
            width: 150,
          },
          {
            title: '科目',
            key: 'subjectName',
            align: 'center',
            width: 150,
          },
          {
            title: '同步卷数',
            align: 'center',
            width: 150, // 100
            render: (h, params) =>
              h(
                TextButton,
                {
                  type: 'info',
                  onClick: () => this.gotoPapers(params.row),
                },
                () => params.row.paperCount
              ),
          },
        ],

        isModeBookList: true,
        selectedCoachBook: null,
        coachBookPaperList: [],
      }
    },

    computed: {
      terms() {
        return this.$store.getters['emarking/terms']() || []
      },
      currentTerm() {
        return this.terms.find(t => t.termName === this.currentTermValue) || null
      },
      grades() {
        return this.$store.getters['emarking/gradeSubjects']() || []
      },
      allSubjects() {
        return this.$store.getters['emarking/subjects']()
      },
      subjects() {
        if (this.gradeId) {
          let grade = this.grades.find(g => g.id == this.gradeId)
          return grade ? grade.subjects : []
        } else {
          return this.allSubjects
        }
      },
      paperColumns() {
        const _Columns = [
          {
            title: '试卷号',
            width: 100,
            render: (h, params) =>
              h(
                'span',
                {},
                params.row.coachBookCode && params.row.sortCode
                  ? `${params.row.coachBookCode}-${params.row.sortCode.toString().padStart(2, '0')}`
                  : ''
              ),
          },
          {
            title: '试卷名称',
            key: 'name',
            minWidth: 150,
          },
        ]

        if (this.selectedCoachBook && this.selectedCoachBook.gradeId > 6) {
          // 小学不可下载答题卡（题卡合一）
          _Columns.push({
            title: '答题卡',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.feedbackSheetPdfUrl
                ? h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.downloadFeedbackSheet(params.row),
                    },
                    () => '下载'
                  )
                : h('span', {}, '-'),
          })
        }

        _Columns.push(
          {
            title: '答案',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.answerPDFUrl
                ? h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.downloadAnswerFile(params.row),
                    },
                    () => '下载'
                  )
                : h('span', {}, '-'),
          },
          {
            title: '听力音频',
            width: 150,
            align: 'center',
            render: (h, params) =>
              params.row.listeningAudioUrl
                ? h(
                    TextButton,
                    {
                      type: 'primary',
                      onClick: () => this.downloadPaperFile(params.row),
                    },
                    () => '下载'
                  )
                : h('span', {}, '-'),
          }
        )

        return _Columns
      },
    },

    created() {
      if (this.terms.length) {
        this.currentTermValue = this.terms[0].termName
      }

      this.fetchPageCoachBookList()
    },

    methods: {
      /**
       * fetch
       */
      fetchPageCoachBookList() {
        if (!this.currentTerm) {
          return Promise.resolve()
        }

        return apiGetSchoolReferenceBookByPage({
          semesterId: this.currentTerm.semesterId,
          term: this.currentTerm.term,
          gradeId: this.gradeId || undefined,
          subjectId: this.subjectId || undefined,
          bookName: this.bookName.trim() || undefined,
          currentPage: this.currentPage || 1,
          pageSize: this.pageSize || 10,
          onlyOpen: true,
        })
          .then(response => {
            this.total = response?.total || 0
            this.pageCoachBookList = (response?.records || []).map(b => {
              b.termName = (
                this.terms.find(t => t.semesterId === b.semesterId && t.term === b.term) || { termName: '' }
              ).termName
              b.gradeName = (this.grades.find(g => g.id === b.gradeId) || { name: '' }).name
              b.subjectName = (this.allSubjects.find(s => s.id === b.subjectId) || { name: '' }).name
              return b
            })
          })
          .catch(() => {
            this.total = 0
            this.pageCoachBookList = []
          })
      },
      fetchCoachBookPapers() {
        if (this.selectedCoachBook && this.selectedCoachBook.id) {
          return apiGetCoachBookPapers(this.selectedCoachBook.id, true).then(
            response =>
              (this.coachBookPaperList = (response || []).map((p, index) => {
                p.sortCode = index + 1
                return p
              }))
          )
        } else {
          return Promise.resolve()
        }
      },

      /**
       * event function
       */
      onChangeTerm() {
        this.changePage()
      },
      changeGradeId() {
        if (this.gradeId && this.subjectId) {
          const SelectedGrade = this.grades.find(g => g.id === this.gradeId)
          if ((SelectedGrade.subjects || []).every(s => s.id !== this.subjectId)) {
            this.subjectId = ''
          }
        }
        this.changePage()
      },
      changeSubjectId() {
        this.changePage()
      },
      changeKeyword: debounce(function () {
        this.changePage()
      }, 600),
      changePageSize(size = 10) {
        this.pageSize = size
      },
      changePage(page = 1) {
        this.currentPage = page
        this.fetchPageCoachBookList()
      },
      gotoPapers(book) {
        if (book) {
          this.selectedCoachBook = book
          this.coachBookPaperList = []
          this.fetchCoachBookPapers()
          this.isModeBookList = false
        }
      },
      back() {
        this.isModeBookList = true
      },
      downloadAnswerFile(paper) {
        if (paper.answerPDFUrl) {
          downloadUrl(
            paper.answerPDFUrl,
            `${this.selectedCoachBook.bookName.trim()}-${paper.coachBookCode}-${paper.sortCode.toString().padStart(2, '0')}-${paper.name.trim()}-答案.pdf`
          )
        } else {
          this.$Message.warning({
            duration: 4,
            content: '获取文件路径失败',
          })
        }
      },
      downloadPaperFile(paper) {
        if (paper.listeningAudioUrl) {
          downloadUrl(
            paper.listeningAudioUrl,
            `${this.selectedCoachBook.bookName.trim()}-${paper.coachBookCode}-${paper.sortCode.toString().padStart(2, '0')}-${paper.name.trim()}-听力音频.mp3`
          )
        } else {
          this.$Message.warning({
            duration: 4,
            content: '获取文件路径失败',
          })
        }
      },
      async downloadFeedbackSheet(paperInfo) {
        downloadUrl(
          paperInfo.feedbackSheetPdfUrl,
          this.selectedCoachBook.bookName.trim() +
            '-' +
            paperInfo.coachBookCode +
            '-' +
            paperInfo.sortCode.toString().padStart(2, '0') +
            '-' +
            paperInfo.name.trim() +
            '-答题卡.pdf'
        )
      },
    },
  }
</script>

<template>
  <div class="container-spe-file-download">
    <template v-if="isModeBookList">
      <div class="filter-bar">
        <div class="filter-item">
          <span class="filter-item-label">学期</span>
          <Select
            v-model="currentTermValue"
            transfer
            class="filter-item-body term"
            style="width: 200px"
            placement="bottom"
            @on-change="onChangeTerm"
          >
            <Option v-for="t of terms" :key="t.termName" :value="t.termName">{{ t.termName }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">年级</span>
          <Select
            v-model="gradeId"
            style="width: 100px"
            clearable
            transfer
            placement="bottom"
            @on-change="changeGradeId"
          >
            <Option v-for="g in grades" :key="g.id" :value="g.id">{{ g.name }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">科目</span>
          <Select
            v-model="subjectId"
            style="width: 160px"
            clearable
            transfer
            placement="bottom"
            @on-change="changeSubjectId"
          >
            <Option v-for="s of subjects" :key="s.id" :value="s.id">{{ s.name }}</Option>
          </Select>
        </div>
        <div class="filter-item">
          <span class="filter-item-label">书名</span>
          <Input
            v-model="bookName"
            style="width: 250px"
            suffix="md-search"
            clearable
            placeholder="书名关键字"
            @on-change="changeKeyword"
          />
        </div>
      </div>

      <div v-if="total">
        <Table :columns="bookColumns" :data="pageCoachBookList"></Table>

        <Page
          :total="total"
          :page-size="pageSize"
          :model-value="currentPage"
          :page-size-opts="[10, 20, 50]"
          show-total
          show-elevator
          show-sizer
          transfer
          style="float: right; margin-top: 16px"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        ></Page>
      </div>
      <no-data v-else></no-data>
    </template>
    <template v-else>
      <div class="section-header">
        <div class="back">
          <TextButton type="primary" icon="ios-arrow-back" title="返回教辅列表" @click="back"></TextButton>
        </div>
        <div class="title-bar">
          <div class="title">{{ (selectedCoachBook && selectedCoachBook.bookName) || '' }} ——【同步卷列表】</div>
          <div v-if="selectedCoachBook" class="book-info">
            <span v-if="selectedCoachBook.partner" class="info-item">{{ selectedCoachBook.partner }}</span>
            <span v-if="selectedCoachBook.pressName" class="info-item">{{ selectedCoachBook.pressName }}</span>
            <span v-if="selectedCoachBook.termName" class="info-item">{{ selectedCoachBook.termName }}</span>
            <span v-if="selectedCoachBook.gradeName" class="info-item">{{ selectedCoachBook.gradeName }}</span>
            <span v-if="selectedCoachBook.subjectName" class="info-item">{{ selectedCoachBook.subjectName }}</span>
          </div>
        </div>
        <div class="action">
          <Button type="primary" @click="fetchCoachBookPapers">刷新</Button>
        </div>
      </div>

      <Table
        v-if="coachBookPaperList && coachBookPaperList.length"
        :columns="paperColumns"
        :data="coachBookPaperList"
      ></Table>
      <no-data v-else></no-data>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .container-spe-file-download {
    padding: 0 20px;

    .filter-bar {
      @include flex(row, flex-start, center);
      margin-bottom: 16px;

      .filter-item {
        @include flex(row, flex-start, center);

        &:not(:last-child) {
          margin-right: 20px;
        }

        .filter-item-label {
          margin-right: 10px;
          white-space: nowrap;
        }
      }
    }

    .section-header {
      @include flex(row, flex-start, center);
      margin-bottom: 8px;
      line-height: 1;

      .back {
        color: $color-primary;
        font-size: $font-size-large;
      }

      .title-bar {
        flex-grow: 1;

        .title {
          font-size: $font-size-large;
        }

        .book-info {
          color: $color-second-title;
          line-height: 2.2em;

          .info-item {
            &:not(:last-child) {
              margin-right: 1em;
            }
          }
        }
      }
    }
  }
</style>
