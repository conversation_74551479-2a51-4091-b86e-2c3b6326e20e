import ajax from '@/api/ajax'

/**
 * 获取教辅导入信息
 */
export function apiGetCoachBookIngestionInfo(coachBookId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/byCoachBookId',
    params: {
      coachBookId,
    },
    requestName: '获取教辅导入信息',
  })
}

/**
 * 创建教辅导入项目
 */
export function apiCreateCoachBookIngestion({ coachBookId, fileType }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/create',
    params: {
      coachBookId,
      fileType,
    },
    requestName: '创建教辅导入项目',
  })
}

/**
 * 修改上传文件类型
 */
export function apiChangeCoachBookIngestionFileType({ ingestionId, fileType }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/changeFileType',
    params: {
      ingestionId,
      fileType,
    },
    requestName: '修改上传文件类型',
  })
}

/**
 * 上传pdf
 */
export function apiUploadCoachBookIngestionPDF({ ingestionId, file, onUploadProgress }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/upload',
    params: {
      ingestionId,
    },
    data: {
      file,
    },
    useFormData: true,
    onUploadProgress,
  })
}

/**
 * 获取上传图片批次号
 */
export function apiGetUploadCoachBookIngestionImagesBatchNumber(ingestionId) {
  return ajax.request({
    method: 'get',
    url: 'ques/coachBookIngestion/uploadImagesBatchNumber',
    params: {
      ingestionId,
    },
    requestName: '获取上传图片批次号',
  })
}

/**
 * 上传图片
 */
export function apiUploadCoachBookIngestionImage({ ingestionId, batchNumber, index, file }) {
  return ajax.request({
    method: 'post',
    url: 'ques/coachBookIngestion/uploadImage',
    params: {
      ingestionId,
      batchNumber,
      index,
    },
    data: {
      file,
    },
    useFormData: true,
  })
}

/**
 * 完成上传图片
 */
export function apiCompleteUploadCoachBookIngestionImagesBatch({ ingestionId, batchNumber }) {
  return ajax.request({
    method: 'put',
    url: 'ques/coachBookIngestion/uploadImageComplete',
    params: {
      ingestionId,
      batchNumber,
    },
    requestName: '完成上传图片',
  })
}
